"""
Test configuration setup
Sets up environment variables and mocks for testing
"""

import os
import sys
from unittest.mock import patch, MagicMock

def setup_test_environment():
    """Set up test environment variables and mocks"""
    # Set required environment variables for testing
    os.environ.setdefault('DATABASE_URL', 'postgresql://test:test@localhost:5432/test_db')
    os.environ.setdefault('OPENAI_API_KEY', 'test-key')
    os.environ.setdefault('ENVIRONMENT', 'test')
    
    # Mock the settings import to prevent validation errors
    mock_settings = MagicMock()
    mock_settings.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db'
    mock_settings.OPENAI_API_KEY = 'test-key'
    mock_settings.ENVIRONMENT = 'test'
    
    # Patch the settings import
    sys.modules['backend.app.core.config'] = MagicMock()
    sys.modules['backend.app.core.config'].settings = mock_settings
    
    return mock_settings

def teardown_test_environment():
    """Clean up test environment"""
    # Remove test environment variables
    test_vars = ['DATABASE_URL', 'OPENAI_API_KEY', 'ENVIRONMENT']
    for var in test_vars:
        if var in os.environ:
            del os.environ[var]
    
    # Remove mocked modules
    modules_to_remove = [
        'backend.app.core.config',
        'backend.app.core',
        'backend.app',
        'backend'
    ]
    for module in modules_to_remove:
        if module in sys.modules:
            del sys.modules[module]