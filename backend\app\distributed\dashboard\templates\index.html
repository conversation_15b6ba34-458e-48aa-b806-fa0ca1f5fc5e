{% extends "base.html" %}

{% block title %}Dashboard - Distributed ETL{% endblock %}

{% block content %}
<div class="row">
    <!-- Stage Progress Overview -->
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie"></i> Processing Stage Progress
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                <div class="row" id="stage-charts">
                    <!-- Stage charts will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Worker Status Summary -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Worker Status
                </h5>
            </div>
            <div class="card-body text-center">
                <canvas id="worker-status-chart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <!-- System Metrics -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tachometer-alt"></i> System Metrics
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-primary" id="total-entries">--</h3>
                            <small class="text-muted">Total Entries</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-success" id="active-workers">--</h3>
                            <small class="text-muted">Active Workers</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-info" id="processing-rate">--</h3>
                            <small class="text-muted">Entries/Hour</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h3 class="text-warning" id="error-rate">--</h3>
                            <small class="text-muted">Error Rate %</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history"></i> Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Worker</th>
                                <th>Stage</th>
                                <th>Action</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody id="activity-log">
                            <tr>
                                <td colspan="5" class="text-center text-muted">Loading activity...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let stageCharts = {};
let workerStatusChart = null;

// Stage colors matching the monolithic system
const stageColors = {
    'download_feeds': '#343a40',
    'download_full_text': '#28a745',
    'compute_embeddings': '#28a745',
    'sentiment_analysis': '#90ee90',
    'llm_analysis': '#dda0dd',
    'iptc_classification': '#ffffe0',
    'duplicate_check': '#e0ffff',
    'generate_descriptions': '#f8f9fa',
    'generate_image_prompts': '#007bff',
    'generate_preview_images': '#add8e6'
};

async function loadDashboardData() {
    try {
        // Load stage progress
        const stageResponse = await fetch('/api/stages/progress');
        const stageData = await stageResponse.json();
        updateStageCharts(stageData.stages);
        
        // Load worker status
        const workerResponse = await fetch('/api/workers/status');
        const workerData = await workerResponse.json();
        updateWorkerChart(workerData);
        updateMetrics(stageData, workerData);
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

function updateStageCharts(stages) {
    const container = document.getElementById('stage-charts');

    // Get current stage names
    const currentStages = new Set(Object.keys(stages).filter(stage => stages[stage].total > 0));
    const existingStages = new Set(Object.keys(stageCharts));

    // Remove charts for stages that no longer have data
    existingStages.forEach(stageName => {
        if (!currentStages.has(stageName)) {
            if (stageCharts[stageName]) {
                stageCharts[stageName].destroy();
                delete stageCharts[stageName];
            }
            const element = document.getElementById(`chart-${stageName}`)?.closest('.col-md-4');
            if (element) {
                element.remove();
            }
        }
    });

    Object.entries(stages).forEach(([stageName, stats]) => {
        if (stats.total > 0) {
            // Update existing chart if it exists
            if (stageCharts[stageName]) {
                const chart = stageCharts[stageName];
                chart.data.datasets[0].data = [stats.completed, stats.in_progress, stats.pending, stats.failed];
                chart.update('active'); // Smooth animation transition
            } else {
                // Create new chart
                const col = document.createElement('div');
                col.className = 'col-md-4 col-lg-3 mb-3';

                const card = document.createElement('div');
                card.className = 'card h-100';

                const cardBody = document.createElement('div');
                cardBody.className = 'card-body text-center';

                const title = document.createElement('h6');
                title.className = 'card-title';
                title.textContent = stageName.replace('_', ' ').toUpperCase();

                const canvas = document.createElement('canvas');
                canvas.id = `chart-${stageName}`;
                canvas.style.width = '150px';
                canvas.style.height = '150px';

                cardBody.appendChild(title);
                cardBody.appendChild(canvas);
                card.appendChild(cardBody);
                col.appendChild(card);
                container.appendChild(col);

                // Create donut chart with animation
                const ctx = canvas.getContext('2d');
                stageCharts[stageName] = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Completed', 'In Progress', 'Pending', 'Failed'],
                        datasets: [{
                            data: [stats.completed, stats.in_progress, stats.pending, stats.failed],
                            backgroundColor: ['#28a745', '#ffc107', '#6c757d', '#dc3545'],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: false,
                        maintainAspectRatio: true,
                        animation: {
                            duration: 1000,
                            easing: 'easeInOutQuart'
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
        }
    });
}

function updateWorkerChart(workerData) {
    const ctx = document.getElementById('worker-status-chart').getContext('2d');

    if (workerStatusChart) {
        // Update existing chart data for smooth animation
        workerStatusChart.data.datasets[0].data = [workerData.running, workerData.stopped, workerData.error];
        workerStatusChart.update('active'); // Use 'active' animation mode for smooth transitions
    } else {
        // Create new chart only if it doesn't exist
        workerStatusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Running', 'Stopped', 'Error'],
                datasets: [{
                    data: [workerData.running, workerData.stopped, workerData.error],
                    backgroundColor: ['#28a745', '#6c757d', '#dc3545'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 750,
                    easing: 'easeInOutQuart'
                },
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
}

function updateMetrics(stageData, workerData) {
    // Calculate total entries
    let totalEntries = 0;
    Object.values(stageData.stages).forEach(stage => {
        totalEntries += stage.total;
    });
    
    document.getElementById('total-entries').textContent = totalEntries.toLocaleString();
    document.getElementById('active-workers').textContent = workerData.running;
    
    // Placeholder for processing rate and error rate
    document.getElementById('processing-rate').textContent = '--';
    document.getElementById('error-rate').textContent = '--';
}

function refreshData() {
    loadDashboardData();
}

function updateDashboard(data) {
    // Handle WebSocket updates
    if (data.type === 'stage_update') {
        updateStageCharts(data.stages);
    } else if (data.type === 'worker_update') {
        updateWorkerChart(data.workers);
    }
}

// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    
    // Refresh every 30 seconds
    setInterval(loadDashboardData, 30000);
});
</script>
{% endblock %}
