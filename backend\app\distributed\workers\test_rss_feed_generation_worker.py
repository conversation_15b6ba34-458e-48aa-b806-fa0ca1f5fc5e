"""
Unit tests for RSSFeedGenerationWorker.

Tests the RSS feed generation worker functionality including batch processing,
duplicate removal, feed generation, and file handling.
"""

import pytest
import os
import tempfile
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone, timedelta

from backend.app.distributed.workers.rss_feed_generation_worker import RSSFeedGenerationWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestRSSFeedGenerationWorker:
    """Test suite for RSSFeedGenerationWorker."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.query.return_value.filter.return_value.order_by.return_value = []
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def temp_output_file(self):
        """Create a temporary output file for testing."""
        with tempfile.NamedTemporaryFile(suffix='.rss', delete=False) as f:
            temp_file = f.name
        yield temp_file
        # Cleanup
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    @pytest.fixture
    def worker_config(self, temp_output_file):
        """Create a test worker configuration."""
        config = WorkerConfig(
            worker_id="test-rss-worker",
            stages=[ProcessingStage.GENERATE_RSS_FEED],
            batch_size=5,
            heartbeat_interval=30,
            claim_timeout=300,
            stage_configs={
                ProcessingStage.GENERATE_RSS_FEED: {
                    'sentiment_threshold': 0.5,
                    'similarity_threshold': 0.85,
                    'output_file': temp_output_file,
                    'feed_title': 'Test RSS Feed',
                    'feed_description': 'Test RSS feed description',
                    'feed_link': 'http://test.com/feed.rss',
                    'days_back': 1
                }
            }
        )
        return config
    
    @pytest.fixture
    def sample_entries(self):
        """Create sample Entry objects for testing."""
        entries = []
        for i in range(3):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.description = f"Test Description {i}"
            entry.link = f"http://test.com/article-{i}"
            entry.published = datetime.now(timezone.utc) - timedelta(hours=i)
            entry.llm_positive = 0.7
            # Create different embeddings for each entry
            embedding = np.random.rand(384).astype(np.float32)
            entry.embedding = embedding.tobytes()
            entries.append(entry)
        return entries
    
    @pytest.fixture
    def sample_entry_dicts(self):
        """Create sample entry dictionaries for testing."""
        entries = []
        for i in range(3):
            embedding = np.random.rand(384).astype(np.float32)
            entries.append({
                'entry_id': f"test-entry-{i}",
                'title': f"Test Title {i}",
                'description': f"Test Description {i}",
                'link': f"http://test.com/article-{i}",
                'published': datetime.now(timezone.utc) - timedelta(hours=i),
                'embedding': embedding.tobytes(),
                'llm_positive': 0.7
            })
        return entries
    
    def test_worker_initialization(self, worker_config, mock_db_session_factory):
        """Test worker initialization with correct configuration."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            assert worker.sentiment_threshold == 0.5
            assert worker.similarity_threshold == 0.85
            assert worker.feed_title == 'Test RSS Feed'
            assert worker.feed_description == 'Test RSS feed description'
            assert worker.days_back == 1
    
    def test_worker_initialization_invalid_stage(self, mock_db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.FEED_DOWNLOAD],  # Wrong stage
            batch_size=5
        )
        
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            with pytest.raises(ValueError, match="RSSFeedGenerationWorker requires GENERATE_RSS_FEED stage"):
                RSSFeedGenerationWorker(invalid_config, mock_db_session_factory)
    
    def test_process_batch_wrong_stage(self, worker_config, mock_db_session_factory, sample_entries):
        """Test process_batch rejects wrong processing stage."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(sample_entries, ProcessingStage.FEED_DOWNLOAD)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(sample_entries)
    
    def test_process_batch_no_qualifying_entries(self, worker_config, mock_db_session_factory, sample_entries):
        """Test process_batch when no qualifying entries are found."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock _get_qualifying_entries to return empty list
            with patch.object(worker, '_get_qualifying_entries', return_value=[]):
                results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_RSS_FEED)
                
                # Should return True for all entries (nothing to process)
                assert all(success for success in results.values())
                assert len(results) == len(sample_entries)
    
    def test_process_batch_successful_generation(self, worker_config, mock_db_session_factory, sample_entries, sample_entry_dicts):
        """Test successful RSS feed generation."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock the methods
            with patch.object(worker, '_get_qualifying_entries', return_value=sample_entry_dicts):
                with patch.object(worker, '_remove_duplicates', return_value=sample_entry_dicts):
                    with patch.object(worker, '_generate_rss_feed', return_value=True):
                        results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_RSS_FEED)
                        
                        # Should return True for all entries
                        assert all(success for success in results.values())
                        assert len(results) == len(sample_entries)
    
    def test_process_batch_generation_failure(self, worker_config, mock_db_session_factory, sample_entries, sample_entry_dicts):
        """Test RSS feed generation failure."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock the methods with generation failure
            with patch.object(worker, '_get_qualifying_entries', return_value=sample_entry_dicts):
                with patch.object(worker, '_remove_duplicates', return_value=sample_entry_dicts):
                    with patch.object(worker, '_generate_rss_feed', return_value=False):
                        results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_RSS_FEED)
                        
                        # Should return False for all entries
                        assert all(not success for success in results.values())
                        assert len(results) == len(sample_entries)
    
    def test_process_batch_exception(self, worker_config, mock_db_session_factory, sample_entries):
        """Test process_batch handles exceptions."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock _get_qualifying_entries to raise exception
            with patch.object(worker, '_get_qualifying_entries', side_effect=Exception("Database error")):
                results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_RSS_FEED)
                
                # Should return False for all entries
                assert all(not success for success in results.values())
                assert len(results) == len(sample_entries)
    
    def test_get_qualifying_entries_success(self, worker_config, mock_db_session_factory):
        """Test successful retrieval of qualifying entries."""
        # Create mock query results
        mock_entry1 = Mock()
        mock_entry1.entry_id = "entry-1"
        mock_entry1.title = "Test Title 1"
        mock_entry1.description = "Test Description 1"
        mock_entry1.link = "http://test.com/1"
        mock_entry1.published = datetime.now(timezone.utc)
        mock_entry1.embedding = np.random.rand(384).astype(np.float32).tobytes()
        mock_entry1.llm_positive = 0.7
        
        mock_entry2 = Mock()
        mock_entry2.entry_id = "entry-2"
        mock_entry2.title = "Test Title 2"
        mock_entry2.description = "Test Description 2"
        mock_entry2.link = "http://test.com/2"
        mock_entry2.published = datetime.now(timezone.utc) - timedelta(hours=1)
        mock_entry2.embedding = np.random.rand(384).astype(np.float32).tobytes()
        mock_entry2.llm_positive = 0.8
        
        mock_session = mock_db_session_factory()
        mock_session.query.return_value.filter.return_value.order_by.return_value = [mock_entry1, mock_entry2]
        
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            entries = worker._get_qualifying_entries()
            
            assert len(entries) == 2
            assert entries[0]['entry_id'] == "entry-1"
            assert entries[1]['entry_id'] == "entry-2"
            mock_session.close.assert_called_once()
    
    def test_get_qualifying_entries_database_error(self, worker_config, mock_db_session_factory):
        """Test handling of database errors when retrieving entries."""
        mock_session = mock_db_session_factory()
        mock_session.query.side_effect = Exception("Database error")
        
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            entries = worker._get_qualifying_entries()
            
            assert entries == []
            mock_session.close.assert_called_once()
    
    def test_remove_duplicates_no_duplicates(self, worker_config, mock_db_session_factory, sample_entry_dicts):
        """Test duplicate removal when no duplicates exist."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock cosine_similarity to return low similarities
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.cosine_similarity') as mock_cosine:
                mock_cosine.return_value = np.array([[1.0, 0.3, 0.2], [0.3, 1.0, 0.4], [0.2, 0.4, 1.0]])
                
                unique_entries = worker._remove_duplicates(sample_entry_dicts)
                
                # Should keep all entries (no duplicates)
                assert len(unique_entries) == len(sample_entry_dicts)
    
    def test_remove_duplicates_with_duplicates(self, worker_config, mock_db_session_factory, sample_entry_dicts):
        """Test duplicate removal when duplicates exist."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock cosine_similarity to return high similarity between first two entries
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.cosine_similarity') as mock_cosine:
                mock_cosine.return_value = np.array([[1.0, 0.9, 0.2], [0.9, 1.0, 0.3], [0.2, 0.3, 1.0]])
                
                unique_entries = worker._remove_duplicates(sample_entry_dicts)
                
                # Should remove one duplicate (keep 2 out of 3)
                assert len(unique_entries) == 2
                # Should keep the most recent one (first entry has most recent timestamp)
                assert unique_entries[0]['entry_id'] == 'test-entry-0'
    
    def test_remove_duplicates_empty_list(self, worker_config, mock_db_session_factory):
        """Test duplicate removal with empty entry list."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            unique_entries = worker._remove_duplicates([])
            
            assert unique_entries == []
    
    def test_remove_duplicates_error_handling(self, worker_config, mock_db_session_factory, sample_entry_dicts):
        """Test duplicate removal error handling."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock cosine_similarity to raise exception
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.cosine_similarity', side_effect=Exception("Similarity error")):
                unique_entries = worker._remove_duplicates(sample_entry_dicts)
                
                # Should return original entries on error
                assert len(unique_entries) == len(sample_entry_dicts)
    
    def test_generate_rss_feed_success(self, worker_config, mock_db_session_factory, sample_entry_dicts, temp_output_file):
        """Test successful RSS feed generation."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            worker.output_file = temp_output_file
            
            # Mock FeedGenerator
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.FeedGenerator') as mock_fg_class:
                mock_fg = Mock()
                mock_fe = Mock()
                mock_fg.add_entry.return_value = mock_fe
                mock_fg_class.return_value = mock_fg
                
                # Mock file creation
                with patch('os.path.exists', return_value=True):
                    with patch('os.path.getsize', return_value=1024):
                        result = worker._generate_rss_feed(sample_entry_dicts)
                        
                        assert result is True
                        mock_fg.title.assert_called_once_with('Test RSS Feed')
                        mock_fg.description.assert_called_once_with('Test RSS feed description')
                        mock_fg.rss_file.assert_called_once_with(temp_output_file)
                        assert mock_fg.add_entry.call_count == len(sample_entry_dicts)
    
    def test_generate_rss_feed_feedgen_not_available(self, worker_config, mock_db_session_factory, sample_entry_dicts):
        """Test RSS feed generation when feedgen library is not available."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock ImportError for FeedGenerator
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.FeedGenerator', side_effect=ImportError("No module named 'feedgen'")):
                result = worker._generate_rss_feed(sample_entry_dicts)
                
                assert result is False
    
    def test_generate_rss_feed_file_not_created(self, worker_config, mock_db_session_factory, sample_entry_dicts, temp_output_file):
        """Test RSS feed generation when output file is not created."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            worker.output_file = temp_output_file
            
            # Mock FeedGenerator
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.FeedGenerator') as mock_fg_class:
                mock_fg = Mock()
                mock_fg_class.return_value = mock_fg
                
                # Mock file not existing after generation
                with patch('os.path.exists', return_value=False):
                    result = worker._generate_rss_feed(sample_entry_dicts)
                    
                    assert result is False
    
    def test_generate_rss_feed_exception(self, worker_config, mock_db_session_factory, sample_entry_dicts):
        """Test RSS feed generation exception handling."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Mock FeedGenerator to raise exception
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.FeedGenerator', side_effect=Exception("Feed generation error")):
                result = worker._generate_rss_feed(sample_entry_dicts)
                
                assert result is False
    
    def test_generate_rss_feed_missing_fields(self, worker_config, mock_db_session_factory, temp_output_file):
        """Test RSS feed generation with entries missing fields."""
        # Create entries with missing fields
        entries_with_missing_fields = [
            {
                'entry_id': 'test-1',
                'title': None,  # Missing title
                'description': None,  # Missing description
                'link': None,  # Missing link
                'published': datetime.now(timezone.utc),
                'embedding': np.random.rand(384).astype(np.float32).tobytes(),
                'llm_positive': 0.7
            }
        ]
        
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            worker.output_file = temp_output_file
            
            # Mock FeedGenerator
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.FeedGenerator') as mock_fg_class:
                mock_fg = Mock()
                mock_fe = Mock()
                mock_fg.add_entry.return_value = mock_fe
                mock_fg_class.return_value = mock_fg
                
                # Mock file creation
                with patch('os.path.exists', return_value=True):
                    with patch('os.path.getsize', return_value=512):
                        result = worker._generate_rss_feed(entries_with_missing_fields)
                        
                        assert result is True
                        # Should handle missing fields gracefully
                        mock_fe.title.assert_called_once_with('Untitled')
                        mock_fe.link.assert_called_once_with(href='')
                        mock_fe.description.assert_called_once_with('No description available')
    
    def test_get_worker_specific_health_file_exists(self, worker_config, mock_db_session_factory, temp_output_file):
        """Test worker-specific health information when output file exists."""
        # Create the output file
        with open(temp_output_file, 'w') as f:
            f.write("test rss content")
        
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.get_health_status') as mock_health:
                mock_health.return_value = {'status': 'healthy'}
                
                worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
                worker.output_file = temp_output_file
                health = worker.get_worker_specific_health()
                
                assert health['worker_type'] == 'RSSFeedGenerationWorker'
                assert health['sentiment_threshold'] == 0.5
                assert health['similarity_threshold'] == 0.85
                assert health['output_file'] == temp_output_file
                assert health['days_back'] == 1
                assert health['feed_title'] == 'Test RSS Feed'
                assert health['output_file_exists'] is True
                assert 'output_file_size' in health
                assert 'output_file_modified' in health
                assert ProcessingStage.GENERATE_RSS_FEED.value in health['supported_stages']
    
    def test_get_worker_specific_health_file_not_exists(self, worker_config, mock_db_session_factory):
        """Test worker-specific health information when output file doesn't exist."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.get_health_status') as mock_health:
                mock_health.return_value = {'status': 'healthy'}
                
                worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
                worker.output_file = '/nonexistent/file.rss'
                health = worker.get_worker_specific_health()
                
                assert health['output_file_exists'] is False
    
    def test_get_worker_specific_health_file_error(self, worker_config, mock_db_session_factory, temp_output_file):
        """Test worker-specific health information when file operations fail."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.get_health_status') as mock_health:
                mock_health.return_value = {'status': 'healthy'}
                
                worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
                worker.output_file = temp_output_file
                
                # Mock os.path.exists to return True but os.path.getsize to raise exception
                with patch('os.path.exists', return_value=True):
                    with patch('os.path.getsize', side_effect=OSError("Permission denied")):
                        health = worker.get_worker_specific_health()
                        
                        assert health['output_file_exists'] is True
                        assert 'output_file_error' in health
    
    def test_cleanup_resources(self, worker_config, mock_db_session_factory):
        """Test resource cleanup."""
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            
            # Should not raise any exceptions
            worker.cleanup_resources()
    
    def test_timezone_handling(self, worker_config, mock_db_session_factory, temp_output_file):
        """Test proper timezone handling for published dates."""
        # Create entry with naive datetime
        entry_with_naive_date = {
            'entry_id': 'test-1',
            'title': 'Test Title',
            'description': 'Test Description',
            'link': 'http://test.com/1',
            'published': datetime(2023, 1, 1, 12, 0, 0),  # Naive datetime
            'embedding': np.random.rand(384).astype(np.float32).tobytes(),
            'llm_positive': 0.7
        }
        
        with patch('backend.app.distributed.workers.rss_feed_generation_worker.DistributedWorker.__init__'):
            worker = RSSFeedGenerationWorker(worker_config, mock_db_session_factory)
            worker.output_file = temp_output_file
            
            # Mock FeedGenerator
            with patch('backend.app.distributed.workers.rss_feed_generation_worker.FeedGenerator') as mock_fg_class:
                mock_fg = Mock()
                mock_fe = Mock()
                mock_fg.add_entry.return_value = mock_fe
                mock_fg_class.return_value = mock_fg
                
                # Mock file creation
                with patch('os.path.exists', return_value=True):
                    with patch('os.path.getsize', return_value=512):
                        result = worker._generate_rss_feed([entry_with_naive_date])
                        
                        assert result is True
                        # Should have called pubDate with timezone-aware datetime
                        mock_fe.pubDate.assert_called_once()
                        called_date = mock_fe.pubDate.call_args[0][0]
                        assert called_date.tzinfo is not None