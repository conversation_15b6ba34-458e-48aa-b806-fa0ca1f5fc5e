-- Find news stories that appear across multiple sources
-- Shows duplicate groups that span across different news sources
WITH duplicate_sources AS (
    SELECT 
        dup_entry_id,
        COUNT(DISTINCT source) AS source_count
    FROM 
        entries
    GROUP BY 
        dup_entry_id
    HAVING 
        COUNT(DISTINCT source) > 1
)
SELECT 
    ds.dup_entry_id,
    ds.source_count,
    e.title,
    e.published,
    e.source,
    s.name AS source_name
FROM 
    duplicate_sources ds
JOIN 
    entries e ON ds.dup_entry_id = e.dup_entry_id
LEFT JOIN
    sources s ON e.source = s.source
WHERE
    e.entry_id = e.dup_entry_id  -- Get the original entry in each group
ORDER BY 
    ds.source_count DESC,
    e.published DESC
FETCH FIRST 100 ROWS ONLY
