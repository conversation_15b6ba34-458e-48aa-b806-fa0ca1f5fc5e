"""
Tests for singleton stage cooldown functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone, timedelta

from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Worker


def test_stage_has_no_cooldown():
    """Test that stages without cooldown are not affected."""
    # Create mock database session
    mock_session = Mock()
    
    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)
    
    # Test stage without cooldown (download_full_text has cooldown_minutes=0)
    result = work_queue.is_stage_in_cooldown(ProcessingStage.DOWNLOAD_FULL_TEXT)
    
    # Should not be in cooldown
    assert result is False
    
    # Database should not be queried for stages without cooldown
    mock_session.query.assert_not_called()


def test_stage_never_executed_not_in_cooldown():
    """Test that a stage that has never been executed is not in cooldown."""
    # Create mock database session
    mock_session = Mock()

    # Mock query to return no workers with execution records
    mock_session.query.return_value.filter.return_value.all.return_value = []

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Test stage with cooldown that has never been executed
    result = work_queue.is_stage_in_cooldown(ProcessingStage.DOWNLOAD_FEEDS)

    # Should not be in cooldown
    assert result is False

    # Database should be queried
    mock_session.query.assert_called_once()


def test_stage_in_cooldown():
    """Test that a stage is correctly identified as being in cooldown."""
    import json

    # Create mock database session
    mock_session = Mock()

    # Mock worker with execution record from 5 minutes ago (within 15-minute cooldown)
    mock_worker = Mock(spec=Worker)
    execution_time = datetime.now(timezone.utc) - timedelta(minutes=5)
    mock_worker.singleton_executions = json.dumps({
        "download_feeds": execution_time.isoformat()
    })
    mock_worker.worker_id = "test-worker"

    mock_session.query.return_value.filter.return_value.all.return_value = [mock_worker]

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Test stage with 15-minute cooldown
    result = work_queue.is_stage_in_cooldown(ProcessingStage.DOWNLOAD_FEEDS)

    # Should be in cooldown
    assert result is True


def test_stage_cooldown_expired():
    """Test that a stage is not in cooldown after the cooldown period has expired."""
    import json

    # Create mock database session
    mock_session = Mock()

    # Mock worker with execution record from 20 minutes ago (outside 15-minute cooldown)
    mock_worker = Mock(spec=Worker)
    execution_time = datetime.now(timezone.utc) - timedelta(minutes=20)
    mock_worker.singleton_executions = json.dumps({
        "download_feeds": execution_time.isoformat()
    })
    mock_worker.worker_id = "test-worker"

    mock_session.query.return_value.filter.return_value.all.return_value = [mock_worker]

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Test stage with 15-minute cooldown
    result = work_queue.is_stage_in_cooldown(ProcessingStage.DOWNLOAD_FEEDS)

    # Should not be in cooldown
    assert result is False


def test_record_stage_execution_new_record():
    """Test recording stage execution for a worker that hasn't executed stages before."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = False

    # Mock context manager for begin()
    mock_transaction = Mock()
    mock_session.begin.return_value.__enter__ = Mock(return_value=mock_transaction)
    mock_session.begin.return_value.__exit__ = Mock(return_value=None)

    # Mock worker with no existing singleton_executions
    mock_worker = Mock(spec=Worker)
    mock_worker.worker_id = "test-worker"
    mock_worker.singleton_executions = None

    mock_session.query.return_value.filter.return_value.first.return_value = mock_worker

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Record stage execution
    work_queue.record_stage_execution(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")

    # Verify that the worker's singleton_executions was updated
    import json
    executions = json.loads(mock_worker.singleton_executions)
    assert "download_feeds" in executions
    assert executions["download_feeds"] is not None


def test_record_stage_execution_update_existing():
    """Test recording stage execution for a worker that has executed stages before."""
    import json

    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = False

    # Mock context manager for begin()
    mock_transaction = Mock()
    mock_session.begin.return_value.__enter__ = Mock(return_value=mock_transaction)
    mock_session.begin.return_value.__exit__ = Mock(return_value=None)

    # Mock worker with existing singleton_executions
    mock_worker = Mock(spec=Worker)
    mock_worker.worker_id = "test-worker"
    old_time = datetime.now(timezone.utc) - timedelta(hours=1)
    mock_worker.singleton_executions = json.dumps({
        "download_feeds": old_time.isoformat(),
        "other_stage": "2024-01-01T00:00:00+00:00"
    })

    mock_session.query.return_value.filter.return_value.first.return_value = mock_worker

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Record stage execution
    work_queue.record_stage_execution(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")

    # Verify that the worker's singleton_executions was updated
    executions = json.loads(mock_worker.singleton_executions)
    assert "download_feeds" in executions
    assert "other_stage" in executions  # Other stages should be preserved
    assert executions["download_feeds"] != old_time.isoformat()  # Should be updated


def test_record_stage_execution_no_cooldown():
    """Test that recording execution is skipped for stages without cooldown."""
    # Create mock database session
    mock_session = Mock()
    
    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)
    
    # Try to record execution for stage without cooldown
    work_queue.record_stage_execution(ProcessingStage.DOWNLOAD_FULL_TEXT, "test-worker")
    
    # Database should not be accessed
    mock_session.query.assert_not_called()
    mock_session.add.assert_not_called()


def test_record_stage_execution_with_existing_transaction():
    """Test recording stage execution when a transaction is already active."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = True  # Transaction already active

    # Mock worker
    mock_worker = Mock(spec=Worker)
    mock_worker.worker_id = "test-worker"
    mock_worker.singleton_executions = None

    mock_session.query.return_value.filter.return_value.first.return_value = mock_worker

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Record stage execution
    work_queue.record_stage_execution(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")

    # Verify that begin() was not called (since transaction already active)
    mock_session.begin.assert_not_called()

    # Verify that the worker's singleton_executions was updated
    import json
    executions = json.loads(mock_worker.singleton_executions)
    assert "download_feeds" in executions


def test_worker_respects_cooldown():
    """Test that worker respects cooldown periods for singleton stages."""
    from backend.app.distributed.distributed_worker import DistributedWorker
    from backend.app.distributed.worker_config import WorkerConfig

    # Create a concrete test worker class
    class TestWorker(DistributedWorker):
        def process_batch(self, entries, stage):
            return True

    # Create mock work queue manager
    mock_work_queue = Mock()

    # Mock cooldown check to return True (stage is in cooldown)
    mock_work_queue.is_stage_in_cooldown.return_value = True

    # Create worker config
    config = WorkerConfig(
        worker_id="test-worker",
        stages=[ProcessingStage.DOWNLOAD_FEEDS],
        batch_size=1
    )

    # Create mock database session factory
    mock_db_session_factory = Mock()
    mock_session = Mock()
    mock_db_session_factory.return_value = mock_session

    # Create worker
    worker = TestWorker(config, mock_db_session_factory)

    # Test processing singleton stage in cooldown
    result = worker._process_singleton_stage(mock_work_queue, ProcessingStage.DOWNLOAD_FEEDS)

    # Verify that cooldown was checked
    mock_work_queue.is_stage_in_cooldown.assert_called_once_with(ProcessingStage.DOWNLOAD_FEEDS)

    # Verify that stage was not claimed (due to cooldown)
    mock_work_queue.claim_singleton_stage.assert_not_called()

    # Verify that processing was skipped
    assert result is False


if __name__ == "__main__":
    pytest.main([__file__])
