# TLS Setup for Breaking Bright API

## Quick Setup (One-time)

### Option A: PowerShell Script (Recommended for Windows)

1. **Install OpenSSL** (if not already installed):
   ```powershell
   # Using Chocolatey
   choco install openssl
   
   # Or download from: https://slproweb.com/products/Win32OpenSSL.html
   ```

2. **Run the PowerShell script:**
   ```powershell
   cd backend/k8s
   .\create-tls-cert.ps1
   ```

3. **Apply the TLS secret:**
   ```powershell
   kubectl apply -f tls-secret.yaml
   ```

4. **Update the LoadBalancer with HTTPS support:**
   ```powershell
   kubectl apply -f backend-loadbalancer.yaml
   ```

### Option B: Manual Commands (Windows CMD/PowerShell)

1. **Create certificate manually:**
   ```cmd
   cd backend\k8s
   
   openssl req -x509 -newkey rsa:4096 -keyout tls.key -out tls.crt -days 36500 -nodes -subj "/C=DE/ST=Germany/L=Germany/O=BreakingBright/CN=breakingbright.de" -addext "subjectAltName=DNS:breakingbright.de,DNS:*.breakingbright.de,DNS:api.breakingbright.de"
   ```

2. **Create Kubernetes secret:**
   ```cmd
   kubectl create secret tls api-tls-secret --cert=tls.crt --key=tls.key --dry-run=client -o yaml > tls-secret.yaml
   ```

3. **Apply configurations:**
   ```cmd
   kubectl apply -f tls-secret.yaml
   kubectl apply -f backend-loadbalancer.yaml
   ```

4. **Clean up certificate files:**
   ```cmd
   del tls.key tls.crt
   ```

## Verify Setup

```bash
# Check if secret was created
kubectl get secret api-tls-secret

# Check LoadBalancer status
kubectl get svc breaking-bright-api-lb

# Test HTTPS endpoint (once DNS propagates)
curl -k https://api.breakingbright.de/docs
```

## Cloudflare Configuration

1. Set DNS A record: `api` → `[LoadBalancer IP]` (orange cloud enabled)
2. SSL/TLS → Overview → Set to **"Full"** mode
3. SSL/TLS → Edge Certificates → Enable "Always Use HTTPS"

## Certificate Details

- **Validity**: 36500 days (~100 years)
- **Algorithm**: RSA 4096-bit
- **Subject**: CN=breakingbright.de
- **SAN**: breakingbright.de, *.breakingbright.de, api.breakingbright.de

The certificate is self-signed, which is perfect for Cloudflare's "Full" SSL mode.

## Troubleshooting

If HTTPS doesn't work:
1. Check if Oracle Cloud LoadBalancer supports SSL termination with annotations
2. Consider using nginx-ingress-controller instead
3. Verify the TLS secret exists: `kubectl describe secret api-tls-secret`