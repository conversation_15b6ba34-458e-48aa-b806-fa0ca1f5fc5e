from flask import render_template, request, jsonify
from sqlalchemy import text
from datetime import datetime
import logging
from etl.dashboard.stats import create_session
from base64 import b64encode

def register_entry_routes(app, config):
    def generate_page_links(current_page, total_pages, entry_id, dup_entry_id, per_page):
        """Generate HTML for page number links in pagination."""
        html = ""
        
        if total_pages <= 7:
            start_page = 1
            end_page = total_pages
        else:
            if current_page <= 4:
                start_page = 1
                end_page = 7
            elif current_page >= total_pages - 3:
                start_page = total_pages - 6
                end_page = total_pages
            else:
                start_page = current_page - 3
                end_page = current_page + 3

        for p in range(start_page, end_page + 1):
            active_class = 'active' if p == current_page else ''
            html += f"""
            <li class="page-item {active_class}">
                <a class="page-link" href="/entry_details?{entry_id and f'entry_id={entry_id}' or f'dup_entry_id={dup_entry_id}'}&page={p}&per_page={per_page}">{p}</a>
            </li>
            """

        return html

    @app.route('/entry_details', methods=['GET'])
    def entry_details():
        """Display details for a specific entry or all entries with a given dup_entry_id."""
        try:
            entry_id = request.args.get('entry_id')
            dup_entry_id = request.args.get('dup_entry_id')
            
            if not entry_id and not dup_entry_id:
                return "No entry_id or dup_entry_id provided", 400

            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 50))

            with create_session() as connection:
                if entry_id:
                    query = text("""
                        SELECT e.*, s.name as source_name, c.category as category_name
                        FROM entries e
                        LEFT JOIN sources s ON e.source = s.source
                        LEFT JOIN categories c ON e.iptc_newscode = c.iptc_newscode
                        WHERE e.entry_id = :entry_id
                    """)
                    result = connection.execute(query, {"entry_id": entry_id})
                    entries = [dict(row._asdict()) for row in result.fetchall()]
                    
                    # Convert preview_img to base64 if it exists
                    for entry in entries:
                        if entry.get('preview_img'):
                            entry['preview_img'] = b64encode(entry['preview_img']).decode('utf-8')
                    
                    title = f"Entry Details: {entry_id}"
                    total_entries = len(entries)
                    total_pages = 1
                    is_single_entry = True
                    show_similarity = False
                else:
                    count_query = text("""
                        SELECT COUNT(*) as total
                        FROM entries
                        WHERE dup_entry_id = :dup_entry_id
                    """)
                    count_result = connection.execute(count_query, {"dup_entry_id": dup_entry_id})
                    total_entries = count_result.fetchone()[0]
                    total_pages = (total_entries + per_page - 1) // per_page

                    page = max(1, min(page, total_pages))
                    offset = (page - 1) * per_page

                    query = text("""
                        SELECT e.*, s.name as source_name, c.category as category_name, e.dup_entry_conf
                        FROM entries e
                        LEFT JOIN sources s ON e.source = s.source
                        LEFT JOIN categories c ON e.iptc_newscode = c.iptc_newscode
                        WHERE e.dup_entry_id = :dup_entry_id
                        ORDER BY e.published DESC
                        OFFSET :offset ROWS FETCH NEXT :per_page ROWS ONLY
                    """)
                    result = connection.execute(query, {
                        "dup_entry_id": dup_entry_id,
                        "offset": offset,
                        "per_page": per_page
                    })
                    entries = [dict(row._asdict()) for row in result.fetchall()]
                    title = f"Duplicate Group: {dup_entry_id}"
                    is_single_entry = False
                    show_similarity = True

                for entry in entries:
                    if 'published' in entry and entry['published']:
                        entry['published'] = datetime.fromisoformat(str(entry['published']))

                return render_template('entry_details.html',
                                    title=title,
                                    entries=entries,
                                    total_entries=total_entries,
                                    is_single_entry=is_single_entry,
                                    show_similarity=show_similarity)

        except Exception as e:
            logging.error(f"Error displaying entry details: {e}")
            return f"Error displaying entry details: {str(e)}", 500

    @app.route('/reset_property', methods=['POST'])
    def reset_property():
        """Reset a specific property of an entry to NULL."""
        try:
            data = request.get_json()
            entry_id = data.get('entry_id')
            property_name = data.get('property')

            # Validate input
            if not entry_id or not property_name:
                return jsonify({'success': False, 'error': 'Missing required parameters'}), 400

            # List of allowed properties to reset
            allowed_properties = {
                'source': 'source',
                'iptc_newscode': 'category',
                'llm_positive': 'sentiment',
                'dup_entry_id': 'duplicate group',
                'description': 'description',
                'image_prompt': 'image keywords',
                'full_text': 'full text',
                'preview_img': 'preview image'
            }

            if property_name not in allowed_properties:
                return jsonify({'success': False, 'error': 'Invalid property'}), 400

            with create_session() as connection:
                # Create the update query
                query = text(f"""
                    UPDATE entries 
                    SET {property_name} = NULL 
                    WHERE entry_id = :entry_id
                """)
                
                connection.execute(query, {"entry_id": entry_id})
                connection.commit()

                return jsonify({
                    'success': True,
                    'message': f'Successfully reset {allowed_properties[property_name]}'
                })

        except Exception as e:
            logging.error(f"Error resetting property: {str(e)}")
            return jsonify({'success': False, 'error': str(e)}), 500





