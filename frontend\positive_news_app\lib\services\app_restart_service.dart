import 'package:flutter/material.dart';

/// Service to handle app restart functionality
class AppRestartService {
  static VoidCallback? _restartCallback;

  /// Register the restart callback from the main app
  static void registerRestartCallback(VoidCallback callback) {
    _restartCallback = callback;
  }

  /// Restart the app by calling the registered callback
  static void restartApp() {
    if (_restartCallback != null) {
      _restartCallback!();
    } else {
      debugPrint('AppRestartService: No restart callback registered');
    }
  }

  /// Check if restart functionality is available
  static bool get canRestart => _restartCallback != null;
}
