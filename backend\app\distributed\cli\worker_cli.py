#!/usr/bin/env python3
"""
CLI tool for managing distributed ETL workers.
Provides commands for starting, stopping, monitoring, and managing workers.
"""

import argparse
import asyncio
import json
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent.parent.parent))

from ..worker_config import WorkerConfig, WorkerConfigLoader
from ..worker_factory import WorkerFactory
from ..worker_manager import WorkerManager
from ..work_queue_manager import WorkQueueManager
from ..processing_stage import ProcessingStage
from ..stale_work_cleanup import StaleWorkCleanupSystem
from ..metrics_collector import MetricsCollector
from ..distributed_worker import WorkerState


class WorkerCLI:
    """Command-line interface for worker management."""
    
    def __init__(self):
        # Import here to avoid circular imports
        from backend.app.distributed.logging_config import setup_distributed_logging, get_worker_logger

        # Setup distributed logging
        setup_distributed_logging()
        self.logger = get_worker_logger('WorkerCLI')

    def setup_logging(self, log_level: str = "INFO"):
        """Setup logging configuration using distributed logging system."""
        from backend.app.distributed.logging_config import setup_distributed_logging
        setup_distributed_logging(log_level=log_level)
    
    def create_parser(self) -> argparse.ArgumentParser:
        """Create the argument parser."""
        parser = argparse.ArgumentParser(
            description="Distributed ETL Worker Management CLI",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Examples:
  # Start a worker with default configuration
  python worker_cli.py start --worker-id worker-001

  # Start a specialized feed worker
  python worker_cli.py start --worker-id feed-001 --stages download_feeds --batch-size 50

  # List all active workers
  python worker_cli.py list

  # Stop a specific worker
  python worker_cli.py stop --worker-id worker-001

  # Show worker status and metrics
  python worker_cli.py status --worker-id worker-001

  # Clean up stale work claims
  python worker_cli.py cleanup --timeout 60

  # Show work queue status
  python worker_cli.py queue-status

  # Start worker manager
  python worker_cli.py manager --port 8080
            """
        )
        
        parser.add_argument(
            '--log-level',
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='Set logging level'
        )
        
        parser.add_argument(
            '--config-file',
            type=str,
            help='Path to configuration file'
        )
        
        subparsers = parser.add_subparsers(dest='command', help='Available commands')
        
        # Start worker command
        start_parser = subparsers.add_parser('start', help='Start a worker')
        start_parser.add_argument('--worker-id', help='Unique worker identifier (auto-generated if not provided)')
        start_parser.add_argument('--stages', nargs='+', help='Processing stages to handle')
        start_parser.add_argument('--batch-size', type=int, help='Batch size for processing')
        start_parser.add_argument('--timeout', type=int, help='Claim timeout in minutes')
        start_parser.add_argument('--max-retries', type=int, help='Maximum retry attempts')
        start_parser.add_argument('--health-port', type=int, help='Health check port')
        start_parser.add_argument('--daemon', action='store_true', help='Run as daemon')
        
        # Stop worker command
        stop_parser = subparsers.add_parser('stop', help='Stop a worker')
        stop_parser.add_argument('--worker-id', required=True, help='Worker ID to stop')
        stop_parser.add_argument('--force', action='store_true', help='Force stop without graceful shutdown')
        
        # List workers command
        list_parser = subparsers.add_parser('list', help='List all workers')
        list_parser.add_argument('--format', choices=['table', 'json'], default='table', help='Output format')
        list_parser.add_argument('--filter-stage', help='Filter by processing stage')
        list_parser.add_argument('--filter-status', help='Filter by worker status')
        
        # Worker status command
        status_parser = subparsers.add_parser('status', help='Show worker status')
        status_parser.add_argument('--worker-id', help='Specific worker ID (optional)')
        status_parser.add_argument('--detailed', action='store_true', help='Show detailed metrics')
        
        # Queue status command
        queue_parser = subparsers.add_parser('queue-status', help='Show work queue status')
        queue_parser.add_argument('--stage', help='Filter by processing stage')
        queue_parser.add_argument('--format', choices=['table', 'json'], default='table', help='Output format')
        
        # Cleanup command
        cleanup_parser = subparsers.add_parser('cleanup', help='Clean up stale work claims')
        cleanup_parser.add_argument('--timeout', type=int, default=60, help='Stale timeout in minutes')
        cleanup_parser.add_argument('--dry-run', action='store_true', help='Show what would be cleaned up')
        
        # Manager command
        manager_parser = subparsers.add_parser('manager', help='Start worker manager')
        manager_parser.add_argument('--port', type=int, default=8080, help='Manager port')
        manager_parser.add_argument('--workers', type=int, default=5, help='Number of workers to manage')
        
        # Metrics command
        metrics_parser = subparsers.add_parser('metrics', help='Show worker metrics')
        metrics_parser.add_argument('--worker-id', help='Specific worker ID')
        metrics_parser.add_argument('--since', help='Show metrics since (e.g., "1h", "30m")')
        metrics_parser.add_argument('--format', choices=['table', 'json'], default='table', help='Output format')
        
        return parser
    
    async def start_worker(self, args) -> None:
        """Start a distributed worker."""
        try:
            # Load configuration
            if args.config_file:
                config = WorkerConfigLoader.from_file(args.config_file)
            else:
                # Build config from command line arguments using the loader
                # This ensures proper worker ID generation with timestamps
                stages = []
                if args.stages:
                    stage_strings = []
                    for stage_str in args.stages:
                        try:
                            ProcessingStage(stage_str)  # Validate stage
                            stage_strings.append(stage_str)
                        except ValueError:
                            self.logger.error(f"Unknown processing stage: {stage_str}")
                            return
                else:
                    stage_strings = [stage.value for stage in ProcessingStage]  # All stages

                # Use WorkerConfigLoader to get proper worker ID generation
                config = WorkerConfigLoader.from_environment(
                    worker_id=args.worker_id,  # Will be auto-generated if None
                    stages=stage_strings
                )

                # Override with command line arguments
                if args.batch_size:
                    config.batch_size = args.batch_size
                if args.timeout:
                    config.claim_timeout_minutes = args.timeout
                if args.max_retries:
                    config.max_retries = args.max_retries
                if args.health_port:
                    config.health_check_port = args.health_port
            
            # Create and start worker using factory
            worker = WorkerFactory.create_worker(config)
            
            self.logger.info(f"Starting worker {config.worker_id} with stages: {[s.value for s in config.stages]}")
            
            if args.daemon:
                # Run as daemon (this would need proper daemonization in production)
                worker.start()
                # Keep the process alive for daemon mode
                try:
                    while True:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    self.logger.info("Received interrupt signal, shutting down...")
                    worker.stop()
            else:
                # Run in foreground
                try:
                    worker.start()
                    # Keep the process alive until interrupted
                    while True:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    self.logger.info("Received interrupt signal, shutting down...")
                    worker.stop()
                    
        except Exception as e:
            self.logger.error(f"Failed to start worker: {e}")
            sys.exit(1)
    
    async def stop_worker(self, args) -> None:
        """Stop a specific worker."""
        try:
            # This would need to connect to the worker's management interface
            # For now, we'll show what the implementation would look like
            
            self.logger.info(f"Stopping worker {args.worker_id}...")
            
            if args.force:
                self.logger.warning("Force stopping worker (may lose in-progress work)")
                # Implementation would send SIGKILL
            else:
                self.logger.info("Gracefully stopping worker...")
                # Implementation would send SIGTERM and wait for graceful shutdown
            
            # In a real implementation, this would:
            # 1. Connect to worker's management port
            # 2. Send shutdown signal
            # 3. Wait for confirmation or timeout
            # 4. Report success/failure
            
            print(f"Worker {args.worker_id} stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to stop worker: {e}")
            sys.exit(1)
    
    async def list_workers(self, args) -> None:
        """List all active workers."""
        try:
            # Import database session factory
            from backend.app.core.database import SessionLocal
            from backend.app.models.models import Worker

            # Query actual workers from database
            db = SessionLocal()
            try:
                db_workers = db.query(Worker).all()

                workers = []
                for worker in db_workers:
                    # Parse stages from the stages column (handle both JSON and string formats)
                    stages = []
                    if worker.stages:
                        try:
                            # Try parsing as JSON first
                            import json
                            if worker.stages.startswith('[') and worker.stages.endswith(']'):
                                stages = json.loads(worker.stages)
                            else:
                                # Handle plain string format
                                stages = [worker.stages.strip()]
                        except (json.JSONDecodeError, AttributeError):
                            # Fallback to comma-separated parsing
                            stages = [s.strip() for s in str(worker.stages).split(',') if s.strip()]

                    workers.append({
                        "worker_id": worker.worker_id,
                        "stages": stages,
                        "status": worker.status or "unknown",
                        "batch_size": worker.current_batch_size or 0,
                        "last_heartbeat": worker.last_heartbeat.isoformat() if worker.last_heartbeat else "never",
                        "processed_entries": worker.entries_processed or 0,
                        "worker_type": worker.worker_type or "unknown"
                    })
            finally:
                db.close()
            
            # Apply filters
            if args.filter_stage:
                workers = [w for w in workers if args.filter_stage in w["stages"]]
            
            if args.filter_status:
                workers = [w for w in workers if w["status"] == args.filter_status]
            
            if args.format == 'json':
                print(json.dumps(workers, indent=2))
            else:
                # Table format with proper alignment
                if not workers:
                    print("No workers found.")
                    return

                # Print header
                header = f"{'Worker ID':<35} {'Type':<20} {'Stages':<25} {'Status':<10} {'Batch Size':<12} {'Processed':<10}"
                print(header)
                print("-" * len(header))

                # Print workers
                for worker in workers:
                    # Truncate worker ID if too long
                    worker_id = worker["worker_id"]
                    if len(worker_id) > 34:
                        worker_id = worker_id[:31] + "..."

                    # Format stages
                    stages_str = ",".join(worker["stages"][:2])  # Show first 2 stages
                    if len(worker["stages"]) > 2:
                        stages_str += "..."
                    if len(stages_str) > 24:
                        stages_str = stages_str[:21] + "..."

                    # Format worker type
                    worker_type = worker["worker_type"]
                    if len(worker_type) > 19:
                        worker_type = worker_type[:16] + "..."

                    # Print row with proper formatting
                    row = f"{worker_id:<35} {worker_type:<20} {stages_str:<25} {worker['status']:<10} {worker['batch_size']:<12} {worker['processed_entries']:<10}"
                    print(row)
                
        except Exception as e:
            self.logger.error(f"Failed to list workers: {e}")
            sys.exit(1)
    
    async def show_status(self, args) -> None:
        """Show worker status and metrics."""
        try:
            if args.worker_id:
                # Show specific worker status
                worker_status = {
                    "worker_id": args.worker_id,
                    "status": "running",
                    "uptime": "2h 15m",
                    "stages": ["download_feeds"],
                    "current_batch": {
                        "size": 25,
                        "progress": 18,
                        "started_at": "2025-01-15T10:25:00Z"
                    },
                    "metrics": {
                        "total_processed": 1250,
                        "success_rate": 98.5,
                        "avg_processing_time": 2.3,
                        "errors_last_hour": 2
                    }
                }
                
                print(f"Worker: {worker_status['worker_id']}")
                print(f"Status: {worker_status['status']}")
                print(f"Uptime: {worker_status['uptime']}")
                print(f"Stages: {', '.join(worker_status['stages'])}")
                
                if args.detailed:
                    print("\nCurrent Batch:")
                    batch = worker_status['current_batch']
                    print(f"  Size: {batch['size']}")
                    print(f"  Progress: {batch['progress']}/{batch['size']}")
                    print(f"  Started: {batch['started_at']}")
                    
                    print("\nMetrics:")
                    metrics = worker_status['metrics']
                    print(f"  Total Processed: {metrics['total_processed']}")
                    print(f"  Success Rate: {metrics['success_rate']}%")
                    print(f"  Avg Processing Time: {metrics['avg_processing_time']}s")
                    print(f"  Errors (last hour): {metrics['errors_last_hour']}")
            else:
                # Show overall system status
                system_status = {
                    "total_workers": 8,
                    "active_workers": 7,
                    "failed_workers": 1,
                    "total_entries_processed": 15420,
                    "entries_in_queue": 234,
                    "avg_processing_rate": 125.5  # entries per minute
                }
                
                print("System Status:")
                print(f"  Total Workers: {system_status['total_workers']}")
                print(f"  Active Workers: {system_status['active_workers']}")
                print(f"  Failed Workers: {system_status['failed_workers']}")
                print(f"  Total Processed: {system_status['total_entries_processed']}")
                print(f"  Queue Size: {system_status['entries_in_queue']}")
                print(f"  Processing Rate: {system_status['avg_processing_rate']} entries/min")
                
        except Exception as e:
            self.logger.error(f"Failed to show status: {e}")
            sys.exit(1)
    
    async def show_queue_status(self, args) -> None:
        """Show work queue status."""
        try:
            # Import database session factory
            from backend.app.core.database import SessionLocal
            from backend.app.models.models import Entry, Source, Worker
            from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus, ProcessingStatusValue
            from backend.app.distributed.progress_reporter import aggregate_worker_progress
            from sqlalchemy import func
            from datetime import datetime, timedelta, timezone
            import json

            # Query actual queue statistics from database
            db = SessionLocal()
            try:
                queue_status = []

                # Try to get progress from worker reports first (faster)
                aggregated_progress = aggregate_worker_progress(db)

                if aggregated_progress:
                    # Use worker-reported progress
                    for stage_name, progress_data in aggregated_progress.items():
                        # Only include stages that have some activity
                        total_activity = progress_data.get('pending', 0) + progress_data.get('in_progress', 0) + progress_data.get('failed', 0)
                        if total_activity > 0:
                            queue_status.append({
                                "stage": stage_name,
                                "pending": progress_data.get('pending', 0),
                                "in_progress": progress_data.get('in_progress', 0),
                                "failed": progress_data.get('failed', 0),
                                "avg_wait_time": "0s",  # Placeholder
                                "source": "worker_reports"
                            })
                else:
                    # Fall back to direct calculation with proper stage filtering
                    from backend.app.distributed.processing_stage import get_stage_selection_criteria, resolve_filter_values
                    from backend.app.core.config import settings
                    from datetime import timedelta
                    from sqlalchemy import or_

                    # Define stages that create entries vs process entries
                    entry_creation_stages = {ProcessingStage.DOWNLOAD_FEEDS}
                    entry_processing_stages = {s for s in ProcessingStage if s not in entry_creation_stages}

                    # Count statistics for entry processing stages only
                    stage_stats = {}

                    for stage in entry_processing_stages:
                        try:
                            criteria = get_stage_selection_criteria(stage)

                            # Build query with same logic as monolithic ETL
                            query = db.query(Entry)

                            # Apply mandatory fields (must not be None)
                            if criteria.mandatory_fields:
                                for field in criteria.mandatory_fields:
                                    if hasattr(Entry, field):
                                        query = query.filter(getattr(Entry, field).isnot(None))

                            # Apply analysis fields (should be None for processing)
                            if criteria.analysis_fields:
                                analysis_conditions = []
                                for field in criteria.analysis_fields:
                                    if hasattr(Entry, field):
                                        analysis_conditions.append(getattr(Entry, field).is_(None))
                                if analysis_conditions:
                                    query = query.filter(or_(*analysis_conditions))

                            # Apply filters with resolved values
                            resolved_filters = resolve_filter_values(criteria.filters, settings)
                            has_published_filter = False

                            for field, operator, value in resolved_filters:
                                if hasattr(Entry, field):
                                    col = getattr(Entry, field)
                                    if field == 'published':
                                        has_published_filter = True

                                    if operator == '>=':
                                        query = query.filter(col >= value)
                                    elif operator == '<':
                                        query = query.filter(col < value)
                                    elif operator == '==':
                                        query = query.filter(col == value)

                            # Apply default time filter if no published filter provided
                            if not has_published_filter:
                                lookback_days = getattr(settings, 'NEWS_ETL_LOOKBACK_DAYS', 30)
                                cutoff_date = datetime.now(timezone.utc) - timedelta(days=lookback_days)
                                query = query.filter(Entry.published >= cutoff_date)

                            # Only consider entries that have processing_status
                            query = query.filter(Entry.processing_status.isnot(None))

                            # Get all matching entries
                            entries = query.all()

                            stage_counts = {
                                'pending': 0,
                                'in_progress': 0,
                                'completed': 0,
                                'failed': 0
                            }

                            # Parse processing status for each entry
                            for entry in entries:
                                try:
                                    processing_status = ProcessingStatus.from_json(entry.processing_status)
                                    stage_status = processing_status.get_stage_status(stage)
                                    status_value = stage_status.status.value

                                    if status_value in stage_counts:
                                        stage_counts[status_value] += 1
                                except Exception:
                                    # Skip entries with invalid processing status
                                    continue

                            stage_stats[stage.value] = stage_counts

                        except Exception as e:
                            self.logger.warning(f"Error calculating stats for stage {stage.value}: {e}")
                            continue

                    # Add RSS source status for download_feeds stage (fallback calculation)
                    # Count RSS sources from configuration file instead of database
                    import yaml
                    import os
                    from backend.app.core.config import settings

                    rss_file_path = settings.NEWS_RSS_SOURCES_FILE
                    if not os.path.isabs(rss_file_path):
                        rss_file_path = os.path.join('etl', rss_file_path)

                    try:
                        with open(rss_file_path, 'r', encoding='utf-8') as file:
                            config = yaml.safe_load(file)
                            rss_sources = config.get('rss_sources', [])
                            total_sources = len(rss_sources)
                    except Exception:
                        total_sources = 0

                    # Check if download_feeds is currently running (singleton stage)
                    download_feeds_in_progress = 0
                    workers = db.query(Worker).filter(Worker.status == 'running').all()
                    for worker in workers:
                        if worker.singleton_executions:
                            try:
                                executions = json.loads(worker.singleton_executions)
                                if 'download_feeds' in executions:
                                    # Check if currently executing (within last 5 minutes)
                                    last_exec = datetime.fromisoformat(executions['download_feeds'].replace('Z', '+00:00'))
                                    if (datetime.now(timezone.utc) - last_exec).total_seconds() < 300:
                                        download_feeds_in_progress = 1
                                        break
                            except:
                                continue

                    # Add download_feeds status (RSS sources, not entries)
                    queue_status.append({
                        "stage": "download_feeds",
                        "pending": total_sources if download_feeds_in_progress == 0 else 0,
                        "in_progress": download_feeds_in_progress,
                        "failed": 0,  # Would need to track failed RSS sources
                        "completed": 0,  # RSS sources don't have "completed" state
                        "avg_wait_time": "0s",
                        "note": f"RSS sources (total: {total_sources})",
                        "source": "direct_calculation"
                    })

                    # Build queue status for entry processing stages with activity
                    for stage in entry_processing_stages:
                        stage_name = stage.value
                        stats = stage_stats[stage_name]

                        # Calculate average wait time (simplified)
                        avg_wait_time = "0s"  # Placeholder - would need more complex calculation

                        # Only include stages that have some activity
                        total_activity = stats['pending'] + stats['in_progress'] + stats['failed']
                        if total_activity > 0:
                            queue_status.append({
                                "stage": stage_name,
                                "pending": stats['pending'],
                                "in_progress": stats['in_progress'],
                                "failed": stats['failed'],
                                "avg_wait_time": avg_wait_time,
                                "source": "direct_calculation"
                            })

                # Always add download_feeds status for worker reports case too
                if aggregated_progress and 'download_feeds' not in aggregated_progress:
                    # Add download_feeds status even when using worker reports
                    import yaml
                    import os
                    from backend.app.core.config import settings

                    rss_file_path = settings.NEWS_RSS_SOURCES_FILE
                    if not os.path.isabs(rss_file_path):
                        rss_file_path = os.path.join('etl', rss_file_path)

                    try:
                        with open(rss_file_path, 'r', encoding='utf-8') as file:
                            config = yaml.safe_load(file)
                            rss_sources = config.get('rss_sources', [])
                            total_sources = len(rss_sources)
                    except Exception:
                        total_sources = 0

                    queue_status.append({
                        "stage": "download_feeds",
                        "pending": total_sources,
                        "in_progress": 0,
                        "failed": 0,
                        "avg_wait_time": "0s",
                        "note": f"RSS sources (total: {total_sources})",
                        "source": "worker_reports"
                    })
            finally:
                db.close()
            
            # Apply stage filter
            if args.stage:
                queue_status = [q for q in queue_status if q["stage"] == args.stage]
            
            if args.format == 'json':
                print(json.dumps(queue_status, indent=2))
            else:
                print(f"{'Stage':<25} {'Pending':<10} {'In Progress':<12} {'Failed':<8} {'Avg Wait':<10}")
                print("-" * 75)
                for queue in queue_status:
                    print(f"{queue['stage']:<25} {queue['pending']:<10} {queue['in_progress']:<12} "
                          f"{queue['failed']:<8} {queue['avg_wait_time']:<10}")
                
        except Exception as e:
            self.logger.error(f"Failed to show queue status: {e}")
            sys.exit(1)
    
    async def cleanup_stale_work(self, args) -> None:
        """Clean up stale work claims."""
        try:
            # Import database session factory
            from backend.app.core.database import SessionLocal
            from backend.app.models.models import Entry
            from datetime import datetime, timedelta

            # Calculate cutoff time
            cutoff_time = datetime.utcnow() - timedelta(minutes=args.timeout)

            db = SessionLocal()
            try:
                # Find stale claims (entries claimed but not completed or failed)
                stale_entries = db.query(Entry).filter(
                    Entry.claimed_by.isnot(None),
                    Entry.claimed_at < cutoff_time
                ).all()

                if args.dry_run:
                    self.logger.info(f"Dry run: Finding stale claims older than {args.timeout} minutes")
                    print(f"Found {len(stale_entries)} stale claims:")
                    for entry in stale_entries:
                        print(f"  Entry: {entry.entry_id}, Worker: {entry.claimed_by}, "
                              f"Claimed: {entry.claimed_at.isoformat() if entry.claimed_at else 'unknown'}")
                else:
                    self.logger.info(f"Cleaning up {len(stale_entries)} stale claims older than {args.timeout} minutes")

                    # Release stale claims
                    cleaned_count = 0
                    for entry in stale_entries:
                        entry.claimed_by = None
                        entry.claimed_at = None
                        cleaned_count += 1

                    db.commit()
                    print(f"Successfully cleaned up {cleaned_count} stale claims")
            finally:
                db.close()
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup stale work: {e}")
            sys.exit(1)
    
    async def start_manager(self, args) -> None:
        """Start the worker manager."""
        try:
            self.logger.info(f"Starting worker manager on port {args.port}")

            # Import database session factory and health check server
            from backend.app.core.database import SessionLocal
            from backend.app.distributed.health_check_server import HealthCheckServer

            # Create worker manager with database session factory
            manager = WorkerManager(SessionLocal)

            # Configure manager with target worker count
            if hasattr(manager, 'max_workers_per_stage'):
                manager.max_workers_per_stage = args.workers
            if hasattr(manager, 'target_worker_count'):
                manager.target_worker_count = args.workers

            # Create health provider function for the manager
            def manager_health_provider():
                return {
                    "status": "healthy",
                    "timestamp": datetime.now().isoformat(),
                    "manager_state": manager._state.value if hasattr(manager, '_state') else "unknown",
                    "target_workers": args.workers,
                    "registered_workers": len(manager.workers) if hasattr(manager, 'workers') else 0,
                    "active_workers": len([w for w in manager.workers.values() if hasattr(w, 'state') and w.state == WorkerState.RUNNING]) if hasattr(manager, 'workers') else 0,
                    "max_workers_per_stage": getattr(manager, 'max_workers_per_stage', 'not_set')
                }

            # Start health check server
            health_server = HealthCheckServer(args.port, manager_health_provider)
            health_server.start()

            try:
                manager.start()

                # Create the requested number of workers
                await self._create_managed_workers(manager, args.workers)

                self.logger.info(f"Worker manager started with health endpoint at http://localhost:{args.port}/health")

                # Keep the manager running until interrupted
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                self.logger.info("Received interrupt signal, shutting down manager...")
                health_server.stop()
                manager.stop()

        except Exception as e:
            self.logger.error(f"Failed to start manager: {e}")
            sys.exit(1)

    async def _create_managed_workers(self, manager, worker_count: int) -> None:
        """Create and start the specified number of workers under manager control."""
        from backend.app.distributed.worker_config import WorkerConfigLoader
        from backend.app.distributed.worker_factory import WorkerFactory
        from backend.app.distributed.processing_stage import ProcessingStage
        import socket
        import re
        import os

        # Get hostname for configuration matching
        hostname = socket.gethostname()

        # Default: implemented stages (full ETL chain)
        default_stages = [
            ProcessingStage.DOWNLOAD_FEEDS,
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            ProcessingStage.COMPUTE_EMBEDDINGS,
            ProcessingStage.SENTIMENT_ANALYSIS,
            ProcessingStage.LLM_ANALYSIS,
            ProcessingStage.IPTC_CLASSIFICATION,
            ProcessingStage.DUPLICATE_CHECK,
            ProcessingStage.GENERATE_DESCRIPTIONS,
            ProcessingStage.GENERATE_IMAGE_PROMPTS,
            ProcessingStage.GENERATE_PREVIEW_IMAGES
        ]

        # Configuration-based stage selection
        configured_stages = self._get_configured_stages(hostname, default_stages)

        created_workers = 0
        for i in range(worker_count):
            try:
                # Create worker config with configured stages
                config = WorkerConfigLoader.from_environment(
                    worker_id=None,  # Auto-generate
                    stages=[stage.value for stage in configured_stages]  # Convert enums to strings
                )

                # Override specific settings for managed workers
                config.batch_size = 10
                config.claim_timeout_minutes = 5
                config.heartbeat_interval_seconds = 30
                config.no_work_delay_seconds = 120

                # Import database session factory
                from backend.app.core.database import SessionLocal

                # Create worker instance using WorkerFactory (handles all stages)
                worker = WorkerFactory.create_worker(config, SessionLocal)

                # Register with manager (correct signature)
                manager.register_worker(worker)

                # Start the worker in background thread (not async)
                import threading
                worker_thread = threading.Thread(
                    target=worker.start,
                    name=f"managed-worker-{worker.config.worker_id}",
                    daemon=False
                )
                worker_thread.start()

                stage_names = [s.value for s in configured_stages]
                worker_type = type(worker).__name__
                self.logger.info(f"Created {worker_type} {worker.config.worker_id} with stages: {stage_names}")
                created_workers += 1

            except Exception as e:
                self.logger.error(f"Failed to create worker {i+1}: {e}")
                import traceback
                self.logger.error(f"Error details: {traceback.format_exc()}")

        self.logger.info(f"Successfully created {created_workers}/{worker_count} workers")

    def _get_configured_stages(self, hostname: str, default_stages: list) -> list:
        """Get configured stages based on hostname and environment variables."""
        from backend.app.distributed.processing_stage import ProcessingStage
        import re
        import os

        # Check for stage configuration via environment variables
        # Format: WORKER_STAGES_INCLUDE=stage1,stage2 or WORKER_STAGES_EXCLUDE=stage1,stage2
        # Format: WORKER_STAGES_HOSTNAME_PATTERN=pattern:include:stage1,stage2

        # Start with default stages
        configured_stages = default_stages.copy()

        # Check for hostname-based configuration
        hostname_config = os.getenv("WORKER_STAGES_HOSTNAME_CONFIG", "")
        if hostname_config:
            # Format: "pattern1:include:stage1,stage2;pattern2:exclude:stage3,stage4"
            for config_entry in hostname_config.split(";"):
                if not config_entry.strip():
                    continue

                parts = config_entry.strip().split(":")
                if len(parts) != 3:
                    continue

                pattern, action, stages_str = parts

                # Check if hostname matches pattern
                if re.search(pattern, hostname, re.IGNORECASE):
                    stage_names = [s.strip() for s in stages_str.split(",") if s.strip()]
                    stages = []

                    # Convert stage names to enums
                    for stage_name in stage_names:
                        try:
                            stage = ProcessingStage(stage_name)
                            stages.append(stage)
                        except ValueError:
                            self.logger.warning(f"Unknown stage '{stage_name}' in hostname config")

                    if action.lower() == "include":
                        configured_stages = stages
                        self.logger.info(f"Hostname '{hostname}' matched pattern '{pattern}': including only {[s.value for s in stages]}")
                        break
                    elif action.lower() == "exclude":
                        configured_stages = [s for s in configured_stages if s not in stages]
                        self.logger.info(f"Hostname '{hostname}' matched pattern '{pattern}': excluding {[s.value for s in stages]}")

        # Check for global include/exclude (overrides hostname config)
        include_stages = os.getenv("WORKER_STAGES_INCLUDE", "")
        if include_stages:
            stage_names = [s.strip() for s in include_stages.split(",") if s.strip()]
            stages = []
            for stage_name in stage_names:
                try:
                    stage = ProcessingStage(stage_name)
                    stages.append(stage)
                except ValueError:
                    self.logger.warning(f"Unknown stage '{stage_name}' in WORKER_STAGES_INCLUDE")
            if stages:
                configured_stages = stages
                self.logger.info(f"Global include: using only stages {[s.value for s in stages]}")

        exclude_stages = os.getenv("WORKER_STAGES_EXCLUDE", "")
        if exclude_stages:
            stage_names = [s.strip() for s in exclude_stages.split(",") if s.strip()]
            stages = []
            for stage_name in stage_names:
                try:
                    stage = ProcessingStage(stage_name)
                    stages.append(stage)
                except ValueError:
                    self.logger.warning(f"Unknown stage '{stage_name}' in WORKER_STAGES_EXCLUDE")
            if stages:
                configured_stages = [s for s in configured_stages if s not in stages]
                self.logger.info(f"Global exclude: removing stages {[s.value for s in stages]}")

        return configured_stages
    
    async def show_metrics(self, args) -> None:
        """Show worker metrics."""
        try:
            # This would query the metrics collector for worker performance data
            if args.worker_id:
                metrics = {
                    "worker_id": args.worker_id,
                    "uptime": "2h 15m",
                    "total_processed": 1250,
                    "success_rate": 98.5,
                    "avg_processing_time": 2.3,
                    "throughput": 9.2,  # entries per minute
                    "memory_usage": "245MB",
                    "cpu_usage": "15%",
                    "error_rate": 1.5,
                    "last_error": "Connection timeout at 2025-01-15T10:15:00Z"
                }
                
                if args.format == 'json':
                    print(json.dumps(metrics, indent=2))
                else:
                    print(f"Metrics for {metrics['worker_id']}:")
                    print(f"  Uptime: {metrics['uptime']}")
                    print(f"  Total Processed: {metrics['total_processed']}")
                    print(f"  Success Rate: {metrics['success_rate']}%")
                    print(f"  Avg Processing Time: {metrics['avg_processing_time']}s")
                    print(f"  Throughput: {metrics['throughput']} entries/min")
                    print(f"  Memory Usage: {metrics['memory_usage']}")
                    print(f"  CPU Usage: {metrics['cpu_usage']}")
                    print(f"  Error Rate: {metrics['error_rate']}%")
                    if metrics['last_error']:
                        print(f"  Last Error: {metrics['last_error']}")
            else:
                # Show system-wide metrics
                system_metrics = {
                    "total_workers": 8,
                    "active_workers": 7,
                    "total_throughput": 125.5,
                    "avg_success_rate": 97.8,
                    "total_memory_usage": "1.8GB",
                    "avg_cpu_usage": "23%"
                }
                
                print("System Metrics:")
                print(f"  Total Workers: {system_metrics['total_workers']}")
                print(f"  Active Workers: {system_metrics['active_workers']}")
                print(f"  Total Throughput: {system_metrics['total_throughput']} entries/min")
                print(f"  Avg Success Rate: {system_metrics['avg_success_rate']}%")
                print(f"  Total Memory Usage: {system_metrics['total_memory_usage']}")
                print(f"  Avg CPU Usage: {system_metrics['avg_cpu_usage']}")
                
        except Exception as e:
            self.logger.error(f"Failed to show metrics: {e}")
            sys.exit(1)
    
    async def run(self) -> None:
        """Main CLI entry point."""
        parser = self.create_parser()
        args = parser.parse_args()
        
        if not args.command:
            parser.print_help()
            return
        
        self.setup_logging(args.log_level)
        
        # Route to appropriate command handler
        if args.command == 'start':
            await self.start_worker(args)
        elif args.command == 'stop':
            await self.stop_worker(args)
        elif args.command == 'list':
            await self.list_workers(args)
        elif args.command == 'status':
            await self.show_status(args)
        elif args.command == 'queue-status':
            await self.show_queue_status(args)
        elif args.command == 'cleanup':
            await self.cleanup_stale_work(args)
        elif args.command == 'manager':
            await self.start_manager(args)
        elif args.command == 'metrics':
            await self.show_metrics(args)
        else:
            parser.print_help()


def main():
    """Main entry point for the CLI."""
    cli = WorkerCLI()
    asyncio.run(cli.run())


if __name__ == '__main__':
    main()