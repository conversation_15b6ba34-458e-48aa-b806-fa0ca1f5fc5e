#!/usr/bin/env python3
"""
Test script to verify the clear error workers functionality.

This script creates some test error workers and then tests the clear functionality.
"""

import requests
import json
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from backend.app.core.database import SessionLocal
from backend.app.models.models import Worker


def create_test_error_workers(count: int = 3):
    """Create some test error workers for testing."""
    with SessionLocal() as db:
        for i in range(count):
            worker = Worker(
                worker_id=f"test-error-worker-{i}",
                status="error",
                last_error=f"Test error {i}",
                started_at=datetime.now(timezone.utc),
                stopped_at=datetime.now(timezone.utc),
                last_heartbeat=datetime.now(timezone.utc),
                current_batch_size=0
            )
            db.add(worker)
        db.commit()
        print(f"Created {count} test error workers")


def test_clear_error_workers():
    """Test the clear error workers API endpoint."""
    url = "http://127.0.0.1:8081/api/workers/clear_errors"
    
    try:
        response = requests.post(url, headers={'Content-Type': 'application/json'})
        response.raise_for_status()
        
        result = response.json()
        print(f"Clear workers result: {json.dumps(result, indent=2)}")
        
        return result
        
    except Exception as e:
        print(f"Error testing clear workers: {e}")
        return None


def count_error_workers():
    """Count current error workers in database."""
    with SessionLocal() as db:
        count = db.query(Worker).filter(Worker.status == 'error').count()
        print(f"Current error workers in database: {count}")
        return count


def main():
    print("Testing clear error workers functionality...")
    print("-" * 50)
    
    # Count initial error workers
    initial_count = count_error_workers()
    
    # Create some test error workers if none exist
    if initial_count == 0:
        create_test_error_workers(3)
        count_error_workers()
    
    # Test the clear functionality
    result = test_clear_error_workers()
    
    if result:
        print(f"Successfully cleared {result.get('cleared_count', 0)} error workers")
    
    # Count final error workers
    final_count = count_error_workers()
    
    print(f"Workers cleared: {initial_count - final_count}")
    print("Test completed!")


if __name__ == "__main__":
    main()
