import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/screens/category/category_view_screen.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/models/news_model.dart';

// Use existing mocks
import 'service_test.mocks.dart';

void main() {
  group('CategoryViewScreen Deduplication Tests', () {
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: MultiProvider(
          providers: [
            Provider<ApiService>.value(value: mockApiService),
            ChangeNotifierProvider<SettingsProvider>(
              create: (_) => SettingsProvider(),
            ),
          ],
          child: const CategoryViewScreen(
            categoryCode: 'test_category',
            categoryName: 'Test Category',
          ),
        ),
      );
    }

    testWidgets('should deduplicate news items when loading more data with loop-based approach', (WidgetTester tester) async {
      // Create test data with duplicates
      final initialItems = [
        EntryWithSource(
          entryId: 'entry1',
          title: 'News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now().subtract(const Duration(hours: 1)),
          dupEntryId: 'dup1',
        ),
        EntryWithSource(
          entryId: 'entry2',
          title: 'News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now().subtract(const Duration(hours: 2)),
          dupEntryId: 'dup2',
        ),
      ];

      // First batch of new items (mostly duplicates)
      final firstBatchItems = [
        EntryWithSource(
          entryId: 'entry3',
          title: 'News 1 Duplicate',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.now().subtract(const Duration(hours: 3)),
          dupEntryId: 'dup1', // Duplicate
        ),
        EntryWithSource(
          entryId: 'entry4',
          title: 'News 2 Duplicate',
          link: 'https://example.com/4',
          source: 'source4',
          published: DateTime.now().subtract(const Duration(hours: 4)),
          dupEntryId: 'dup2', // Duplicate
        ),
      ];

      // Second batch with unique items
      final secondBatchItems = [
        EntryWithSource(
          entryId: 'entry5',
          title: 'News 5',
          link: 'https://example.com/5',
          source: 'source5',
          published: DateTime.now().subtract(const Duration(hours: 5)),
          dupEntryId: 'dup5',
        ),
        EntryWithSource(
          entryId: 'entry6',
          title: 'News 6',
          link: 'https://example.com/6',
          source: 'source6',
          published: DateTime.now().subtract(const Duration(hours: 6)),
          dupEntryId: 'dup6',
        ),
      ];

      // Mock initial API response
      when(mockApiService.getNews(
        category: 'test_category',
        skip: 0,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: initialItems,
        total: 6,
        hasMore: true,
      ));

      // Mock first "load more" API response (mostly duplicates)
      when(mockApiService.getNews(
        category: 'test_category',
        skip: 10,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: firstBatchItems,
        total: 6,
        hasMore: true,
      ));

      // Mock second "load more" API response (unique items)
      when(mockApiService.getNews(
        category: 'test_category',
        skip: 20,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: secondBatchItems,
        total: 6,
        hasMore: false,
      ));

      // Build the widget
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify initial load
      expect(find.text('News 1'), findsOneWidget);
      expect(find.text('News 2'), findsOneWidget);

      // Simulate scrolling to trigger load more
      final scrollable = find.byType(Scrollable);
      await tester.scrollUntilVisible(
        find.text('News 2'),
        500.0,
        scrollable: scrollable,
      );
      await tester.pumpAndSettle();

      // Verify that the loop-based approach loaded enough unique items
      expect(find.text('News 1'), findsOneWidget);
      expect(find.text('News 2'), findsOneWidget);
      expect(find.text('News 5'), findsOneWidget);
      expect(find.text('News 6'), findsOneWidget);

      // Verify duplicates are not shown
      expect(find.text('News 1 Duplicate'), findsNothing);
      expect(find.text('News 2 Duplicate'), findsNothing);

      // Verify multiple API calls were made due to the loop
      verify(mockApiService.getNews(
        category: 'test_category',
        skip: 0,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).called(1);

      // The loop should have made additional calls to get enough unique items
      verify(mockApiService.getNews(
        category: 'test_category',
        skip: 10,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).called(1);
    });

    testWidgets('should handle multiple consecutive batches with duplicates', (WidgetTester tester) async {
      // Create initial items
      final initialItems = [
        EntryWithSource(
          entryId: 'entry1',
          title: 'News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now().subtract(const Duration(hours: 1)),
          dupEntryId: 'dup1',
        ),
      ];

      // Create multiple batches that are mostly duplicates
      final batch1Items = List.generate(10, (index) => EntryWithSource(
        entryId: 'batch1_entry$index',
        title: 'News 1 Duplicate $index',
        link: 'https://example.com/batch1_$index',
        source: 'source_batch1_$index',
        published: DateTime.now().subtract(Duration(hours: 2 + index)),
        dupEntryId: 'dup1', // All duplicates of entry1
      ));

      final batch2Items = List.generate(10, (index) => EntryWithSource(
        entryId: 'batch2_entry$index',
        title: 'News 1 Another Duplicate $index',
        link: 'https://example.com/batch2_$index',
        source: 'source_batch2_$index',
        published: DateTime.now().subtract(Duration(hours: 12 + index)),
        dupEntryId: 'dup1', // All duplicates of entry1
      ));

      // Finally, a batch with unique items
      final batch3Items = List.generate(5, (index) => EntryWithSource(
        entryId: 'batch3_entry$index',
        title: 'Unique News ${index + 2}',
        link: 'https://example.com/batch3_$index',
        source: 'source_batch3_$index',
        published: DateTime.now().subtract(Duration(hours: 22 + index)),
        dupEntryId: 'dup${index + 2}', // Unique items
      ));

      // Mock responses
      when(mockApiService.getNews(
        category: 'test_category',
        skip: 0,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: initialItems,
        total: 26,
        hasMore: true,
      ));

      when(mockApiService.getNews(
        category: 'test_category',
        skip: 10,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: batch1Items,
        total: 26,
        hasMore: true,
      ));

      when(mockApiService.getNews(
        category: 'test_category',
        skip: 20,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: batch2Items,
        total: 26,
        hasMore: true,
      ));

      when(mockApiService.getNews(
        category: 'test_category',
        skip: 30,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: batch3Items,
        total: 26,
        hasMore: false,
      ));

      // Build the widget
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify initial load
      expect(find.text('News 1'), findsOneWidget);

      // Simulate scrolling to trigger load more
      final scrollable = find.byType(Scrollable);
      await tester.scrollUntilVisible(
        find.text('News 1'),
        500.0,
        scrollable: scrollable,
      );
      await tester.pumpAndSettle();

      // The loop should have continued until it found enough unique items
      expect(find.text('News 1'), findsOneWidget);
      expect(find.text('Unique News 2'), findsOneWidget);
      expect(find.text('Unique News 3'), findsOneWidget);
      expect(find.text('Unique News 4'), findsOneWidget);
      expect(find.text('Unique News 5'), findsOneWidget);
      expect(find.text('Unique News 6'), findsOneWidget);

      // Verify that multiple API calls were made
      verify(mockApiService.getNews(
        category: 'test_category',
        skip: 10,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).called(1);

      verify(mockApiService.getNews(
        category: 'test_category',
        skip: 20,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).called(1);

      verify(mockApiService.getNews(
        category: 'test_category',
        skip: 30,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).called(1);
    });

    testWidgets('should handle empty duplicate response gracefully', (WidgetTester tester) async {
      // Mock API response with no items
      when(mockApiService.getNews(
        category: 'test_category',
        skip: 0,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenAnswer((_) async => PaginatedNewsResponse(
        items: [],
        total: 0,
        hasMore: false,
      ));

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show "no news found" message
      expect(find.text('Keine Nachrichten gefunden'), findsOneWidget);
    });

    testWidgets('should handle API errors gracefully', (WidgetTester tester) async {
      // Mock API to throw an error
      when(mockApiService.getNews(
        category: 'test_category',
        skip: 0,
        limit: 10,
        lookbackHours: anyNamed('lookbackHours'),
      )).thenThrow(Exception('Network error'));

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show error message in snackbar
      expect(find.byType(SnackBar), findsOneWidget);
      expect(find.textContaining('Fehler beim Laden der Nachrichten'), findsOneWidget);
    });
  });
}
