                        -HC:\PortableUnsafe\flutterSdk\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-<PERSON>ANDROID_PLATFORM=android-21
-<PERSON>ANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973
-DC<PERSON>KE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Dokumente\Private\Coding\goodNews\frontend\positive_news_app\build\app\intermediates\cxx\Debug\2q3l5s4o\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Dokumente\Private\Coding\goodNews\frontend\positive_news_app\build\app\intermediates\cxx\Debug\2q3l5s4o\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BC:\Dokumente\Private\Coding\goodNews\frontend\positive_news_app\android\app\.cxx\Debug\2q3l5s4o\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2