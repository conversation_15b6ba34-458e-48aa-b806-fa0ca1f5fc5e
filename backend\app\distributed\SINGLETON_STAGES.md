# Singleton Stage Implementation

This document describes the implementation of singleton stages in the distributed ETL processing system. Singleton stages are processing stages that should only be executed by one worker at a time to prevent race conditions, with optional cooldown periods to limit execution frequency.

## Overview

The singleton stage functionality was implemented to address the issue where tasks like `download_feeds` don't operate on existing entries but instead create new entries by downloading from RSS feeds. These tasks need to ensure that only one worker is processing them at any given time to prevent duplicate entries and race conditions.

## Key Components

### 1. StageSelectionCriteria Enhancement

Added a new `requires_singleton` flag and `cooldown_minutes` field to the `StageSelectionCriteria` dataclass:

```python
@dataclass
class StageSelectionCriteria:
    mandatory_fields: List[str]
    analysis_fields: List[str]
    filters: List[Tuple[str, str, Any]]
    creates_new_entries: bool = False
    requires_singleton: bool = False  # NEW: Singleton stage flag
    cooldown_minutes: int = 0         # NEW: Cooldown period in minutes
```

The `download_feeds` stage is configured as a singleton with a 15-minute cooldown:

```python
ProcessingStage.DOWNLOAD_FEEDS: StageSelectionCriteria(
    mandatory_fields=[],
    analysis_fields=[],
    filters=[],
    creates_new_entries=True,
    requires_singleton=True,  # Only one worker can process this at a time
    cooldown_minutes=15       # Run at most once every 15 minutes
)
```

### 2. Database Schema Changes

Added a new `singleton_stages` column to the `workers` table:

```sql
ALTER TABLE workers ADD singleton_stages CLOB;
```

This column stores a comma-separated list of singleton stages currently being processed by each worker.

### 3. Singleton Stage Execution Tracking

Added a new `singleton_executions` column to the `workers` table to track when singleton stages were last executed for cooldown management:

```sql
ALTER TABLE workers ADD singleton_executions CLOB;
```

This column stores a JSON object mapping stage names to their last execution times:

```json
{
  "download_feeds": "2025-09-20T23:45:30.123456+00:00",
  "other_singleton_stage": "2025-09-20T22:30:15.789012+00:00"
}
```

This consolidated approach eliminates the need for a separate table and keeps execution tracking co-located with worker data.

### 4. WorkQueueManager Extensions

Added several new methods to `WorkQueueManager` for singleton stage management and cooldown tracking:

**Singleton Stage Management Methods:**
- `is_singleton_stage_claimed(stage)`: Check if a singleton stage is already being processed
- `claim_singleton_stage(stage, worker_id)`: Attempt to claim a singleton stage for processing
- `release_singleton_stage(stage, worker_id)`: Release a singleton stage claim

**Cooldown Management Methods:**
- `is_stage_in_cooldown(stage)`: Checks if a stage is currently in its cooldown period by examining all workers' execution history
- `record_stage_execution(stage, worker_id)`: Records when a stage was executed by updating the worker's `singleton_executions` JSON

### 5. DistributedWorker Processing Logic

Modified the `_process_stage_batch` method to handle singleton stages differently, including cooldown checks:

```python
def _process_stage_batch(self, stage: ProcessingStage) -> bool:
    # Handle singleton stages differently
    if stage_requires_singleton(stage):
        return self._process_singleton_stage(work_queue, stage)
    
    # Regular entry-based processing for non-singleton stages
    # ... existing logic
```

Added a new `_process_singleton_stage` method that:
1. **Checks if the stage is in cooldown period** (NEW)
2. **Adds random delay to prevent race conditions when cooldown ends** (NEW)
3. **Re-checks cooldown after delay** (NEW)
4. Checks if the stage is already claimed by another worker
5. Attempts to claim the stage if available
6. Executes the stage processing (without entry retrieval)
7. **Records the execution time for cooldown tracking** (NEW)
8. Always releases the stage claim when done

### 6. Worker Health Management

Updated `WorkerHealthManager` to handle singleton stages during:
- Worker registration: Clears singleton stages on startup
- Worker unregistration: Clears singleton stages on shutdown
- Failed worker recovery: Clears singleton stages for failed workers

## How It Works

### Normal Operation

1. Worker starts and registers with the system
2. Worker enters processing loop and encounters a singleton stage
3. Worker checks if the singleton stage is already claimed by another worker
4. If not claimed, worker claims the stage and processes it
5. Worker releases the stage claim when processing is complete
6. Other workers skip the stage while it's claimed

### Race Condition Prevention

The system uses database-level locking to prevent race conditions:
- Claims are made within database transactions
- The claiming process checks for existing claims before creating new ones
- Only one worker can successfully claim a singleton stage at a time

### Failure Recovery

If a worker fails while processing a singleton stage:
- The worker health monitoring system detects the failure
- The failed worker's singleton stage claims are automatically cleared
- Other workers can then claim and process the singleton stage

## Configuration

To make a stage singleton, update its `StageSelectionCriteria`:

```python
ProcessingStage.YOUR_STAGE: StageSelectionCriteria(
    mandatory_fields=[],
    analysis_fields=[],
    filters=[],
    creates_new_entries=True,  # Usually true for singleton stages
    requires_singleton=True    # Enable singleton behavior
)
```

## Helper Functions

Three new helper functions are available:

- `stage_requires_singleton(stage)`: Check if a stage requires singleton processing
- `stage_requires_entry_retrieval(stage)`: Check if a stage needs to retrieve existing entries
- `stage_creates_new_entries(stage)`: Check if a stage creates new entries (existing function)

## Testing

Comprehensive tests are provided in:
- `test_singleton_stages.py`: Unit tests for singleton stage management
- `test_singleton_integration.py`: Integration tests for the complete system

## Performance Tuning

The system includes configurable delays to prevent excessive database load:

- `processing_delay_seconds` (default: 0.1): Small delay between processing different stages
- `no_work_delay_seconds` (default: 2.0): Longer delay when no work is found across all stages

These can be configured via environment variables:
- `WORKER_PROCESSING_DELAY_SECONDS`
- `WORKER_NO_WORK_DELAY_SECONDS`

## Benefits

1. **Race Condition Prevention**: Only one worker processes singleton stages at a time
2. **Automatic Cleanup**: Failed workers automatically release their singleton claims
3. **Rate Limiting**: Cooldown functionality prevents excessive server load by limiting execution frequency
4. **Configurable Throttling**: Different stages can have different cooldown periods based on their requirements
5. **Race Condition Prevention**: Random delays prevent multiple workers from starting the same stage simultaneously after cooldown ends
6. **Unique Worker IDs**: Worker IDs now include timestamps with milliseconds to prevent conflicts when multiple workers start with the same name
7. **Consolidated Data Storage**: Execution tracking is stored in the workers table as JSON, eliminating the need for separate tables
8. **Backward Compatibility**: Regular stages continue to work unchanged
9. **Scalability**: Non-singleton stages can still be processed by multiple workers
10. **Monitoring**: Singleton stage claims are tracked in the database
11. **Database Load Management**: Configurable delays prevent excessive database queries

## Recent Improvements

### Worker ID Uniqueness
Worker IDs now include timestamps with milliseconds to prevent conflicts:
```
hostname-uuid8chars-20250920_235530_123
```

### Consolidated Execution Tracking
Singleton stage execution times are now stored directly in the workers table as JSON:
```json
{
  "download_feeds": "2025-09-20T23:45:30.123456+00:00"
}
```

### Race Condition Prevention
Added random delays (100ms-2s) when cooldown periods end to prevent multiple workers from starting the same stage simultaneously.

## Usage Example

The `download_feeds` stage now works as follows:

1. Multiple workers can be configured with the `download_feeds` stage
2. Only one worker will actually process feeds at any given time
3. Other workers will skip the stage if it's already being processed
4. If the processing worker fails, another worker can take over
5. No duplicate entries are created from concurrent feed downloads

This implementation ensures that singleton tasks like feed downloading are processed safely in a distributed environment while maintaining the benefits of distributed processing for other stages.
