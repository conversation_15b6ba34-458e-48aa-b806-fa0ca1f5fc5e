import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from etl.news_aggregator import DATABASE_FILE, Entry

# Logging-Konfiguration
logging.basicConfig(level=logging.INFO)

def remove_entries(source_condition):
    """Entferne alle Einträge einer definierten Quelle aus der Datenbank"""
    logging.info(f"Entferne alle Einträge mit Bedinung Source ~= {source_condition}")
    engine = create_engine(DATABASE_FILE)
    Session = scoped_session(sessionmaker(bind=engine))
    session = Session()
    try:
        entries = session.query(Entry).filter(Entry.source.like(source_condition)).all()
        for entry in entries:
            session.delete(entry)
        session.commit()
        logging.info(f"{len(entries)} Einträge entfernt")
    except Exception as e:
        session.rollback()
        logging.error(f"Fehler beim Entfernen der Einträge: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    remove_entries(source_condition='%winfuture%')
    remove_entries(source_condition='%heise-Rubrik-IT%')
    
