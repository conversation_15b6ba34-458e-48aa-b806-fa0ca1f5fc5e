# One Thread Per Stage Architecture - Usage Guide

## Overview

The `OneThreadPerStageWorker` implements the **exact same architecture as the monolithic ETL**, where each processing stage runs in its own dedicated thread. This ensures:

- **One thread per stage** (not multiple workers per stage)
- **Each thread only updates its own stage's processing status**
- **No interference between stages**
- **Continuous processing like the monolithic ETL**

## Architecture Comparison

### Monolithic ETL (etl/news_aggregator.py)
```python
threading.Thread(target=aggregator.download_full_texts, name="DownloadFullTextsThread").start()
threading.Thread(target=aggregator.llm_sentiment_ad, name="LLMSentimentAdThread").start()
threading.Thread(target=aggregator.classify_iptc_newscode, name="ClassifyIPTCNewsCodeThread").start()
threading.Thread(target=aggregator.check_and_update_duplicates, name="DuplicateCheckThread").start()
threading.Thread(target=aggregator.compute_embeddings, name="ComputeEmbeddingsThread").start()
```

### Distributed Implementation
```python
from backend.app.distributed.parallel_worker import start_monolithic_style_threads

# Start threads exactly like monolithic ETL
worker = start_monolithic_style_threads()
# Creates: DownloadFullTextThread, LLMAnalysisThread, IPTCClassificationThread, etc.
```

## Key Features

### 1. Stage-Isolated Processing Status Updates

Each thread **only updates its own stage** in the `processing_status` JSON field:

```python
# Thread for download_full_text stage
processing_status.mark_stage_in_progress(ProcessingStage.DOWNLOAD_FULL_TEXT, worker_id)
# Only updates: {"download_full_text": {"status": "in_progress", "worker_id": "..."}}

# Thread for llm_analysis stage  
processing_status.mark_stage_completed(ProcessingStage.LLM_ANALYSIS, worker_id)
# Only updates: {"llm_analysis": {"status": "completed", "worker_id": "..."}}
```

**No interference** - each thread works independently on its assigned stage.

### 2. Distributed Work Claiming

Unlike the monolithic ETL that processes ALL entries, each thread:
1. **Claims a batch** of entries for its specific stage
2. **Processes the batch** 
3. **Updates processing_status** for its stage only
4. **Continues to next batch**

### 3. Safe Database Operations

Each thread uses **isolated database sessions**:
```python
@contextmanager
def _get_stage_session(self):
    session = self.db_session_factory()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()
```

## Usage Examples

### Command Line Interface

```bash
# Start all stages (one thread each) - matches monolithic ETL
python -m backend.app.distributed.cli.parallel_worker_cli monolithic

# Start specific stages only
python -m backend.app.distributed.cli.parallel_worker_cli monolithic --stages download_full_text llm_analysis compute_embeddings

# List available stages
python -m backend.app.distributed.cli.parallel_worker_cli list-stages
```

### Programmatic Usage

```python
from backend.app.distributed.parallel_worker import (
    OneThreadPerStageWorker,
    start_monolithic_style_threads
)
from backend.app.distributed.processing_stage import ProcessingStage

# Method 1: Use convenience function (recommended)
worker = start_monolithic_style_threads()

# Method 2: Manual setup
stages = [ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.LLM_ANALYSIS]
worker = OneThreadPerStageWorker(stages)
worker.start()

# Monitor status
status = worker.get_status()
print(f"Active threads: {status['active_threads']}/{status['total_stages']}")

# Graceful shutdown
worker.stop(timeout=30.0)
```

### Thread Naming Convention

The threads are named to match the monolithic ETL pattern:

| Stage | Thread Name |
|-------|-------------|
| `download_full_text` | `DownloadFullTextThread` |
| `llm_analysis` | `LLMAnalysisThread` |
| `iptc_classification` | `IPTCClassificationThread` |
| `duplicate_check` | `DuplicateCheckThread` |
| `compute_embeddings` | `ComputeEmbeddingsThread` |
| `generate_preview_images` | `GeneratePreviewImagesThread` |

## Processing Status Management

### Status Structure
Each entry's `processing_status` field contains JSON like:
```json
{
  "download_full_text": {
    "status": "completed",
    "completed_at": "2025-09-25T20:30:00Z",
    "worker_id": "stage-threads-abc123-download_full_text"
  },
  "llm_analysis": {
    "status": "in_progress", 
    "worker_id": "stage-threads-abc123-llm_analysis"
  },
  "compute_embeddings": {
    "status": "pending"
  }
}
```

### Status Updates
Each thread **only modifies its own stage**:
```python
# DownloadFullTextThread only updates download_full_text
# LLMAnalysisThread only updates llm_analysis  
# ComputeEmbeddingsThread only updates compute_embeddings
```

This prevents conflicts and ensures accurate progress tracking.

## Performance Benefits

### Compared to Sequential Processing
- **Multiple stages run simultaneously** (like monolithic ETL)
- **No stage blocks others** 
- **Better resource utilization**

### Compared to Multiple Workers Per Stage
- **No competition** for the same work
- **Simpler coordination**
- **Matches proven monolithic pattern**

### Database Performance
- **Isolated sessions** prevent connection conflicts
- **Batch processing** reduces database load
- **Proper transaction handling** ensures data consistency

## Monitoring and Debugging

### Status Monitoring
```python
status = worker.get_status()
print(f"Worker ID: {status['worker_id']}")
print(f"Worker Type: {status['worker_type']}")  # "one_thread_per_stage"
print(f"Active Threads: {status['active_threads']}/{status['total_stages']}")

# Per-stage statistics
for stage_name, stats in status['stage_stats'].items():
    print(f"{stage_name}: {stats['batches_processed']} batches, {stats['errors']} errors")
```

### Thread Health
```python
for stage, thread_info in status['stage_threads'].items():
    print(f"{stage}: {'alive' if thread_info['alive'] else 'dead'}")
```

## Error Handling

Each thread handles errors independently:
- **Processing errors**: Mark stage as failed, continue with next entry
- **Database errors**: Rollback transaction, retry with backoff
- **Connection errors**: Recreate session, continue processing

Failed entries are marked in `processing_status` with error details:
```json
{
  "compute_embeddings": {
    "status": "failed",
    "error": "Connection timeout",
    "retry_count": 2,
    "worker_id": "stage-threads-abc123-compute_embeddings"
  }
}
```

## Migration from Monolithic ETL

The distributed architecture is designed to be a **drop-in replacement**:

1. **Same thread-per-stage pattern**
2. **Same continuous processing model**  
3. **Same resource utilization**
4. **Added distributed work claiming**
5. **Added isolated status updates**

This ensures a smooth transition while gaining distributed processing benefits.
