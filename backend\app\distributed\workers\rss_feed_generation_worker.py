"""
RSS Feed Generation worker for distributed ETL processing.

This module provides the RSSFeedGenerationWorker class that handles generating
RSS feeds from processed news entries in batch processing mode.
"""

import logging
import os
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

from sqlalchemy.orm import Session
from sqlalchemy import and_

from backend.app.distributed.distributed_worker import Distri<PERSON><PERSON>orker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class RSSFeedGenerationWorker(DistributedWorker):
    """
    Worker specialized in generating RSS feeds from processed news entries.
    
    Adapts the existing generate_rss_feed logic for batch processing with
    efficient RSS feed generation from processed entries and proper file handling.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the RSS feed generation worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.GENERATE_RSS_FEED not in config.stages:
            raise ValueError("RSSFeedGenerationWorker requires GENERATE_RSS_FEED stage")
        
        # Configuration for RSS feed generation
        self.sentiment_threshold = config.get_stage_config(
            ProcessingStage.GENERATE_RSS_FEED, 'sentiment_threshold', 0.5
        )
        self.similarity_threshold = config.get_stage_config(
            ProcessingStage.GENERATE_RSS_FEED, 'similarity_threshold', 0.85
        )
        self.output_file = config.get_stage_config(
            ProcessingStage.GENERATE_RSS_FEED, 'output_file', 'positive_feed.rss'
        )
        self.feed_title = config.get_stage_config(
            ProcessingStage.GENERATE_RSS_FEED, 'feed_title', 'Positiver Nachrichten-Feed'
        )
        self.feed_description = config.get_stage_config(
            ProcessingStage.GENERATE_RSS_FEED, 'feed_description', 'Aggregierter Feed mit positiven Nachrichten'
        )
        self.feed_link = config.get_stage_config(
            ProcessingStage.GENERATE_RSS_FEED, 'feed_link', 'http://example.com/positive-feed.rss'
        )
        self.days_back = config.get_stage_config(
            ProcessingStage.GENERATE_RSS_FEED, 'days_back', 1
        )
        
        logger.info(
            f"Initialized RSSFeedGenerationWorker {self.worker_id} "
            f"(sentiment_threshold: {self.sentiment_threshold}, "
            f"similarity_threshold: {self.similarity_threshold}, "
            f"output_file: {self.output_file}, "
            f"days_back: {self.days_back})"
        )
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for RSS feed generation.
        
        Note: RSS feed generation is typically done as a single operation across
        all qualifying entries, not per individual entry. This method will
        generate the feed once if any entries are provided.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be GENERATE_RSS_FEED)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.GENERATE_RSS_FEED:
            logger.error(f"RSSFeedGenerationWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing RSS feed generation for {len(entries)} entries")
        
        # For RSS feed generation, we need to get all qualifying entries from the database
        # rather than just processing the provided batch
        try:
            all_entries = self._get_qualifying_entries()
            
            if not all_entries:
                logger.info("No qualifying entries found for RSS feed generation")
                # Return True for all provided entries since there's nothing to process
                return {entry.entry_id: True for entry in entries}
            
            # Remove duplicates using semantic similarity
            unique_entries = self._remove_duplicates(all_entries)
            
            # Generate RSS feed
            success = self._generate_rss_feed(unique_entries)
            
            # Return the same result for all entries in the batch
            result = {entry.entry_id: success for entry in entries}
            
            if success:
                logger.info(f"Successfully generated RSS feed with {len(unique_entries)} unique entries")
            else:
                logger.error("Failed to generate RSS feed")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in RSS feed generation: {e}")
            return {entry.entry_id: False for entry in entries}
    
    def _get_qualifying_entries(self) -> List[Dict]:
        """
        Get all entries that qualify for RSS feed inclusion.
        
        Returns:
            List of dictionaries with entry data
        """
        try:
            db_session = self.db_session_factory()
            
            try:
                # Calculate date threshold
                date_threshold = datetime.now(timezone.utc).date()
                if self.days_back > 1:
                    from datetime import timedelta
                    date_threshold = date_threshold - timedelta(days=self.days_back - 1)
                
                # Query qualifying entries
                query = db_session.query(Entry).filter(
                    and_(
                        Entry.llm_positive >= self.sentiment_threshold,
                        Entry.published >= date_threshold,
                        Entry.embedding.isnot(None)  # Need embeddings for duplicate detection
                    )
                ).order_by(Entry.published.desc())
                
                entries = []
                for entry in query:
                    entries.append({
                        'entry_id': entry.entry_id,
                        'title': entry.title,
                        'description': entry.description,
                        'link': entry.link,
                        'published': entry.published,
                        'embedding': entry.embedding,
                        'llm_positive': entry.llm_positive
                    })
                
                logger.debug(f"Found {len(entries)} qualifying entries for RSS feed")
                return entries
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Error fetching qualifying entries: {e}")
            return []
    
    def _remove_duplicates(self, entries: List[Dict]) -> List[Dict]:
        """
        Remove semantic duplicates using embedding similarity.
        
        Args:
            entries: List of entry dictionaries with embeddings
            
        Returns:
            List of unique entries (duplicates removed)
        """
        if not entries:
            return entries
        
        logger.info(f"Checking for semantic duplicates among {len(entries)} entries")
        
        try:
            # Convert embeddings to numpy array
            embeddings = np.array([
                np.frombuffer(entry['embedding'], dtype=np.float32) 
                for entry in entries
            ])
            
            # Track which entries to keep (True = keep, False = remove as duplicate)
            keep_filter = np.ones(len(entries), dtype=bool)
            
            for i, entry in enumerate(entries):
                if not keep_filter[i]:
                    continue  # Already marked as duplicate
                
                # Compute similarities with all entries
                similarities = cosine_similarity([embeddings[i]], embeddings)[0]
                
                # Find duplicates (similarity above threshold, excluding self-match)
                duplicate_indices = np.where(similarities > self.similarity_threshold)[0]
                
                if len(duplicate_indices) > 1:
                    logger.debug(f"Found semantic duplicates for '{entry['title']}' with similarities {similarities[duplicate_indices]}")
                    
                    # Sort duplicates by publication date (most recent first)
                    duplicate_indices = sorted(
                        duplicate_indices, 
                        key=lambda idx: entries[idx]['published'], 
                        reverse=True
                    )
                    
                    # Keep the most recent one, mark others as duplicates
                    for idx in duplicate_indices[1:]:
                        keep_filter[idx] = False
                        logger.debug(f"Removing duplicate: '{entries[idx]['title']}' (published: {entries[idx]['published']})")
            
            # Filter to unique entries
            unique_entries = [entry for i, entry in enumerate(entries) if keep_filter[i]]
            
            removed_count = len(entries) - len(unique_entries)
            logger.info(f"Removed {removed_count} semantic duplicates, {len(unique_entries)} unique entries remain")
            
            return unique_entries
            
        except Exception as e:
            logger.error(f"Error removing duplicates: {e}")
            # Return original entries if duplicate removal fails
            return entries
    
    def _generate_rss_feed(self, entries: List[Dict]) -> bool:
        """
        Generate RSS feed file from unique entries.
        
        Args:
            entries: List of unique entry dictionaries
            
        Returns:
            True if RSS feed was generated successfully, False otherwise
        """
        try:
            # Import FeedGenerator (may not be available in all environments)
            try:
                from feedgen.feed import FeedGenerator
            except ImportError:
                logger.error("feedgen library not available - cannot generate RSS feed")
                return False
            
            logger.info(f"Generating RSS feed with {len(entries)} entries")
            
            # Create feed generator
            fg = FeedGenerator()
            fg.title(self.feed_title)
            fg.description(self.feed_description)
            fg.link(href=self.feed_link, rel='self')
            fg.language('de')  # Assuming German content based on original implementation
            
            # Add entries to feed
            for entry in entries:
                fe = fg.add_entry()
                fe.title(entry['title'] or 'Untitled')
                fe.link(href=entry['link'] or '')
                fe.description(entry['description'] or entry['title'] or 'No description available')
                
                if entry['published']:
                    # Ensure published date is timezone-aware
                    pub_date = entry['published']
                    if pub_date.tzinfo is None:
                        pub_date = pub_date.replace(tzinfo=timezone.utc)
                    fe.pubDate(pub_date)
            
            # Generate RSS file
            fg.rss_file(self.output_file)
            
            # Verify file was created
            if os.path.exists(self.output_file):
                file_size = os.path.getsize(self.output_file)
                logger.info(f"RSS feed generated successfully: {self.output_file} ({file_size} bytes)")
                return True
            else:
                logger.error(f"RSS feed file was not created: {self.output_file}")
                return False
                
        except Exception as e:
            logger.error(f"Error generating RSS feed: {e}")
            return False
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to RSS feed generation worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add RSS feed generation worker specific metrics
        health.update({
            'worker_type': 'RSSFeedGenerationWorker',
            'sentiment_threshold': self.sentiment_threshold,
            'similarity_threshold': self.similarity_threshold,
            'output_file': self.output_file,
            'days_back': self.days_back,
            'feed_title': self.feed_title,
            'supported_stages': [ProcessingStage.GENERATE_RSS_FEED.value]
        })
        
        # Check if output file exists and add file info
        if os.path.exists(self.output_file):
            try:
                file_size = os.path.getsize(self.output_file)
                file_mtime = os.path.getmtime(self.output_file)
                health.update({
                    'output_file_exists': True,
                    'output_file_size': file_size,
                    'output_file_modified': datetime.fromtimestamp(file_mtime, tz=timezone.utc).isoformat()
                })
            except Exception as e:
                health.update({
                    'output_file_exists': True,
                    'output_file_error': str(e)
                })
        else:
            health.update({
                'output_file_exists': False
            })
        
        return health
    
    def cleanup_resources(self) -> None:
        """Clean up resources when worker shuts down."""
        logger.info("Cleaning up RSS feed generation worker resources")
        
        # No specific resources to clean up for this worker
        logger.info("RSS feed generation worker resources cleaned up")