import threading

# Thread progress tracking
thread_progress = {}

def update_progress(status, progress=None, size=None):
    """Update the progress of a thread."""
    if threading.current_thread().name not in thread_progress:
        thread_progress[threading.current_thread().name] = {}
    if "status" not in thread_progress[threading.current_thread().name] or \
            thread_progress[threading.current_thread().name]["status"] != status:
        # new state
        thread_progress[threading.current_thread().name]["status"] = status
        thread_progress[threading.current_thread().name]["size"] = size
        thread_progress[threading.current_thread().name]["progress"] = progress
    else:
        # same state
        thread_progress[threading.current_thread().name]["size"] = \
            size if size else thread_progress[threading.current_thread().name]["size"]
        thread_progress[threading.current_thread().name]["progress"] = \
            progress if progress else thread_progress[threading.current_thread().name]["progress"]