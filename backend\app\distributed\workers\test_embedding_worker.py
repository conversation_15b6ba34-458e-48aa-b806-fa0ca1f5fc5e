"""
Tests for the EmbeddingWorker class.

This module provides comprehensive tests for the embedding computation worker,
including batch processing, error handling, and resource management.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from backend.app.distributed.workers.embedding_worker import EmbeddingWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestEmbeddingWorker:
    """Test cases for EmbeddingWorker."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        config = WorkerConfig(
            worker_id="test-embedding-worker",
            stages=[ProcessingStage.COMPUTE_EMBEDDINGS],
            batch_size=5
        )
        # Set stage configs using string keys as expected by get_stage_config
        config.stage_configs = {
            ProcessingStage.COMPUTE_EMBEDDINGS.value: {
                'model_name': 'BAAI/bge-m3',
                'batch_optimize_model': True,
                'preload_model': False,
                'max_text_length': 1000
            }
        }
        return config
    
    @pytest.fixture
    def sample_entries(self):
        """Create sample Entry objects for testing."""
        entries = []
        for i in range(3):
            entry = Entry()
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.full_text = f"This is the full text content for test entry {i}. " * 10
            entries.append(entry)
        return entries
    
    @pytest.fixture
    def mock_embedding_model(self):
        """Create a mock SentenceTransformer model."""
        mock_model = Mock()
        # Mock batch encoding
        mock_embeddings = np.random.rand(3, 768).astype(np.float32)
        mock_tensor = Mock()
        mock_tensor.cpu.return_value.numpy.return_value = mock_embeddings
        mock_model.encode.return_value = mock_tensor
        return mock_model
    
    def test_worker_initialization(self, worker_config, mock_db_session_factory):
        """Test worker initialization with correct configuration."""
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        assert worker.worker_id == "test-embedding-worker"
        assert ProcessingStage.COMPUTE_EMBEDDINGS in worker.config.stages
        assert worker.model_name == 'BAAI/bge-m3'
        assert worker.batch_optimize_model is True
        assert worker.preload_model is False
        assert worker.max_text_length == 1000
        assert worker._embedding_model is None  # Not preloaded
    
    def test_worker_initialization_invalid_stage(self, mock_db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.SENTIMENT_ANALYSIS],  # Wrong stage
            batch_size=5
        )
        
        with pytest.raises(ValueError, match="EmbeddingWorker requires COMPUTE_EMBEDDINGS stage"):
            EmbeddingWorker(config, mock_db_session_factory)
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_model_initialization(self, mock_sentence_transformer, worker_config, mock_db_session_factory):
        """Test embedding model initialization."""
        mock_model = Mock()
        mock_sentence_transformer.return_value = mock_model
        
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        # Test lazy loading
        model = worker._get_embedding_model()
        
        assert model == mock_model
        assert worker._embedding_model == mock_model
        mock_sentence_transformer.assert_called_once_with('BAAI/bge-m3')
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_preload_model(self, mock_sentence_transformer, mock_db_session_factory):
        """Test model preloading during initialization."""
        mock_model = Mock()
        mock_sentence_transformer.return_value = mock_model
        
        config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.COMPUTE_EMBEDDINGS],
            batch_size=5
        )
        config.stage_configs = {
            ProcessingStage.COMPUTE_EMBEDDINGS.value: {
                'preload_model': True
            }
        }
        
        worker = EmbeddingWorker(config, mock_db_session_factory)
        
        # Model should be preloaded
        assert worker._embedding_model == mock_model
        mock_sentence_transformer.assert_called_once()
    
    def test_process_batch_wrong_stage(self, worker_config, mock_db_session_factory, sample_entries):
        """Test processing batch with wrong stage returns all failures."""
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        results = worker.process_batch(sample_entries, ProcessingStage.SENTIMENT_ANALYSIS)
        
        assert len(results) == 3
        assert all(not success for success in results.values())
    
    def test_process_batch_missing_text(self, worker_config, mock_db_session_factory):
        """Test processing entries with missing full_text."""
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        # Create entry without full_text
        entry = Entry()
        entry.entry_id = "test-entry-no-text"
        entry.title = "Test Title"
        entry.full_text = None
        
        results = worker.process_batch([entry], ProcessingStage.COMPUTE_EMBEDDINGS)
        
        assert len(results) == 1
        assert results["test-entry-no-text"] is False
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_process_batch_optimized_success(self, mock_sentence_transformer, worker_config, 
                                           mock_db_session_factory, sample_entries):
        """Test successful batch-optimized processing."""
        # Setup mock model
        mock_model = Mock()
        mock_embeddings = np.random.rand(3, 768).astype(np.float32)
        mock_tensor = Mock()
        mock_tensor.cpu.return_value.numpy.return_value = mock_embeddings
        mock_model.encode.return_value = mock_tensor
        mock_sentence_transformer.return_value = mock_model
        
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        # Mock the database update method
        worker._update_entry_embedding = Mock(return_value=True)
        
        results = worker.process_batch(sample_entries, ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Verify results
        assert len(results) == 3
        assert all(success for success in results.values())
        
        # Verify model was called with batch
        mock_model.encode.assert_called_once()
        call_args = mock_model.encode.call_args[0]
        assert len(call_args[0]) == 3  # Batch of 3 texts
        
        # Verify database updates
        assert worker._update_entry_embedding.call_count == 3
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_process_batch_individual_fallback(self, mock_sentence_transformer, worker_config,
                                             mock_db_session_factory, sample_entries):
        """Test fallback to individual processing when batch fails."""
        # Setup mock model that fails on batch but succeeds individually
        mock_model = Mock()
        
        # First call (batch) raises exception
        # Subsequent calls (individual) return single embeddings
        mock_embedding = np.random.rand(768).astype(np.float32)
        mock_tensor_individual = Mock()
        mock_tensor_individual.cpu.return_value.numpy.return_value = [mock_embedding]
        
        mock_model.encode.side_effect = [
            Exception("Batch processing failed"),  # First call fails
            mock_tensor_individual,  # Individual calls succeed
            mock_tensor_individual,
            mock_tensor_individual
        ]
        
        mock_sentence_transformer.return_value = mock_model
        
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        # Mock the database update method
        worker._update_entry_embedding = Mock(return_value=True)
        
        results = worker.process_batch(sample_entries, ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Verify results
        assert len(results) == 3
        assert all(success for success in results.values())
        
        # Verify fallback to individual processing (4 calls: 1 failed batch + 3 individual)
        assert mock_model.encode.call_count == 4
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_process_batch_individual_mode(self, mock_sentence_transformer, mock_db_session_factory, sample_entries):
        """Test individual processing mode."""
        config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.COMPUTE_EMBEDDINGS],
            batch_size=5
        )
        config.stage_configs = {
            ProcessingStage.COMPUTE_EMBEDDINGS.value: {
                'batch_optimize_model': False  # Disable batch optimization
            }
        }
        
        # Setup mock model
        mock_model = Mock()
        mock_embedding = np.random.rand(768).astype(np.float32)
        mock_tensor = Mock()
        mock_tensor.cpu.return_value.numpy.return_value = [mock_embedding]
        mock_model.encode.return_value = mock_tensor
        mock_sentence_transformer.return_value = mock_model
        
        worker = EmbeddingWorker(config, mock_db_session_factory)
        
        # Mock the database update method
        worker._update_entry_embedding = Mock(return_value=True)
        
        results = worker.process_batch(sample_entries, ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Verify results
        assert len(results) == 3
        assert all(success for success in results.values())
        
        # Verify individual processing (3 calls for 3 entries)
        assert mock_model.encode.call_count == 3
    
    def test_text_truncation(self, worker_config, mock_db_session_factory):
        """Test text truncation for long texts."""
        # Create entry with very long text
        entry = Entry()
        entry.entry_id = "test-long-text"
        entry.title = "Test Title"
        entry.full_text = "A" * 2000  # Longer than max_text_length (1000)
        
        with patch('sentence_transformers.SentenceTransformer') as mock_st:
            mock_model = Mock()
            mock_embedding = np.random.rand(768).astype(np.float32)
            mock_tensor = Mock()
            mock_tensor.cpu.return_value.numpy.return_value = [mock_embedding]
            mock_model.encode.return_value = mock_tensor
            mock_st.return_value = mock_model
            
            worker = EmbeddingWorker(worker_config, mock_db_session_factory)
            worker._update_entry_embedding = Mock(return_value=True)
            
            results = worker.process_batch([entry], ProcessingStage.COMPUTE_EMBEDDINGS)
            
            # Verify text was truncated
            call_args = mock_model.encode.call_args[0]
            assert len(call_args[0][0]) == 1000  # Truncated to max_text_length
    
    def test_update_entry_embedding_success(self, worker_config, mock_db_session_factory):
        """Test successful database update of embedding."""
        mock_session = mock_db_session_factory()
        
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        # Test embedding update
        embedding = np.random.rand(768).astype(np.float32)
        result = worker._update_entry_embedding("test-entry", embedding)
        
        assert result is True
        mock_session.query.assert_called_once()
        mock_session.commit.assert_called_once()
        mock_session.close.assert_called_once()
    
    def test_update_entry_embedding_database_error(self, worker_config, mock_db_session_factory):
        """Test database error handling during embedding update."""
        mock_session = mock_db_session_factory()
        mock_session.query.side_effect = Exception("Database error")
        
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        # Test embedding update with error
        embedding = np.random.rand(768).astype(np.float32)
        result = worker._update_entry_embedding("test-entry", embedding)
        
        assert result is False
        mock_session.rollback.assert_called_once()
        mock_session.close.assert_called_once()
    
    def test_get_worker_specific_health(self, worker_config, mock_db_session_factory):
        """Test worker-specific health information."""
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        health = worker.get_worker_specific_health()
        
        assert health['worker_type'] == 'EmbeddingWorker'
        assert health['model_name'] == 'BAAI/bge-m3'
        assert health['batch_optimize_model'] is True
        assert health['preload_model'] is False
        assert health['max_text_length'] == 1000
        assert health['model_loaded'] is False
        assert ProcessingStage.COMPUTE_EMBEDDINGS.value in health['supported_stages']
    
    def test_cleanup_resources(self, worker_config, mock_db_session_factory):
        """Test resource cleanup."""
        with patch('sentence_transformers.SentenceTransformer') as mock_st:
            mock_model = Mock()
            mock_st.return_value = mock_model
            
            worker = EmbeddingWorker(worker_config, mock_db_session_factory)
            
            # Load model
            worker._get_embedding_model()
            assert worker._embedding_model is not None
            
            # Cleanup resources
            worker.cleanup_resources()
            assert worker._embedding_model is None
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_model_import_error(self, mock_sentence_transformer, worker_config, mock_db_session_factory):
        """Test handling of SentenceTransformer import error."""
        mock_sentence_transformer.side_effect = ImportError("SentenceTransformer not available")
        
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        with pytest.raises(ImportError):
            worker._get_embedding_model()
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_model_loading_error(self, mock_sentence_transformer, worker_config, mock_db_session_factory):
        """Test handling of model loading error."""
        mock_sentence_transformer.side_effect = Exception("Model loading failed")
        
        worker = EmbeddingWorker(worker_config, mock_db_session_factory)
        
        with pytest.raises(Exception, match="Model loading failed"):
            worker._get_embedding_model()


class TestEmbeddingWorkerIntegration:
    """Integration tests for EmbeddingWorker with various entry types."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def integration_config(self):
        """Create configuration for integration tests."""
        config = WorkerConfig(
            worker_id="integration-embedding-worker",
            stages=[ProcessingStage.COMPUTE_EMBEDDINGS],
            batch_size=10
        )
        config.stage_configs = {
            ProcessingStage.COMPUTE_EMBEDDINGS.value: {
                'model_name': 'BAAI/bge-m3',
                'batch_optimize_model': True,
                'max_text_length': 512
            }
        }
        return config
    
    def create_test_entry(self, entry_id: str, text_content: str) -> Entry:
        """Create a test entry with specified content."""
        entry = Entry()
        entry.entry_id = entry_id
        entry.title = f"Title for {entry_id}"
        entry.full_text = text_content
        return entry
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_various_text_lengths(self, mock_sentence_transformer, integration_config, mock_db_session_factory):
        """Test processing entries with various text lengths."""
        # Setup mock model
        mock_model = Mock()
        
        def mock_encode(texts, **kwargs):
            # Return embeddings based on number of texts
            num_texts = len(texts)
            embeddings = np.random.rand(num_texts, 768).astype(np.float32)
            mock_tensor = Mock()
            mock_tensor.cpu.return_value.numpy.return_value = embeddings
            return mock_tensor
        
        mock_model.encode.side_effect = mock_encode
        mock_sentence_transformer.return_value = mock_model
        
        # Create entries with different text lengths
        entries = [
            self.create_test_entry("short", "Short text."),
            self.create_test_entry("medium", "Medium length text. " * 20),
            self.create_test_entry("long", "Very long text content. " * 100),
            self.create_test_entry("empty", ""),
        ]
        
        worker = EmbeddingWorker(integration_config, mock_db_session_factory)
        worker._update_entry_embedding = Mock(return_value=True)
        
        results = worker.process_batch(entries, ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Check results - empty text should fail, others should succeed
        assert len(results) == 4
        assert results["short"] is True
        assert results["medium"] is True
        assert results["long"] is True
        assert results["empty"] is False  # Empty text should fail
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_mixed_success_failure(self, mock_sentence_transformer, integration_config, mock_db_session_factory):
        """Test batch processing with mixed success and failure results."""
        # Setup mock model
        mock_model = Mock()
        embeddings = np.random.rand(3, 768).astype(np.float32)
        mock_tensor = Mock()
        mock_tensor.cpu.return_value.numpy.return_value = embeddings
        mock_model.encode.return_value = mock_tensor
        mock_sentence_transformer.return_value = mock_model
        
        entries = [
            self.create_test_entry("success1", "Text 1"),
            self.create_test_entry("failure", "Text 2"),
            self.create_test_entry("success2", "Text 3"),
        ]
        
        # Mock database update to fail for one entry
        def mock_update(entry_id, embedding):
            return entry_id != "failure"
        
        worker = EmbeddingWorker(integration_config, mock_db_session_factory)
        worker._update_entry_embedding = Mock(side_effect=mock_update)
        
        results = worker.process_batch(entries, ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Check mixed results
        assert len(results) == 3
        assert results["success1"] is True
        assert results["failure"] is False
        assert results["success2"] is True
    
    def test_empty_batch(self, integration_config, mock_db_session_factory):
        """Test processing empty batch."""
        worker = EmbeddingWorker(integration_config, mock_db_session_factory)
        
        results = worker.process_batch([], ProcessingStage.COMPUTE_EMBEDDINGS)
        
        assert len(results) == 0