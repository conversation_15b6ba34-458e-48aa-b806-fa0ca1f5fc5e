# Production Environment Configuration for Distributed ETL Workers

# Worker Configuration
WORKER_LOG_LEVEL=INFO
WORKER_METRICS_ENABLED=true
WORKER_BATCH_SIZE=25
WORKER_CLAIM_TIMEOUT_MINUTES=30
WORKER_MAX_RETRIES=3
WORKER_HEARTBEAT_INTERVAL_SECONDS=60
WORKER_PROCESSING_DELAY_SECONDS=0.1

# Database Configuration
WORKER_CONNECTION_POOL_SIZE=5
WORKER_CONNECTION_TIMEOUT_SECONDS=30

# Performance Tuning (Production - optimized for performance)
WORKER_BATCH_CLAIM_RETRY_DELAY_SECONDS=1.0
WORKER_MAX_BATCH_CLAIM_RETRIES=5
WORKER_SHUTDOWN_TIMEOUT_SECONDS=300

# Health Check Configuration
WORKER_HEALTH_CHECK_PORT=8080

# Stage-specific Production Settings
FEED_WORKER_BATCH_SIZE=50
FEED_WORKER_CLAIM_TIMEOUT_MINUTES=15
FEED_WORKER_MAX_RETRIES=5

FULLTEXT_WORKER_BATCH_SIZE=10
FULLTEXT_WORKER_CLAIM_TIMEOUT_MINUTES=20
FULLTEXT_WORKER_MAX_RETRIES=3

ANALYSIS_WORKER_BATCH_SIZE=5
ANALYSIS_WORKER_CLAIM_TIMEOUT_MINUTES=60
ANALYSIS_WORKER_MAX_RETRIES=3
ANALYSIS_WORKER_PROCESSING_DELAY_SECONDS=0.5

ML_WORKER_BATCH_SIZE=8
ML_WORKER_CLAIM_TIMEOUT_MINUTES=45
ML_WORKER_MAX_RETRIES=3

GENERATION_WORKER_BATCH_SIZE=3
GENERATION_WORKER_CLAIM_TIMEOUT_MINUTES=90
GENERATION_WORKER_MAX_RETRIES=2

UTILITY_WORKER_BATCH_SIZE=15
UTILITY_WORKER_CLAIM_TIMEOUT_MINUTES=30
UTILITY_WORKER_MAX_RETRIES=3

# Cleanup Service Configuration
CLEANUP_INTERVAL_MINUTES=5
STALE_TIMEOUT_MINUTES=60

# Production API Keys (use production keys - set via secrets)
# OPENAI_API_KEY=${OPENAI_API_KEY}
# IONOS_API_KEY=${IONOS_API_KEY}
# HUGGINGFACE_TOKEN=${HUGGINGFACE_TOKEN}
# STABILITY_API_KEY=${STABILITY_API_KEY}