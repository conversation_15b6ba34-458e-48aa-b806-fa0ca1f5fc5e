"""
Comprehensive error handling and retry system for distributed ETL processing.

This module provides exponential backoff, dead letter queue management,
error classification, and comprehensive failure handling strategies.
"""

import logging
import time
import json
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import List, Dict, Any, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, func, text

from backend.app.models.models import Entry
from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus, ProcessingStatusValue
from backend.app.distributed.work_queue_manager import WorkQueueManager

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Classification of error severity levels."""
    TRANSIENT = "transient"      # Temporary errors that should be retried
    PERSISTENT = "persistent"    # Errors that may resolve with time
    FATAL = "fatal"             # Errors that won't resolve with retries
    CONFIGURATION = "configuration"  # Configuration or setup errors


class ErrorCategory(Enum):
    """Categories of errors for different handling strategies."""
    NETWORK = "network"                    # Network connectivity issues
    RATE_LIMIT = "rate_limit"             # API rate limiting
    RESOURCE = "resource"                 # Resource exhaustion (memory, disk)
    AUTHENTICATION = "authentication"     # Auth/permission errors
    DATA_FORMAT = "data_format"          # Invalid data format
    EXTERNAL_SERVICE = "external_service" # Third-party service errors
    DATABASE = "database"                # Database connectivity/constraint errors
    PROCESSING = "processing"            # Processing logic errors
    TIMEOUT = "timeout"                  # Operation timeout
    UNKNOWN = "unknown"                  # Unclassified errors


@dataclass
class ErrorClassification:
    """Classification of an error for handling strategy."""
    category: ErrorCategory
    severity: ErrorSeverity
    retry_strategy: str
    max_retries: int
    base_delay_seconds: float
    max_delay_seconds: float
    backoff_multiplier: float
    description: str


@dataclass
class RetryAttempt:
    """Information about a retry attempt."""
    attempt_number: int
    timestamp: datetime
    worker_id: str
    error_message: str
    delay_seconds: float
    next_retry_at: Optional[datetime] = None


@dataclass
class FailureRecord:
    """Complete failure record for an entry and stage."""
    entry_id: str
    stage: ProcessingStage
    first_failure_at: datetime
    last_failure_at: datetime
    total_attempts: int
    retry_attempts: List[RetryAttempt]
    error_classification: ErrorClassification
    is_dead_letter: bool = False
    dead_letter_reason: Optional[str] = None
    manual_intervention_required: bool = False


class ErrorClassifier:
    """Classifies errors and determines retry strategies."""
    
    def __init__(self):
        """Initialize with default error classification rules."""
        self._classification_rules = self._build_default_rules()
    
    def _build_default_rules(self) -> Dict[str, ErrorClassification]:
        """Build default error classification rules."""
        return {
            # Network errors - transient, aggressive retry
            "connection": ErrorClassification(
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.TRANSIENT,
                retry_strategy="exponential_backoff",
                max_retries=5,
                base_delay_seconds=2.0,
                max_delay_seconds=300.0,
                backoff_multiplier=2.0,
                description="Network connection errors"
            ),
            "timeout": ErrorClassification(
                category=ErrorCategory.TIMEOUT,
                severity=ErrorSeverity.TRANSIENT,
                retry_strategy="exponential_backoff",
                max_retries=3,
                base_delay_seconds=5.0,
                max_delay_seconds=600.0,
                backoff_multiplier=2.0,
                description="Operation timeout errors"
            ),
            "rate_limit": ErrorClassification(
                category=ErrorCategory.RATE_LIMIT,
                severity=ErrorSeverity.PERSISTENT,
                retry_strategy="exponential_backoff",
                max_retries=10,
                base_delay_seconds=60.0,
                max_delay_seconds=3600.0,
                backoff_multiplier=1.5,
                description="API rate limiting"
            ),
            "authentication": ErrorClassification(
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.FATAL,
                retry_strategy="no_retry",
                max_retries=0,
                base_delay_seconds=0.0,
                max_delay_seconds=0.0,
                backoff_multiplier=1.0,
                description="Authentication/authorization errors"
            ),
            "memory": ErrorClassification(
                category=ErrorCategory.RESOURCE,
                severity=ErrorSeverity.PERSISTENT,
                retry_strategy="exponential_backoff",
                max_retries=3,
                base_delay_seconds=30.0,
                max_delay_seconds=1800.0,
                backoff_multiplier=2.0,
                description="Memory exhaustion errors"
            ),
            "disk_space": ErrorClassification(
                category=ErrorCategory.RESOURCE,
                severity=ErrorSeverity.PERSISTENT,
                retry_strategy="linear_backoff",
                max_retries=5,
                base_delay_seconds=300.0,
                max_delay_seconds=1800.0,
                backoff_multiplier=1.0,
                description="Disk space errors"
            ),
            "data_format": ErrorClassification(
                category=ErrorCategory.DATA_FORMAT,
                severity=ErrorSeverity.FATAL,
                retry_strategy="no_retry",
                max_retries=0,
                base_delay_seconds=0.0,
                max_delay_seconds=0.0,
                backoff_multiplier=1.0,
                description="Invalid data format errors"
            ),
            "external_service": ErrorClassification(
                category=ErrorCategory.EXTERNAL_SERVICE,
                severity=ErrorSeverity.PERSISTENT,
                retry_strategy="exponential_backoff",
                max_retries=5,
                base_delay_seconds=10.0,
                max_delay_seconds=900.0,
                backoff_multiplier=2.0,
                description="External service errors"
            ),
            "database": ErrorClassification(
                category=ErrorCategory.DATABASE,
                severity=ErrorSeverity.TRANSIENT,
                retry_strategy="exponential_backoff",
                max_retries=3,
                base_delay_seconds=1.0,
                max_delay_seconds=60.0,
                backoff_multiplier=2.0,
                description="Database connectivity errors"
            ),
            "processing": ErrorClassification(
                category=ErrorCategory.PROCESSING,
                severity=ErrorSeverity.PERSISTENT,
                retry_strategy="exponential_backoff",
                max_retries=2,
                base_delay_seconds=5.0,
                max_delay_seconds=300.0,
                backoff_multiplier=2.0,
                description="Processing logic errors"
            )
        }
    
    def classify_error(self, error_message: str, exception: Optional[Exception] = None) -> ErrorClassification:
        """
        Classify an error and return appropriate handling strategy.
        
        Args:
            error_message: Error message to classify
            exception: Optional exception object for additional context
            
        Returns:
            ErrorClassification with handling strategy
        """
        error_lower = error_message.lower()
        
        # Check for specific error patterns
        for pattern, classification in self._classification_rules.items():
            if pattern in error_lower:
                return classification
        
        # Additional pattern matching
        if any(term in error_lower for term in ["connection", "network", "unreachable"]):
            return self._classification_rules["connection"]
        
        if any(term in error_lower for term in ["timeout", "timed out"]):
            return self._classification_rules["timeout"]
        
        if any(term in error_lower for term in ["rate limit", "too many requests", "quota"]):
            return self._classification_rules["rate_limit"]
        
        if any(term in error_lower for term in ["unauthorized", "forbidden", "authentication"]):
            return self._classification_rules["authentication"]
        
        if any(term in error_lower for term in ["memory", "out of memory", "oom"]):
            return self._classification_rules["memory"]
        
        if any(term in error_lower for term in ["disk", "space", "storage"]):
            return self._classification_rules["disk_space"]
        
        if any(term in error_lower for term in ["invalid", "malformed", "parse", "format"]):
            return self._classification_rules["data_format"]
        
        if any(term in error_lower for term in ["service unavailable", "bad gateway", "502", "503"]):
            return self._classification_rules["external_service"]
        
        if any(term in error_lower for term in ["database", "sql", "connection pool"]):
            return self._classification_rules["database"]
        
        # Default to processing error
        return self._classification_rules["processing"]
    
    def add_classification_rule(self, pattern: str, classification: ErrorClassification) -> None:
        """Add a custom error classification rule."""
        self._classification_rules[pattern] = classification
    
    def get_all_rules(self) -> Dict[str, ErrorClassification]:
        """Get all classification rules."""
        return self._classification_rules.copy()


class RetryCalculator:
    """Calculates retry delays using various backoff strategies."""
    
    @staticmethod
    def exponential_backoff(attempt: int, 
                          base_delay: float, 
                          max_delay: float, 
                          multiplier: float = 2.0,
                          jitter: bool = True) -> float:
        """
        Calculate exponential backoff delay.
        
        Args:
            attempt: Attempt number (1-based)
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds
            multiplier: Backoff multiplier
            jitter: Whether to add random jitter
            
        Returns:
            Delay in seconds
        """
        import random
        
        delay = base_delay * (multiplier ** (attempt - 1))
        delay = min(delay, max_delay)
        
        if jitter:
            # Add ±25% jitter to prevent thundering herd
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
            delay = max(0, delay)
        
        return delay
    
    @staticmethod
    def linear_backoff(attempt: int, 
                      base_delay: float, 
                      max_delay: float, 
                      increment: Optional[float] = None) -> float:
        """
        Calculate linear backoff delay.
        
        Args:
            attempt: Attempt number (1-based)
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds
            increment: Linear increment (defaults to base_delay)
            
        Returns:
            Delay in seconds
        """
        if increment is None:
            increment = base_delay
        
        delay = base_delay + (increment * (attempt - 1))
        return min(delay, max_delay)
    
    @staticmethod
    def fixed_delay(attempt: int, delay: float, max_delay: float) -> float:
        """
        Calculate fixed delay (no backoff).
        
        Args:
            attempt: Attempt number (ignored)
            delay: Fixed delay in seconds
            max_delay: Maximum delay in seconds
            
        Returns:
            Delay in seconds
        """
        return min(delay, max_delay)


class DeadLetterQueue:
    """Manages entries that have failed repeatedly and require manual intervention."""
    
    def __init__(self, db_session: Session):
        """Initialize with database session."""
        self.db = db_session
    
    def add_to_dead_letter_queue(self, 
                                entry_id: str, 
                                stage: ProcessingStage,
                                failure_record: FailureRecord,
                                reason: str) -> None:
        """
        Add an entry to the dead letter queue.
        
        Args:
            entry_id: Entry ID to add
            stage: Processing stage that failed
            failure_record: Complete failure record
            reason: Reason for dead letter classification
        """
        try:
            with self.db.begin():
                # Get the entry
                entry = self.db.query(Entry).filter(Entry.entry_id == entry_id).first()
                if not entry:
                    logger.error(f"Entry {entry_id} not found for dead letter queue")
                    return
                
                # Update processing status to mark as dead letter
                processing_status = ProcessingStatus.from_json(entry.processing_status)
                processing_status.mark_stage_failed(stage, "dead_letter_queue", reason)
                
                # Create dead letter record (convert enums and datetimes to strings for JSON serialization)
                failure_record_dict = asdict(failure_record)
                # Convert enum values to strings
                failure_record_dict['stage'] = failure_record.stage.value
                failure_record_dict['error_classification']['category'] = failure_record.error_classification.category.value
                failure_record_dict['error_classification']['severity'] = failure_record.error_classification.severity.value
                # Convert datetime values to ISO strings
                failure_record_dict['first_failure_at'] = failure_record.first_failure_at.isoformat()
                failure_record_dict['last_failure_at'] = failure_record.last_failure_at.isoformat()
                # Convert retry attempts
                for attempt in failure_record_dict['retry_attempts']:
                    attempt['timestamp'] = attempt['timestamp'].isoformat() if isinstance(attempt['timestamp'], datetime) else attempt['timestamp']
                    if attempt.get('next_retry_at') and isinstance(attempt['next_retry_at'], datetime):
                        attempt['next_retry_at'] = attempt['next_retry_at'].isoformat()
                
                dead_letter_data = {
                    "is_dead_letter": True,
                    "dead_letter_reason": reason,
                    "dead_letter_timestamp": datetime.now(timezone.utc).isoformat(),
                    "failure_record": failure_record_dict,
                    "manual_intervention_required": True
                }
                
                # Update entry
                entry.processing_status = processing_status.to_json()
                entry.last_error = f"DEAD_LETTER: {reason}"
                entry.retry_count = failure_record.total_attempts
                
                # Store dead letter data in a separate field if available
                # For now, we'll store it in the processing status
                current_status_data = json.loads(entry.processing_status) if entry.processing_status else {}
                current_status_data["dead_letter_info"] = dead_letter_data
                entry.processing_status = json.dumps(current_status_data)
                
                logger.warning(
                    f"Added entry {entry_id} to dead letter queue for stage {stage.value}: {reason}"
                )
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to add entry {entry_id} to dead letter queue: {e}")
            self.db.rollback()
            raise
    
    def get_dead_letter_entries(self, 
                               stage: Optional[ProcessingStage] = None,
                               limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get entries from the dead letter queue.
        
        Args:
            stage: Optional stage filter
            limit: Maximum number of entries to return
            
        Returns:
            List of dead letter entries with metadata
        """
        try:
            query = self.db.query(Entry).filter(
                Entry.last_error.like("DEAD_LETTER:%")
            )
            
            if stage:
                # This is a simplified filter - in practice you'd need more sophisticated JSON querying
                query = query.filter(Entry.processing_status.like(f'%{stage.value}%'))
            
            entries = query.limit(limit).all()
            
            dead_letter_entries = []
            for entry in entries:
                try:
                    status_data = json.loads(entry.processing_status) if entry.processing_status else {}
                    dead_letter_info = status_data.get("dead_letter_info", {})
                    
                    dead_letter_entries.append({
                        "entry_id": entry.entry_id,
                        "title": getattr(entry, 'title', 'Unknown'),
                        "published": entry.published.isoformat() if entry.published else None,
                        "last_error": entry.last_error,
                        "retry_count": entry.retry_count,
                        "dead_letter_info": dead_letter_info
                    })
                except Exception as e:
                    logger.warning(f"Error processing dead letter entry {entry.entry_id}: {e}")
            
            return dead_letter_entries
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get dead letter entries: {e}")
            return []
    
    def retry_dead_letter_entry(self, 
                               entry_id: str, 
                               stage: ProcessingStage,
                               reset_retry_count: bool = True) -> bool:
        """
        Retry a dead letter entry by removing it from the dead letter queue.
        
        Args:
            entry_id: Entry ID to retry
            stage: Processing stage to retry
            reset_retry_count: Whether to reset the retry count
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.db.begin():
                entry = self.db.query(Entry).filter(Entry.entry_id == entry_id).first()
                if not entry:
                    logger.error(f"Entry {entry_id} not found for dead letter retry")
                    return False
                
                # Parse and update processing status
                processing_status = ProcessingStatus.from_json(entry.processing_status)
                processing_status.set_stage_status(stage, ProcessingStatusValue.PENDING)
                
                # Remove dead letter information
                status_data = json.loads(entry.processing_status) if entry.processing_status else {}
                if "dead_letter_info" in status_data:
                    del status_data["dead_letter_info"]
                
                # Update entry
                entry.processing_status = json.dumps(status_data)
                entry.last_error = None
                entry.claimed_by = None
                entry.claimed_at = None
                
                if reset_retry_count:
                    entry.retry_count = 0
                
                logger.info(f"Retrying dead letter entry {entry_id} for stage {stage.value}")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to retry dead letter entry {entry_id}: {e}")
            self.db.rollback()
            return False
    
    def get_dead_letter_stats(self) -> Dict[str, Any]:
        """Get statistics about the dead letter queue."""
        try:
            # Count total dead letter entries
            total_count = self.db.query(Entry).filter(
                Entry.last_error.like("DEAD_LETTER:%")
            ).count()
            
            # Get counts by stage (simplified - would need better JSON querying in production)
            stage_counts = {}
            for stage in ProcessingStage:
                count = self.db.query(Entry).filter(
                    and_(
                        Entry.last_error.like("DEAD_LETTER:%"),
                        Entry.processing_status.like(f'%{stage.value}%')
                    )
                ).count()
                if count > 0:
                    stage_counts[stage.value] = count
            
            return {
                "total_dead_letter_entries": total_count,
                "by_stage": stage_counts,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get dead letter stats: {e}")
            return {"error": str(e)}


class ComprehensiveErrorHandler:
    """
    Comprehensive error handling system with retry logic and dead letter queue.
    
    Provides intelligent retry strategies, error classification, and dead letter
    queue management for failed entries.
    """
    
    def __init__(self, db_session: Session):
        """Initialize with database session."""
        self.db = db_session
        self.classifier = ErrorClassifier()
        self.dead_letter_queue = DeadLetterQueue(db_session)
        self.retry_calculator = RetryCalculator()
        
        # Failure tracking
        self._failure_records: Dict[str, FailureRecord] = {}
    
    def handle_processing_error(self, 
                              entry_id: str,
                              stage: ProcessingStage,
                              error_message: str,
                              worker_id: str,
                              exception: Optional[Exception] = None) -> Tuple[bool, Optional[float]]:
        """
        Handle a processing error with intelligent retry logic.
        
        Args:
            entry_id: Entry ID that failed
            stage: Processing stage that failed
            error_message: Error message
            worker_id: Worker that encountered the error
            exception: Optional exception object
            
        Returns:
            Tuple of (should_retry, delay_seconds)
        """
        # Classify the error
        classification = self.classifier.classify_error(error_message, exception)
        
        # Get or create failure record
        failure_key = f"{entry_id}:{stage.value}"
        failure_record = self._get_or_create_failure_record(
            entry_id, stage, classification, error_message, worker_id
        )
        
        # Check if we should retry
        should_retry = self._should_retry(failure_record, classification)
        
        if not should_retry:
            # Add to dead letter queue
            reason = self._get_dead_letter_reason(failure_record, classification)
            self.dead_letter_queue.add_to_dead_letter_queue(
                entry_id, stage, failure_record, reason
            )
            return False, None
        
        # Calculate retry delay
        delay_seconds = self._calculate_retry_delay(failure_record, classification)
        
        # Update failure record with retry attempt
        self._record_retry_attempt(failure_record, error_message, worker_id, delay_seconds)
        
        # Update database with retry information
        self._update_entry_retry_info(entry_id, stage, failure_record, delay_seconds)
        
        logger.info(
            f"Scheduling retry for entry {entry_id} stage {stage.value} "
            f"(attempt {failure_record.total_attempts + 1}/{classification.max_retries}) "
            f"in {delay_seconds:.1f}s"
        )
        
        return True, delay_seconds
    
    def _get_or_create_failure_record(self, 
                                    entry_id: str,
                                    stage: ProcessingStage,
                                    classification: ErrorClassification,
                                    error_message: str,
                                    worker_id: str) -> FailureRecord:
        """Get existing failure record or create a new one."""
        failure_key = f"{entry_id}:{stage.value}"
        
        if failure_key not in self._failure_records:
            now = datetime.now(timezone.utc)
            self._failure_records[failure_key] = FailureRecord(
                entry_id=entry_id,
                stage=stage,
                first_failure_at=now,
                last_failure_at=now,
                total_attempts=0,
                retry_attempts=[],
                error_classification=classification
            )
        
        return self._failure_records[failure_key]
    
    def _should_retry(self, failure_record: FailureRecord, classification: ErrorClassification) -> bool:
        """Determine if an entry should be retried."""
        # Check if error is fatal
        if classification.severity == ErrorSeverity.FATAL:
            return False
        
        # Check if max retries exceeded
        if failure_record.total_attempts >= classification.max_retries:
            return False
        
        # Check if retry strategy allows retries
        if classification.retry_strategy == "no_retry":
            return False
        
        return True
    
    def _calculate_retry_delay(self, failure_record: FailureRecord, classification: ErrorClassification) -> float:
        """Calculate the delay before next retry."""
        attempt_number = failure_record.total_attempts + 1
        
        if classification.retry_strategy == "exponential_backoff":
            return self.retry_calculator.exponential_backoff(
                attempt_number,
                classification.base_delay_seconds,
                classification.max_delay_seconds,
                classification.backoff_multiplier
            )
        elif classification.retry_strategy == "linear_backoff":
            return self.retry_calculator.linear_backoff(
                attempt_number,
                classification.base_delay_seconds,
                classification.max_delay_seconds
            )
        elif classification.retry_strategy == "fixed_delay":
            return self.retry_calculator.fixed_delay(
                attempt_number,
                classification.base_delay_seconds,
                classification.max_delay_seconds
            )
        else:
            # Default to exponential backoff
            return self.retry_calculator.exponential_backoff(
                attempt_number,
                classification.base_delay_seconds,
                classification.max_delay_seconds
            )
    
    def _record_retry_attempt(self, 
                            failure_record: FailureRecord,
                            error_message: str,
                            worker_id: str,
                            delay_seconds: float) -> None:
        """Record a retry attempt in the failure record."""
        now = datetime.now(timezone.utc)
        
        retry_attempt = RetryAttempt(
            attempt_number=failure_record.total_attempts + 1,
            timestamp=now,
            worker_id=worker_id,
            error_message=error_message,
            delay_seconds=delay_seconds,
            next_retry_at=now + timedelta(seconds=delay_seconds)
        )
        
        failure_record.retry_attempts.append(retry_attempt)
        failure_record.total_attempts += 1
        failure_record.last_failure_at = now
    
    def _update_entry_retry_info(self, 
                               entry_id: str,
                               stage: ProcessingStage,
                               failure_record: FailureRecord,
                               delay_seconds: float) -> None:
        """Update entry in database with retry information."""
        try:
            with self.db.begin():
                entry = self.db.query(Entry).filter(Entry.entry_id == entry_id).first()
                if not entry:
                    logger.error(f"Entry {entry_id} not found for retry update")
                    return
                
                # Update processing status
                processing_status = ProcessingStatus.from_json(entry.processing_status)
                processing_status.mark_stage_failed(
                    stage, 
                    failure_record.retry_attempts[-1].worker_id,
                    failure_record.retry_attempts[-1].error_message
                )
                
                # Add retry information to processing status
                status_data = json.loads(processing_status.to_json())
                if stage.value not in status_data:
                    status_data[stage.value] = {}
                
                # Convert error classification to JSON-serializable format
                error_classification_dict = asdict(failure_record.error_classification)
                error_classification_dict['category'] = failure_record.error_classification.category.value
                error_classification_dict['severity'] = failure_record.error_classification.severity.value
                
                status_data[stage.value]["retry_info"] = {
                    "next_retry_at": failure_record.retry_attempts[-1].next_retry_at.isoformat(),
                    "delay_seconds": delay_seconds,
                    "total_attempts": failure_record.total_attempts,
                    "error_classification": error_classification_dict
                }
                
                # Update entry
                entry.processing_status = json.dumps(status_data)
                entry.retry_count = failure_record.total_attempts
                entry.last_error = failure_record.retry_attempts[-1].error_message
                
                # Release claim so it can be retried later
                entry.claimed_by = None
                entry.claimed_at = None
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to update entry retry info: {e}")
            self.db.rollback()
            raise
    
    def _get_dead_letter_reason(self, failure_record: FailureRecord, classification: ErrorClassification) -> str:
        """Get reason for adding entry to dead letter queue."""
        if classification.severity == ErrorSeverity.FATAL:
            return f"Fatal error: {classification.description}"
        elif failure_record.total_attempts >= classification.max_retries:
            return f"Max retries exceeded ({classification.max_retries}): {classification.description}"
        else:
            return f"Processing failed: {classification.description}"
    
    def get_retry_ready_entries(self, stage: ProcessingStage, limit: int = 100) -> List[str]:
        """
        Get entries that are ready for retry (past their retry delay).
        
        Args:
            stage: Processing stage to check
            limit: Maximum number of entries to return
            
        Returns:
            List of entry IDs ready for retry
        """
        try:
            now = datetime.now(timezone.utc)
            
            # Query entries with retry information
            entries = (
                self.db.query(Entry)
                .filter(
                    and_(
                        Entry.claimed_by.is_(None),  # Not currently claimed
                        Entry.processing_status.like(f'%{stage.value}%'),  # Has this stage
                        Entry.processing_status.like('%retry_info%')  # Has retry info
                    )
                )
                .limit(limit * 2)  # Get more to filter
                .all()
            )
            
            ready_entries = []
            for entry in entries:
                try:
                    status_data = json.loads(entry.processing_status) if entry.processing_status else {}
                    stage_data = status_data.get(stage.value, {})
                    retry_info = stage_data.get("retry_info", {})
                    
                    if retry_info and "next_retry_at" in retry_info:
                        next_retry_at = datetime.fromisoformat(retry_info["next_retry_at"])
                        if now >= next_retry_at:
                            ready_entries.append(entry.entry_id)
                            
                            if len(ready_entries) >= limit:
                                break
                
                except Exception as e:
                    logger.warning(f"Error checking retry readiness for entry {entry.entry_id}: {e}")
            
            return ready_entries
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get retry ready entries: {e}")
            return []
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get comprehensive error handling statistics."""
        try:
            now = datetime.now(timezone.utc)
            
            # Get failure record statistics
            failure_stats = {
                "total_failure_records": len(self._failure_records),
                "by_stage": {},
                "by_error_category": {},
                "by_severity": {}
            }
            
            for failure_record in self._failure_records.values():
                stage_name = failure_record.stage.value
                category = failure_record.error_classification.category.value
                severity = failure_record.error_classification.severity.value
                
                failure_stats["by_stage"][stage_name] = failure_stats["by_stage"].get(stage_name, 0) + 1
                failure_stats["by_error_category"][category] = failure_stats["by_error_category"].get(category, 0) + 1
                failure_stats["by_severity"][severity] = failure_stats["by_severity"].get(severity, 0) + 1
            
            # Get dead letter statistics
            dead_letter_stats = self.dead_letter_queue.get_dead_letter_stats()
            
            return {
                "timestamp": now.isoformat(),
                "failure_records": failure_stats,
                "dead_letter_queue": dead_letter_stats,
                "error_classification_rules": len(self.classifier.get_all_rules())
            }
            
        except Exception as e:
            logger.error(f"Failed to get error statistics: {e}")
            return {"error": str(e)}
    
    def cleanup_old_failure_records(self, max_age_hours: int = 24) -> int:
        """
        Clean up old failure records to prevent memory leaks.
        
        Args:
            max_age_hours: Maximum age of failure records to keep
            
        Returns:
            Number of records cleaned up
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)
        
        cleaned_count = 0
        keys_to_remove = []
        
        for key, failure_record in self._failure_records.items():
            if failure_record.last_failure_at < cutoff_time:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self._failure_records[key]
            cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old failure records")
        
        return cleaned_count