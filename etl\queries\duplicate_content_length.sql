-- Compare content length across duplicate news from different sources
-- Shows which sources provide more detailed coverage of the same stories
WITH duplicate_groups AS (
    SELECT 
        dup_entry_id
    FROM 
        entries
    GROUP BY 
        dup_entry_id
    HAVING 
        COUNT(DISTINCT source) > 1
)
SELECT 
    e.dup_entry_id,
    e.entry_id,
    e.title,
    e.source,
    s.name AS source_name,
    e.published,
    LENGTH(e.full_text) AS content_length,
    CASE 
        WHEN LENGTH(e.full_text) > 5000 THEN 'Long'
        WHEN LENGTH(e.full_text) > 2000 THEN 'Medium'
        ELSE 'Short'
    END AS length_category
FROM 
    entries e
JOIN 
    duplicate_groups dg ON e.dup_entry_id = dg.dup_entry_id
LEFT JOIN
    sources s ON e.source = s.source
WHERE
    e.full_text IS NOT NULL
ORDER BY 
    e.dup_entry_id,
    content_length DESC
FETCH FIRST 100 ROWS ONLY
