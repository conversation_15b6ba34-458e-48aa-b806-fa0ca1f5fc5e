#!/usr/bin/env python3
"""
Performance test for optimized database queries.
This tests the new simplified queries without subqueries.
"""

import asyncio
import aiohttp
import time
import statistics
from typing import List, Dict

BASE_URL = "http://localhost:8000"

async def test_endpoint_performance(session: aiohttp.ClientSession, url: str, params: Dict = None, iterations: int = 10) -> Dict:
    """Test endpoint performance over multiple iterations."""
    times = []
    errors = 0
    
    for i in range(iterations):
        start_time = time.time()
        try:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    await response.json()
                    times.append(time.time() - start_time)
                else:
                    errors += 1
                    print(f"Error {response.status} on iteration {i+1}")
        except Exception as e:
            errors += 1
            print(f"Exception on iteration {i+1}: {e}")
    
    if not times:
        return {"error": "No successful requests"}
    
    return {
        "avg": statistics.mean(times),
        "min": min(times),
        "max": max(times),
        "median": statistics.median(times),
        "success_rate": len(times) / iterations * 100,
        "errors": errors,
        "total_requests": iterations
    }

async def run_performance_tests():
    """Run performance tests on the optimized endpoints."""
    
    test_cases = [
        {
            "name": "News List",
            "url": f"{BASE_URL}/news/",
            "params": {"limit": 20, "min_positive": 0.7},
            "expected_improvement": "50-80% faster than complex queries"
        },
        {
            "name": "News Categories",
            "url": f"{BASE_URL}/news/categories/",
            "params": {"limit_per_category": 3, "min_positive": 0.7},
            "expected_improvement": "60-90% faster than complex queries"
        },
        {
            "name": "Home Screen Data",
            "url": f"{BASE_URL}/news/categories/",
            "params": {"limit_per_category": 3, "min_positive": 0.7},
            "expected_improvement": "70-90% faster than complex queries"
        },
        {
            "name": "Category Specific News",
            "url": f"{BASE_URL}/news/",
            "params": {"category": "01000000", "limit": 10, "min_positive": 0.7},
            "expected_improvement": "40-70% faster than complex queries"
        },
        {
            "name": "Similar News Check (Batch)",
            "url": f"{BASE_URL}/news/check-similar",
            "method": "POST",
            "data": ["test_id_1", "test_id_2", "test_id_3"],
            "expected_improvement": "Efficient batch processing"
        }
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        print("🚀 Starting Optimized Query Performance Tests")
        print("=" * 60)
        
        for test_case in test_cases:
            print(f"\n📊 Testing: {test_case['name']}")
            print(f"Expected: {test_case['expected_improvement']}")
            
            if test_case.get('method') == 'POST':
                # Handle POST requests
                times = []
                errors = 0
                
                for i in range(10):
                    start_time = time.time()
                    try:
                        async with session.post(
                            test_case['url'],
                            json=test_case['data'],
                            headers={'Content-Type': 'application/json'}
                        ) as response:
                            if response.status == 200:
                                await response.json()
                                times.append(time.time() - start_time)
                            else:
                                errors += 1
                    except Exception as e:
                        errors += 1
                        print(f"  ❌ Error on iteration {i+1}: {e}")
                
                if times:
                    results[test_case['name']] = {
                        "avg": statistics.mean(times),
                        "min": min(times),
                        "max": max(times),
                        "success_rate": len(times) / 10 * 100,
                        "errors": errors
                    }
                else:
                    results[test_case['name']] = {"error": "No successful requests"}
            else:
                # Handle GET requests
                result = await test_endpoint_performance(
                    session,
                    test_case['url'],
                    test_case.get('params'),
                    iterations=10
                )
                results[test_case['name']] = result
            
            # Print immediate results
            if 'error' not in results[test_case['name']]:
                stats = results[test_case['name']]
                print(f"  ✅ Average: {stats['avg']:.3f}s")
                print(f"  ⚡ Min: {stats['min']:.3f}s")
                print(f"  🐌 Max: {stats['max']:.3f}s")
                print(f"  📈 Success Rate: {stats['success_rate']:.1f}%")
            else:
                print(f"  ❌ {results[test_case['name']]['error']}")
    
    # Print comprehensive summary
    print("\n" + "=" * 60)
    print("📋 OPTIMIZED QUERY PERFORMANCE SUMMARY")
    print("=" * 60)
    
    performance_targets = {
        "News List": 0.3,  # Target: under 300ms
        "News Categories": 0.2,  # Target: under 200ms
        "Home Screen Data": 0.4,  # Target: under 400ms
        "Category Specific News": 0.25,  # Target: under 250ms
        "Similar News Check (Batch)": 0.1,  # Target: under 100ms
    }
    
    passed_tests = 0
    total_tests = 0
    
    for name, stats in results.items():
        if 'error' in stats:
            print(f"❌ {name}: FAILED - {stats['error']}")
            continue
            
        total_tests += 1
        target = performance_targets.get(name, 0.5)
        status = "✅ PASS" if stats['avg'] <= target else "⚠️  SLOW"
        
        if stats['avg'] <= target:
            passed_tests += 1
        
        print(f"{status} {name}:")
        print(f"    Average: {stats['avg']:.3f}s (target: {target:.3f}s)")
        print(f"    Range: {stats['min']:.3f}s - {stats['max']:.3f}s")
        print(f"    Success: {stats['success_rate']:.1f}%")
        
        if stats.get('errors', 0) > 0:
            print(f"    Errors: {stats['errors']}")
    
    print(f"\n🎯 Overall Performance: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All performance targets met! Optimized queries are working optimally.")
    elif passed_tests >= total_tests * 0.8:
        print("👍 Most performance targets met. Minor optimizations may be needed.")
    else:
        print("⚠️  Performance targets not met. Further optimization required.")
    
    # Database optimization suggestions
    print("\n💡 OPTIMIZATION RECOMMENDATIONS:")
    print("1. Ensure these indexes exist:")
    print("   - CREATE INDEX idx_entry_published_positive ON entry(published DESC, llm_positive);")
    print("   - CREATE INDEX idx_entry_category_published ON entry(iptc_newscode, published DESC);")
    print("   - CREATE INDEX idx_entry_time_filter ON entry(published, llm_positive, llm_is_ad);")
    print("2. Consider connection pooling optimization")
    print("3. Monitor database query execution plans")
    print("4. Use EXPLAIN ANALYZE on slow queries")
    
    return results

async def compare_with_baseline():
    """Compare optimized queries with a baseline."""
    print("\n🔄 BASELINE COMPARISON")
    print("=" * 40)
    
    # Test a simple endpoint multiple times to establish baseline
    async with aiohttp.ClientSession() as session:
        baseline_times = []
        
        for i in range(5):
            start_time = time.time()
            try:
                async with session.get(f"{BASE_URL}/") as response:
                    if response.status == 200:
                        await response.json()
                        baseline_times.append(time.time() - start_time)
            except Exception as e:
                print(f"Baseline test failed: {e}")
        
        if baseline_times:
            baseline_avg = statistics.mean(baseline_times)
            print(f"📊 Baseline API response time: {baseline_avg:.3f}s")
            
            # Test our optimized endpoint
            news_times = []
            for i in range(5):
                start_time = time.time()
                try:
                    async with session.get(f"{BASE_URL}/news/", params={"limit": 10}) as response:
                        if response.status == 200:
                            await response.json()
                            news_times.append(time.time() - start_time)
                except Exception as e:
                    print(f"News test failed: {e}")
            
            if news_times:
                news_avg = statistics.mean(news_times)
                overhead = news_avg - baseline_avg
                print(f"📊 Optimized news query: {news_avg:.3f}s")
                print(f"📊 Query overhead: {overhead:.3f}s")
                
                if overhead < 0.1:
                    print("✅ Excellent: Query overhead is minimal")
                elif overhead < 0.2:
                    print("👍 Good: Query overhead is acceptable")
                else:
                    print("⚠️  High query overhead - needs optimization")

if __name__ == "__main__":
    print("Optimized Query Performance Tester")
    print("Make sure your backend is running on http://localhost:8000")
    print("Press Enter to start tests or Ctrl+C to cancel...")
    input()
    
    asyncio.run(run_performance_tests())
    asyncio.run(compare_with_baseline())