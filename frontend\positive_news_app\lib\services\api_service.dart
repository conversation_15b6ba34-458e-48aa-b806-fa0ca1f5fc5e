import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:breakingbright/models/news_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/services/duplicate_handler_service.dart';
import 'package:breakingbright/services/environment_service.dart';

class ApiService {
  // HTTP-Client für Anfragen
  final http.Client _client = http.Client();

  // Dynamic base URL based on environment
  String get baseUrl {
    EnvironmentService.logEnvironmentInfo();
    return EnvironmentService.apiBaseUrl;
  }

  // Dynamic debug flag based on environment
  bool get _debug => EnvironmentService.isDebug;

  /// Logs a debug message if [_debug] is true.
  ///
  /// This can be set to true in development to see the URLs being used for
  /// API requests.
  void _logDebug(String message) {
    if (_debug) {
      debugPrint('ApiService: $message');
    }
  }

  // Holt Nachrichten nach Kategorien mit automatischer Duplikatbehandlung
  Future<NewsResponse> getNewsByCategories({
    double minPositive = 0.7,
    int limitPerCategory = 3,
    BuildContext? context,
  }) async {
    final lookbackHours =
        Provider.of<SettingsProvider>(context!, listen: false).lookbackHours;
    try {
      // Smart approach: Request more items to account for potential duplicates
      // This ensures we still get the desired number after deduplication
      final requestLimit =
          (limitPerCategory * 1.5).ceil(); // 50% buffer for duplicates

      final response = await _client.get(
        Uri.parse(
            '$baseUrl/news/categories/?min_positive=$minPositive&hours=$lookbackHours&limit_per_category=$requestLimit'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final rawResponse =
            NewsResponse.fromJson(json.decode(utf8.decode(response.bodyBytes)));

        // Apply duplicate removal and limit to desired count
        final deduplicatedCategories = rawResponse.categories.map((category) {
          // Remove duplicates from this category
          final dedupedNews =
              DuplicateHandlerService.removeDuplicates(category.news);

          // Debug: Log deduplication results
          if (category.news.length != dedupedNews.length) {
            _logDebug(
                '🚨 HOME SCREEN: Category ${category.category} had duplicates: ${category.news.length} → ${dedupedNews.length} items');
          }

          // Limit to the originally requested count (for home screen layout consistency)
          final limitedNews = dedupedNews.take(limitPerCategory).toList();

          _logDebug(
              'Category ${category.category}: ${category.news.length} → ${dedupedNews.length} → ${limitedNews.length} news items (limited for layout)');

          return CategoryNews(
            category: category.category,
            categoryCode: category.categoryCode,
            news: limitedNews,
          );
        }).toList();

        return NewsResponse(categories: deduplicatedCategories);
      } else {
        throw Exception(
            'Fehler beim Laden der Nachrichten: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Holt Nachrichten mit Filteroptionen und Pagination (mit Duplikatbehandlung)
  // Universal method for all paginated news requests
  Future<PaginatedNewsResponse> getNews({
    int skip = 0,
    int limit = 10,
    double minPositive = 0.7,
    String? category,
    BuildContext? context,
    int? lookbackHours, // Optional override for lookback hours
  }) async {
    final effectiveLookbackHours = lookbackHours ??
        Provider.of<SettingsProvider>(context!, listen: false).lookbackHours;
    try {
      // Smart approach: Request more items to account for potential duplicates
      final requestLimit = (limit * 1.3).ceil(); // 30% buffer for duplicates

      String url =
          '$baseUrl/news/?skip=$skip&limit=$requestLimit&min_positive=$minPositive&hours=$effectiveLookbackHours';
      if (category != null) {
        url += '&category=$category';
      }

      final response = await _client.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final rawResponse = PaginatedNewsResponse.fromJson(
            json.decode(utf8.decode(response.bodyBytes)));

        // Apply duplicate removal - keep all non-duplicates (don't limit back to original count)
        final dedupedItems =
            DuplicateHandlerService.removeDuplicates(rawResponse.items);

        _logDebug(
            'News pagination: ${rawResponse.items.length} → ${dedupedItems.length} items (kept all non-duplicates)');

        return PaginatedNewsResponse(
          items: dedupedItems, // Return all non-duplicate items
          total: rawResponse.total, // Keep original total for pagination
          hasMore: rawResponse.hasMore,
        );
      } else {
        throw Exception(
            'Fehler beim Laden der Nachrichten: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Holt ähnliche Nachrichten zu einer bestimmten Nachricht
  Future<SimilarNewsResponse> getSimilarNews(String entryId) async {
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/news/$entryId/similar'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return SimilarNewsResponse.fromJson(
            json.decode(utf8.decode(response.bodyBytes)));
      } else {
        throw Exception(
            'Fehler beim Laden ähnlicher Nachrichten: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Batch check for similar news - new lazy loading approach
  Future<Map<String, bool>> checkSimilarNews(List<String> entryIds) async {
    if (entryIds.isEmpty) return {};

    try {
      final response = await _client.post(
        Uri.parse('$baseUrl/news/check-similar'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(entryIds),
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> result =
            json.decode(utf8.decode(response.bodyBytes));
        return result.map((key, value) => MapEntry(key, value as bool));
      } else {
        _logDebug('Error checking similar news: ${response.statusCode}');
        return {};
      }
    } catch (e) {
      _logDebug('Network error checking similar news: $e');
      return {};
    }
  }

  // Legacy method - kept for backward compatibility but now uses batch check
  Future<bool> hasSimilarNews(String entryId) async {
    final result = await checkSimilarNews([entryId]);
    return result[entryId] ?? false;
  }

  // Sucht nach Nachrichten mit einem Suchbegriff
  Future<PaginatedNewsResponse> searchNews(String query) async {
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/news/?search=$query'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return PaginatedNewsResponse.fromJson(
            json.decode(utf8.decode(response.bodyBytes)));
      } else {
        throw Exception(
            'Fehler bei der Suche: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Netzwerkfehler: $e');
    }
  }

  // Testet die Verbindung zum Backend
  Future<bool> testConnection() async {
    try {
      final response = await _client.get(Uri.parse('$baseUrl/'));
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  Future<NewsResponse> getHomeScreenData({
    double minPositive = 0.7,
    int limitPerCategory = 3,
    BuildContext? context,
  }) async {
    // Use the same deduplication logic as getNewsByCategories
    return getNewsByCategories(
      minPositive: minPositive,
      limitPerCategory: limitPerCategory,
      context: context,
    );
  }

  // Enhanced method to handle both API images and fallbacks
  String getImageUrl(String entryId, {bool hasImage = false}) {
    return hasImage
        ? '$baseUrl/news/$entryId/image'
        : 'https://picsum.photos/seed/$entryId/400/200';
  }
}
