"""Add distributed processing work queue columns and indexes

Revision ID: 4f16f52da124
Revises: 470351eae93c
Create Date: 2025-09-18 00:18:59.333343

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import oracle

# revision identifiers, used by Alembic.
revision: str = '4f16f52da124'
down_revision: Union[str, None] = '470351eae93c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add new columns for distributed processing work queue management
    op.add_column('entries', sa.Column('processing_status', sa.CLOB(), nullable=True))
    op.add_column('entries', sa.Column('claimed_by', sa.String(length=255), nullable=True))
    op.add_column('entries', sa.Column('claimed_at', oracle.TIMESTAMP(), nullable=True))
    op.add_column('entries', sa.Column('retry_count', sa.Integer(), nullable=True, default=0))
    op.add_column('entries', sa.Column('last_error', sa.CLOB(), nullable=True))
    
    # Set default retry_count to 0 for existing entries
    op.execute("UPDATE entries SET retry_count = 0 WHERE retry_count IS NULL")
    
    # Add indexes for efficient work claiming queries
    op.create_index('idx_entries_claimed_at', 'entries', ['claimed_at'], unique=False)
    op.create_index('idx_entries_claimed_by_at', 'entries', ['claimed_by', 'claimed_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop indexes for distributed processing work queue management
    op.drop_index('idx_entries_claimed_by_at', table_name='entries')
    op.drop_index('idx_entries_claimed_at', table_name='entries')
    
    # Drop columns for distributed processing work queue management
    op.drop_column('entries', 'last_error')
    op.drop_column('entries', 'retry_count')
    op.drop_column('entries', 'claimed_at')
    op.drop_column('entries', 'claimed_by')
    op.drop_column('entries', 'processing_status')
    # ### end Alembic commands ###
