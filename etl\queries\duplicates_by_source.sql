-- Analysis of duplicate news by source
-- Shows which sources have the most duplicated content
SELECT 
    e.source,
    s.name AS source_name,
    COUNT(e.entry_id) AS total_entries,
    COUNT(CASE WHEN e.entry_id <> e.dup_entry_id THEN 1 END) AS duplicate_entries,
    ROUND(COUNT(CASE WHEN e.entry_id <> e.dup_entry_id THEN 1 END) * 100.0 / COUNT(e.entry_id), 2) AS duplicate_percentage
FROM 
    entries e
LEFT JOIN
    sources s ON e.source = s.source
GROUP BY 
    e.source, s.name
HAVING 
    COUNT(e.entry_id) > 10
ORDER BY 
    duplicate_percentage DESC
