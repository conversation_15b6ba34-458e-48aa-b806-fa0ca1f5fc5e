@echo off
echo ETL Manager
echo ===========
echo 1. Check ETL health once
echo 2. Start continuous monitoring
echo 3. Restart ETL process
echo 4. Stop ETL process
echo.
set /p choice="Enter choice (1-4): "

cd /d "%~dp0"

if "%choice%"=="1" (
    python watchdog.py --check-once
) else if "%choice%"=="2" (
    echo Starting continuous monitoring... Press Ctrl+C to stop
    python watchdog.py
) else if "%choice%"=="3" (
    python watchdog.py --restart
) else if "%choice%"=="4" (
    python watchdog.py --stop
) else (
    echo Invalid choice
)

pause