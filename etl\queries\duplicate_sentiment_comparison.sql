-- Compare sentiment scores across duplicate news from different sources
-- Shows how the same story might be presented differently by different sources
WITH duplicate_groups AS (
    SELECT 
        dup_entry_id
    FROM 
        entries
    GROUP BY 
        dup_entry_id
    HAVING 
        COUNT(DISTINCT source) > 1
        AND COUNT(*) > 2
)
SELECT 
    e.dup_entry_id,
    e.entry_id,
    e.title,
    e.source,
    s.name AS source_name,
    e.published,
    e.llm_positive,
    e.llm_neutral,
    e.llm_negative,
    e.llm_is_ad
FROM 
    entries e
JOIN 
    duplicate_groups dg ON e.dup_entry_id = dg.dup_entry_id
LEFT JOIN
    sources s ON e.source = s.source
ORDER BY 
    e.dup_entry_id,
    e.published DESC
FETCH FIRST 100 ROWS ONLY
