# Distributed ETL Dashboard

## Overview

This directory will contain the dashboard functionality for the distributed ETL system, providing the same monitoring capabilities as the monolithic news_aggregator with additional distributed-specific features.

## Planned Features

### 1. **Dashboard with Donut Charts**
- **Stage Progress Charts**: Visual representation of processing stages
- **Worker Breakdown**: Charts broken down by individual workers
- **Real-time Updates**: Live monitoring of processing status
- **Historical Trends**: Graphs showing processing trends over time

### 2. **SQL Query Tool with LLM Support**
- **Interactive SQL Interface**: Execute queries against the database
- **LLM-Assisted Queries**: Natural language to SQL conversion
- **Query History**: Save and reuse common queries
- **Result Visualization**: Charts and tables for query results

### 3. **Worker Management Interface**
- **Worker Status**: Real-time worker health monitoring
- **Stage Assignment**: Configure which workers handle which stages
- **Performance Metrics**: Worker throughput and error rates
- **Log Viewer**: Centralized log viewing with filtering

## Implementation Plan

### Phase 1: Core Dashboard (Priority: High)
1. **Create Flask/FastAPI web interface**
2. **Implement donut charts for stage progress**
3. **Add worker-specific breakdowns**
4. **Real-time WebSocket updates**

### Phase 2: SQL Query Tool (Priority: High)
1. **Port existing SQL query functionality from monolithic system**
2. **Add LLM integration for natural language queries**
3. **Implement query result visualization**
4. **Add query sharing and history**

### Phase 3: Advanced Features (Priority: Medium)
1. **Historical trend analysis**
2. **Performance optimization suggestions**
3. **Automated alerting system**
4. **Configuration management UI**

## Integration Options

### Option 1: Integrated with Manager
- **Pros**: Single service, easier deployment
- **Cons**: Manager becomes more complex

### Option 2: Separate Dashboard Service
- **Pros**: Clean separation of concerns, scalable
- **Cons**: Additional service to manage

**Recommendation**: Start with Option 2 (separate service) for better maintainability.

## Technical Stack

- **Backend**: FastAPI or Flask
- **Frontend**: React or Vue.js with Chart.js/D3.js
- **Real-time**: WebSockets or Server-Sent Events
- **Database**: Same Oracle database as ETL system
- **Authentication**: JWT-based (if needed)

## File Structure
```
backend/app/distributed/dashboard/
├── README.md                 # This file
├── __init__.py
├── app.py                   # Main dashboard application
├── api/
│   ├── __init__.py
│   ├── workers.py           # Worker status endpoints
│   ├── stages.py            # Stage progress endpoints
│   ├── queries.py           # SQL query endpoints
│   └── metrics.py           # Performance metrics endpoints
├── templates/
│   ├── index.html           # Main dashboard page
│   ├── workers.html         # Worker management page
│   └── queries.html         # SQL query interface
├── static/
│   ├── css/
│   ├── js/
│   └── charts/              # Chart components
└── utils/
    ├── __init__.py
    ├── database.py          # Database utilities
    ├── llm_query.py         # LLM query assistance
    └── metrics.py           # Metrics calculation
```

## Next Steps

1. **Create basic Flask/FastAPI application**
2. **Implement worker status API endpoints**
3. **Create donut chart components**
4. **Port SQL query functionality**
5. **Add LLM integration for query assistance**
6. **Implement real-time updates**

## Configuration

The dashboard will use the same configuration system as the distributed workers:
- Database connection from `backend.app.core.database`
- Settings from `backend.app.core.config`
- Logging from `backend.app.distributed.logging_config`
