"""
Worker Manager for orchestrating distributed ETL processing.

This module provides comprehensive worker coordination, registration, discovery,
dynamic scaling, and assignment optimization for distributed ETL workers.
"""

import logging
import threading
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Set, Any, Callable, Tuple
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict

from sqlalchemy.orm import Session

from backend.app.distributed.distributed_worker import Di<PERSON>ributedWor<PERSON>, WorkerState
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.worker_health_manager import WorkerHealthManager
from backend.app.distributed.work_queue_manager import WorkQueueManager

logger = logging.getLogger(__name__)


class WorkerManagerState(Enum):
    """Worker manager lifecycle states."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class WorkerAssignment:
    """Represents a worker assignment to processing stages."""
    worker_id: str
    stages: List[ProcessingStage]
    priority: int = 0
    load_factor: float = 1.0  # Multiplier for workload calculation


@dataclass
class ScalingDecision:
    """Represents a scaling decision for workers."""
    stage: ProcessingStage
    current_workers: int
    recommended_workers: int
    reason: str
    urgency: int = 0  # 0=low, 1=medium, 2=high


class WorkerManager:
    """
    Manages multiple distributed workers with orchestration capabilities.
    
    Provides worker registration, discovery, dynamic scaling, assignment optimization,
    and comprehensive monitoring for distributed ETL processing.
    """
    
    def __init__(self, db_session_factory: Callable[[], Session]):
        """
        Initialize the worker manager.
        
        Args:
            db_session_factory: Factory function to create database sessions
        """
        self.db_session_factory = db_session_factory
        
        # Worker management
        self.workers: Dict[str, DistributedWorker] = {}
        self.worker_assignments: Dict[str, WorkerAssignment] = {}
        self.worker_configs: Dict[str, WorkerConfig] = {}
        
        # State management
        self._state = WorkerManagerState.STOPPED
        self._shutdown_event = threading.Event()
        self._management_thread: Optional[threading.Thread] = None
        self._scaling_thread: Optional[threading.Thread] = None
        
        # Configuration
        self.management_interval_seconds = 30
        self.scaling_interval_seconds = 60
        self.stale_worker_threshold_minutes = 5
        self.auto_scaling_enabled = True
        self.max_workers_per_stage = 10
        self.min_workers_per_stage = 1
        
        # Metrics and monitoring
        self._metrics = {
            'workers_started': 0,
            'workers_stopped': 0,
            'workers_failed': 0,
            'scaling_decisions': 0,
            'assignments_optimized': 0
        }
        
        # Health manager for database operations
        self._health_manager: Optional[WorkerHealthManager] = None
        self._work_queue_manager: Optional[WorkQueueManager] = None
        
        logger.info("Initialized WorkerManager")
    
    @property
    def state(self) -> WorkerManagerState:
        """Get current manager state."""
        return self._state
    
    @property
    def is_running(self) -> bool:
        """Check if manager is currently running."""
        return self._state == WorkerManagerState.RUNNING
    
    def start(self) -> None:
        """
        Start the worker manager and its management threads.
        
        Raises:
            RuntimeError: If manager is already running
        """
        if self._state != WorkerManagerState.STOPPED:
            raise RuntimeError(f"Cannot start manager in state {self._state.value}")
        
        logger.info("Starting WorkerManager...")
        self._state = WorkerManagerState.STARTING
        
        try:
            # Initialize database managers
            self._initialize_database_managers()
            
            # Reset shutdown event
            self._shutdown_event.clear()
            
            # Start management thread
            self._management_thread = threading.Thread(
                target=self._management_loop,
                name="worker-manager",
                daemon=False
            )
            self._management_thread.start()
            
            # Start scaling thread if auto-scaling is enabled
            if self.auto_scaling_enabled:
                self._scaling_thread = threading.Thread(
                    target=self._scaling_loop,
                    name="worker-scaler",
                    daemon=False
                )
                self._scaling_thread.start()
            
            self._state = WorkerManagerState.RUNNING
            logger.info("WorkerManager started successfully")
            
        except Exception as e:
            self._state = WorkerManagerState.ERROR
            logger.error(f"Failed to start WorkerManager: {e}")
            raise
    
    def stop(self, timeout: Optional[int] = None) -> None:
        """
        Stop the worker manager and all managed workers.
        
        Args:
            timeout: Maximum time to wait for shutdown
        """
        if self._state in (WorkerManagerState.STOPPED, WorkerManagerState.STOPPING):
            return
        
        timeout = timeout or 300  # 5 minutes default
        logger.info(f"Stopping WorkerManager (timeout: {timeout}s)...")
        
        self._state = WorkerManagerState.STOPPING
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Stop all managed workers
        self.stop_all_workers(timeout=timeout // 2)
        
        # Wait for management threads to finish
        if self._management_thread and self._management_thread.is_alive():
            self._management_thread.join(timeout=30)
        
        if self._scaling_thread and self._scaling_thread.is_alive():
            self._scaling_thread.join(timeout=30)
        
        self._state = WorkerManagerState.STOPPED
        logger.info("WorkerManager stopped")
    
    def register_worker(self, worker: DistributedWorker, assignment: Optional[WorkerAssignment] = None) -> None:
        """
        Register a worker with the manager.
        
        Args:
            worker: Worker instance to register
            assignment: Optional worker assignment configuration
        """
        worker_id = worker.worker_id
        
        if worker_id in self.workers:
            logger.warning(f"Worker {worker_id} already registered, updating...")
        
        self.workers[worker_id] = worker
        self.worker_configs[worker_id] = worker.config
        
        # Create default assignment if none provided
        if assignment is None:
            assignment = WorkerAssignment(
                worker_id=worker_id,
                stages=worker.config.stages,
                priority=0,
                load_factor=1.0
            )
        
        self.worker_assignments[worker_id] = assignment
        
        logger.info(f"Registered worker {worker_id} with stages: {[s.value for s in assignment.stages]}")
    
    def unregister_worker(self, worker_id: str) -> None:
        """
        Unregister a worker from the manager.
        
        Args:
            worker_id: Worker identifier to unregister
        """
        if worker_id not in self.workers:
            logger.warning(f"Attempted to unregister non-existent worker: {worker_id}")
            return
        
        # Stop the worker if it's running
        worker = self.workers[worker_id]
        if worker.is_running:
            try:
                worker.stop()
            except Exception as e:
                logger.error(f"Error stopping worker {worker_id} during unregistration: {e}")
        
        # Remove from tracking
        del self.workers[worker_id]
        self.worker_assignments.pop(worker_id, None)
        self.worker_configs.pop(worker_id, None)
        
        logger.info(f"Unregistered worker {worker_id}")
    
    def start_worker(self, worker_id: str) -> bool:
        """
        Start a specific worker.
        
        Args:
            worker_id: Worker identifier to start
            
        Returns:
            True if worker was started successfully, False otherwise
        """
        if worker_id not in self.workers:
            logger.error(f"Cannot start unknown worker: {worker_id}")
            return False
        
        worker = self.workers[worker_id]
        
        try:
            if not worker.is_running:
                worker.start()
                self._metrics['workers_started'] += 1
                logger.info(f"Started worker {worker_id}")
                return True
            else:
                logger.warning(f"Worker {worker_id} is already running")
                return True
                
        except Exception as e:
            logger.error(f"Failed to start worker {worker_id}: {e}")
            self._metrics['workers_failed'] += 1
            return False
    
    def stop_worker(self, worker_id: str, timeout: Optional[int] = None) -> bool:
        """
        Stop a specific worker.
        
        Args:
            worker_id: Worker identifier to stop
            timeout: Maximum time to wait for shutdown
            
        Returns:
            True if worker was stopped successfully, False otherwise
        """
        if worker_id not in self.workers:
            logger.error(f"Cannot stop unknown worker: {worker_id}")
            return False
        
        worker = self.workers[worker_id]
        
        try:
            if worker.is_running:
                worker.stop(timeout)
                self._metrics['workers_stopped'] += 1
                logger.info(f"Stopped worker {worker_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop worker {worker_id}: {e}")
            return False
    
    def start_all_workers(self) -> Dict[str, bool]:
        """
        Start all registered workers.
        
        Returns:
            Dictionary mapping worker IDs to success status
        """
        logger.info(f"Starting {len(self.workers)} workers...")
        
        results = {}
        for worker_id in self.workers:
            results[worker_id] = self.start_worker(worker_id)
        
        successful = sum(1 for success in results.values() if success)
        logger.info(f"Started {successful}/{len(self.workers)} workers successfully")
        
        return results
    
    def stop_all_workers(self, timeout: Optional[int] = None) -> Dict[str, bool]:
        """
        Stop all managed workers.
        
        Args:
            timeout: Maximum time to wait for each worker to stop
            
        Returns:
            Dictionary mapping worker IDs to success status
        """
        logger.info(f"Stopping {len(self.workers)} workers...")
        
        results = {}
        for worker_id in self.workers:
            results[worker_id] = self.stop_worker(worker_id, timeout)
        
        successful = sum(1 for success in results.values() if success)
        logger.info(f"Stopped {successful}/{len(self.workers)} workers successfully")
        
        return results
    
    def get_worker_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status of all managed workers.
        
        Returns:
            Dictionary mapping worker IDs to their status information
        """
        status = {}
        
        for worker_id, worker in self.workers.items():
            try:
                worker_status = worker.get_health_status()
                
                # Add assignment information
                assignment = self.worker_assignments.get(worker_id)
                if assignment:
                    worker_status['assignment'] = {
                        'stages': [s.value for s in assignment.stages],
                        'priority': assignment.priority,
                        'load_factor': assignment.load_factor
                    }
                
                status[worker_id] = worker_status
                
            except Exception as e:
                logger.error(f"Failed to get status for worker {worker_id}: {e}")
                status[worker_id] = {
                    'worker_id': worker_id,
                    'error': str(e),
                    'healthy': False
                }
        
        return status
    
    def get_running_workers(self) -> List[DistributedWorker]:
        """Get list of currently running workers."""
        return [worker for worker in self.workers.values() if worker.is_running]
    
    def get_workers_by_stage(self, stage: ProcessingStage) -> List[DistributedWorker]:
        """
        Get workers that can process a specific stage.
        
        Args:
            stage: Processing stage to filter by
            
        Returns:
            List of workers that can handle the stage
        """
        stage_workers = []
        
        for worker_id, assignment in self.worker_assignments.items():
            if stage in assignment.stages:
                worker = self.workers.get(worker_id)
                if worker and worker.is_running:
                    stage_workers.append(worker)
        
        return stage_workers
    
    def optimize_worker_assignments(self) -> Dict[str, List[ProcessingStage]]:
        """
        Optimize worker assignments based on current workload and performance.
        
        Returns:
            Dictionary mapping worker IDs to their optimized stage assignments
        """
        try:
            # Get current queue metrics for all stages
            db_session = self.db_session_factory()
            work_queue = WorkQueueManager(db_session)
            queue_metrics = work_queue.get_comprehensive_metrics()
            
            # Get worker performance metrics
            worker_status = self.get_worker_status()
            
            # Calculate optimal assignments
            optimized_assignments = self._calculate_optimal_assignments(
                queue_metrics, worker_status
            )
            
            # Update assignments if they've changed
            changes_made = False
            for worker_id, new_stages in optimized_assignments.items():
                current_assignment = self.worker_assignments.get(worker_id)
                if current_assignment and set(current_assignment.stages) != set(new_stages):
                    current_assignment.stages = new_stages
                    changes_made = True
                    logger.info(f"Optimized assignment for worker {worker_id}: {[s.value for s in new_stages]}")
            
            if changes_made:
                self._metrics['assignments_optimized'] += 1
            
            return optimized_assignments
            
        except Exception as e:
            logger.error(f"Failed to optimize worker assignments: {e}")
            return {}
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def analyze_scaling_needs(self) -> List[ScalingDecision]:
        """
        Analyze current workload and determine scaling needs.
        
        Returns:
            List of scaling decisions for different stages
        """
        scaling_decisions = []
        
        try:
            # Get current queue metrics
            db_session = self.db_session_factory()
            work_queue = WorkQueueManager(db_session)
            queue_metrics = work_queue.get_comprehensive_metrics()
            
            # Analyze each stage
            for stage in ProcessingStage:
                stage_metrics = queue_metrics.get(stage.value, {})
                current_workers = len(self.get_workers_by_stage(stage))
                
                decision = self._analyze_stage_scaling(stage, stage_metrics, current_workers)
                if decision:
                    scaling_decisions.append(decision)
            
            return scaling_decisions
            
        except Exception as e:
            logger.error(f"Failed to analyze scaling needs: {e}")
            return []
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def get_manager_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive metrics about the worker manager.
        
        Returns:
            Dictionary with manager metrics and statistics
        """
        running_workers = len(self.get_running_workers())
        total_workers = len(self.workers)
        
        # Calculate stage distribution
        stage_distribution = defaultdict(int)
        for assignment in self.worker_assignments.values():
            for stage in assignment.stages:
                stage_distribution[stage.value] += 1
        
        # Get worker health summary
        worker_status = self.get_worker_status()
        healthy_workers = sum(1 for status in worker_status.values() if status.get('healthy', False))
        
        return {
            'manager_state': self._state.value,
            'total_workers': total_workers,
            'running_workers': running_workers,
            'healthy_workers': healthy_workers,
            'stage_distribution': dict(stage_distribution),
            'auto_scaling_enabled': self.auto_scaling_enabled,
            'metrics': self._metrics.copy(),
            'configuration': {
                'management_interval_seconds': self.management_interval_seconds,
                'scaling_interval_seconds': self.scaling_interval_seconds,
                'stale_worker_threshold_minutes': self.stale_worker_threshold_minutes,
                'max_workers_per_stage': self.max_workers_per_stage,
                'min_workers_per_stage': self.min_workers_per_stage
            }
        }
    
    def cleanup_failed_workers(self) -> int:
        """
        Remove workers that are in error state and cannot be recovered.
        
        Returns:
            Number of workers that were cleaned up
        """
        failed_workers = []
        
        for worker_id, worker in self.workers.items():
            if worker.state == WorkerState.ERROR:
                failed_workers.append(worker_id)
        
        for worker_id in failed_workers:
            logger.warning(f"Cleaning up failed worker {worker_id}")
            self.unregister_worker(worker_id)
        
        if failed_workers:
            self._metrics['workers_failed'] += len(failed_workers)
        
        return len(failed_workers)
    
    def _initialize_database_managers(self) -> None:
        """Initialize database manager instances."""
        try:
            db_session = self.db_session_factory()
            self._health_manager = WorkerHealthManager(db_session)
            self._work_queue_manager = WorkQueueManager(db_session)
        except Exception as e:
            logger.error(f"Failed to initialize database managers: {e}")
            raise
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def _management_loop(self) -> None:
        """Main management loop for worker coordination."""
        logger.info("Starting worker management loop")
        
        while not self._shutdown_event.is_set():
            try:
                # Perform management tasks
                self._perform_management_tasks()
                
                # Wait for next iteration
                self._shutdown_event.wait(self.management_interval_seconds)
                
            except Exception as e:
                logger.error(f"Error in management loop: {e}", exc_info=True)
                self._shutdown_event.wait(5.0)  # Wait before retry
        
        logger.info("Worker management loop stopped")
    
    def _scaling_loop(self) -> None:
        """Auto-scaling loop for dynamic worker management."""
        logger.info("Starting auto-scaling loop")
        
        while not self._shutdown_event.is_set():
            try:
                # Analyze scaling needs
                scaling_decisions = self.analyze_scaling_needs()
                
                # Apply scaling decisions
                for decision in scaling_decisions:
                    if decision.urgency >= 1:  # Only act on medium/high urgency
                        self._apply_scaling_decision(decision)
                
                # Wait for next iteration
                self._shutdown_event.wait(self.scaling_interval_seconds)
                
            except Exception as e:
                logger.error(f"Error in scaling loop: {e}", exc_info=True)
                self._shutdown_event.wait(10.0)  # Wait before retry
        
        logger.info("Auto-scaling loop stopped")
    
    def _perform_management_tasks(self) -> None:
        """Perform routine management tasks."""
        # Clean up failed workers
        cleaned_up = self.cleanup_failed_workers()
        if cleaned_up > 0:
            logger.info(f"Cleaned up {cleaned_up} failed workers")

        # Clean up old error workers from database
        self._cleanup_old_error_workers()

        # Optimize worker assignments periodically
        if len(self.workers) > 1:
            self.optimize_worker_assignments()

        # Check for stale workers and attempt recovery
        self._check_and_recover_stale_workers()
    
    def _check_and_recover_stale_workers(self) -> None:
        """Check for stale workers and attempt recovery."""
        if not self._health_manager:
            return
        
        try:
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            
            # Detect and recover from failed workers
            recovery_stats = health_manager.recover_failed_workers(
                stale_threshold_minutes=self.stale_worker_threshold_minutes
            )
            
            if recovery_stats['stale_workers_detected'] > 0:
                logger.warning(
                    f"Recovered from {recovery_stats['stale_workers_detected']} stale workers, "
                    f"released {recovery_stats['entries_released']} entries"
                )
            
        except Exception as e:
            logger.error(f"Failed to check and recover stale workers: {e}")
        finally:
            if 'db_session' in locals():
                db_session.close()

    def _cleanup_old_error_workers(self) -> None:
        """Clean up old error workers from database."""
        try:
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)

            # Clean up error workers older than 24 hours
            cleanup_stats = health_manager.cleanup_old_error_workers(error_age_hours=24)

            if cleanup_stats['workers_removed'] > 0:
                logger.info(
                    f"Cleaned up {cleanup_stats['workers_removed']} old error workers "
                    f"(found {cleanup_stats['old_error_workers_found']} candidates)"
                )

        except Exception as e:
            logger.error(f"Failed to cleanup old error workers: {e}")
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def _calculate_optimal_assignments(self, 
                                     queue_metrics: Dict[str, Dict[str, int]],
                                     worker_status: Dict[str, Dict[str, Any]]) -> Dict[str, List[ProcessingStage]]:
        """
        Calculate optimal worker assignments based on current metrics.
        
        Args:
            queue_metrics: Current queue metrics for all stages
            worker_status: Current status of all workers
            
        Returns:
            Dictionary mapping worker IDs to optimal stage assignments
        """
        assignments = {}
        
        # Simple assignment strategy: assign workers to stages with highest pending work
        stage_workloads = []
        for stage_name, metrics in queue_metrics.items():
            try:
                stage = ProcessingStage(stage_name)
                pending_work = metrics.get('pending', 0) + metrics.get('failed', 0)
                stage_workloads.append((stage, pending_work))
            except ValueError:
                continue  # Skip unknown stages
        
        # Sort stages by workload (highest first)
        stage_workloads.sort(key=lambda x: x[1], reverse=True)
        
        # Assign workers to stages based on their current performance
        for worker_id in self.workers:
            if worker_id not in worker_status:
                continue
            
            status = worker_status[worker_id]
            if not status.get('healthy', False):
                continue
            
            # Get current assignment
            current_assignment = self.worker_assignments.get(worker_id)
            if not current_assignment:
                continue
            
            # For now, keep current assignments but could optimize based on performance
            assignments[worker_id] = current_assignment.stages
        
        return assignments
    
    def _analyze_stage_scaling(self, 
                              stage: ProcessingStage,
                              stage_metrics: Dict[str, int],
                              current_workers: int) -> Optional[ScalingDecision]:
        """
        Analyze scaling needs for a specific stage.
        
        Args:
            stage: Processing stage to analyze
            stage_metrics: Current metrics for the stage
            current_workers: Number of workers currently assigned to the stage
            
        Returns:
            Scaling decision if scaling is needed, None otherwise
        """
        pending_work = stage_metrics.get('pending', 0)
        failed_work = stage_metrics.get('failed', 0)
        in_progress = stage_metrics.get('in_progress', 0)
        
        total_work = pending_work + failed_work
        
        # Simple scaling logic
        if total_work == 0:
            # No work, consider scaling down
            if current_workers > self.min_workers_per_stage:
                return ScalingDecision(
                    stage=stage,
                    current_workers=current_workers,
                    recommended_workers=max(self.min_workers_per_stage, current_workers - 1),
                    reason="No pending work",
                    urgency=0
                )
        elif total_work > current_workers * 50:  # Arbitrary threshold
            # High workload, consider scaling up
            if current_workers < self.max_workers_per_stage:
                urgency = 2 if total_work > current_workers * 100 else 1
                return ScalingDecision(
                    stage=stage,
                    current_workers=current_workers,
                    recommended_workers=min(self.max_workers_per_stage, current_workers + 1),
                    reason=f"High workload: {total_work} pending items",
                    urgency=urgency
                )
        
        return None
    
    def _apply_scaling_decision(self, decision: ScalingDecision) -> None:
        """
        Apply a scaling decision by starting or stopping workers.
        
        Args:
            decision: Scaling decision to apply
        """
        logger.info(f"Applying scaling decision for {decision.stage.value}: "
                   f"{decision.current_workers} -> {decision.recommended_workers} workers "
                   f"({decision.reason})")
        
        self._metrics['scaling_decisions'] += 1
        
        # For now, just log the decision
        # In a full implementation, this would create/destroy workers
        # or reassign existing workers to different stages