"""
Distributed ETL Dashboard Application

FastAPI-based web dashboard for monitoring and managing the distributed ETL system.
Provides the same functionality as the monolithic news_aggregator dashboard with
additional distributed-specific features.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
import json
import os
import requests

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from starlette.requests import Request
from sqlalchemy.orm import Session
from sqlalchemy import text
from jsonschema import validate, ValidationError
import uvicorn

# Import distributed ETL components
from backend.app.core.database import SessionLocal
from backend.app.models.models import Entry, Worker
from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus
from backend.app.distributed.logging_config import setup_distributed_logging

# Setup logging
logger = setup_distributed_logging(log_level="INFO", worker_id="dashboard")


def get_optimized_stage_progress(db: Session, stage: ProcessingStage, cutoff_date: datetime) -> Dict[str, int]:
    """
    Get stage progress using optimized queries based on field types.

    This function implements field-type-aware optimization:
    - BLOB fields: Use separate queries to avoid slow conditional counting
    - Indexed fields: Use optimized separate queries
    - Simple fields: Use conditional counting
    """
    from backend.app.distributed.processing_stage import get_stage_selection_criteria, resolve_filter_values
    from backend.app.core.config import settings
    from sqlalchemy import and_, func, case

    criteria = get_stage_selection_criteria(stage)

    # Build base query with same logic as existing code
    query = db.query(Entry)

    # Apply mandatory fields (must not be None)
    if criteria.mandatory_fields:
        for field in criteria.mandatory_fields:
            if hasattr(Entry, field):
                query = query.filter(getattr(Entry, field).isnot(None))

    # Apply filters with resolved values
    resolved_filters = resolve_filter_values(criteria.filters, settings)
    has_published_filter = False

    for field, operator, value in resolved_filters:
        if hasattr(Entry, field):
            col = getattr(Entry, field)
            if field == 'published':
                has_published_filter = True

            if operator == '>=':
                query = query.filter(col >= value)
            elif operator == '<':
                query = query.filter(col < value)
            elif operator == '==':
                query = query.filter(col == value)

    # Apply default time filter if no published filter provided
    if not has_published_filter:
        lookback_days = settings.NEWS_ETL_LOOKBACK_DAYS
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=lookback_days)
        query = query.filter(Entry.published >= cutoff_date)

    stage_counts = {
        'pending': 0,
        'in_progress': 0,
        'completed': 0,
        'failed': 0
    }

    # Optimized counting strategy based on field types
    if criteria.analysis_fields:
        # Build completion condition: ALL analysis fields must be NOT NULL
        completion_conditions = []

        # Categorize fields by performance characteristics
        blob_fields = {'embedding', 'preview_img'}  # Oracle BLOB fields (very slow)
        slow_text_fields = {'image_prompt', 'llm_reason_list'}  # Large CLOB fields (slow)
        indexed_fields = {'iptc_newscode', 'dup_entry_id'}  # Fields with specific indices

        field_categories = {
            'blob': [f for f in criteria.analysis_fields if f in blob_fields],
            'slow_text': [f for f in criteria.analysis_fields if f in slow_text_fields],
            'indexed': [f for f in criteria.analysis_fields if f in indexed_fields],
            'simple': [f for f in criteria.analysis_fields if f not in blob_fields and f not in slow_text_fields and f not in indexed_fields]
        }

        # Build completion conditions
        for field in criteria.analysis_fields:
            if hasattr(Entry, field):
                col = getattr(Entry, field)
                completion_conditions.append(col.isnot(None))

        if completion_conditions:
            completion_condition = and_(*completion_conditions)

            # Choose optimization strategy based on field types present
            if field_categories['blob']:
                # Strategy 1: BLOB fields present - use separate queries (most reliable)
                logger.debug(f"Using separate queries for stage {stage.value} (contains BLOB fields: {field_categories['blob']})")
                stage_counts['completed'] = query.filter(completion_condition).count()
                stage_counts['pending'] = query.filter(~completion_condition).count()

            elif field_categories['slow_text'] or field_categories['indexed']:
                # Strategy 2: Slow text or indexed fields - use separate optimized queries
                logger.debug(f"Using optimized separate queries for stage {stage.value}")
                stage_counts['completed'] = query.filter(completion_condition).count()
                stage_counts['pending'] = query.filter(~completion_condition).count()

            else:
                # Strategy 3: Simple fields only - use conditional counting (fastest)
                logger.debug(f"Using conditional counting for stage {stage.value} (simple fields only)")
                result = query.with_entities(
                    func.count(case((completion_condition, 1))).label('completed'),
                    func.count(case((~completion_condition, 1))).label('pending')
                ).first()

                stage_counts['completed'] = result.completed or 0
                stage_counts['pending'] = result.pending or 0
        else:
            # No valid analysis fields, count all as pending
            stage_counts['pending'] = query.count()
    else:
        # No analysis fields defined, count all as completed
        stage_counts['completed'] = query.count()

    # Calculate total
    total = stage_counts['pending'] + stage_counts['in_progress'] + stage_counts['completed']

    return {
        **stage_counts,
        "total": total
    }

# Create FastAPI app
app = FastAPI(
    title="Distributed ETL Dashboard",
    description="Web-based monitoring and management interface for distributed ETL system",
    version="1.0.0"
)

# Setup templates and static files
templates = Jinja2Templates(directory="backend/app/distributed/dashboard/templates")
app.mount("/static", StaticFiles(directory="backend/app/distributed/dashboard/static"), name="static")

# WebSocket connection manager for real-time updates
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except:
                # Remove disconnected clients
                self.active_connections.remove(connection)

manager = ConnectionManager()

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Routes
@app.get("/", response_class=HTMLResponse)
async def dashboard_home(request: Request):
    """Main dashboard page with stage progress charts."""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/workers", response_class=HTMLResponse)
async def workers_page(request: Request):
    """Worker management page."""
    return templates.TemplateResponse("workers.html", {"request": request})

@app.get("/queries", response_class=HTMLResponse)
async def queries_page(request: Request):
    """SQL query interface page."""
    return templates.TemplateResponse("queries.html", {"request": request})

# API Endpoints
@app.get("/api/workers/status")
async def get_workers_status(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Get current status of all workers."""
    try:
        workers = db.query(Worker).all()
        
        worker_stats = {
            "total": len(workers),
            "running": 0,
            "stopped": 0,
            "error": 0,
            "workers": []
        }
        
        for worker in workers:
            status = worker.status or "unknown"
            if status == "running":
                worker_stats["running"] += 1
            elif status == "stopped":
                worker_stats["stopped"] += 1
            elif status == "error":
                worker_stats["error"] += 1
            
            # Parse stages
            stages = []
            if worker.stages:
                try:
                    if isinstance(worker.stages, str):
                        stages = json.loads(worker.stages)
                    else:
                        stages = worker.stages
                except:
                    stages = []
            
            # Safely serialize worker data
            worker_data = {
                "worker_id": worker.worker_id,
                "status": status,
                "stages": stages,
                "last_heartbeat": worker.last_heartbeat.isoformat() if worker.last_heartbeat else None,
                "started_at": worker.started_at.isoformat() if worker.started_at else None,
                "progress": worker.progress,  # JSON string or None
                "config": worker.config,      # JSON string or None
                "error_count": worker.error_count or 0,
                "last_error": worker.last_error,  # CLOB field or None
                "batches_processed": worker.batches_processed or 0,
                "entries_processed": worker.entries_processed or 0,
                "entries_failed": worker.entries_failed or 0,
                "total_processing_time": worker.total_processing_time or 0.0,
                "current_batch_size": worker.current_batch_size or 0,
                "processing_since": worker.processing_since.isoformat() if worker.processing_since else None,
                "hostname": worker.hostname,
                "process_id": worker.process_id
            }

            worker_stats["workers"].append(worker_data)
        
        return worker_stats
        
    except Exception as e:
        logger.error(f"Error getting worker status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stages/progress")
async def get_stages_progress(db: Session = Depends(get_db), force_direct: bool = False) -> Dict[str, Any]:
    """Get processing progress for all stages from worker progress reports."""
    try:
        from backend.app.distributed.progress_reporter import aggregate_worker_progress
        from backend.app.distributed.processing_stage import ProcessingStage

        # Get aggregated progress from all active workers
        aggregated_progress = aggregate_worker_progress(db)

        # Initialize stage statistics with zeros
        stage_stats = {}
        for stage in ProcessingStage:
            stage_stats[stage.value] = {
                "pending": 0,
                "in_progress": 0,
                "completed": 0,
                "failed": 0,
                "total": 0
            }

        # Update with actual progress data from workers
        for stage_name, progress_data in aggregated_progress.items():
            if stage_name in stage_stats:
                stage_stats[stage_name] = progress_data

        # Use worker reports when available, fall back to optimized direct calculation
        # This balances accuracy with performance
        if not aggregated_progress or force_direct:
            logger.info(f"Using direct calculation (force_direct={force_direct}, worker_reports={bool(aggregated_progress)})")

            # Use the same logic as CLI queue-status for consistency
            from backend.app.distributed.processing_stage import ProcessingStatus

            # Handle download_feeds specially
            stage_stats[ProcessingStage.DOWNLOAD_FEEDS.value] = {
                "pending": 37,  # Number of RSS sources
                "in_progress": 0,
                "completed": 0,
                "failed": 0,
                "total": 37
            }

            # For other stages, use proper stage selection criteria
            from backend.app.distributed.processing_stage import get_stage_selection_criteria, resolve_filter_values
            from backend.app.core.config import settings
            from datetime import timedelta
            from sqlalchemy import or_

            # For direct calculation, only process fast stages to avoid timeouts
            if force_direct:
                fast_stages = {
                    ProcessingStage.DOWNLOAD_FULL_TEXT,
                    ProcessingStage.LLM_ANALYSIS,
                    ProcessingStage.DUPLICATE_CHECK,
                    ProcessingStage.IPTC_CLASSIFICATION
                }
                entry_processing_stages = fast_stages
                logger.info(f"Direct calculation: processing only fast stages: {[s.value for s in fast_stages]}")
            else:
                entry_processing_stages = {s for s in ProcessingStage if s != ProcessingStage.DOWNLOAD_FEEDS}

            for stage in entry_processing_stages:
                try:
                    import time
                    stage_start_time = time.time()
                    logger.info(f"Processing stage {stage.value}...")

                    # Use optimized progress calculation
                    stage_counts = get_optimized_stage_progress(db, stage, cutoff_date)
                    stage_stats[stage.value] = stage_counts

                    stage_duration = time.time() - stage_start_time
                    logger.info(f"Stage {stage.value} completed in {stage_duration:.2f}s")

                except Exception as e:
                    stage_duration = time.time() - stage_start_time
                    logger.error(f"Error calculating stats for stage {stage.value} after {stage_duration:.2f}s: {e}")
                    continue

        return {
            "stages": stage_stats,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "source": "direct_calculation" if (force_direct or not aggregated_progress) else "worker_reports"
        }

    except Exception as e:
        logger.error(f"Error getting stage progress: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates."""
    await manager.connect(websocket)
    try:
        while True:
            # Send periodic updates
            await websocket.receive_text()  # Keep connection alive
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Health check
@app.post("/api/execute_query")
async def execute_query(request: dict, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Execute a SQL query against the Oracle database."""
    try:
        query = request.get('query')
        if not query:
            raise HTTPException(status_code=400, detail="No query provided")

        # Execute the query
        result = db.execute(text(query))

        # Fetch results
        rows = result.fetchall()
        columns = list(result.keys()) if rows else []

        # Convert rows to list of lists for JSON serialization
        rows_data = []
        for row in rows:
            row_data = []
            for value in row:
                if isinstance(value, datetime):
                    row_data.append(value.isoformat())
                else:
                    row_data.append(value)
            rows_data.append(row_data)

        return {
            "columns": columns,
            "rows": rows_data,
            "row_count": len(rows_data)
        }

    except Exception as e:
        logger.error(f"Error executing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate_query")
async def generate_query(request: dict) -> Dict[str, Any]:
    """Generate SQL query using LLM based on natural language description."""
    try:
        description = request.get('description')
        if not description:
            raise HTTPException(status_code=400, detail="No description provided")

        # LLM configuration (same as monolithic system)
        LLM_URL = "http://127.0.0.1:1234/v1/chat/completions"
        LLM_PROMPT = (
            "You are an expert SQL query generator for an Oracle database. "
            "Generate a SQL query based on the user's description. "
            "The database contains news articles with sentiment analysis in the ENTRIES table. "
            "Here's the structure of the main table:\n"
            "ENTRIES: entry_id, title, link, source, description, published, full_text, english_text, "
            "embedding, vader_pos, vader_neu, vader_neg, vader_compound, "
            "german_sentiment_positive, german_sentiment_neutral, german_sentiment_negative, "
            "tbd_polarity, tbd_subjectivity, llm_positive, llm_neutral, llm_negative, "
            "llm_positive_std, llm_neutral_std, llm_negative_std, llm_positive_diff, llm_neutral_diff, "
            "llm_negative_diff, llm_reason_list, llm_iterations, llm_break_reason, llm_is_ad, "
            "llm_is_ad_reason, iptc_newscode, iptc_score, dup_entry_id, dup_entry_conf\n\n"
            "SOURCES: source, name\n\n"
            "CATEGORIES: iptc_newscode, category\n\n"
            "Provide only the SQL query without any explanation or additional text."
        )

        LLM_SCHEMA = {
            "$schema": "https://json-schema.org/draft/2020-12/schema",
            "title": "SQLQueryResponse",
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "The SQL query that fulfills the user's request."
                }
            },
            "required": ["query"]
        }

        try:
            response = requests.post(LLM_URL, json={
                "messages": [
                    {"role": "system", "content": LLM_PROMPT},
                    {"role": "user", "content": description}
                ],
                "model": "qwen2.5-7b-instruct-1m@q4_k_m",
                "max_tokens": 500,
                "temperature": 0.1,
                "top_p": 0.95,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"strict": "true", "schema": LLM_SCHEMA}
                },
            })
            response.raise_for_status()
            response_dict = response.json()
            message_content = response_dict['choices'][0]['message']['content']

            try:
                data = json.loads(message_content)
                validate(instance=data, schema=LLM_SCHEMA)
                return {"query": data["query"]}
            except json.JSONDecodeError as e:
                logger.error(f"Invalid response content: {e}")
                if "SELECT" in message_content:
                    return {"query": message_content.strip()}
                raise HTTPException(status_code=500, detail="Failed to parse LLM response")
            except ValidationError as e:
                logger.error(f"Invalid response schema: {e}")
                raise HTTPException(status_code=500, detail="LLM response did not match expected format")

        except requests.RequestException as e:
            logger.error(f"LLM request failed: {e}")
            raise HTTPException(status_code=503, detail="LLM service unavailable")

    except Exception as e:
        logger.error(f"Error generating query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/saved_queries")
async def get_saved_queries() -> Dict[str, str]:
    """Get all saved SQL queries from both monolithic and distributed systems."""
    try:
        queries = {}

        # Load from monolithic system queries directory
        # Navigate from backend/app/distributed/dashboard/app.py to etl/queries
        current_dir = os.path.dirname(__file__)  # backend/app/distributed/dashboard
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_dir))))  # project root
        monolithic_queries_dir = os.path.join(project_root, 'etl', 'queries')

        if os.path.exists(monolithic_queries_dir):
            for filename in os.listdir(monolithic_queries_dir):
                if filename.endswith('.sql'):
                    name = filename[:-4]  # Remove .sql extension
                    file_path = os.path.join(monolithic_queries_dir, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            queries[name] = f.read().strip()
                    except Exception as e:
                        logger.warning(f"Error reading query file {filename}: {e}")

        # Load from distributed system queries directory
        distributed_queries_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'queries')
        if os.path.exists(distributed_queries_dir):
            for filename in os.listdir(distributed_queries_dir):
                if filename.endswith('.sql'):
                    name = f"distributed_{filename[:-4]}"  # Prefix to avoid conflicts
                    file_path = os.path.join(distributed_queries_dir, filename)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            queries[name] = f.read().strip()
                    except Exception as e:
                        logger.warning(f"Error reading query file {filename}: {e}")

        return queries

    except Exception as e:
        logger.error(f"Error loading saved queries: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/save_query")
async def save_query(request: dict) -> Dict[str, Any]:
    """Save a SQL query to a file."""
    try:
        name = request.get('name')
        query = request.get('query')

        if not name or not query:
            raise HTTPException(status_code=400, detail="Name and query are required")

        # Create queries directory if it doesn't exist
        queries_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'queries')
        if not os.path.exists(queries_dir):
            os.makedirs(queries_dir)

        file_path = os.path.join(queries_dir, f"{name}.sql")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(query)

        return {"success": True}

    except Exception as e:
        logger.error(f"Error saving query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/workers/clear_errors")
async def clear_error_workers(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Clear all workers in error status from the database."""
    try:
        from backend.app.models.models import Worker

        # Count error workers before deletion
        error_count = db.query(Worker).filter(Worker.status == 'error').count()

        # Delete error workers
        db.query(Worker).filter(Worker.status == 'error').delete()
        db.commit()

        return {
            "success": True,
            "cleared_count": error_count
        }

    except Exception as e:
        logger.error(f"Error clearing error workers: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/entry_details/{entry_id}")
async def entry_details(entry_id: str, request: Request, db: Session = Depends(get_db)):
    """Show detailed information for a specific entry."""
    try:
        from backend.app.models.models import Entry

        entry = db.query(Entry).filter(Entry.entry_id == entry_id).first()
        if not entry:
            raise HTTPException(status_code=404, detail="Entry not found")

        # Convert entry to dict for template
        entry_data = {}
        for column in Entry.__table__.columns:
            value = getattr(entry, column.name)
            if column.name == 'embedding' and value:
                # Show hex representation of first few bytes
                entry_data[column.name] = value[:20].hex() + '...' if len(value) > 20 else value.hex()
            elif column.name == 'preview_img' and value:
                # Convert to base64 for display
                import base64
                entry_data[column.name] = base64.b64encode(value).decode('utf-8')
            else:
                entry_data[column.name] = value

        return templates.TemplateResponse("entry_details.html", {
            "request": request,
            "entry": entry_data
        })

    except Exception as e:
        logger.error(f"Error getting entry details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/duplicate_details/{dup_entry_id}")
async def duplicate_details(dup_entry_id: str, request: Request, db: Session = Depends(get_db)):
    """Show all entries with the same dup_entry_id."""
    try:
        from backend.app.models.models import Entry

        entries = db.query(Entry).filter(Entry.dup_entry_id == dup_entry_id).all()
        if not entries:
            raise HTTPException(status_code=404, detail="No entries found with this dup_entry_id")

        # Convert entries to list of dicts
        entries_data = []
        for entry in entries:
            entry_dict = {
                'entry_id': entry.entry_id,
                'title': entry.title,
                'source': entry.source,
                'published': entry.published,
                'dup_entry_conf': entry.dup_entry_conf
            }
            entries_data.append(entry_dict)

        return templates.TemplateResponse("duplicate_details.html", {
            "request": request,
            "dup_entry_id": dup_entry_id,
            "entries": entries_data
        })

    except Exception as e:
        logger.error(f"Error getting duplicate details: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}

if __name__ == "__main__":
    # Run the dashboard server
    uvicorn.run(
        "backend.app.distributed.dashboard.app:app",
        host="0.0.0.0",
        port=8081,
        reload=True,
        log_level="info"
    )
