"""
Advanced tests for NewsAggregator
Covers error handling, edge cases, performance considerations, and configuration validation
"""

import unittest
from unittest.mock import Mock, patch, mock_open, MagicMock
import os
import sys
import tempfile
import threading
import time
from datetime import datetime, timezone, timedelta
import pandas as pd
import numpy as np

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)

# Set up test environment before importing news_aggregator
from test_config import setup_test_environment
setup_test_environment()


class TestConfigurationValidation(unittest.TestCase):
    """Test configuration validation and error handling"""

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_invalid_database_url(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test handling of invalid database URL"""
        mock_settings.DATABASE_URL = "invalid://database/url"
        mock_create_engine.side_effect = Exception("Invalid database URL")
        
        try:
            from news_aggregator import NewsAggregator
            
            with self.assertRaises(Exception):
                NewsAggregator(clear_last_entries=0, recreate_table=False)
                
        except ImportError:
            self.skipTest("NewsAggregator import failed")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    @patch('builtins.open', side_effect=FileNotFoundError("RSS sources file not found"))
    def test_missing_rss_sources_file(self, mock_file, mock_scoped_session, mock_create_engine, mock_settings):
        """Test handling of missing RSS sources configuration file"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        mock_settings.NEWS_RSS_SOURCES_FILE = "nonexistent.yaml"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            with self.assertRaises(FileNotFoundError):
                aggregator._load_rss_sources()
                
        except ImportError:
            self.skipTest("NewsAggregator import failed")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    @patch('builtins.open', new_callable=mock_open, read_data="invalid: yaml: content: [")
    def test_invalid_yaml_configuration(self, mock_file, mock_scoped_session, mock_create_engine, mock_settings):
        """Test handling of invalid YAML configuration"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        mock_settings.NEWS_RSS_SOURCES_FILE = "invalid.yaml"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            with self.assertRaises(Exception):  # YAML parsing error
                aggregator._load_rss_sources()
                
        except ImportError:
            self.skipTest("NewsAggregator import failed")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    @patch('builtins.open', new_callable=mock_open, read_data="""
rss_sources:
  - url: "https://example.com/rss"
    # Missing entry_id_from field
""")
    def test_incomplete_rss_configuration(self, mock_file, mock_scoped_session, mock_create_engine, mock_settings):
        """Test handling of incomplete RSS source configuration"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        mock_settings.NEWS_RSS_SOURCES_FILE = "incomplete.yaml"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            sources = aggregator._load_rss_sources()
            
            # Should load but may cause issues later
            self.assertEqual(len(sources), 1)
            self.assertNotIn('entry_id_from', sources[0])
            
        except ImportError:
            self.skipTest("NewsAggregator import failed")


class TestErrorHandling(unittest.TestCase):
    """Test error handling and recovery mechanisms"""

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_entry_id_generation_with_missing_fields(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test entry ID generation when required fields are missing"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Create a mock entry that doesn't have the 'link' attribute
            mock_entry = Mock(spec=['title'])  # Only has title, not link
            mock_entry.title = "Test Article"
            
            source_config = {'entry_id_from': 'title,link'}
            
            # This should raise AttributeError when trying to access the missing 'link' attribute
            with self.assertRaises(AttributeError):
                aggregator._generate_entry_id(mock_entry, source_config)
                
        except ImportError:
            self.skipTest("NewsAggregator import failed")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_html_cleaning_with_malformed_html(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test HTML cleaning with malformed HTML content"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Test with malformed HTML
            malformed_html = "<p>Unclosed paragraph<div>Nested without closing<span>More nesting"
            cleaned = aggregator._clean_html_content(malformed_html)
            
            # Should still clean successfully
            self.assertNotIn('<', cleaned)
            self.assertIn('Unclosed paragraph', cleaned)
            self.assertIn('Nested without closing', cleaned)
            
        except ImportError:
            self.skipTest("NewsAggregator import failed")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_session_scope_database_connection_error(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test session scope handling of database connection errors"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        # Mock session that fails on operations
        mock_session = Mock()
        mock_session.commit.side_effect = Exception("Database connection lost")
        mock_session_factory = Mock()
        mock_session_factory.return_value = mock_session
        mock_session_factory.remove = Mock()
        mock_scoped_session.return_value = mock_session_factory
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            with self.assertRaises(Exception):
                with aggregator.session_scope() as session:
                    session.query("test")
                    # This should trigger the commit error
            
            # Verify cleanup was attempted
            mock_session.rollback.assert_called()
            mock_session.close.assert_called()
            mock_session_factory.remove.assert_called()
            
        except ImportError:
            self.skipTest("NewsAggregator import failed")


class TestEdgeCases(unittest.TestCase):
    """Test edge cases and boundary conditions"""

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_entry_id_generation_with_unicode_characters(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test entry ID generation with Unicode characters"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Mock entry with Unicode characters
            mock_entry = Mock()
            mock_entry.title = "Test Article with émojis 🚀 and ümlauts"
            mock_entry.link = "https://example.com/tëst-ärticle"
            
            source_config = {'entry_id_from': 'title,link'}
            
            entry_id = aggregator._generate_entry_id(mock_entry, source_config)
            
            # Should generate valid hash
            self.assertIsInstance(entry_id, str)
            self.assertEqual(len(entry_id), 64)
            
        except ImportError:
            self.skipTest("NewsAggregator import failed")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_html_cleaning_with_very_large_content(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test HTML cleaning with very large content"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Create large HTML content
            large_html = "<p>" + "Very long content " * 10000 + "</p>"
            
            cleaned = aggregator._clean_html_content(large_html)
            
            # Should handle large content
            self.assertNotIn('<', cleaned)
            self.assertIn('Very long content', cleaned)
            
        except ImportError:
            self.skipTest("NewsAggregator import failed")

    @patch('news_aggregator.settings')
    @patch('news_aggregator.create_engine')
    @patch('news_aggregator.scoped_session')
    def test_entry_id_generation_consistency(self, mock_scoped_session, mock_create_engine, mock_settings):
        """Test that entry ID generation is consistent for same input"""
        mock_settings.DATABASE_URL = "sqlite:///:memory:"
        
        try:
            from news_aggregator import NewsAggregator
            
            aggregator = NewsAggregator(clear_last_entries=0, recreate_table=False)
            
            # Mock entry
            mock_entry = Mock()
            mock_entry.title = "Consistent Test Article"
            mock_entry.link = "https://example.com/consistent"
            
            source_config = {'entry_id_from': 'title,link'}
            
            # Generate ID multiple times
            id1 = aggregator._generate_entry_id(mock_entry, source_config)
            id2 = aggregator._generate_entry_id(mock_entry, source_config)
            id3 = aggregator._generate_entry_id(mock_entry, source_config)
            
            # Should be identical
            self.assertEqual(id1, id2)
            self.assertEqual(id2, id3)
            
        except ImportError:
            self.skipTest("NewsAggregator import failed")


class TestDataProcessing(unittest.TestCase):
    """Test data processing and transformation logic"""

    def test_dataframe_duplicate_removal_edge_cases(self):
        """Test DataFrame duplicate removal with edge cases"""
        import pandas as pd
        
        # Test with all duplicates
        entries = [
            {'entry_id': 'test1', 'title': 'Article 1'},
            {'entry_id': 'test1', 'title': 'Article 1'},
            {'entry_id': 'test1', 'title': 'Article 1'},
        ]
        
        df = pd.DataFrame(entries).drop_duplicates(subset=['entry_id'])
        self.assertEqual(len(df), 1)
        
        # Test with empty DataFrame
        empty_df = pd.DataFrame(columns=['entry_id', 'title']).drop_duplicates(subset=['entry_id'])
        self.assertEqual(len(empty_df), 0)
        
        # Test with single entry
        single_entry = [{'entry_id': 'test1', 'title': 'Article 1'}]
        single_df = pd.DataFrame(single_entry).drop_duplicates(subset=['entry_id'])
        self.assertEqual(len(single_df), 1)

    def test_datetime_operations_edge_cases(self):
        """Test datetime operations with edge cases"""
        from datetime import datetime, timezone, timedelta
        
        # Test with different timezones
        utc_time = datetime.now(timezone.utc)
        local_time = datetime.now()
        
        # Test timezone conversion
        self.assertIsNotNone(utc_time.tzinfo)
        self.assertIsNone(local_time.tzinfo)
        
        # Test edge case: very old date
        old_date = datetime(1970, 1, 1, tzinfo=timezone.utc)
        time_diff = utc_time - old_date
        self.assertGreater(time_diff.days, 19000)  # More than 50 years
        
        # Test edge case: future date
        future_date = utc_time + timedelta(days=365)
        self.assertGreater(future_date, utc_time)

    def test_text_processing_edge_cases(self):
        """Test text processing with edge cases"""
        
        # Test with empty string
        empty_text = ""
        truncated = empty_text[:500]
        self.assertEqual(truncated, "")
        
        # Test with None
        none_text = None
        if none_text:
            truncated = none_text[:500]
        else:
            truncated = ""
        self.assertEqual(truncated, "")
        
        # Test with exactly at limit
        exact_text = "a" * 500
        truncated = exact_text[:500]
        self.assertEqual(len(truncated), 500)
        
        # Test with special characters
        special_text = "Text with\nnewlines\tand\rtabs"
        self.assertIn('\n', special_text)
        self.assertIn('\t', special_text)
        self.assertIn('\r', special_text)


class TestConcurrencyAndThreading(unittest.TestCase):
    """Test concurrent operations and thread safety"""

    def test_thread_safety_simulation(self):
        """Simulate concurrent access patterns"""
        import threading
        import time
        
        results = []
        errors = []
        
        def worker_function(worker_id):
            try:
                # Simulate some work
                time.sleep(0.01)
                result = f"Worker {worker_id} completed"
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker_function, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify results
        self.assertEqual(len(results), 10)
        self.assertEqual(len(errors), 0)

    def test_multiprocessing_simulation(self):
        """Test multiprocessing patterns used in the aggregator"""
        import platform
        
        # Skip on Windows due to pickling issues with local functions
        if platform.system() == 'Windows':
            self.skipTest("Multiprocessing tests skipped on Windows due to pickling limitations")
        
        # Test multiprocessing concepts without actually creating processes
        # This tests the data serialization and logic patterns used in the aggregator
        
        # Test data that would be passed to multiprocessing
        test_data = {
            'mandatory_fields': ['title', 'link'],
            'analysis_fields': ['full_text'],
            'filters': [('published', '>=', 'some_date')],
            'parquet_path': '/tmp/test.parquet'
        }
        
        # Test serialization (what multiprocessing would do)
        import pickle
        try:
            serialized = pickle.dumps(test_data)
            deserialized = pickle.loads(serialized)
            self.assertEqual(test_data, deserialized)
        except Exception as e:
            self.fail(f"Data serialization failed: {e}")
        
        # Test the logic that would run in a separate process
        def simulate_worker_logic(data):
            """Simulate what would happen in a worker process"""
            result = {
                'processed_fields': len(data['mandatory_fields']) + len(data['analysis_fields']),
                'filter_count': len(data['filters']),
                'output_path': data['parquet_path']
            }
            return result
        
        result = simulate_worker_logic(test_data)
        self.assertEqual(result['processed_fields'], 3)  # 2 mandatory + 1 analysis
        self.assertEqual(result['filter_count'], 1)
        self.assertEqual(result['output_path'], '/tmp/test.parquet')


class TestPerformanceConsiderations(unittest.TestCase):
    """Test performance-related aspects"""

    def test_large_dataset_handling(self):
        """Test handling of large datasets"""
        import pandas as pd
        
        # Create large dataset
        large_data = []
        for i in range(10000):
            large_data.append({
                'entry_id': f'test_{i}',
                'title': f'Article {i}',
                'content': f'Content for article {i}' * 10
            })
        
        # Test DataFrame operations
        df = pd.DataFrame(large_data)
        
        # Test duplicate removal performance
        start_time = time.time()
        df_deduped = df.drop_duplicates(subset=['entry_id'])
        end_time = time.time()
        
        # Should complete in reasonable time (less than 1 second)
        self.assertLess(end_time - start_time, 1.0)
        self.assertEqual(len(df_deduped), 10000)  # No duplicates in this case

    def test_memory_usage_patterns(self):
        """Test memory usage patterns"""
        import sys
        
        # Test with large string
        large_string = "x" * 1000000  # 1MB string
        size_before = sys.getsizeof(large_string)
        
        # Test string operations
        truncated = large_string[:500000]  # 500KB
        size_after = sys.getsizeof(truncated)
        
        # Verify memory usage
        self.assertLess(size_after, size_before)
        self.assertEqual(len(truncated), 500000)

    def test_batch_processing_simulation(self):
        """Test batch processing patterns"""
        
        def process_batch(batch_data, batch_size=100):
            """Simulate batch processing"""
            results = []
            for i in range(0, len(batch_data), batch_size):
                batch = batch_data[i:i + batch_size]
                # Process batch
                batch_result = [f"processed_{item}" for item in batch]
                results.extend(batch_result)
            return results
        
        # Test with various batch sizes
        test_data = list(range(1000))
        
        # Small batches
        result_small = process_batch(test_data, batch_size=50)
        self.assertEqual(len(result_small), 1000)
        
        # Large batches
        result_large = process_batch(test_data, batch_size=500)
        self.assertEqual(len(result_large), 1000)
        
        # Single batch
        result_single = process_batch(test_data, batch_size=1000)
        self.assertEqual(len(result_single), 1000)


if __name__ == '__main__':
    unittest.main(verbosity=2)