"""add_singleton_stages_column_to_workers

Revision ID: 819c0f44868c
Revises: 2bafd4be870a
Create Date: 2025-09-20 11:55:47.769563

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '819c0f44868c'
down_revision: Union[str, None] = '2bafd4be870a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('workers', sa.Column('singleton_stages', sa.CLOB(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('workers', 'singleton_stages')
    # ### end Alembic commands ###
