import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class WebViewScreen extends StatefulWidget {
  final String url;
  final String title;

  const WebViewScreen({
    super.key,
    required this.url,
    required this.title,
  });

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  WebViewController? _controller;
  bool _isLoading = true;
  
  @override
  void initState() {
    super.initState();
    
    if (kIsWeb) {
      // For web platform, open directly in a new tab
      _openInBrowser();
      
      // Close this screen after a short delay
      Future.delayed(Duration.zero, () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    } else if (defaultTargetPlatform == TargetPlatform.windows) {
      // For Windows platform, open in external browser
      _openInBrowser();
      
      // Close this screen after a short delay
      Future.delayed(Duration.zero, () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    } else {
      // Mobile implementation remains the same
      // For mobile platforms, use the WebView plugin
      late final PlatformWebViewControllerCreationParams params;
      if (WebViewPlatform.instance is WebKitWebViewPlatform) {
        params = WebKitWebViewControllerCreationParams(
          allowsInlineMediaPlayback: true,
          mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
        );
      } else {
        params = const PlatformWebViewControllerCreationParams();
      }
      
      _controller = WebViewController.fromPlatformCreationParams(params)
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              setState(() {
                _isLoading = true;
              });
            },
            onPageFinished: (String url) {
              setState(() {
                _isLoading = false;
              });
            },
            onWebResourceError: (WebResourceError error) {
              // Don't show error message for ORB errors since the page still loads
              if (!error.description.contains('ERR_BLOCKED_BY_ORB')) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Fehler beim Laden der Seite: ${error.description}')),
                  );
                }
              }
            },
          ),
        )
        ..loadRequest(Uri.parse(widget.url));
        
      // Configure platform-specific features
      if (_controller?.platform is AndroidWebViewController) {
        AndroidWebViewController.enableDebugging(kDebugMode);
        (_controller!.platform as AndroidWebViewController)
            .setMediaPlaybackRequiresUserGesture(false);
      }
    }
  }
  
  void _openInBrowser() async {
    final Uri uri = Uri.parse(widget.url);
    try {
      await launchUrl(
        uri,
        mode: kIsWeb 
            ? LaunchMode.platformDefault  // For web, use platform default (new tab)
            : LaunchMode.externalApplication,  // For mobile, use external browser
      );
    } catch (e) {
      // Check if the widget is still mounted before using context
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Konnte Link nicht öffnen: ${widget.url}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // For web or Windows platform, this screen should close immediately after opening the URL in a new tab
    // But we still need to render something while that happens
    if (kIsWeb || defaultTargetPlatform == TargetPlatform.windows) {
      return Scaffold(
        appBar: AppBar(
          title: Text(widget.title),
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    // For mobile platforms, show the WebView
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            SvgPicture.asset(
              'assets/icons/logo.svg',
              height: 28,
              width: 28,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                widget.title,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _controller!.reload();
              setState(() {
                _isLoading = true;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.open_in_browser),
            onPressed: _openInBrowser,
          ),
        ],
      ),
      body: Stack(
        children: [
          if (_controller != null)
            WebViewWidget(controller: _controller!),
          
          // Show loading indicator
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}





