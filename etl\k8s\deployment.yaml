apiVersion: apps/v1
kind: Deployment
metadata:
  name: breaking-bright-etl
spec:
  replicas: 1
  selector:
    matchLabels:
      app: breaking-bright-etl
  template:
    metadata:
      labels:
        app: breaking-bright-etl
    spec:
      containers:
      - name: etl
        image: your-registry/breaking-bright-etl:latest
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: breaking-bright-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: breaking-bright-secrets
              key: openai-api-key
        - name: IONOS_API_KEY
          valueFrom:
            secretKeyRef:
              name: breaking-bright-secrets
              key: ionos-api-key
        - name: INTERVAL_FEEDS
          value: "900"
        - name: INTERVAL_FULL_TEXTS
          value: "30"
        - name: INTERVAL_SENTIMENT
          value: "30"
        - name: NEWS_RSS_SOURCES_FILE
          value: "news_sources.yaml"
        volumeMounts:
        - name: config-volume
          mountPath: /app/etl/news_sources.yaml
          subPath: news_sources.yaml
      volumes:
      - name: config-volume
        configMap:
          name: breaking-bright-etl-config