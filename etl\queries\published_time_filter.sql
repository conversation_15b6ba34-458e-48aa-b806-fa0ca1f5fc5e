SELECT entries.entry_id, entries.title, entries.link, entries.source, entries.description, entries.published, entries.full_text, entries.english_text, entries.embedding, entries.vader_pos, entries.vader_neu, entries.vader_neg, entries.vader_compound, entries.german_sentiment_positive, entries.german_sentiment_neutral, entries.german_sentiment_negative, entries.tbd_polarity, entries.tbd_subjectivity, entries.llm_positive, entries.llm_neutral, entries.llm_negative, entries.llm_positive_std, entries.llm_neutral_std, entries.llm_negative_std, entries.llm_positive_diff, entries.llm_neutral_diff, entries.llm_negative_diff, entries.llm_reason_list, entries.llm_iterations, entries.llm_break_reason, entries.llm_is_ad, entries.llm_is_ad_reason, entries.llm_model, entries.llm_provider, entries.iptc_newscode, entries.iptc_score, entries.dup_entry_id, entries.dup_entry_conf, entries.description_auto_generated, entries.image_prompt, entries.preview_img, entries.preview_model, entries.preview_provider 
FROM entries 
WHERE entries.full_text IS NOT NULL AND entries.image_prompt IS NULL AND entries.published >= TO_TIMESTAMP('2025-06-13 20:29:06.245469', 'YYYY-MM-DD HH24:MI:SS.FF') ORDER BY entries.published DESC