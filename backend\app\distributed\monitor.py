#!/usr/bin/env python3
"""
Simple monitoring script for distributed ETL dashboard.

This script continuously monitors the dashboard endpoints and logs performance metrics.
Useful for detecting performance regressions or issues in production.
"""

import time
import requests
import logging
from datetime import datetime
from typing import Dict, Any
import argparse
import signal
import sys


class DashboardMonitor:
    """Simple monitoring utility for distributed ETL dashboard."""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8081", interval: int = 30):
        self.base_url = base_url
        self.interval = interval
        self.running = True
        self.logger = self._setup_logging()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        logger = logging.getLogger("dashboard_monitor")
        logger.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler("dashboard_monitor.log")
        file_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(console_handler)
        logger.addHandler(file_handler)
        
        return logger
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    def check_endpoint(self, endpoint: str, timeout: int = 10) -> Dict[str, Any]:
        """Check a single endpoint and return metrics."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            start_time = time.perf_counter()
            response = requests.get(url, timeout=timeout)
            end_time = time.perf_counter()
            
            duration_ms = (end_time - start_time) * 1000
            
            return {
                "endpoint": endpoint,
                "status_code": response.status_code,
                "response_time_ms": duration_ms,
                "success": response.status_code == 200,
                "error": None
            }
            
        except requests.exceptions.Timeout:
            return {
                "endpoint": endpoint,
                "status_code": None,
                "response_time_ms": timeout * 1000,
                "success": False,
                "error": "Timeout"
            }
        except requests.exceptions.ConnectionError:
            return {
                "endpoint": endpoint,
                "status_code": None,
                "response_time_ms": None,
                "success": False,
                "error": "Connection Error"
            }
        except Exception as e:
            return {
                "endpoint": endpoint,
                "status_code": None,
                "response_time_ms": None,
                "success": False,
                "error": str(e)
            }
    
    def run_health_check(self) -> Dict[str, Any]:
        """Run health check on all monitored endpoints."""
        endpoints = [
            "/health",
            "/api/stages/progress",
            "/api/workers/status",
            "/"
        ]
        
        results = {}
        overall_healthy = True
        
        for endpoint in endpoints:
            result = self.check_endpoint(endpoint)
            results[endpoint] = result
            
            if not result["success"]:
                overall_healthy = False
        
        results["overall_healthy"] = overall_healthy
        results["timestamp"] = datetime.now().isoformat()
        
        return results
    
    def log_results(self, results: Dict[str, Any]):
        """Log monitoring results."""
        timestamp = results["timestamp"]
        overall_healthy = results["overall_healthy"]
        
        if overall_healthy:
            self.logger.info(f"Health check PASSED at {timestamp}")
        else:
            self.logger.warning(f"Health check FAILED at {timestamp}")
        
        # Log individual endpoint results
        for endpoint, result in results.items():
            if endpoint in ["overall_healthy", "timestamp"]:
                continue
                
            if result["success"]:
                response_time = result["response_time_ms"]
                self.logger.info(
                    f"  {endpoint}: OK ({response_time:.2f}ms)"
                )
            else:
                error = result["error"]
                self.logger.error(
                    f"  {endpoint}: FAILED - {error}"
                )
    
    def run(self):
        """Run the monitoring loop."""
        self.logger.info(f"Starting dashboard monitor for {self.base_url}")
        self.logger.info(f"Check interval: {self.interval} seconds")
        self.logger.info("Press Ctrl+C to stop")
        
        while self.running:
            try:
                results = self.run_health_check()
                self.log_results(results)
                
                # Sleep for the specified interval
                for _ in range(self.interval):
                    if not self.running:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"Monitoring error: {e}")
                time.sleep(5)  # Short delay before retrying
        
        self.logger.info("Dashboard monitor stopped")


def main():
    parser = argparse.ArgumentParser(description="Monitor distributed ETL dashboard")
    parser.add_argument("--url", default="http://127.0.0.1:8081",
                       help="Base URL for the dashboard (default: http://127.0.0.1:8081)")
    parser.add_argument("--interval", type=int, default=30,
                       help="Check interval in seconds (default: 30)")
    
    args = parser.parse_args()
    
    monitor = DashboardMonitor(args.url, args.interval)
    
    try:
        monitor.run()
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")
    except Exception as e:
        print(f"Monitor failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
