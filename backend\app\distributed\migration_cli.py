#!/usr/bin/env python3
"""
Command-line interface for managing ETL processing migrations.

This CLI tool provides commands for:
- Migrating from monolithic to distributed processing
- Validating migration status and data integrity
- Rolling back failed migrations
- Monitoring migration progress
"""

import argparse
import json
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.app.core.config import settings
from backend.app.models.models import Base
from backend.app.distributed.migration_utils import (
    MigrationManager, MigrationValidator, MigrationConfig, 
    MigrationMode, migration_session
)
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_database():
    """Setup database connection and session factory."""
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal


def cmd_status(args):
    """Show current migration status."""
    print("Checking migration status...")
    
    session_factory = setup_database()
    
    with migration_session(session_factory) as session:
        manager = MigrationManager(session)
        status = manager.get_migration_status()
        
        if 'error' in status:
            print(f"Error getting status: {status['error']}")
            return 1
        
        print("\n=== Migration Status ===")
        print(f"Current Mode: {status['current_mode']}")
        print(f"Total Entries: {status['total_entries']:,}")
        print(f"Entries with Processing Status: {status['entries_with_processing_status']:,}")
        print(f"Migration Progress: {status['migration_percentage']:.1f}%")
        print(f"Active Workers: {status['active_workers']}")
        print(f"Claimed Entries: {status['claimed_entries']}")
        print(f"Last Updated: {status['timestamp']}")
        
        return 0


def cmd_validate(args):
    """Validate migration data integrity."""
    print("Validating migration data integrity...")
    
    session_factory = setup_database()
    
    with migration_session(session_factory) as session:
        validator = MigrationValidator(session)
        
        # Get sample of entries to validate
        sample_size = args.sample_size or 100
        
        # Get entry IDs for validation
        sample_entries = (
            session.query(session.query(Base.metadata.tables['entries'].c.entry_id))
            .limit(sample_size)
            .all()
        )
        
        entry_ids = [row[0] for row in sample_entries]
        
        if not entry_ids:
            print("No entries found for validation")
            return 0
        
        print(f"Validating {len(entry_ids)} entries...")
        
        # Validate processing status consistency
        consistency_result = validator.validate_processing_status_consistency(entry_ids)
        
        print("\n=== Processing Status Validation ===")
        print(f"Total Entries: {consistency_result['total_entries']}")
        print(f"Consistent Entries: {consistency_result['consistent_entries']}")
        print(f"Inconsistent Entries: {consistency_result['inconsistent_entries']}")
        
        if consistency_result['errors']:
            print(f"\nErrors ({len(consistency_result['errors'])}):")
            for error in consistency_result['errors'][:10]:  # Show first 10 errors
                print(f"  - {error}")
            if len(consistency_result['errors']) > 10:
                print(f"  ... and {len(consistency_result['errors']) - 10} more errors")
        
        if consistency_result['inconsistencies']:
            print(f"\nInconsistencies ({len(consistency_result['inconsistencies'])}):")
            for inconsistency in consistency_result['inconsistencies'][:10]:
                print(f"  - {inconsistency}")
            if len(consistency_result['inconsistencies']) > 10:
                print(f"  ... and {len(consistency_result['inconsistencies']) - 10} more inconsistencies")
        
        # Validate work queue integrity
        queue_result = validator.validate_work_queue_integrity()
        
        print("\n=== Work Queue Validation ===")
        print(f"Total Claimed Entries: {queue_result['total_claimed_entries']}")
        print(f"Stale Claims: {queue_result['stale_claims']}")
        print(f"Orphaned Claims: {queue_result['orphaned_claims']}")
        print(f"Invalid Claims: {queue_result['invalid_claims']}")
        
        if queue_result['errors']:
            print(f"\nWork Queue Errors:")
            for error in queue_result['errors']:
                print(f"  - {error}")
        
        # Return error code if validation found issues
        total_issues = (
            consistency_result['inconsistent_entries'] + 
            len(consistency_result['errors']) +
            queue_result['stale_claims'] +
            queue_result['orphaned_claims'] +
            len(queue_result['errors'])
        )
        
        if total_issues > 0:
            print(f"\nValidation completed with {total_issues} issues found")
            return 1
        else:
            print("\nValidation completed successfully - no issues found")
            return 0


def cmd_migrate(args):
    """Perform migration to distributed processing."""
    target_mode = MigrationMode(args.mode)
    
    print(f"Starting migration to {target_mode.value} mode...")
    
    # Create migration configuration
    config = MigrationConfig(
        target_mode=target_mode,
        batch_size=args.batch_size or 1000,
        validation_sample_size=args.validation_sample_size or 100,
        timeout_minutes=args.timeout or 30,
        backup_before_migration=not args.no_backup,
        validate_data_integrity=not args.no_validation,
        rollback_on_failure=not args.no_rollback
    )
    
    print(f"Migration Configuration:")
    print(f"  Target Mode: {config.target_mode.value}")
    print(f"  Batch Size: {config.batch_size}")
    print(f"  Validation Sample Size: {config.validation_sample_size}")
    print(f"  Timeout: {config.timeout_minutes} minutes")
    print(f"  Create Backup: {config.backup_before_migration}")
    print(f"  Validate Data: {config.validate_data_integrity}")
    print(f"  Rollback on Failure: {config.rollback_on_failure}")
    
    if not args.yes:
        response = input("\nProceed with migration? (y/N): ")
        if response.lower() != 'y':
            print("Migration cancelled")
            return 0
    
    session_factory = setup_database()
    
    with migration_session(session_factory) as session:
        manager = MigrationManager(session)
        result = manager.migrate_to_distributed(config)
        
        print(f"\n=== Migration Result ===")
        print(f"Success: {result.success}")
        print(f"State: {result.state.value}")
        print(f"Message: {result.message}")
        print(f"Entries Migrated: {result.entries_migrated}")
        print(f"Entries Validated: {result.entries_validated}")
        print(f"Duration: {result.duration_seconds:.2f} seconds")
        print(f"Rollback Performed: {result.rollback_performed}")
        
        if result.validation_errors:
            print(f"\nValidation Errors ({len(result.validation_errors)}):")
            for error in result.validation_errors[:10]:
                print(f"  - {error}")
            if len(result.validation_errors) > 10:
                print(f"  ... and {len(result.validation_errors) - 10} more errors")
        
        return 0 if result.success else 1


def cmd_rollback(args):
    """Rollback migration changes."""
    print("Rolling back migration changes...")
    
    if not args.yes:
        response = input("This will undo migration changes. Are you sure? (y/N): ")
        if response.lower() != 'y':
            print("Rollback cancelled")
            return 0
    
    session_factory = setup_database()
    
    with migration_session(session_factory) as session:
        manager = MigrationManager(session)
        success = manager._rollback_migration()
        
        if success:
            print("Rollback completed successfully")
            return 0
        else:
            print("Rollback failed")
            return 1


def cmd_cleanup(args):
    """Clean up stale claims and orphaned work."""
    print("Cleaning up stale claims and orphaned work...")
    
    session_factory = setup_database()
    
    with migration_session(session_factory) as session:
        from backend.app.distributed.work_queue_manager import WorkQueueManager
        
        work_queue = WorkQueueManager(session)
        
        # Clean up stale claims
        stale_timeout_minutes = args.timeout or 60
        cleaned_count = work_queue.cleanup_stale_claims(stale_timeout_minutes)
        
        print(f"Cleaned up {cleaned_count} stale claims (older than {stale_timeout_minutes} minutes)")
        
        return 0


def cmd_export_status(args):
    """Export migration status to JSON file."""
    print("Exporting migration status...")
    
    session_factory = setup_database()
    
    with migration_session(session_factory) as session:
        manager = MigrationManager(session)
        status = manager.get_migration_status()
        
        # Add detailed validation if requested
        if args.include_validation:
            validator = MigrationValidator(session)
            
            # Get sample for validation
            sample_entries = (
                session.query(session.query(Base.metadata.tables['entries'].c.entry_id))
                .limit(100)
                .all()
            )
            entry_ids = [row[0] for row in sample_entries]
            
            if entry_ids:
                consistency_result = validator.validate_processing_status_consistency(entry_ids)
                queue_result = validator.validate_work_queue_integrity()
                
                status['validation'] = {
                    'processing_status_consistency': consistency_result,
                    'work_queue_integrity': queue_result
                }
        
        # Export to file
        output_file = args.output or f"migration_status_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(output_file, 'w') as f:
            json.dump(status, f, indent=2, default=str)
        
        print(f"Migration status exported to: {output_file}")
        return 0


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="ETL Processing Migration Management CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s status                           # Show current migration status
  %(prog)s validate --sample-size 500      # Validate 500 entries
  %(prog)s migrate distributed --yes       # Migrate to distributed mode
  %(prog)s rollback --yes                  # Rollback migration
  %(prog)s cleanup --timeout 120           # Clean up claims older than 2 hours
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Show migration status')
    status_parser.set_defaults(func=cmd_status)
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate migration data')
    validate_parser.add_argument('--sample-size', type=int, default=100,
                               help='Number of entries to validate (default: 100)')
    validate_parser.set_defaults(func=cmd_validate)
    
    # Migrate command
    migrate_parser = subparsers.add_parser('migrate', help='Perform migration')
    migrate_parser.add_argument('mode', choices=['distributed', 'hybrid'],
                              help='Target migration mode')
    migrate_parser.add_argument('--batch-size', type=int, default=1000,
                              help='Migration batch size (default: 1000)')
    migrate_parser.add_argument('--validation-sample-size', type=int, default=100,
                              help='Validation sample size (default: 100)')
    migrate_parser.add_argument('--timeout', type=int, default=30,
                              help='Migration timeout in minutes (default: 30)')
    migrate_parser.add_argument('--no-backup', action='store_true',
                              help='Skip creating backup before migration')
    migrate_parser.add_argument('--no-validation', action='store_true',
                              help='Skip data validation after migration')
    migrate_parser.add_argument('--no-rollback', action='store_true',
                              help='Disable automatic rollback on failure')
    migrate_parser.add_argument('--yes', action='store_true',
                              help='Skip confirmation prompts')
    migrate_parser.set_defaults(func=cmd_migrate)
    
    # Rollback command
    rollback_parser = subparsers.add_parser('rollback', help='Rollback migration')
    rollback_parser.add_argument('--yes', action='store_true',
                               help='Skip confirmation prompt')
    rollback_parser.set_defaults(func=cmd_rollback)
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up stale claims')
    cleanup_parser.add_argument('--timeout', type=int, default=60,
                              help='Stale claim timeout in minutes (default: 60)')
    cleanup_parser.set_defaults(func=cmd_cleanup)
    
    # Export command
    export_parser = subparsers.add_parser('export-status', help='Export status to JSON')
    export_parser.add_argument('--output', help='Output file path')
    export_parser.add_argument('--include-validation', action='store_true',
                             help='Include detailed validation results')
    export_parser.set_defaults(func=cmd_export_status)
    
    # Parse arguments
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        return args.func(args)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    except Exception as e:
        logger.error(f"Command failed: {e}", exc_info=True)
        print(f"Error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())