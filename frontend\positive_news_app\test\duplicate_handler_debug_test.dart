import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/services/duplicate_handler_service.dart';
import 'package:breakingbright/models/news_model.dart';

void main() {
  group('DuplicateHandlerService Debug Tests', () {
    test('Test with actual problematic entries', () {
      // Create test data matching the actual problematic entries
      final testNewsItems = [
        EntryWithSource(
          entryId:
              'a712401f72f635208b72268837e9990698726c4edbe3bac3ea59d5f13a174c24',
          title: 'Test News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId:
              'a78015d6b781bab123b30775800e9e2bdd4f7e4fedd23fcfcc58d0600bc2ef74',
        ),
        EntryWithSource(
          entryId:
              '95b69921aa7be93a1e1be8e8d3f19f6159660fcdb0adccc3e0c7e2f3c83a13dc',
          title: 'Test News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'), // Earlier
          dupEntryId:
              'a78015d6b781bab123b30775800e9e2bdd4f7e4fedd23fcfcc58d0600bc2ef74', // Same dup_entry_id
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(testNewsItems);

      // Should have only 1 item (the more recent one)
      expect(result.length, 1);

      // Should keep the more recent entry (first one)
      expect(result.first.entryId,
          'a712401f72f635208b72268837e9990698726c4edbe3bac3ea59d5f13a174c24');
    });

    test('Test with null dupEntryId handling', () {
      final testNewsItems = [
        EntryWithSource(
          entryId: 'entry_1',
          title: 'News with null dupEntryId',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now(),
          dupEntryId: null, // null dupEntryId
        ),
        EntryWithSource(
          entryId: 'entry_2',
          title: 'News with empty dupEntryId',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now(),
          dupEntryId: '', // empty dupEntryId
        ),
        EntryWithSource(
          entryId: 'entry_3',
          title: 'News with valid dupEntryId',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.now(),
          dupEntryId: 'valid_dup_id',
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(testNewsItems);

      // All should be kept as unique (null and empty are treated as unique)
      expect(result.length, 3);
    });

    test('Test duplicate detection with same dupEntryId', () {
      final testNewsItems = [
        EntryWithSource(
          entryId: 'entry_1',
          title: 'First duplicate',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'same_dup_id',
        ),
        EntryWithSource(
          entryId: 'entry_2',
          title: 'Second duplicate',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'), // Earlier
          dupEntryId: 'same_dup_id', // Same dup_entry_id
        ),
        EntryWithSource(
          entryId: 'entry_3',
          title: 'Third duplicate',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.parse('2024-01-01T13:00:00Z'), // Latest
          dupEntryId: 'same_dup_id', // Same dup_entry_id
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(testNewsItems);

      // Should have only 1 item (the most recent one)
      expect(result.length, 1);

      // Should keep the most recent entry (entry_3)
      expect(result.first.entryId, 'entry_3');
      expect(result.first.published, DateTime.parse('2024-01-01T13:00:00Z'));
    });

    test('Test tie-breaker with same publish time', () {
      final testNewsItems = [
        EntryWithSource(
          entryId: 'entry_b',
          title: 'Second alphabetically',
          link: 'https://example.com/b',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'same_dup_id',
        ),
        EntryWithSource(
          entryId: 'entry_a',
          title: 'First alphabetically',
          link: 'https://example.com/a',
          source: 'source2',
          published: DateTime.parse('2024-01-01T12:00:00Z'), // Same time
          dupEntryId: 'same_dup_id', // Same dup_entry_id
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(testNewsItems);

      // Should have only 1 item
      expect(result.length, 1);

      // Should keep the one with smaller entry_id (entry_a)
      expect(result.first.entryId, 'entry_a');
    });

    test('Test mixed scenario with duplicates and unique items', () {
      final testNewsItems = [
        EntryWithSource(
          entryId: 'dup_1a',
          title: 'Duplicate Group 1 - First',
          link: 'https://example.com/1a',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'dup_group_1',
        ),
        EntryWithSource(
          entryId: 'dup_1b',
          title: 'Duplicate Group 1 - Second',
          link: 'https://example.com/1b',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'), // Earlier
          dupEntryId: 'dup_group_1', // Same group
        ),
        EntryWithSource(
          entryId: 'unique_1',
          title: 'Unique Item',
          link: 'https://example.com/unique',
          source: 'source3',
          published: DateTime.parse('2024-01-01T10:00:00Z'),
          dupEntryId: null, // Unique
        ),
        EntryWithSource(
          entryId: 'dup_2a',
          title: 'Duplicate Group 2 - Only',
          link: 'https://example.com/2a',
          source: 'source4',
          published: DateTime.parse('2024-01-01T09:00:00Z'),
          dupEntryId: 'dup_group_2', // Different group, only member
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(testNewsItems);

      // Should have 3 items: most recent from dup_group_1, unique_1, and dup_2a
      expect(result.length, 3);

      // Check which items were kept
      final keptIds = result.map((item) => item.entryId).toSet();
      expect(keptIds.contains('dup_1a'), true); // Most recent from group 1
      expect(keptIds.contains('dup_1b'), false); // Older from group 1
      expect(keptIds.contains('unique_1'), true); // Unique item
      expect(keptIds.contains('dup_2a'), true); // Only item in group 2
    });

    test('Performance test with larger dataset', () {
      final testNewsItems = List.generate(100, (index) {
        return EntryWithSource(
          entryId: 'test_$index',
          title: 'Test News $index',
          link: 'https://example.com/$index',
          source: 'source${index % 10}',
          published: DateTime.now().subtract(Duration(minutes: index)),
          dupEntryId: index < 50 ? 'dup_group_${index % 10}' : 'unique_$index',
        );
      });

      final stopwatch = Stopwatch()..start();
      final result = DuplicateHandlerService.removeDuplicates(testNewsItems);
      stopwatch.stop();

      // Should have removed duplicates efficiently
      expect(result.length, lessThan(testNewsItems.length));
      expect(stopwatch.elapsedMilliseconds, lessThan(100),
          reason: 'Deduplication should be fast even with larger datasets');
    });
  });
}
