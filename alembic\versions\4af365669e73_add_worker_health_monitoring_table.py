"""add_worker_health_monitoring_table

Revision ID: 4af365669e73
Revises: 8e4e82fc0167
Create Date: 2025-09-18 23:13:19.064514

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import oracle

# revision identifiers, used by Alembic.
revision: str = '4af365669e73'
down_revision: Union[str, None] = '8e4e82fc0167'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('workers',
    sa.Column('worker_id', sa.String(length=255), nullable=False),
    sa.Column('worker_type', sa.String(length=100), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('stages', sa.CLOB(), nullable=True),
    sa.Column('config', sa.CLOB(), nullable=True),
    sa.Column('last_heartbeat', oracle.TIMESTAMP(), nullable=True),
    sa.Column('heartbeat_interval_seconds', sa.Integer(), nullable=True),
    sa.Column('started_at', oracle.TIMESTAMP(), nullable=True),
    sa.Column('stopped_at', oracle.TIMESTAMP(), nullable=True),
    sa.Column('batches_processed', sa.Integer(), nullable=True),
    sa.Column('entries_processed', sa.Integer(), nullable=True),
    sa.Column('entries_failed', sa.Integer(), nullable=True),
    sa.Column('total_processing_time', sa.Float(), nullable=True),
    sa.Column('current_batch_size', sa.Integer(), nullable=True),
    sa.Column('processing_since', oracle.TIMESTAMP(), nullable=True),
    sa.Column('last_error', sa.CLOB(), nullable=True),
    sa.Column('error_count', sa.Integer(), nullable=True),
    sa.Column('hostname', sa.String(length=255), nullable=True),
    sa.Column('process_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('worker_id')
    )
    op.create_index('idx_workers_last_heartbeat', 'workers', ['last_heartbeat'], unique=False)
    op.create_index('idx_workers_status', 'workers', ['status'], unique=False)
    op.create_index('idx_workers_status_heartbeat', 'workers', ['status', 'last_heartbeat'], unique=False)
    op.create_index('idx_workers_type', 'workers', ['worker_type'], unique=False)
    op.drop_table('sdw$err$_categories')
    op.drop_table('categories_ordered')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('categories_ordered',
    sa.Column('iptc_newscode', sa.VARCHAR(length=4000), nullable=True),
    sa.Column('category', sa.VARCHAR(length=4000), nullable=True),
    oracle_compress='QUERY HIGH ROW LEVEL LOCKING',
    oracle_tablespace='DATA'
    )
    op.create_table('sdw$err$_categories',
    sa.Column('ora_err_number$', oracle.NUMBER(asdecimal=False), nullable=True),
    sa.Column('ora_err_mesg$', sa.VARCHAR(length=2000), nullable=True),
    sa.Column('ora_err_rowid$', sa.NullType(), nullable=True),
    sa.Column('ora_err_optyp$', sa.VARCHAR(length=2), nullable=True),
    sa.Column('ora_err_tag$', sa.VARCHAR(length=2000), nullable=True),
    sa.Column('iptc_newscode', sa.VARCHAR(length=32767), nullable=True),
    sa.Column('category', sa.VARCHAR(length=32767), nullable=True),
    sa.Column('sort_order', sa.VARCHAR(length=4000), nullable=True),
    comment='DML Error Logging table for "DEVXPS15"."CATEGORIES"',
    oracle_compress='QUERY HIGH ROW LEVEL LOCKING',
    oracle_tablespace='DATA'
    )
    op.drop_index('idx_workers_type', table_name='workers')
    op.drop_index('idx_workers_status_heartbeat', table_name='workers')
    op.drop_index('idx_workers_status', table_name='workers')
    op.drop_index('idx_workers_last_heartbeat', table_name='workers')
    op.drop_table('workers')
    # ### end Alembic commands ###
