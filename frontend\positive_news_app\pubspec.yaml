name: breakingbright
description: <PERSON><PERSON> plattformübergreifende Nachrichten-App mit ausschließlich positiven Nachrichten.
publish_to: 'none'
version: 1.0.0+6

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  video_player: ^2.9.2
  cupertino_icons: ^1.0.6
  http: ^1.2.2
  provider: ^6.1.2
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.0
  url_launcher: ^6.3.1
  webview_flutter: ^4.4.2
  webview_flutter_android: ^4.8.2
  webview_flutter_wkwebview: ^3.9.2
  intl: ^0.20.2
  shared_preferences: ^2.2.3
  # Removed web package as we're no longer using iframes

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  shared_preferences_platform_interface: ^2.3.2
  mockito: ^5.4.4
  build_runner: ^2.4.8
  flutter_launcher_icons: ^0.14.4 # Use the latest version

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/videos/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/icons/logo_whiteOutline.png"
  # You can add more specific configurations for adaptive icons on Android if needed
  # adaptive_icon_background: "#ffffff"
  # adaptive_icon_foreground: "assets/icon/app_icon_foreground.png"