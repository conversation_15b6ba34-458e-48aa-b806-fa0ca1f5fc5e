"""
Tests for operational tools and utilities.
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock

from ..cli.worker_cli import Worker<PERSON>I
from ..diagnostics.health_checker import <PERSON>Checker, HealthStatus, SystemMetrics
from ..diagnostics.performance_tuner import PerformanceTuner, OptimizationLevel
from ..worker_config import WorkerConfig
from ..processing_stage import ProcessingStage


class TestWorkerCLI:
    """Test the worker CLI functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.cli = WorkerCLI()
        self.test_config = WorkerConfig(
            worker_id="test-worker-001",
            stages=[ProcessingStage.DOWNLOAD_FEEDS],
            batch_size=25
        )
    
    def test_create_parser(self):
        """Test CLI parser creation."""
        parser = self.cli.create_parser()
        
        # Test that all expected commands are present
        args = parser.parse_args(['start', '--worker-id', 'test-worker'])
        assert args.command == 'start'
        assert args.worker_id == 'test-worker'
        
        args = parser.parse_args(['list', '--format', 'json'])
        assert args.command == 'list'
        assert args.format == 'json'
        
        args = parser.parse_args(['status', '--detailed'])
        assert args.command == 'status'
        assert args.detailed is True
    
    @pytest.mark.asyncio
    async def test_list_workers_json_format(self):
        """Test listing workers in JSON format."""
        # Mock the args
        args = Mock()
        args.format = 'json'
        args.filter_stage = None
        args.filter_status = None
        
        # Capture output
        with patch('builtins.print') as mock_print:
            await self.cli.list_workers(args)
            
            # Verify JSON output was printed
            mock_print.assert_called()
            output = mock_print.call_args[0][0]
            
            # Should be valid JSON
            parsed = json.loads(output)
            assert isinstance(parsed, list)
            assert len(parsed) > 0
            assert 'worker_id' in parsed[0]
    
    @pytest.mark.asyncio
    async def test_list_workers_table_format(self):
        """Test listing workers in table format."""
        args = Mock()
        args.format = 'table'
        args.filter_stage = None
        args.filter_status = None
        
        with patch('builtins.print') as mock_print:
            await self.cli.list_workers(args)
            
            # Should print table headers and data
            mock_print.assert_called()
            calls = mock_print.call_args_list
            
            # Check for table header
            header_found = any('Worker ID' in str(call) for call in calls)
            assert header_found
    
    @pytest.mark.asyncio
    async def test_show_queue_status(self):
        """Test showing queue status."""
        args = Mock()
        args.stage = None
        args.format = 'table'
        
        with patch('builtins.print') as mock_print:
            await self.cli.show_queue_status(args)
            
            mock_print.assert_called()
            calls = mock_print.call_args_list
            
            # Check for expected columns
            header_found = any('Stage' in str(call) and 'Pending' in str(call) for call in calls)
            assert header_found
    
    @pytest.mark.asyncio
    async def test_cleanup_stale_work_dry_run(self):
        """Test cleanup with dry run option."""
        args = Mock()
        args.timeout = 60
        args.dry_run = True
        
        with patch('builtins.print') as mock_print:
            await self.cli.cleanup_stale_work(args)
            
            mock_print.assert_called()
            output = str(mock_print.call_args_list)
            assert 'stale claims' in output.lower()


class TestHealthChecker:
    """Test the health checker functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = WorkerConfig(
            worker_id="test-worker-001",
            stages=[ProcessingStage.DOWNLOAD_FEEDS],
            batch_size=25,
            database_url="sqlite:///test.db"
        )
        self.health_checker = HealthChecker(self.config)
    
    @pytest.mark.asyncio
    async def test_check_database_connectivity_success(self):
        """Test successful database connectivity check."""
        with patch('backend.app.distributed.work_queue_manager.WorkQueueManager'):
            result = await self.health_checker.check_database_connectivity()
            
            assert result.component == "database"
            assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING]
            assert result.response_time_ms is not None
            assert "database_url" in result.details
    
    @pytest.mark.asyncio
    async def test_check_database_connectivity_failure(self):
        """Test database connectivity check failure."""
        with patch('backend.app.distributed.work_queue_manager.WorkQueueManager', side_effect=Exception("Connection failed")):
            result = await self.health_checker.check_database_connectivity()
            
            assert result.component == "database"
            assert result.status == HealthStatus.CRITICAL
            assert "Connection failed" in result.message
            assert "error" in result.details
    
    @pytest.mark.asyncio
    async def test_check_work_queue_health(self):
        """Test work queue health check."""
        with patch('backend.app.distributed.work_queue_manager.WorkQueueManager'):
            result = await self.health_checker.check_work_queue_health()
            
            assert result.component == "work_queue"
            assert result.status in [HealthStatus.HEALTHY, HealthStatus.WARNING, HealthStatus.CRITICAL]
            assert "total_pending" in result.details
            assert "stage_stats" in result.details
    
    def test_get_system_metrics(self):
        """Test system metrics collection."""
        with patch('psutil.cpu_percent', return_value=45.0), \
             patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.disk_usage') as mock_disk, \
             patch('psutil.net_io_counters') as mock_network, \
             patch('psutil.getloadavg', return_value=(1.0, 1.5, 2.0)):
            
            # Mock memory object
            mock_memory.return_value = Mock(
                total=**********,
                available=**********,
                percent=50.0
            )
            
            # Mock disk object
            mock_disk.return_value = Mock(
                total=**********00,
                used=50000000000,
                free=50000000000,
                percent=50.0
            )
            
            # Mock network object
            mock_network.return_value = Mock(
                bytes_sent=**********,
                bytes_recv=**********
            )
            
            metrics = self.health_checker.get_system_metrics()
            
            assert isinstance(metrics, SystemMetrics)
            assert metrics.cpu_percent == 45.0
            assert metrics.memory_percent == 50.0
            assert metrics.disk_percent == 50.0
            assert metrics.load_average == (1.0, 1.5, 2.0)
    
    def test_check_system_resources_healthy(self):
        """Test system resource check with healthy values."""
        with patch.object(self.health_checker, 'get_system_metrics') as mock_metrics:
            mock_metrics.return_value = SystemMetrics(
                cpu_percent=45.0,
                memory_percent=60.0,
                memory_used_mb=2000.0,
                memory_available_mb=1000.0,
                disk_percent=70.0,
                disk_used_gb=50.0,
                disk_free_gb=20.0,
                network_sent_mb=100.0,
                network_recv_mb=200.0,
                load_average=(1.0, 1.5, 2.0),
                uptime_seconds=3600.0
            )
            
            result = self.health_checker.check_system_resources()
            
            assert result.component == "system_resources"
            assert result.status == HealthStatus.HEALTHY
            assert "healthy" in result.message.lower()
    
    def test_check_system_resources_critical(self):
        """Test system resource check with critical values."""
        with patch.object(self.health_checker, 'get_system_metrics') as mock_metrics, \
             patch('psutil.cpu_count', return_value=4):
            
            mock_metrics.return_value = SystemMetrics(
                cpu_percent=95.0,  # Critical CPU
                memory_percent=95.0,  # Critical memory
                memory_used_mb=7500.0,
                memory_available_mb=500.0,
                disk_percent=98.0,  # Critical disk
                disk_used_gb=98.0,
                disk_free_gb=2.0,
                network_sent_mb=100.0,
                network_recv_mb=200.0,
                load_average=(10.0, 12.0, 15.0),  # Critical load
                uptime_seconds=3600.0
            )
            
            result = self.health_checker.check_system_resources()
            
            assert result.component == "system_resources"
            assert result.status == HealthStatus.CRITICAL
            assert "High CPU usage" in result.message or "High memory usage" in result.message
    
    @pytest.mark.asyncio
    async def test_run_comprehensive_health_check(self):
        """Test comprehensive health check."""
        with patch.object(self.health_checker, 'check_database_connectivity') as mock_db, \
             patch.object(self.health_checker, 'check_work_queue_health') as mock_queue, \
             patch.object(self.health_checker, 'check_system_resources') as mock_system, \
             patch.object(self.health_checker, 'check_external_apis') as mock_apis, \
             patch.object(self.health_checker, 'get_system_metrics') as mock_metrics:
            
            # Mock all health checks to return healthy status
            mock_db.return_value = Mock(status=HealthStatus.HEALTHY, component="database")
            mock_queue.return_value = Mock(status=HealthStatus.HEALTHY, component="work_queue")
            mock_system.return_value = Mock(status=HealthStatus.HEALTHY, component="system_resources")
            mock_apis.return_value = []
            
            mock_metrics.return_value = SystemMetrics(
                cpu_percent=45.0, memory_percent=60.0, memory_used_mb=2000.0,
                memory_available_mb=1000.0, disk_percent=70.0, disk_used_gb=50.0,
                disk_free_gb=20.0, network_sent_mb=100.0, network_recv_mb=200.0,
                load_average=(1.0, 1.5, 2.0), uptime_seconds=3600.0
            )
            
            report = await self.health_checker.run_comprehensive_health_check()
            
            assert report.worker_id == self.config.worker_id
            assert report.overall_status == HealthStatus.HEALTHY
            assert len(report.health_checks) >= 3  # db, queue, system
            assert report.system_metrics is not None
    
    def test_format_health_report(self):
        """Test health report formatting."""
        # Create a mock health report
        from ..diagnostics.health_checker import WorkerHealthReport, HealthCheckResult
        
        system_metrics = SystemMetrics(
            cpu_percent=45.0, memory_percent=60.0, memory_used_mb=2000.0,
            memory_available_mb=1000.0, disk_percent=70.0, disk_used_gb=50.0,
            disk_free_gb=20.0, network_sent_mb=100.0, network_recv_mb=200.0,
            load_average=(1.0, 1.5, 2.0), uptime_seconds=3600.0
        )
        
        health_checks = [
            HealthCheckResult(
                component="database",
                status=HealthStatus.HEALTHY,
                message="Database connectivity OK",
                details={},
                timestamp=datetime.now(),
                response_time_ms=50.0
            )
        ]
        
        report = WorkerHealthReport(
            worker_id="test-worker",
            overall_status=HealthStatus.HEALTHY,
            timestamp=datetime.now(),
            uptime_seconds=3600.0,
            system_metrics=system_metrics,
            health_checks=health_checks,
            work_queue_status={},
            recent_errors=[],
            performance_metrics={"total_processed": 100}
        )
        
        formatted = self.health_checker.format_health_report(report)
        
        assert "test-worker" in formatted
        assert "HEALTHY" in formatted
        assert "System Metrics:" in formatted
        assert "Health Checks:" in formatted


class TestPerformanceTuner:
    """Test the performance tuner functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.config = WorkerConfig(
            worker_id="test-worker-001",
            stages=[ProcessingStage.DOWNLOAD_FEEDS],
            batch_size=25,
            claim_timeout_minutes=30
        )
        self.tuner = PerformanceTuner(self.config)
    
    def test_add_metric(self):
        """Test adding performance metrics."""
        self.tuner.add_metric("processing_time", 5.0, "seconds", ProcessingStage.DOWNLOAD_FEEDS)
        
        assert len(self.tuner.metrics_history) == 1
        metric = self.tuner.metrics_history[0]
        assert metric.name == "processing_time"
        assert metric.value == 5.0
        assert metric.unit == "seconds"
        assert metric.stage == ProcessingStage.DOWNLOAD_FEEDS
    
    def test_analyze_batch_size_performance_slow_processing(self):
        """Test batch size analysis for slow processing."""
        # Add metrics indicating slow processing
        for i in range(15):
            self.tuner.add_metric("batch_processing_time", 300.0, "seconds")  # 300s for batch of 25 = 12s per entry
            self.tuner.add_metric("throughput_per_minute", 2.0, "entries/min")
        
        recommendations = self.tuner.analyze_batch_size_performance()
        
        assert len(recommendations) > 0
        batch_rec = next((r for r in recommendations if r.category == "batch_size"), None)
        assert batch_rec is not None
        assert batch_rec.level in [OptimizationLevel.MEDIUM_IMPACT, OptimizationLevel.HIGH_IMPACT]
        assert batch_rec.recommended_value < self.config.batch_size
    
    def test_analyze_batch_size_performance_fast_processing(self):
        """Test batch size analysis for fast processing."""
        # Add metrics indicating fast processing
        for i in range(15):
            self.tuner.add_metric("batch_processing_time", 25.0, "seconds")  # 25s for batch of 25 = 1s per entry
            self.tuner.add_metric("throughput_per_minute", 60.0, "entries/min")
        
        recommendations = self.tuner.analyze_batch_size_performance()
        
        batch_rec = next((r for r in recommendations if r.category == "batch_size"), None)
        if batch_rec:  # May not recommend increase if already at reasonable size
            assert batch_rec.recommended_value > self.config.batch_size
    
    def test_analyze_timeout_configuration_too_short(self):
        """Test timeout analysis when timeout is too short."""
        # Add metrics indicating processing takes longer than timeout allows
        for i in range(10):
            self.tuner.add_metric("batch_processing_time", 2400.0, "seconds")  # 40 minutes > 30 minute timeout
        
        recommendations = self.tuner.analyze_timeout_configuration()
        
        timeout_rec = next((r for r in recommendations if r.category == "timeout"), None)
        assert timeout_rec is not None
        assert timeout_rec.level == OptimizationLevel.HIGH_IMPACT
        assert "60 minutes" in str(timeout_rec.recommended_value)  # Should recommend ~60 minutes
    
    def test_analyze_resource_utilization_high_cpu(self):
        """Test resource analysis with high CPU usage."""
        system_metrics = SystemMetrics(
            cpu_percent=90.0,  # High CPU
            memory_percent=60.0,
            memory_used_mb=2000.0,
            memory_available_mb=1000.0,
            disk_percent=70.0,
            disk_used_gb=50.0,
            disk_free_gb=20.0,
            network_sent_mb=100.0,
            network_recv_mb=200.0,
            load_average=(1.0, 1.5, 2.0),
            uptime_seconds=3600.0
        )
        
        recommendations = self.tuner.analyze_resource_utilization(system_metrics)
        
        cpu_rec = next((r for r in recommendations if "CPU" in r.title), None)
        assert cpu_rec is not None
        assert cpu_rec.level == OptimizationLevel.HIGH_IMPACT
        assert "90.0%" in str(cpu_rec.current_value)
    
    def test_analyze_resource_utilization_low_disk_space(self):
        """Test resource analysis with low disk space."""
        system_metrics = SystemMetrics(
            cpu_percent=45.0,
            memory_percent=60.0,
            memory_used_mb=2000.0,
            memory_available_mb=1000.0,
            disk_percent=95.0,  # Critical disk usage
            disk_used_gb=95.0,
            disk_free_gb=5.0,
            network_sent_mb=100.0,
            network_recv_mb=200.0,
            load_average=(1.0, 1.5, 2.0),
            uptime_seconds=3600.0
        )
        
        recommendations = self.tuner.analyze_resource_utilization(system_metrics)
        
        disk_rec = next((r for r in recommendations if "disk" in r.title.lower()), None)
        assert disk_rec is not None
        assert disk_rec.level == OptimizationLevel.CRITICAL
    
    @pytest.mark.asyncio
    async def test_run_performance_analysis(self):
        """Test comprehensive performance analysis."""
        # Add some test metrics
        for i in range(10):
            self.tuner.add_metric("processing_time", 5.0, "seconds")
            self.tuner.add_metric("throughput_per_minute", 12.0, "entries/min")
            self.tuner.add_metric("error_rate", 2.0, "percent")
        
        system_metrics = SystemMetrics(
            cpu_percent=45.0, memory_percent=60.0, memory_used_mb=2000.0,
            memory_available_mb=1000.0, disk_percent=70.0, disk_used_gb=50.0,
            disk_free_gb=20.0, network_sent_mb=100.0, network_recv_mb=200.0,
            load_average=(1.0, 1.5, 2.0), uptime_seconds=3600.0
        )
        
        analysis = await self.tuner.run_performance_analysis(system_metrics)
        
        assert analysis.worker_id == self.config.worker_id
        assert 0 <= analysis.overall_score <= 100
        assert analysis.metrics_summary is not None
        assert analysis.resource_utilization is not None
    
    def test_format_analysis_report(self):
        """Test performance analysis report formatting."""
        from ..diagnostics.performance_tuner import PerformanceAnalysis, OptimizationRecommendation
        
        recommendations = [
            OptimizationRecommendation(
                category="batch_size",
                level=OptimizationLevel.MEDIUM_IMPACT,
                title="Test recommendation",
                description="Test description",
                current_value=25,
                recommended_value=50,
                expected_improvement="Better performance",
                implementation_steps=["Step 1", "Step 2"],
                risks=["Risk 1"]
            )
        ]
        
        analysis = PerformanceAnalysis(
            worker_id="test-worker",
            analysis_timestamp=datetime.now(),
            analysis_period_hours=1.0,
            overall_score=85.0,
            bottlenecks=["High CPU usage"],
            recommendations=recommendations,
            metrics_summary={"avg_processing_time": 5.0},
            resource_utilization={"cpu_percent": 45.0}
        )
        
        formatted = self.tuner.format_analysis_report(analysis)
        
        assert "test-worker" in formatted
        assert "85.0/100" in formatted
        assert "Test recommendation" in formatted
        assert "GOOD" in formatted  # Score interpretation


if __name__ == '__main__':
    pytest.main([__file__, '-v'])