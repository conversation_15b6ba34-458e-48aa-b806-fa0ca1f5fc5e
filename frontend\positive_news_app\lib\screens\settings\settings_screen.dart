import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/services/app_restart_service.dart';

import 'package:breakingbright/models/news_model.dart';


class SettingsScreen extends StatelessWidget {
  final List<CategoryNews>? availableCategories;

  const SettingsScreen({
    super.key,
    this.availableCategories,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Einstellungen'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context,
                true); // Always return true since settings might have changed
          },
        ),
      ),
      body: ListView(
        children: [
          _buildLookbackHoursSetting(context),
          const Divider(),
          _buildLlmScoreSetting(context),
          const Divider(),
          _buildResetSection(context),
        ],
      ),
    );
  }

  Widget _buildLookbackHoursSetting(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Zeitraum für Nachrichten',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Zeige Nachrichten der letzten ${settings.lookbackHours} Stunden',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Slider(
                value: settings.lookbackHours.toDouble(),
                min: 24,
                max: 168, // 1 week
                divisions: 6,
                label: '${settings.lookbackHours}h',
                onChanged: (value) {
                  settings.setLookbackHours(value.round());
                },
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: const [
                  Text('24h'),
                  Text('168h'),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLlmScoreSetting(BuildContext context) {
    return Consumer<SettingsProvider>(
      builder: (context, settings, child) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Positivitätsbewertung anzeigen',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                'Zeigt die KI-Positivitätsbewertung als Prozentsatz auf den Nachrichtenkarten an. Tippen Sie auf die Bewertung, um die Begründung zu sehen.',
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Bewertung anzeigen'),
                subtitle: Text(
                  settings.showLlmScore
                      ? 'Positivitätsbewertung wird angezeigt'
                      : 'Positivitätsbewertung ist ausgeblendet',
                ),
                value: settings.showLlmScore,
                onChanged: (value) {
                  settings.setShowLlmScore(value);
                },
                contentPadding: EdgeInsets.zero,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildResetSection(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'App zurücksetzen',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Alle Einstellungen, Favoriten und App-Daten werden gelöscht.',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showResetConfirmationDialog(context),
              icon: const Icon(Icons.refresh, color: Colors.white),
              label: const Text(
                'App zurücksetzen',
                style: TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showResetConfirmationDialog(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('App zurücksetzen'),
          content: const Text(
            'Sind Sie sicher, dass Sie die App zurücksetzen möchten?\n\n'
            'Alle Einstellungen, Favoriten und App-Daten werden unwiderruflich gelöscht. '
            'Die App wird anschließend neu gestartet.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Abbrechen'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text(
                'Zurücksetzen',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );

    if (confirmed == true && context.mounted) {
      await _resetApp(context);
    }
  }

  Future<void> _resetApp(BuildContext context) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('App wird zurückgesetzt...'),
            ],
          ),
        ),
      );

      // Get providers before async operations
      final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
      final favoritesService = Provider.of<FavoritesService>(context, listen: false);

      // Reset settings
      await settingsProvider.resetAllSettings();

      // Reset favorites
      favoritesService.clearFavorites();

      // Wait a moment to ensure all operations complete
      await Future.delayed(const Duration(milliseconds: 500));

      if (context.mounted) {
        // Close loading dialog
        Navigator.of(context).pop();

        // Restart the app
        AppRestartService.restartApp();
      }
    } catch (e) {
      if (context.mounted) {
        // Close loading dialog if it's open
        Navigator.of(context).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fehler beim Zurücksetzen: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
