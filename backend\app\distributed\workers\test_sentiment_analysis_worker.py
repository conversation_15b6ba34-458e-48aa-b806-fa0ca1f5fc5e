"""
Integration tests for SentimentAnalysisWorker.

Tests the sentiment analysis worker with various entry types and scenarios
including successful analysis, missing text fields, and error handling.
"""

import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from typing import List

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.app.distributed.workers.sentiment_analysis_worker import SentimentAnalysisWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry, Base

logger = logging.getLogger(__name__)


class TestSentimentAnalysisWorker:
    """Test suite for SentimentAnalysisWorker."""
    
    @pytest.fixture
    def db_session_factory(self):
        """Create an in-memory SQLite database for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        
        def get_session():
            return SessionLocal()
        
        return get_session
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        return WorkerConfig(
            worker_id="test-sentiment-worker",
            stages=[ProcessingStage.SENTIMENT_ANALYSIS],
            batch_size=5,
            heartbeat_interval_seconds=30,
            stage_configs={
                ProcessingStage.SENTIMENT_ANALYSIS.value: {
                    'batch_optimize_models': True,
                    'preload_models': False
                }
            }
        )
    
    @pytest.fixture
    def sample_entries(self, db_session_factory):
        """Create sample entries for testing."""
        session = db_session_factory()
        
        entries = [
            Entry(
                entry_id="test-entry-1",
                title="Positive News Article",
                link="https://example.com/positive",
                description="Great news about community success",
                published=datetime.now(timezone.utc),
                full_text="This is a wonderful story about community cooperation and success.",
                english_text="This is a wonderful story about community cooperation and success."
            ),
            Entry(
                entry_id="test-entry-2", 
                title="Neutral News Article",
                link="https://example.com/neutral",
                description="Regular news update",
                published=datetime.now(timezone.utc),
                full_text="Dies ist ein neutraler Artikel über aktuelle Ereignisse.",
                english_text="This is a neutral article about current events."
            ),
            Entry(
                entry_id="test-entry-3",
                title="Incomplete Article",
                link="https://example.com/incomplete",
                description="Article missing text",
                published=datetime.now(timezone.utc),
                full_text=None,  # Missing full text
                english_text="This article has missing German text."
            ),
            Entry(
                entry_id="test-entry-4",
                title="Another Incomplete Article", 
                link="https://example.com/incomplete2",
                description="Article missing English text",
                published=datetime.now(timezone.utc),
                full_text="Dieser Artikel hat keinen englischen Text.",
                english_text=None  # Missing English text
            )
        ]
        
        for entry in entries:
            session.add(entry)
        session.commit()
        
        # Refresh entries to ensure they're loaded
        for entry in entries:
            session.refresh(entry)
        
        # Detach entries from session so they can be used independently
        session.expunge_all()
        session.close()
        
        return entries
    
    def test_worker_initialization(self, worker_config, db_session_factory):
        """Test worker initialization with correct configuration."""
        worker = SentimentAnalysisWorker(worker_config, db_session_factory)
        
        assert worker.worker_id == "test-sentiment-worker"
        assert ProcessingStage.SENTIMENT_ANALYSIS in worker.config.stages
        assert worker.batch_optimize_models == True
        assert worker.preload_models == False
    
    def test_worker_initialization_invalid_stage(self, db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="invalid-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],  # Wrong stage
            batch_size=5
        )
        
        with pytest.raises(ValueError, match="requires SENTIMENT_ANALYSIS stage"):
            SentimentAnalysisWorker(invalid_config, db_session_factory)
    
    @patch('germansentiment.SentimentModel')
    @patch('nltk.sentiment.vader.SentimentIntensityAnalyzer')
    @patch('textblob_de.TextBlobDE')
    def test_successful_batch_processing(self, mock_textblob, mock_vader, mock_german_model,
                                       worker_config, db_session_factory, sample_entries):
        """Test successful batch processing with mocked sentiment models."""
        # Setup mock German sentiment model
        mock_german_instance = Mock()
        mock_german_instance.predict_sentiment.return_value = (
            ['positive', 'neutral'],  # predictions
            [
                {'positive': 0.8, 'neutral': 0.15, 'negative': 0.05},
                {'positive': 0.3, 'neutral': 0.6, 'negative': 0.1}
            ]  # probabilities
        )
        mock_german_model.return_value = mock_german_instance
        
        # Setup mock VADER analyzer
        mock_vader_instance = Mock()
        mock_vader_instance.polarity_scores.side_effect = [
            {'pos': 0.7, 'neu': 0.2, 'neg': 0.1, 'compound': 0.6},
            {'pos': 0.3, 'neu': 0.6, 'neg': 0.1, 'compound': 0.2}
        ]
        mock_vader.return_value = mock_vader_instance
        
        # Setup mock TextBlobDE
        mock_textblob_instance1 = Mock()
        mock_textblob_instance1.sentiment.polarity = 0.5
        mock_textblob_instance1.sentiment.subjectivity = 0.6
        
        mock_textblob_instance2 = Mock()
        mock_textblob_instance2.sentiment.polarity = 0.1
        mock_textblob_instance2.sentiment.subjectivity = 0.3
        
        mock_textblob.side_effect = [mock_textblob_instance1, mock_textblob_instance2]
        
        # Create worker
        worker = SentimentAnalysisWorker(worker_config, db_session_factory)
        
        # Process batch (only entries with both text fields)
        valid_entries = [entry for entry in sample_entries 
                        if entry.full_text and entry.english_text]
        results = worker.process_batch(valid_entries, ProcessingStage.SENTIMENT_ANALYSIS)
        
        # Verify results
        assert len(results) == 2
        assert results["test-entry-1"] == True
        assert results["test-entry-2"] == True
        
        # Verify database updates
        session = db_session_factory()
        updated_entry1 = session.query(Entry).filter_by(entry_id="test-entry-1").first()
        updated_entry2 = session.query(Entry).filter_by(entry_id="test-entry-2").first()
        
        # Check VADER scores
        assert updated_entry1.vader_pos == 0.7
        assert updated_entry1.vader_compound == 0.6
        assert updated_entry2.vader_pos == 0.3
        
        # Check German sentiment scores
        assert updated_entry1.german_sentiment_positive == 0.8
        assert updated_entry2.german_sentiment_positive == 0.3
        
        # Check TextBlobDE scores
        assert updated_entry1.tbd_polarity == 0.5
        assert updated_entry2.tbd_polarity == 0.1
        
        session.close()
    
    def test_missing_text_fields_handling(self, worker_config, db_session_factory, sample_entries):
        """Test handling of entries with missing text fields."""
        worker = SentimentAnalysisWorker(worker_config, db_session_factory)
        
        # Process entries with missing text fields
        incomplete_entries = [entry for entry in sample_entries 
                            if not entry.full_text or not entry.english_text]
        results = worker.process_batch(incomplete_entries, ProcessingStage.SENTIMENT_ANALYSIS)
        
        # All incomplete entries should fail
        assert results["test-entry-3"] == False  # Missing full_text
        assert results["test-entry-4"] == False  # Missing english_text
    
    def test_wrong_stage_processing(self, worker_config, db_session_factory, sample_entries):
        """Test that worker rejects processing wrong stages."""
        worker = SentimentAnalysisWorker(worker_config, db_session_factory)
        
        # Try to process with wrong stage
        results = worker.process_batch(sample_entries, ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # All entries should fail
        for entry in sample_entries:
            assert results[entry.entry_id] == False
    
    @patch('germansentiment.SentimentModel')
    def test_batch_optimization_fallback(self, mock_german_model, worker_config,
                                       db_session_factory, sample_entries):
        """Test fallback to individual processing when batch optimization fails."""
        # Setup mock to fail on batch processing
        mock_german_instance = Mock()
        mock_german_instance.predict_sentiment.side_effect = [
            Exception("Batch processing failed"),  # First call (batch) fails
            (['positive'], [{'positive': 0.8, 'neutral': 0.15, 'negative': 0.05}]),  # Individual calls succeed
            (['neutral'], [{'positive': 0.3, 'neutral': 0.6, 'negative': 0.1}])
        ]
        mock_german_model.return_value = mock_german_instance
        
        # Mock other models
        with patch('nltk.sentiment.vader.SentimentIntensityAnalyzer') as mock_vader, \
             patch('textblob_de.TextBlobDE') as mock_textblob:
            
            mock_vader_instance = Mock()
            mock_vader_instance.polarity_scores.return_value = {'pos': 0.5, 'neu': 0.3, 'neg': 0.2, 'compound': 0.3}
            mock_vader.return_value = mock_vader_instance
            
            mock_textblob_instance = Mock()
            mock_textblob_instance.sentiment.polarity = 0.2
            mock_textblob_instance.sentiment.subjectivity = 0.4
            mock_textblob.return_value = mock_textblob_instance
            
            worker = SentimentAnalysisWorker(worker_config, db_session_factory)
            
            # Process valid entries
            valid_entries = [entry for entry in sample_entries 
                           if entry.full_text and entry.english_text]
            results = worker.process_batch(valid_entries, ProcessingStage.SENTIMENT_ANALYSIS)
            
            # Should succeed with fallback processing
            assert results["test-entry-1"] == True
            assert results["test-entry-2"] == True
    
    @patch('germansentiment.SentimentModel')
    @patch('nltk.sentiment.vader.SentimentIntensityAnalyzer')
    @patch('textblob_de.TextBlobDE')
    def test_individual_processing_mode(self, mock_textblob, mock_vader, mock_german_model,
                                      db_session_factory, sample_entries):
        """Test individual processing mode (batch_optimize_models=False)."""
        # Create config with individual processing
        config = WorkerConfig(
            worker_id="individual-worker",
            stages=[ProcessingStage.SENTIMENT_ANALYSIS],
            batch_size=5,
            stage_configs={
                ProcessingStage.SENTIMENT_ANALYSIS.value: {
                    'batch_optimize_models': False
                }
            }
        )
        
        # Setup mocks for individual calls
        mock_german_instance = Mock()
        mock_german_instance.predict_sentiment.side_effect = [
            (['positive'], [{'positive': 0.8, 'neutral': 0.15, 'negative': 0.05}]),
            (['neutral'], [{'positive': 0.3, 'neutral': 0.6, 'negative': 0.1}])
        ]
        mock_german_model.return_value = mock_german_instance
        
        mock_vader_instance = Mock()
        mock_vader_instance.polarity_scores.return_value = {'pos': 0.5, 'neu': 0.3, 'neg': 0.2, 'compound': 0.3}
        mock_vader.return_value = mock_vader_instance
        
        mock_textblob_instance = Mock()
        mock_textblob_instance.sentiment.polarity = 0.2
        mock_textblob_instance.sentiment.subjectivity = 0.4
        mock_textblob.return_value = mock_textblob_instance
        
        worker = SentimentAnalysisWorker(config, db_session_factory)
        
        # Process valid entries
        valid_entries = [entry for entry in sample_entries 
                        if entry.full_text and entry.english_text]
        results = worker.process_batch(valid_entries, ProcessingStage.SENTIMENT_ANALYSIS)
        
        # Should succeed with individual processing
        assert results["test-entry-1"] == True
        assert results["test-entry-2"] == True
    
    def test_worker_health_status(self, worker_config, db_session_factory):
        """Test worker health status reporting."""
        worker = SentimentAnalysisWorker(worker_config, db_session_factory)
        
        health = worker.get_worker_specific_health()
        
        assert health['worker_type'] == 'SentimentAnalysisWorker'
        assert health['batch_optimize_models'] == True
        assert health['preload_models'] == False
        assert 'models_loaded' in health
        assert ProcessingStage.SENTIMENT_ANALYSIS.value in health['supported_stages']
    
    @patch('germansentiment.SentimentModel')
    @patch('nltk.sentiment.vader.SentimentIntensityAnalyzer')
    @patch('textblob_de.TextBlobDE')
    def test_database_error_handling(self, mock_textblob, mock_vader, mock_german_model,
                                   worker_config, db_session_factory, sample_entries):
        """Test handling of database errors during updates."""
        # Setup successful model responses
        mock_german_instance = Mock()
        mock_german_instance.predict_sentiment.return_value = (
            ['positive'], [{'positive': 0.8, 'neutral': 0.15, 'negative': 0.05}]
        )
        mock_german_model.return_value = mock_german_instance
        
        mock_vader_instance = Mock()
        mock_vader_instance.polarity_scores.return_value = {'pos': 0.7, 'neu': 0.2, 'neg': 0.1, 'compound': 0.6}
        mock_vader.return_value = mock_vader_instance
        
        mock_textblob_instance = Mock()
        mock_textblob_instance.sentiment.polarity = 0.5
        mock_textblob_instance.sentiment.subjectivity = 0.6
        mock_textblob.return_value = mock_textblob_instance
        
        # Create worker with mocked session factory that fails on commit
        def failing_session_factory():
            session = Mock()
            session.query.return_value.filter_by.return_value.update.side_effect = Exception("DB Error")
            return session
        
        worker = SentimentAnalysisWorker(worker_config, failing_session_factory)
        
        # Process valid entry
        valid_entries = [entry for entry in sample_entries 
                        if entry.full_text and entry.english_text]
        results = worker.process_batch(valid_entries[:1], ProcessingStage.SENTIMENT_ANALYSIS)
        
        # Should fail due to database error
        assert results[valid_entries[0].entry_id] == False
    
    def test_resource_cleanup(self, worker_config, db_session_factory):
        """Test resource cleanup functionality."""
        worker = SentimentAnalysisWorker(worker_config, db_session_factory)
        
        # Simulate loaded models
        worker._german_sentiment_model = Mock()
        worker._vader_analyzer = Mock()
        worker._textblob_initialized = True
        
        # Cleanup resources
        worker.cleanup_resources()
        
        # Verify resources are cleared
        assert worker._german_sentiment_model is None
        assert worker._vader_analyzer is None
        assert worker._textblob_initialized == False


if __name__ == "__main__":
    # Configure logging for test runs
    logging.basicConfig(level=logging.DEBUG)
    
    # Run tests
    pytest.main([__file__, "-v"])