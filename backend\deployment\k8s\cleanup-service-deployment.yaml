apiVersion: apps/v1
kind: Deployment
metadata:
  name: cleanup-service
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: cleanup-service
    app.kubernetes.io/part-of: distributed-etl
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: breaking-bright
      app.kubernetes.io/component: cleanup-service
  template:
    metadata:
      labels:
        app.kubernetes.io/name: breaking-bright
        app.kubernetes.io/component: cleanup-service
        app.kubernetes.io/part-of: distributed-etl
    spec:
      containers:
      - name: cleanup-service
        image: robertschulze/breaking-bright-worker:latest
        imagePullPolicy: Always
        command: ["python", "-m", "backend.app.distributed.stale_work_cleanup"]
        env:
        - name: WORKER_ID
          value: "cleanup-service"
        - name: CLEANUP_INTERVAL_MINUTES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: CLEANUP_INTERVAL_MINUTES
        - name: STALE_TIMEOUT_MINUTES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: STALE_TIMEOUT_MINUTES
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: database-url
        envFrom:
        - configMapRef:
            name: distributed-worker-config
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"
        volumeMounts:
        - name: cleanup-logs
          mountPath: /var/log/cleanup
      volumes:
      - name: cleanup-logs
        emptyDir: {}
      imagePullSecrets:
      - name: docker-registry-secret
      restartPolicy: Always