import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/services/duplicate_handler_service.dart';

void main() {
  group('Category View Pagination Deduplication Logic', () {
    test('should remove duplicates when combining existing and new news items', () {
      // Simulate existing news items in the category view
      final existingItems = [
        EntryWithSource(
          entryId: 'entry1',
          title: 'News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now().subtract(const Duration(hours: 1)),
          dupEntryId: 'dup1',
        ),
        EntryWithSource(
          entryId: 'entry2',
          title: 'News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now().subtract(const Duration(hours: 2)),
          dupEntryId: 'dup2',
        ),
      ];

      // Simulate new items loaded from API (including duplicates)
      final newItems = [
        // This is a duplicate of entry1 (same dupEntryId but older)
        EntryWithSource(
          entryId: 'entry3',
          title: 'News 1 Duplicate',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.now().subtract(const Duration(hours: 3)),
          dupEntryId: 'dup1', // Same as entry1
        ),
        // This is a new unique item
        EntryWithSource(
          entryId: 'entry4',
          title: 'News 4',
          link: 'https://example.com/4',
          source: 'source4',
          published: DateTime.now().subtract(const Duration(hours: 4)),
          dupEntryId: 'dup4',
        ),
        // Another duplicate of entry1 but newer
        EntryWithSource(
          entryId: 'entry5',
          title: 'News 1 Newer Duplicate',
          link: 'https://example.com/5',
          source: 'source5',
          published: DateTime.now(), // Most recent
          dupEntryId: 'dup1', // Same as entry1
        ),
      ];

      // Combine and deduplicate (simulating the logic in _loadMoreData)
      final combinedList = [...existingItems, ...newItems];
      final deduplicatedList = DuplicateHandlerService.removeDuplicates(combinedList);

      // Verify results
      expect(deduplicatedList.length, equals(3)); // Should have 3 unique items

      // Find the items by their dupEntryId
      final dup1Item = deduplicatedList.firstWhere((item) => item.dupEntryId == 'dup1');
      final dup2Item = deduplicatedList.firstWhere((item) => item.dupEntryId == 'dup2');
      final dup4Item = deduplicatedList.firstWhere((item) => item.dupEntryId == 'dup4');

      // Should keep the most recent item for dup1 (entry5)
      expect(dup1Item.entryId, equals('entry5'));
      expect(dup1Item.title, equals('News 1 Newer Duplicate'));

      // Should keep the original item for dup2
      expect(dup2Item.entryId, equals('entry2'));
      expect(dup2Item.title, equals('News 2'));

      // Should keep the new unique item
      expect(dup4Item.entryId, equals('entry4'));
      expect(dup4Item.title, equals('News 4'));
    });

    test('should handle items without dupEntryId correctly', () {
      final items = [
        EntryWithSource(
          entryId: 'entry1',
          title: 'News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now(),
          dupEntryId: null, // No duplicate ID
        ),
        EntryWithSource(
          entryId: 'entry2',
          title: 'News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now(),
          dupEntryId: '', // Empty duplicate ID
        ),
        EntryWithSource(
          entryId: 'entry3',
          title: 'News 3',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.now(),
          dupEntryId: 'dup3',
        ),
      ];

      final deduplicatedList = DuplicateHandlerService.removeDuplicates(items);

      // All items should be kept since they don't have duplicate IDs or have unique ones
      expect(deduplicatedList.length, equals(3));
      expect(deduplicatedList.map((item) => item.entryId).toList(), 
             containsAll(['entry1', 'entry2', 'entry3']));
    });

    test('should preserve order when no duplicates exist', () {
      final items = [
        EntryWithSource(
          entryId: 'entry1',
          title: 'News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now().subtract(const Duration(hours: 1)),
          dupEntryId: 'dup1',
        ),
        EntryWithSource(
          entryId: 'entry2',
          title: 'News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now().subtract(const Duration(hours: 2)),
          dupEntryId: 'dup2',
        ),
        EntryWithSource(
          entryId: 'entry3',
          title: 'News 3',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.now().subtract(const Duration(hours: 3)),
          dupEntryId: 'dup3',
        ),
      ];

      final deduplicatedList = DuplicateHandlerService.removeDuplicates(items);

      // All items should be kept in the same order
      expect(deduplicatedList.length, equals(3));
      expect(deduplicatedList[0].entryId, equals('entry1'));
      expect(deduplicatedList[1].entryId, equals('entry2'));
      expect(deduplicatedList[2].entryId, equals('entry3'));
    });

    test('should handle empty list gracefully', () {
      final List<EntryWithSource> emptyList = [];
      final deduplicatedList = DuplicateHandlerService.removeDuplicates(emptyList);

      expect(deduplicatedList.isEmpty, isTrue);
    });

    test('should simulate loop-based loading with multiple duplicate batches', () {
      // Simulate existing items in the category view
      final existingItems = [
        EntryWithSource(
          entryId: 'existing1',
          title: 'Existing News 1',
          link: 'https://example.com/existing1',
          source: 'source1',
          published: DateTime.now().subtract(const Duration(hours: 1)),
          dupEntryId: 'dup_existing1',
        ),
      ];

      // Simulate multiple batches from API calls
      final batch1 = List.generate(10, (index) => EntryWithSource(
        entryId: 'batch1_$index',
        title: 'Duplicate News $index',
        link: 'https://example.com/batch1_$index',
        source: 'source_batch1_$index',
        published: DateTime.now().subtract(Duration(hours: 2 + index)),
        dupEntryId: 'dup_existing1', // All duplicates
      ));

      final batch2 = List.generate(10, (index) => EntryWithSource(
        entryId: 'batch2_$index',
        title: 'More Duplicate News $index',
        link: 'https://example.com/batch2_$index',
        source: 'source_batch2_$index',
        published: DateTime.now().subtract(Duration(hours: 12 + index)),
        dupEntryId: 'dup_existing1', // All duplicates
      ));

      final batch3 = List.generate(5, (index) => EntryWithSource(
        entryId: 'batch3_$index',
        title: 'Unique News ${index + 2}',
        link: 'https://example.com/batch3_$index',
        source: 'source_batch3_$index',
        published: DateTime.now().subtract(Duration(hours: 22 + index)),
        dupEntryId: 'dup_unique_${index + 2}', // Finally unique items
      ));

      // Simulate the loop logic from _loadMoreData
      final List<EntryWithSource> allNewItems = [];
      const int pageSize = 10;
      const double minUniqueRatio = 0.5;

      // First iteration - add batch1
      allNewItems.addAll(batch1);
      var combinedList = [...existingItems, ...allNewItems];
      var deduplicatedList = DuplicateHandlerService.removeDuplicates(combinedList);
      var newUniqueItemsCount = deduplicatedList.length - existingItems.length;

      // Should not have enough unique items (all are duplicates)
      expect(newUniqueItemsCount < (pageSize * minUniqueRatio), isTrue);
      expect(newUniqueItemsCount, equals(0)); // All duplicates

      // Second iteration - add batch2
      allNewItems.addAll(batch2);
      combinedList = [...existingItems, ...allNewItems];
      deduplicatedList = DuplicateHandlerService.removeDuplicates(combinedList);
      newUniqueItemsCount = deduplicatedList.length - existingItems.length;

      // Still should not have enough unique items (all are duplicates)
      expect(newUniqueItemsCount < (pageSize * minUniqueRatio), isTrue);
      expect(newUniqueItemsCount, equals(0)); // Still all duplicates

      // Third iteration - add batch3
      allNewItems.addAll(batch3);
      combinedList = [...existingItems, ...allNewItems];
      deduplicatedList = DuplicateHandlerService.removeDuplicates(combinedList);
      newUniqueItemsCount = deduplicatedList.length - existingItems.length;

      // Now should have enough unique items
      expect(newUniqueItemsCount >= (pageSize * minUniqueRatio), isTrue);
      expect(newUniqueItemsCount, equals(5)); // 5 unique items from batch3
      expect(deduplicatedList.length, equals(6)); // 1 existing + 5 unique new items

      // Verify the unique items are present
      final uniqueItems = deduplicatedList.where((item) =>
        item.dupEntryId?.startsWith('dup_unique_') == true).toList();
      expect(uniqueItems.length, equals(5));
    });
  });
}
