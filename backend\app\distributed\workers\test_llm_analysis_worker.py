"""
Integration tests for LLMAnalysisWorker.

Tests the LLM analysis worker with mock LLM responses and various scenarios
including successful analysis, rate limiting, and error handling.
"""

import pytest
import logging
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from typing import List

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.app.distributed.workers.llm_analysis_worker import LLMAnalysisWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry, Base

logger = logging.getLogger(__name__)


class TestLLMAnalysisWorker:
    """Test suite for LLMAnalysisWorker."""
    
    @pytest.fixture
    def db_session_factory(self):
        """Create an in-memory SQLite database for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        
        def get_session():
            return SessionLocal()
        
        return get_session
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        return WorkerConfig(
            worker_id="test-llm-worker",
            stages=[ProcessingStage.LLM_ANALYSIS],
            batch_size=3,
            heartbeat_interval_seconds=30,
            stage_configs={
                ProcessingStage.LLM_ANALYSIS.value: {
                    'llm_calls_per_entry': 3,
                    'max_input_length': 2000,
                    'batch_optimize_calls': True,
                    'rate_limit_buffer': 0.1
                }
            }
        )
    
    @pytest.fixture
    def sample_entries(self, db_session_factory):
        """Create sample entries for testing."""
        session = db_session_factory()
        
        entries = [
            Entry(
                entry_id="test-entry-1",
                title="Positive Community News",
                link="https://example.com/positive",
                description="Great news about local community success",
                published=datetime.now(timezone.utc),
                full_text="This is a wonderful story about how the local community came together to help each other during difficult times. The cooperation and mutual support shown by residents demonstrates the best of human nature."
            ),
            Entry(
                entry_id="test-entry-2", 
                title="Technology Update",
                link="https://example.com/tech",
                description="Latest technology developments",
                published=datetime.now(timezone.utc),
                full_text="The new software update includes several improvements to user interface and performance. Users can expect faster loading times and better accessibility features."
            ),
            Entry(
                entry_id="test-entry-3",
                title="Incomplete Article",
                link="https://example.com/incomplete",
                description="Article missing full text",
                published=datetime.now(timezone.utc),
                full_text=None  # Missing full text
            )
        ]
        
        for entry in entries:
            session.add(entry)
        session.commit()
        
        # Refresh entries to ensure they're loaded
        for entry in entries:
            session.refresh(entry)
        
        # Detach entries from session so they can be used independently
        session.expunge_all()
        session.close()
        
        return entries
    
    def test_worker_initialization(self, worker_config, db_session_factory):
        """Test worker initialization with correct configuration."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        assert worker.worker_id == "test-llm-worker"
        assert ProcessingStage.LLM_ANALYSIS in worker.config.stages
        assert worker.llm_calls_per_entry == 3
        assert worker.max_input_length == 2000
        assert worker.batch_optimize_calls == True
    
    def test_worker_initialization_invalid_stage(self, db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="invalid-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],  # Wrong stage
            batch_size=5
        )
        
        with pytest.raises(ValueError, match="requires LLM_ANALYSIS stage"):
            LLMAnalysisWorker(invalid_config, db_session_factory)
    
    def test_successful_batch_processing(self, worker_config, db_session_factory, sample_entries):
        """Test successful batch processing with mock LLM responses."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        # Mock the _call_llm method to return consistent responses
        def mock_call_llm(prompt, schema, input_text, max_tokens=400, temperature=0.1, top_p=0.95):
            return {
                "advertisement": {
                    "is_advertisement": 0.1,
                    "reason": "This appears to be a news article, not an advertisement."
                },
                "sentiment": {
                    "positive": 0.7,
                    "neutral": 0.2,
                    "negative": 0.1,
                    "reason": "The article has a positive tone."
                }
            }, "test_provider", "test_model"
        
        worker._call_llm = mock_call_llm
        
        # Process batch (excluding entry with missing full_text)
        valid_entries = [entry for entry in sample_entries if entry.full_text]
        results = worker.process_batch(valid_entries, ProcessingStage.LLM_ANALYSIS)
        
        # Verify results
        assert len(results) == 2
        assert results["test-entry-1"] == True
        assert results["test-entry-2"] == True
        
        # Verify database updates
        session = db_session_factory()
        updated_entry1 = session.query(Entry).filter_by(entry_id="test-entry-1").first()
        updated_entry2 = session.query(Entry).filter_by(entry_id="test-entry-2").first()
        
        # Check LLM analysis results
        assert updated_entry1.llm_is_ad == 0.1
        assert abs(updated_entry1.llm_positive - 0.7) < 0.01
        assert abs(updated_entry1.llm_neutral - 0.2) < 0.01
        assert abs(updated_entry1.llm_negative - 0.1) < 0.01
        assert updated_entry1.llm_iterations == 3
        
        assert updated_entry2.llm_is_ad == 0.1
        assert abs(updated_entry2.llm_positive - 0.7) < 0.01
        
        session.close()
    
    def test_missing_full_text_handling(self, worker_config, db_session_factory, sample_entries):
        """Test handling of entries with missing full_text field."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        # Process entry with missing full_text
        incomplete_entry = [entry for entry in sample_entries if not entry.full_text][0]
        results = worker.process_batch([incomplete_entry], ProcessingStage.LLM_ANALYSIS)
        
        # Should fail due to missing full_text
        assert results["test-entry-3"] == False
    
    def test_wrong_stage_processing(self, worker_config, db_session_factory, sample_entries):
        """Test that worker rejects processing wrong stages."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        # Try to process with wrong stage
        results = worker.process_batch(sample_entries, ProcessingStage.SENTIMENT_ANALYSIS)
        
        # All entries should fail
        for entry in sample_entries:
            assert results[entry.entry_id] == False
    
    def test_llm_call_failure_handling(self, worker_config, db_session_factory, sample_entries):
        """Test handling of LLM call failures."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        # Mock _call_llm to always fail
        def mock_failing_call_llm(*args, **kwargs):
            raise Exception("LLM API call failed")
        
        worker._call_llm = mock_failing_call_llm
        
        # Process valid entry
        valid_entries = [entry for entry in sample_entries if entry.full_text]
        results = worker.process_batch(valid_entries[:1], ProcessingStage.LLM_ANALYSIS)
        
        # Should fail due to LLM call failures
        assert results[valid_entries[0].entry_id] == False
    
    def test_partial_llm_call_success(self, worker_config, db_session_factory, sample_entries):
        """Test handling when some LLM calls succeed and others fail."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        call_count = 0
        
        def mock_partial_call_llm(prompt, schema, input_text, max_tokens=400, temperature=0.1, top_p=0.95):
            nonlocal call_count
            call_count += 1
            
            if call_count <= 2:  # First 2 calls succeed
                return {
                    "advertisement": {
                        "is_advertisement": 0.1,
                        "reason": "This appears to be a news article."
                    },
                    "sentiment": {
                        "positive": 0.6 + (call_count * 0.1),  # Varying scores
                        "neutral": 0.3,
                        "negative": 0.1 - (call_count * 0.05),
                        "reason": f"Analysis {call_count}"
                    }
                }, "test_provider", "test_model"
            else:  # Third call fails
                raise Exception("Third LLM call failed")
        
        worker._call_llm = mock_partial_call_llm
        
        # Process valid entry
        valid_entries = [entry for entry in sample_entries if entry.full_text]
        results = worker.process_batch(valid_entries[:1], ProcessingStage.LLM_ANALYSIS)
        
        # Should succeed with partial results
        assert results[valid_entries[0].entry_id] == True
        
        # Verify database shows 2 iterations instead of 3
        session = db_session_factory()
        updated_entry = session.query(Entry).filter_by(entry_id=valid_entries[0].entry_id).first()
        assert updated_entry.llm_iterations == 2
        assert updated_entry.llm_break_reason == "LLM call failed: Third LLM call failed"
        session.close()
    
    def test_llm_statistics_calculation(self, worker_config, db_session_factory):
        """Test calculation of statistics from multiple LLM calls."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        # Test with multiple score sets
        scores_list = [
            {"positive": 0.8, "neutral": 0.1, "negative": 0.1},
            {"positive": 0.6, "neutral": 0.3, "negative": 0.1},
            {"positive": 0.7, "neutral": 0.2, "negative": 0.1}
        ]
        
        stats = worker._calculate_llm_statistics(scores_list)
        
        # Check means
        assert abs(stats["positive_mean"] - 0.7) < 0.01  # (0.8 + 0.6 + 0.7) / 3
        assert abs(stats["neutral_mean"] - 0.2) < 0.01   # (0.1 + 0.3 + 0.2) / 3
        assert abs(stats["negative_mean"] - 0.1) < 0.01  # (0.1 + 0.1 + 0.1) / 3
        
        # Check differences
        assert abs(stats["positive_diff"] - 0.2) < 0.01  # 0.8 - 0.6
        assert abs(stats["neutral_diff"] - 0.2) < 0.01   # 0.3 - 0.1
        assert abs(stats["negative_diff"] - 0.0) < 0.01  # 0.1 - 0.1
        
        # Check that standard deviations are calculated
        assert "positive_std" in stats
        assert "neutral_std" in stats
        assert "negative_std" in stats
    
    def test_individual_processing_mode(self, db_session_factory, sample_entries):
        """Test individual processing mode (batch_optimize_calls=False)."""
        config = WorkerConfig(
            worker_id="individual-worker",
            stages=[ProcessingStage.LLM_ANALYSIS],
            batch_size=3,
            stage_configs={
                ProcessingStage.LLM_ANALYSIS.value: {
                    'batch_optimize_calls': False
                }
            }
        )
        
        worker = LLMAnalysisWorker(config, db_session_factory)
        
        # Mock successful LLM calls
        def mock_call_llm(prompt, schema, input_text, max_tokens=400, temperature=0.1, top_p=0.95):
            return {
                "advertisement": {
                    "is_advertisement": 0.2,
                    "reason": "Individual processing test."
                },
                "sentiment": {
                    "positive": 0.5,
                    "neutral": 0.3,
                    "negative": 0.2,
                    "reason": "Individual analysis"
                }
            }, "test_provider", "test_model"
        
        worker._call_llm = mock_call_llm
        
        # Process valid entries
        valid_entries = [entry for entry in sample_entries if entry.full_text]
        results = worker.process_batch(valid_entries, ProcessingStage.LLM_ANALYSIS)
        
        # Should succeed with individual processing
        assert results["test-entry-1"] == True
        assert results["test-entry-2"] == True
    
    def test_worker_health_status(self, worker_config, db_session_factory):
        """Test worker health status reporting."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        health = worker.get_worker_specific_health()
        
        assert health['worker_type'] == 'LLMAnalysisWorker'
        assert health['llm_calls_per_entry'] == 3
        assert health['max_input_length'] == 2000
        assert health['batch_optimize_calls'] == True
        assert health['providers_configured'] > 0
        assert 'rate_limit_status' in health
        assert ProcessingStage.LLM_ANALYSIS.value in health['supported_stages']
    
    def test_database_error_handling(self, worker_config, db_session_factory, sample_entries):
        """Test handling of database errors during updates."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        # Mock successful LLM calls
        def mock_call_llm(prompt, schema, input_text, max_tokens=400, temperature=0.1, top_p=0.95):
            return {
                "advertisement": {
                    "is_advertisement": 0.1,
                    "reason": "Test analysis"
                },
                "sentiment": {
                    "positive": 0.7,
                    "neutral": 0.2,
                    "negative": 0.1,
                    "reason": "Test sentiment"
                }
            }, "test_provider", "test_model"
        
        worker._call_llm = mock_call_llm
        
        # Create worker with mocked session factory that fails on commit
        def failing_session_factory():
            session = Mock()
            session.query.return_value.filter_by.return_value.update.side_effect = Exception("DB Error")
            return session
        
        worker.db_session_factory = failing_session_factory
        
        # Process valid entry
        valid_entries = [entry for entry in sample_entries if entry.full_text]
        results = worker.process_batch(valid_entries[:1], ProcessingStage.LLM_ANALYSIS)
        
        # Should fail due to database error
        assert results[valid_entries[0].entry_id] == False
    
    def test_input_text_truncation(self, worker_config, db_session_factory):
        """Test that input text is properly truncated to max_input_length."""
        worker = LLMAnalysisWorker(worker_config, db_session_factory)
        
        # Create entry with very long text
        session = db_session_factory()
        long_entry = Entry(
            entry_id="long-entry",
            title="Long Article",
            description="Very long description",
            full_text="A" * 5000,  # Much longer than max_input_length (2000)
            published=datetime.now(timezone.utc)
        )
        session.add(long_entry)
        session.commit()
        session.refresh(long_entry)
        session.expunge(long_entry)
        session.close()
        
        # Track the input_text length passed to _call_llm
        received_input_length = None
        
        def mock_call_llm(prompt, schema, input_text, max_tokens=400, temperature=0.1, top_p=0.95):
            nonlocal received_input_length
            received_input_length = len(input_text)
            return {
                "advertisement": {"is_advertisement": 0.1, "reason": "Test"},
                "sentiment": {"positive": 0.7, "neutral": 0.2, "negative": 0.1, "reason": "Test"}
            }, "test_provider", "test_model"
        
        worker._call_llm = mock_call_llm
        
        # Process the long entry
        results = worker.process_batch([long_entry], ProcessingStage.LLM_ANALYSIS)
        
        # Verify input was truncated
        assert received_input_length <= worker.max_input_length
        assert results["long-entry"] == True


if __name__ == "__main__":
    # Configure logging for test runs
    logging.basicConfig(level=logging.DEBUG)
    
    # Run tests
    pytest.main([__file__, "-v"])