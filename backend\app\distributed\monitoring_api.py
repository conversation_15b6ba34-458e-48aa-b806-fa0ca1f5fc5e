"""
Monitoring API for distributed ETL processing.

This module provides REST API endpoints for accessing metrics, monitoring data,
and system health information for the distributed ETL system.
"""

import json
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading

from backend.app.distributed.metrics_collector import MetricsCollector, AlertSeverity
from backend.app.distributed.worker_manager import WorkerManager

logger = logging.getLogger(__name__)


class MonitoringAPIHandler(BaseHTTPRequestHandler):
    """HTTP request handler for monitoring API endpoints."""
    
    def __init__(self, *args, 
                 metrics_collector: Optional[MetricsCollector] = None,
                 worker_manager: Optional[WorkerManager] = None,
                 **kwargs):
        """Initialize handler with monitoring components."""
        self.metrics_collector = metrics_collector
        self.worker_manager = worker_manager
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests for monitoring endpoints."""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            query_params = parse_qs(parsed_url.query)
            
            # Route requests to appropriate handlers
            if path == '/api/health':
                self._handle_health_check()
            elif path == '/api/metrics/system':
                self._handle_system_metrics(query_params)
            elif path == '/api/metrics/workers':
                self._handle_worker_metrics(query_params)
            elif path == '/api/metrics/timeseries':
                self._handle_timeseries_metrics(query_params)
            elif path == '/api/alerts':
                self._handle_alerts(query_params)
            elif path == '/api/dashboard':
                self._handle_dashboard_data()
            elif path == '/api/workers':
                self._handle_workers_status()
            elif path == '/api/workers/scaling':
                self._handle_scaling_analysis()
            elif path.startswith('/api/workers/'):
                worker_id = path.split('/')[-1]
                self._handle_worker_detail(worker_id)
            else:
                self._send_error(404, "Endpoint not found")
                
        except Exception as e:
            logger.error(f"Error handling monitoring API request: {e}", exc_info=True)
            self._send_error(500, "Internal server error")
    
    def do_POST(self):
        """Handle POST requests for monitoring endpoints."""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            if path.startswith('/api/alerts/') and path.endswith('/resolve'):
                alert_id = path.split('/')[-2]
                self._handle_resolve_alert(alert_id)
            elif path == '/api/workers/start':
                self._handle_start_workers()
            elif path == '/api/workers/stop':
                self._handle_stop_workers()
            elif path.startswith('/api/workers/') and path.endswith('/start'):
                worker_id = path.split('/')[-2]
                self._handle_start_worker(worker_id)
            elif path.startswith('/api/workers/') and path.endswith('/stop'):
                worker_id = path.split('/')[-2]
                self._handle_stop_worker(worker_id)
            else:
                self._send_error(404, "Endpoint not found")
                
        except Exception as e:
            logger.error(f"Error handling monitoring API POST request: {e}", exc_info=True)
            self._send_error(500, "Internal server error")
    
    def _handle_health_check(self):
        """Handle health check endpoint."""
        health_data = {
            'status': 'healthy',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'components': {
                'metrics_collector': self.metrics_collector.is_running if self.metrics_collector else False,
                'worker_manager': self.worker_manager.is_running if self.worker_manager else False
            }
        }
        
        # Determine overall health
        all_healthy = all(health_data['components'].values())
        status_code = 200 if all_healthy else 503
        
        if not all_healthy:
            health_data['status'] = 'degraded'
        
        self._send_json_response(health_data, status_code)
    
    def _handle_system_metrics(self, query_params: Dict[str, List[str]]):
        """Handle system metrics endpoint."""
        if not self.metrics_collector:
            self._send_error(503, "Metrics collector not available")
            return
        
        # Get query parameters
        limit = self._get_query_param_int(query_params, 'limit', 1)
        
        # Get system metrics
        metrics_history = self.metrics_collector.get_system_metrics_history(limit)
        
        response = {
            'metrics': [m.to_dict() for m in metrics_history],
            'count': len(metrics_history)
        }
        
        self._send_json_response(response)
    
    def _handle_worker_metrics(self, query_params: Dict[str, List[str]]):
        """Handle worker metrics endpoint."""
        if not self.metrics_collector:
            self._send_error(503, "Metrics collector not available")
            return
        
        # Get query parameters
        worker_id = self._get_query_param(query_params, 'worker_id')
        
        # Get worker metrics
        worker_metrics = self.metrics_collector.get_worker_metrics(worker_id)
        
        response = {
            'workers': {k: v.to_dict() for k, v in worker_metrics.items()},
            'count': len(worker_metrics)
        }
        
        self._send_json_response(response)
    
    def _handle_timeseries_metrics(self, query_params: Dict[str, List[str]]):
        """Handle timeseries metrics endpoint."""
        if not self.metrics_collector:
            self._send_error(503, "Metrics collector not available")
            return
        
        # Get query parameters
        metric_name = self._get_query_param(query_params, 'metric')
        worker_id = self._get_query_param(query_params, 'worker_id')
        since_str = self._get_query_param(query_params, 'since')
        limit = self._get_query_param_int(query_params, 'limit', 100)
        
        # Parse since parameter
        since = None
        if since_str:
            try:
                since = datetime.fromisoformat(since_str.replace('Z', '+00:00'))
            except ValueError:
                self._send_error(400, "Invalid 'since' parameter format")
                return
        
        # Build labels filter
        labels = {}
        if worker_id:
            labels['worker_id'] = worker_id
        
        # Get metric points
        metric_points = self.metrics_collector.get_metric_points(
            metric_name=metric_name,
            labels=labels if labels else None,
            since=since,
            limit=limit
        )
        
        response = {
            'metric_name': metric_name,
            'points': [p.to_dict() for p in metric_points],
            'count': len(metric_points)
        }
        
        self._send_json_response(response)
    
    def _handle_alerts(self, query_params: Dict[str, List[str]]):
        """Handle alerts endpoint."""
        if not self.metrics_collector:
            self._send_error(503, "Metrics collector not available")
            return
        
        # Get query parameters
        severity_str = self._get_query_param(query_params, 'severity')
        resolved_str = self._get_query_param(query_params, 'resolved')
        
        # Parse parameters
        severity = None
        if severity_str:
            try:
                severity = AlertSeverity(severity_str.lower())
            except ValueError:
                self._send_error(400, f"Invalid severity: {severity_str}")
                return
        
        resolved = None
        if resolved_str:
            resolved = resolved_str.lower() == 'true'
        
        # Get alerts
        alerts = self.metrics_collector.get_alerts(severity=severity, resolved=resolved)
        
        response = {
            'alerts': [a.to_dict() for a in alerts],
            'count': len(alerts)
        }
        
        self._send_json_response(response)
    
    def _handle_dashboard_data(self):
        """Handle dashboard data endpoint."""
        if not self.metrics_collector:
            self._send_error(503, "Metrics collector not available")
            return
        
        dashboard_data = self.metrics_collector.get_dashboard_data()
        
        # Add worker manager data if available
        if self.worker_manager:
            dashboard_data['worker_manager'] = self.worker_manager.get_manager_metrics()
        
        self._send_json_response(dashboard_data)
    
    def _handle_workers_status(self):
        """Handle workers status endpoint."""
        if not self.worker_manager:
            self._send_error(503, "Worker manager not available")
            return
        
        worker_status = self.worker_manager.get_worker_status()
        manager_metrics = self.worker_manager.get_manager_metrics()
        
        response = {
            'workers': worker_status,
            'summary': {
                'total_workers': manager_metrics.get('total_workers', 0),
                'running_workers': manager_metrics.get('running_workers', 0),
                'healthy_workers': manager_metrics.get('healthy_workers', 0)
            }
        }
        
        self._send_json_response(response)
    
    def _handle_scaling_analysis(self):
        """Handle scaling analysis endpoint."""
        if not self.worker_manager:
            self._send_error(503, "Worker manager not available")
            return
        
        scaling_decisions = self.worker_manager.analyze_scaling_needs()
        
        response = {
            'scaling_decisions': [
                {
                    'stage': decision.stage.value,
                    'current_workers': decision.current_workers,
                    'recommended_workers': decision.recommended_workers,
                    'reason': decision.reason,
                    'urgency': decision.urgency
                }
                for decision in scaling_decisions
            ],
            'count': len(scaling_decisions)
        }
        
        self._send_json_response(response)
    
    def _handle_worker_detail(self, worker_id: str):
        """Handle individual worker detail endpoint."""
        if not self.worker_manager:
            self._send_error(503, "Worker manager not available")
            return
        
        worker_status = self.worker_manager.get_worker_status()
        
        if worker_id not in worker_status:
            self._send_error(404, f"Worker {worker_id} not found")
            return
        
        worker_data = worker_status[worker_id]
        
        # Add metrics data if available
        if self.metrics_collector:
            worker_metrics = self.metrics_collector.get_worker_metrics(worker_id)
            if worker_id in worker_metrics:
                worker_data['metrics'] = worker_metrics[worker_id].to_dict()
        
        self._send_json_response(worker_data)
    
    def _handle_resolve_alert(self, alert_id: str):
        """Handle alert resolution endpoint."""
        if not self.metrics_collector:
            self._send_error(503, "Metrics collector not available")
            return
        
        success = self.metrics_collector.resolve_alert(alert_id)
        
        if success:
            response = {'message': f'Alert {alert_id} resolved successfully'}
            self._send_json_response(response)
        else:
            self._send_error(404, f"Alert {alert_id} not found")
    
    def _handle_start_workers(self):
        """Handle start all workers endpoint."""
        if not self.worker_manager:
            self._send_error(503, "Worker manager not available")
            return
        
        results = self.worker_manager.start_all_workers()
        
        response = {
            'message': 'Worker start operation completed',
            'results': results,
            'successful': sum(1 for success in results.values() if success),
            'total': len(results)
        }
        
        self._send_json_response(response)
    
    def _handle_stop_workers(self):
        """Handle stop all workers endpoint."""
        if not self.worker_manager:
            self._send_error(503, "Worker manager not available")
            return
        
        results = self.worker_manager.stop_all_workers()
        
        response = {
            'message': 'Worker stop operation completed',
            'results': results,
            'successful': sum(1 for success in results.values() if success),
            'total': len(results)
        }
        
        self._send_json_response(response)
    
    def _handle_start_worker(self, worker_id: str):
        """Handle start individual worker endpoint."""
        if not self.worker_manager:
            self._send_error(503, "Worker manager not available")
            return
        
        success = self.worker_manager.start_worker(worker_id)
        
        if success:
            response = {'message': f'Worker {worker_id} started successfully'}
            self._send_json_response(response)
        else:
            self._send_error(400, f"Failed to start worker {worker_id}")
    
    def _handle_stop_worker(self, worker_id: str):
        """Handle stop individual worker endpoint."""
        if not self.worker_manager:
            self._send_error(503, "Worker manager not available")
            return
        
        success = self.worker_manager.stop_worker(worker_id)
        
        if success:
            response = {'message': f'Worker {worker_id} stopped successfully'}
            self._send_json_response(response)
        else:
            self._send_error(400, f"Failed to stop worker {worker_id}")
    
    def _get_query_param(self, query_params: Dict[str, List[str]], param: str) -> Optional[str]:
        """Get a single query parameter value."""
        values = query_params.get(param, [])
        return values[0] if values else None
    
    def _get_query_param_int(self, query_params: Dict[str, List[str]], param: str, default: int) -> int:
        """Get a query parameter as integer with default."""
        value_str = self._get_query_param(query_params, param)
        if value_str:
            try:
                return int(value_str)
            except ValueError:
                pass
        return default
    
    def _send_json_response(self, data: Dict[str, Any], status_code: int = 200):
        """Send JSON response."""
        json_data = json.dumps(data, indent=2, default=str)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(json_data.encode('utf-8'))))
        self.send_header('Access-Control-Allow-Origin', '*')  # Enable CORS
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def _send_error(self, status_code: int, message: str):
        """Send error response."""
        error_data = {
            'error': message,
            'status_code': status_code,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        self._send_json_response(error_data, status_code)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Override to use our logger instead of stderr."""
        logger.debug(f"Monitoring API request: {format % args}")


class MonitoringAPIServer:
    """
    HTTP server for monitoring API endpoints.
    
    Provides REST API for accessing metrics, worker status, and system monitoring data.
    """
    
    def __init__(self, 
                 port: int,
                 metrics_collector: Optional[MetricsCollector] = None,
                 worker_manager: Optional[WorkerManager] = None):
        """
        Initialize monitoring API server.
        
        Args:
            port: Port to listen on
            metrics_collector: Metrics collector instance
            worker_manager: Worker manager instance
        """
        self.port = port
        self.metrics_collector = metrics_collector
        self.worker_manager = worker_manager
        self.server: Optional[HTTPServer] = None
        self.server_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
    
    def start(self) -> None:
        """Start the monitoring API server."""
        try:
            # Create handler class with dependencies
            def handler_factory(*args, **kwargs):
                return MonitoringAPIHandler(
                    *args,
                    metrics_collector=self.metrics_collector,
                    worker_manager=self.worker_manager,
                    **kwargs
                )
            
            # Create and start server
            self.server = HTTPServer(('0.0.0.0', self.port), handler_factory)
            
            # Start server in separate thread
            self.server_thread = threading.Thread(
                target=self._server_loop,
                name=f"monitoring-api-{self.port}",
                daemon=True
            )
            self.server_thread.start()
            
            logger.info(f"Monitoring API server started on port {self.port}")
            logger.info(f"API endpoints available at http://localhost:{self.port}/api/")
            
        except Exception as e:
            logger.error(f"Failed to start monitoring API server: {e}")
            raise
    
    def stop(self, timeout: int = 5) -> None:
        """Stop the monitoring API server."""
        if not self.server:
            return
        
        logger.info(f"Stopping monitoring API server on port {self.port}")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Shutdown server
        self.server.shutdown()
        
        # Wait for server thread to finish
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=timeout)
        
        self.server.server_close()
        logger.info("Monitoring API server stopped")
    
    def _server_loop(self) -> None:
        """Main server loop."""
        try:
            self.server.serve_forever()
        except Exception as e:
            if not self._shutdown_event.is_set():
                logger.error(f"Monitoring API server error: {e}")
    
    @property
    def is_running(self) -> bool:
        """Check if server is running."""
        return (
            self.server is not None and 
            self.server_thread is not None and 
            self.server_thread.is_alive()
        )
    
    def get_api_endpoints(self) -> Dict[str, str]:
        """Get list of available API endpoints."""
        if not self.port:
            return {}
        
        base_url = f"http://localhost:{self.port}/api"
        return {
            'health': f"{base_url}/health",
            'system_metrics': f"{base_url}/metrics/system",
            'worker_metrics': f"{base_url}/metrics/workers",
            'timeseries_metrics': f"{base_url}/metrics/timeseries",
            'alerts': f"{base_url}/alerts",
            'dashboard': f"{base_url}/dashboard",
            'workers': f"{base_url}/workers",
            'scaling_analysis': f"{base_url}/workers/scaling"
        }


class AlertManager:
    """
    Manages alerting functionality for the monitoring system.
    
    Provides alert notification, escalation, and integration with external systems.
    """
    
    def __init__(self, metrics_collector: MetricsCollector):
        """
        Initialize alert manager.
        
        Args:
            metrics_collector: Metrics collector instance
        """
        self.metrics_collector = metrics_collector
        self._notification_handlers: List[Callable] = []
        self._is_running = False
        self._alert_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        
        logger.info("Initialized AlertManager")
    
    def start(self) -> None:
        """Start the alert manager."""
        if self._is_running:
            return
        
        logger.info("Starting AlertManager...")
        
        self._shutdown_event.clear()
        self._alert_thread = threading.Thread(
            target=self._alert_loop,
            name="alert-manager",
            daemon=False
        )
        self._alert_thread.start()
        self._is_running = True
        
        logger.info("AlertManager started")
    
    def stop(self, timeout: int = 30) -> None:
        """Stop the alert manager."""
        if not self._is_running:
            return
        
        logger.info("Stopping AlertManager...")
        
        self._shutdown_event.set()
        
        if self._alert_thread and self._alert_thread.is_alive():
            self._alert_thread.join(timeout=timeout)
        
        self._is_running = False
        logger.info("AlertManager stopped")
    
    def add_notification_handler(self, handler: Callable) -> None:
        """
        Add a notification handler for alerts.
        
        Args:
            handler: Function that takes an Alert object and sends notifications
        """
        self._notification_handlers.append(handler)
        logger.info("Added alert notification handler")
    
    def _alert_loop(self) -> None:
        """Main alert processing loop."""
        logger.info("Starting alert processing loop")
        
        last_alert_check = datetime.now(timezone.utc)
        
        while not self._shutdown_event.is_set():
            try:
                # Get new alerts since last check
                current_time = datetime.now(timezone.utc)
                new_alerts = [
                    alert for alert in self.metrics_collector.get_alerts(resolved=False)
                    if alert.timestamp > last_alert_check
                ]
                
                # Process new alerts
                for alert in new_alerts:
                    self._process_alert(alert)
                
                last_alert_check = current_time
                
                # Wait before next check
                self._shutdown_event.wait(10.0)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in alert processing loop: {e}", exc_info=True)
                self._shutdown_event.wait(5.0)
        
        logger.info("Alert processing loop stopped")
    
    def _process_alert(self, alert) -> None:
        """Process a single alert."""
        try:
            logger.info(f"Processing alert: {alert.name} ({alert.severity.value})")
            
            # Send notifications
            for handler in self._notification_handlers:
                try:
                    handler(alert)
                except Exception as e:
                    logger.error(f"Error in notification handler: {e}")
            
        except Exception as e:
            logger.error(f"Error processing alert {alert.id}: {e}")


# Example notification handlers

def log_notification_handler(alert) -> None:
    """Simple log-based notification handler."""
    log_level = {
        AlertSeverity.INFO: logging.INFO,
        AlertSeverity.WARNING: logging.WARNING,
        AlertSeverity.ERROR: logging.ERROR,
        AlertSeverity.CRITICAL: logging.CRITICAL
    }.get(alert.severity, logging.INFO)
    
    logger.log(log_level, f"ALERT: {alert.name} - {alert.message}")


def webhook_notification_handler(webhook_url: str):
    """Create a webhook notification handler."""
    import requests
    
    def handler(alert) -> None:
        try:
            payload = {
                'alert': alert.to_dict(),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            response = requests.post(
                webhook_url,
                json=payload,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()
            
            logger.debug(f"Sent alert notification to webhook: {alert.id}")
            
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
    
    return handler