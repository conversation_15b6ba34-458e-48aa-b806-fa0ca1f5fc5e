apiVersion: v1
kind: Secret
metadata:
  name: distributed-worker-secrets
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: distributed-etl
type: Opaque
data:
  # Base64 encoded secrets - replace with actual values
  # Use: echo -n "your-secret-value" | base64
  database-url: ""  # Replace with base64 encoded DATABASE_URL
  openai-api-key: ""  # Replace with base64 encoded OPENAI_API_KEY
  ionos-api-key: ""  # Replace with base64 encoded IONOS_API_KEY
  huggingface-token: ""  # Replace with base64 encoded HUGGINGFACE_TOKEN
  stability-api-key: ""  # Replace with base64 encoded STABILITY_API_KEY
---
apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: distributed-etl
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ""  # Replace with base64 encoded Docker registry credentials