-- Overview of duplicate news entries
-- Shows groups of duplicates with counts, ordered by the largest groups first
SELECT 
    dup_entry_id, 
    COUNT(entry_id) AS duplicate_count,
    MIN(published) AS first_published,
    MAX(published) AS last_published,
    COUNT(DISTINCT source) AS source_count
FROM 
    entries 
WHERE 
    entry_id <> dup_entry_id 
GROUP BY 
    dup_entry_id 
ORDER BY 
    duplicate_count DESC
FETCH FIRST 50 ROWS ONLY
