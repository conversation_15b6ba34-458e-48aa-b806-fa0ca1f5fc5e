"""
Tests for worker delay configurations and behavior.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import time
from datetime import datetime, timezone

from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.distributed_worker import DistributedWorker
from backend.app.distributed.processing_stage import ProcessingStage


class MockDelayTestWorker(DistributedWorker):
    """Mock worker for testing delay behavior."""
    
    def __init__(self, config, db_session_factory):
        super().__init__(config, db_session_factory)
        self.process_calls = []
        self.delay_calls = []
    
    def process_batch(self, entries, stage):
        """Mock process_batch that tracks calls."""
        self.process_calls.append((entries, stage, time.time()))
        return {"processed": len(entries)}


def test_worker_config_delay_settings():
    """Test that delay settings are properly configured."""
    config = WorkerConfig(
        worker_id="test-worker",
        stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
        processing_delay_seconds=0.5,
        no_work_delay_seconds=3.0
    )

    assert config.processing_delay_seconds == 0.5
    assert config.no_work_delay_seconds == 3.0


def test_worker_config_delay_validation():
    """Test that delay validation works correctly."""
    # Test negative processing_delay_seconds
    with pytest.raises(ValueError, match="processing_delay_seconds must be a non-negative number"):
        WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            processing_delay_seconds=-1.0
        )

    # Test negative no_work_delay_seconds
    with pytest.raises(ValueError, match="no_work_delay_seconds must be a non-negative number"):
        WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            no_work_delay_seconds=-2.0
        )


def test_worker_config_from_env():
    """Test that delay settings can be loaded from environment variables."""
    from backend.app.distributed.worker_config import WorkerConfigLoader

    with patch.dict('os.environ', {
        'WORKER_ID': 'test-worker',
        'WORKER_STAGES': 'download_full_text',
        'WORKER_PROCESSING_DELAY_SECONDS': '0.2',
        'WORKER_NO_WORK_DELAY_SECONDS': '5.0'
    }):
        config = WorkerConfigLoader.from_environment()

        assert config.processing_delay_seconds == 0.2
        assert config.no_work_delay_seconds == 5.0


@patch('backend.app.distributed.distributed_worker.WorkQueueManager')
def test_worker_delay_between_stages(mock_work_queue_class):
    """Test that worker adds delay between processing different stages."""
    # Setup mock work queue manager
    mock_work_queue = Mock()
    mock_work_queue_class.return_value = mock_work_queue
    mock_work_queue.claim_batch.return_value = []  # No work available
    
    # Create worker config with short delays for testing
    config = WorkerConfig(
        worker_id="test-delay-worker",
        stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.LLM_ANALYSIS],
        batch_size=1,
        processing_delay_seconds=0.1,  # Short delay between stages
        no_work_delay_seconds=0.2      # Short delay when no work found
    )
    
    # Create mock database session factory
    mock_db_session_factory = Mock()
    mock_session = Mock()
    mock_db_session_factory.return_value = mock_session
    
    # Create worker
    worker = MockDelayTestWorker(config, mock_db_session_factory)
    
    # Mock the shutdown event to stop after a few iterations
    with patch.object(worker, '_shutdown_event') as mock_shutdown:
        call_count = 0
        def mock_wait(delay):
            nonlocal call_count
            call_count += 1
            # Stop after a few calls to prevent infinite loop
            if call_count >= 3:
                mock_shutdown.is_set.return_value = True
            time.sleep(0.01)  # Small actual delay for testing
        
        mock_shutdown.is_set.side_effect = lambda: call_count >= 3
        mock_shutdown.wait.side_effect = mock_wait
        
        # Run the worker loop
        start_time = time.time()
        worker._worker_loop()
        end_time = time.time()
        
        # Verify that delays were called
        assert mock_shutdown.wait.call_count >= 2
        
        # Verify delay amounts - should include both processing delays and no-work delay
        delay_calls = [call[0][0] for call in mock_shutdown.wait.call_args_list]
        
        # Should have processing delays (0.1s) and at least one no-work delay (0.2s)
        assert 0.1 in delay_calls  # Processing delay between stages
        assert 0.2 in delay_calls  # No work delay


@patch('backend.app.distributed.distributed_worker.WorkQueueManager')
def test_worker_no_work_delay(mock_work_queue_class):
    """Test that worker uses no_work_delay_seconds when no work is found."""
    # Setup mock work queue manager
    mock_work_queue = Mock()
    mock_work_queue_class.return_value = mock_work_queue
    mock_work_queue.claim_batch.return_value = []  # No work available
    
    # Create worker config
    config = WorkerConfig(
        worker_id="test-no-work-worker",
        stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
        batch_size=1,
        processing_delay_seconds=0.1,
        no_work_delay_seconds=0.5
    )
    
    # Create mock database session factory
    mock_db_session_factory = Mock()
    mock_session = Mock()
    mock_db_session_factory.return_value = mock_session
    
    # Create worker
    worker = MockDelayTestWorker(config, mock_db_session_factory)
    
    # Mock the shutdown event to stop after one iteration
    with patch.object(worker, '_shutdown_event') as mock_shutdown:
        call_count = 0
        def mock_wait(delay):
            nonlocal call_count
            call_count += 1
            # Stop after first no-work delay
            if delay == 0.5:  # no_work_delay_seconds
                mock_shutdown.is_set.return_value = True
            time.sleep(0.01)  # Small actual delay for testing
        
        mock_shutdown.is_set.side_effect = lambda: call_count > 1
        mock_shutdown.wait.side_effect = mock_wait
        
        # Run the worker loop
        worker._worker_loop()
        
        # Verify that the no-work delay was used
        delay_calls = [call[0][0] for call in mock_shutdown.wait.call_args_list]
        assert 0.5 in delay_calls  # no_work_delay_seconds should be called


def test_worker_config_serialization_includes_delays():
    """Test that delay settings are included in config serialization."""
    config = WorkerConfig(
        worker_id="test-worker",
        stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
        processing_delay_seconds=0.3,
        no_work_delay_seconds=4.0
    )
    
    config_dict = config.to_dict()
    
    assert "processing_delay_seconds" in config_dict
    assert "no_work_delay_seconds" in config_dict
    assert config_dict["processing_delay_seconds"] == 0.3
    assert config_dict["no_work_delay_seconds"] == 4.0


if __name__ == "__main__":
    pytest.main([__file__])
