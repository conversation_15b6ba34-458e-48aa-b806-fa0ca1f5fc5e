"""Add migration utilities support tables

Revision ID: 2bafd4be870a
Revises: 4af365669e73
Create Date: 2025-09-19 22:04:00.617307

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2bafd4be870a'
down_revision: Union[str, None] = '4af365669e73'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create migration_history table for tracking migration operations
    op.create_table(
        'migration_history',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('migration_id', sa.String(255), nullable=False),
        sa.Column('migration_type', sa.String(100), nullable=False),  # 'full', 'partial', 'rollback'
        sa.Column('source_mode', sa.String(50), nullable=False),      # 'monolithic', 'distributed', 'hybrid'
        sa.Column('target_mode', sa.String(50), nullable=False),      # 'monolithic', 'distributed', 'hybrid'
        sa.Column('status', sa.String(50), nullable=False),           # 'in_progress', 'completed', 'failed', 'rolled_back'
        sa.Column('started_at', sa.TIMESTAMP(), nullable=False),
        sa.Column('completed_at', sa.TIMESTAMP(), nullable=True),
        sa.Column('entries_migrated', sa.Integer(), default=0),
        sa.Column('entries_validated', sa.Integer(), default=0),
        sa.Column('validation_errors', sa.CLOB(), nullable=True),     # JSON array of validation errors
        sa.Column('config', sa.CLOB(), nullable=True),               # JSON migration configuration
        sa.Column('result_summary', sa.CLOB(), nullable=True),       # JSON result summary
        sa.Column('error_message', sa.CLOB(), nullable=True),
        sa.Column('rollback_performed', sa.Boolean(), default=False),
        sa.Column('created_by', sa.String(255), nullable=True),      # User or system that initiated migration
    )
    
    # Create indexes for migration history
    op.create_index('idx_migration_history_status', 'migration_history', ['status'])
    op.create_index('idx_migration_history_started_at', 'migration_history', ['started_at'])
    op.create_index('idx_migration_history_migration_id', 'migration_history', ['migration_id'])
    
    # Create migration_backup_metadata table for tracking backups
    op.create_table(
        'migration_backup_metadata',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('backup_id', sa.String(255), nullable=False, unique=True),
        sa.Column('migration_id', sa.String(255), nullable=False),   # Links to migration_history
        sa.Column('backup_type', sa.String(100), nullable=False),    # 'full', 'incremental', 'schema_only'
        sa.Column('backup_location', sa.String(1000), nullable=False), # File path or table name
        sa.Column('backup_size_bytes', sa.BigInteger(), nullable=True),
        sa.Column('entries_backed_up', sa.Integer(), default=0),
        sa.Column('backup_format', sa.String(50), nullable=False),   # 'table', 'json', 'parquet'
        sa.Column('created_at', sa.TIMESTAMP(), nullable=False),
        sa.Column('expires_at', sa.TIMESTAMP(), nullable=True),      # When backup can be cleaned up
        sa.Column('checksum', sa.String(255), nullable=True),        # For integrity verification
        sa.Column('metadata', sa.CLOB(), nullable=True),             # JSON metadata about backup
    )
    
    # Create indexes for backup metadata
    op.create_index('idx_backup_metadata_migration_id', 'migration_backup_metadata', ['migration_id'])
    op.create_index('idx_backup_metadata_created_at', 'migration_backup_metadata', ['created_at'])
    op.create_index('idx_backup_metadata_expires_at', 'migration_backup_metadata', ['expires_at'])
    
    # Create migration_validation_results table for detailed validation tracking
    op.create_table(
        'migration_validation_results',
        sa.Column('id', sa.Integer(), primary_key=True),
        sa.Column('migration_id', sa.String(255), nullable=False),
        sa.Column('validation_type', sa.String(100), nullable=False), # 'consistency', 'integrity', 'comparison'
        sa.Column('entry_id', sa.String(64), nullable=True),          # Specific entry being validated
        sa.Column('validation_status', sa.String(50), nullable=False), # 'passed', 'failed', 'warning'
        sa.Column('error_category', sa.String(100), nullable=True),   # 'data_inconsistency', 'missing_field', etc.
        sa.Column('error_message', sa.CLOB(), nullable=True),
        sa.Column('expected_value', sa.CLOB(), nullable=True),        # JSON of expected values
        sa.Column('actual_value', sa.CLOB(), nullable=True),          # JSON of actual values
        sa.Column('validation_context', sa.CLOB(), nullable=True),    # JSON context information
        sa.Column('validated_at', sa.TIMESTAMP(), nullable=False),
        sa.Column('severity', sa.String(20), nullable=False, default='medium'), # 'low', 'medium', 'high', 'critical'
    )
    
    # Create indexes for validation results
    op.create_index('idx_validation_results_migration_id', 'migration_validation_results', ['migration_id'])
    op.create_index('idx_validation_results_status', 'migration_validation_results', ['validation_status'])
    op.create_index('idx_validation_results_severity', 'migration_validation_results', ['severity'])
    op.create_index('idx_validation_results_entry_id', 'migration_validation_results', ['entry_id'])


def downgrade() -> None:
    # Drop validation results table and indexes
    op.drop_index('idx_validation_results_entry_id', table_name='migration_validation_results')
    op.drop_index('idx_validation_results_severity', table_name='migration_validation_results')
    op.drop_index('idx_validation_results_status', table_name='migration_validation_results')
    op.drop_index('idx_validation_results_migration_id', table_name='migration_validation_results')
    op.drop_table('migration_validation_results')
    
    # Drop backup metadata table and indexes
    op.drop_index('idx_backup_metadata_expires_at', table_name='migration_backup_metadata')
    op.drop_index('idx_backup_metadata_created_at', table_name='migration_backup_metadata')
    op.drop_index('idx_backup_metadata_migration_id', table_name='migration_backup_metadata')
    op.drop_table('migration_backup_metadata')
    
    # Drop migration history table and indexes
    op.drop_index('idx_migration_history_migration_id', table_name='migration_history')
    op.drop_index('idx_migration_history_started_at', table_name='migration_history')
    op.drop_index('idx_migration_history_status', table_name='migration_history')
    op.drop_table('migration_history')
