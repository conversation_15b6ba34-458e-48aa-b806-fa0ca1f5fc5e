apiVersion: v1
kind: ConfigMap
metadata:
  name: breaking-bright-etl-config
data:
  news_sources.yaml: |
    rss_sources:
      - url: https://www.spiegel.de/schlagzeilen/index.rss
        clean_name: Spiegel
        entry_id_from: link,published
      - url: https://www.faz.net/rss/aktuell/
        clean_name: FAZ
        entry_id_from: link,published
      - url: https://www.stern.de/feed/standard/all/
        clean_name: Stern
        entry_id_from: link,published
      - url: https://www.tagesschau.de/xml/rss2/
        clean_name: Tagesschau
        entry_id_from: link,published
      - url: https://www.welt.de/feeds/latest.rss
        clean_name: Welt
        entry_id_from: link,published
      - url: https://newsfeed.zeit.de/index
        clean_name: Zeit
        entry_id_from: link,published