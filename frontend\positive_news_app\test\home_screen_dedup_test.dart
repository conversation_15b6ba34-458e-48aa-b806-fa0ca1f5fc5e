import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/services/duplicate_handler_service.dart';
import 'package:breakingbright/models/news_model.dart';

void main() {
  group('Home Screen vs Category View Deduplication Tests', () {
    test('Both views use same deduplication logic for consistency', () {
      // Test data with the exact problematic entries from the bug report
      final testItems = [
        EntryWithSource(
          entryId:
              'a712401f72f635208b72268837e9990698726c4edbe3bac3ea59d5f13a174c24',
          title: 'Arbeitslosigkeit sinkt im Dezember auf 2,8 Millionen',
          link: 'https://example.com/1',
          source: 'tagesschau.de',
          published: DateTime.parse('2024-12-30T14:00:00Z'),
          dupEntryId:
              'a78015d6b781bab123b30775800e9e2bdd4f7e4fedd23fcfcc58d0600bc2ef74',
          iptcNewscode: '09000000',
        ),
        EntryWithSource(
          entryId:
              '95b69921aa7be93a1e1be8e8d3f19f6159660fcdb0adccc3e0c7e2f3c83a13dc',
          title: 'Arbeitslosigkeit sinkt im Dezember auf 2,8 Millionen',
          link: 'https://example.com/2',
          source: 'zeit.de',
          published: DateTime.parse('2024-12-30T13:00:00Z'), // Earlier time
          dupEntryId:
              'a78015d6b781bab123b30775800e9e2bdd4f7e4fedd23fcfcc58d0600bc2ef74', // Same dup_entry_id
          iptcNewscode: '09000000',
        ),
        EntryWithSource(
          entryId: 'unique_entry_1',
          title: 'Unique Arbeit News',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.parse('2024-12-30T12:00:00Z'),
          dupEntryId: 'different_dup_id',
          iptcNewscode: '09000000',
        ),
      ];

      // Both views should use the same deduplication logic
      final homeScreenResult =
          DuplicateHandlerService.removeDuplicates(testItems);
      final categoryViewResult =
          DuplicateHandlerService.removeDuplicates(testItems);

      // Both should produce identical results
      expect(homeScreenResult.length, categoryViewResult.length,
          reason: 'Both views should produce same deduplication results');

      expect(homeScreenResult.length, 2,
          reason: 'Should have 2 items after removing 1 duplicate');

      expect(homeScreenResult.first.entryId, categoryViewResult.first.entryId,
          reason: 'Both views should keep the same item from duplicate group');

      // Both should keep the more recent entry (a712401f...)
      expect(homeScreenResult.first.entryId,
          'a712401f72f635208b72268837e9990698726c4edbe3bac3ea59d5f13a174c24',
          reason: 'Should keep the more recent entry');
      expect(categoryViewResult.first.entryId,
          'a712401f72f635208b72268837e9990698726c4edbe3bac3ea59d5f13a174c24',
          reason: 'Should keep the more recent entry');

      // Verify the unique entry is also kept
      final homeIds = homeScreenResult.map((item) => item.entryId).toSet();
      final categoryIds =
          categoryViewResult.map((item) => item.entryId).toSet();

      expect(homeIds.contains('unique_entry_1'), true,
          reason: 'Should keep unique entries');
      expect(categoryIds.contains('unique_entry_1'), true,
          reason: 'Should keep unique entries');

      // Verify the older duplicate is removed
      expect(
          homeIds.contains(
              '95b69921aa7be93a1e1be8e8d3f19f6159660fcdb0adccc3e0c7e2f3c83a13dc'),
          false,
          reason: 'Should remove older duplicate');
      expect(
          categoryIds.contains(
              '95b69921aa7be93a1e1be8e8d3f19f6159660fcdb0adccc3e0c7e2f3c83a13dc'),
          false,
          reason: 'Should remove older duplicate');
    });

    test('Home screen applies limiting after deduplication', () {
      // Test that home screen limits results for layout consistency
      final testItems = [
        EntryWithSource(
          entryId: 'item1',
          title: 'News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'dup1',
        ),
        EntryWithSource(
          entryId: 'item2',
          title: 'News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'),
          dupEntryId: 'dup2',
        ),
        EntryWithSource(
          entryId: 'item3',
          title: 'News 3',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.parse('2024-01-01T10:00:00Z'),
          dupEntryId: 'dup3',
        ),
        EntryWithSource(
          entryId: 'item4',
          title: 'News 4',
          link: 'https://example.com/4',
          source: 'source4',
          published: DateTime.parse('2024-01-01T09:00:00Z'),
          dupEntryId: 'dup4',
        ),
      ];

      // Apply deduplication (should keep all since they're unique)
      final dedupedItems = DuplicateHandlerService.removeDuplicates(testItems);

      // Home screen applies limiting for layout consistency
      final limitPerCategory = 3;
      final limitedItems = dedupedItems.take(limitPerCategory).toList();

      expect(dedupedItems.length, 4, reason: 'All items should be unique');
      expect(limitedItems.length, 3,
          reason: 'Home screen should limit to 3 items per category');
    });

    test('Category view keeps all non-duplicates for pagination', () {
      // Test that category view keeps all non-duplicates (doesn't limit back)
      final testItems = [
        EntryWithSource(
          entryId: 'dup1_newer',
          title: 'Duplicate - Newer',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'dup_group_1',
        ),
        EntryWithSource(
          entryId: 'dup1_older',
          title: 'Duplicate - Older',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'),
          dupEntryId: 'dup_group_1',
        ),
        EntryWithSource(
          entryId: 'unique_1',
          title: 'Unique Item 1',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.parse('2024-01-01T10:00:00Z'),
          dupEntryId: 'unique_dup_1',
        ),
        EntryWithSource(
          entryId: 'unique_2',
          title: 'Unique Item 2',
          link: 'https://example.com/4',
          source: 'source4',
          published: DateTime.parse('2024-01-01T09:00:00Z'),
          dupEntryId: 'unique_dup_2',
        ),
        EntryWithSource(
          entryId: 'unique_3',
          title: 'Unique Item 3',
          link: 'https://example.com/5',
          source: 'source5',
          published: DateTime.parse('2024-01-01T08:00:00Z'),
          dupEntryId: 'unique_dup_3',
        ),
      ];

      // Apply deduplication (category view logic)
      final dedupedItems = DuplicateHandlerService.removeDuplicates(testItems);

      // Category view keeps ALL non-duplicates (doesn't limit back)
      // This is the key difference from home screen

      expect(testItems.length, 5, reason: 'Should start with 5 items');
      expect(dedupedItems.length, 4,
          reason: 'Should have 4 items after removing 1 duplicate');

      // Should keep the newer duplicate and all unique items
      final keptIds = dedupedItems.map((item) => item.entryId).toSet();
      expect(keptIds.contains('dup1_newer'), true,
          reason: 'Should keep newer duplicate');
      expect(keptIds.contains('dup1_older'), false,
          reason: 'Should remove older duplicate');
      expect(keptIds.contains('unique_1'), true,
          reason: 'Should keep all unique items');
      expect(keptIds.contains('unique_2'), true,
          reason: 'Should keep all unique items');
      expect(keptIds.contains('unique_3'), true,
          reason: 'Should keep all unique items');
    });

    test(
        'API method consistency - getHomeScreenData delegates to getNewsByCategories',
        () {
      // This test documents that the fix ensures both methods use same logic
      // The fix ensures that getHomeScreenData() now calls getNewsByCategories()
      // This means both methods will use identical deduplication logic

      expect(true, true,
          reason: 'getHomeScreenData now delegates to getNewsByCategories');
    });

    test('Edge case - all items are duplicates', () {
      final allDuplicates = [
        EntryWithSource(
          entryId: 'dup1',
          title: 'Same Story',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'same_dup_id',
        ),
        EntryWithSource(
          entryId: 'dup2',
          title: 'Same Story',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'),
          dupEntryId: 'same_dup_id',
        ),
        EntryWithSource(
          entryId: 'dup3',
          title: 'Same Story',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.parse('2024-01-01T10:00:00Z'),
          dupEntryId: 'same_dup_id',
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(allDuplicates);
      expect(result.length, 1,
          reason: 'Should keep only one item from all duplicates');
      expect(result.first.entryId, 'dup1',
          reason: 'Should keep the most recent item');
    });

    test('Edge case - null dupEntryId values', () {
      final mixedItems = [
        EntryWithSource(
          entryId: 'no_dup_1',
          title: 'Unique Story 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: null, // No duplicate ID
        ),
        EntryWithSource(
          entryId: 'no_dup_2',
          title: 'Unique Story 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'),
          dupEntryId: null, // No duplicate ID
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(mixedItems);
      expect(result.length, 2,
          reason: 'Should keep all items with null dupEntryId');
    });
  });
}
