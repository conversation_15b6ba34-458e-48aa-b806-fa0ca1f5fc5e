"""
Unit tests for ImagePromptWorker.

Tests the image prompt worker functionality including batch processing,
LLM API calls, rate limiting, and database operations.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from backend.app.distributed.workers.image_prompt_worker import Image<PERSON>rompt<PERSON>orker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestImagePromptWorker:
    """Test suite for ImagePromptWorker."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        config = WorkerConfig(
            worker_id="test-image-prompt-worker",
            stages=[ProcessingStage.GENERATE_IMAGE_PROMPTS],
            batch_size=5,
            heartbeat_interval=30,
            claim_timeout=300,
            stage_configs={
                ProcessingStage.GENERATE_IMAGE_PROMPTS: {
                    'sentiment_threshold': 0.5,
                    'ad_threshold': 0.5,
                    'max_input_length': 4000,
                    'max_tokens': 200,
                    'temperature': 0.7,
                    'batch_optimize_llm': False,
                    'llm_providers': [
                        {
                            'provider': 'local',
                            'base_url': 'http://localhost:1234/v1',
                            'model': 'test-model'
                        }
                    ]
                }
            }
        )
        return config
    
    @pytest.fixture
    def sample_entries(self):
        """Create sample Entry objects for testing."""
        entries = []
        for i in range(3):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.image_prompt = None  # No existing prompt
            entry.full_text = f"This is the full text content for test entry {i}. It contains detailed positive news information."
            entry.llm_positive = 0.7  # Above sentiment threshold
            entry.llm_is_ad = 0.2  # Below ad threshold
            entries.append(entry)
        return entries
    
    @pytest.fixture
    def sample_llm_response(self):
        """Create a sample LLM response."""
        return {
            "keywords": "news, positive, uplifting, community, celebration, success, achievement"
        }
    
    def test_worker_initialization(self, worker_config, mock_db_session_factory):
        """Test worker initialization with correct configuration."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            assert worker.sentiment_threshold == 0.5
            assert worker.ad_threshold == 0.5
            assert worker.max_input_length == 4000
            assert worker.max_tokens == 200
            assert worker.temperature == 0.7
            assert worker.batch_optimize_llm is False
            assert len(worker._providers) == 1
    
    def test_worker_initialization_invalid_stage(self, mock_db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.FEED_DOWNLOAD],  # Wrong stage
            batch_size=5
        )
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            with pytest.raises(ValueError, match="ImagePromptWorker requires GENERATE_IMAGE_PROMPTS stage"):
                ImagePromptWorker(invalid_config, mock_db_session_factory)
    
    def test_process_batch_wrong_stage(self, worker_config, mock_db_session_factory, sample_entries):
        """Test process_batch rejects wrong processing stage."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(sample_entries, ProcessingStage.FEED_DOWNLOAD)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(sample_entries)
    
    def test_process_batch_no_full_text(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries without full_text."""
        # Create entries without full_text
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.image_prompt = None
            entry.full_text = None  # Missing full_text
            entry.llm_positive = 0.7
            entry.llm_is_ad = 0.2
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_existing_prompt(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries that already have image prompts."""
        # Create entries with existing prompts
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.image_prompt = f"existing, prompt, keywords, {i}"  # Already has prompt
            entry.full_text = f"Full text {i}"
            entry.llm_positive = 0.7
            entry.llm_is_ad = 0.2
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
            
            # Should return True for all entries (already have prompts)
            assert all(success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_low_sentiment(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries with low sentiment scores."""
        # Create entries with low sentiment scores
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.image_prompt = None
            entry.full_text = f"Full text {i}"
            entry.llm_positive = 0.3  # Below threshold
            entry.llm_is_ad = 0.2
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
            
            # Should return True for all entries (doesn't meet criteria, but not an error)
            assert all(success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_high_ad_score(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries with high ad scores."""
        # Create entries with high ad scores
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.image_prompt = None
            entry.full_text = f"Full text {i}"
            entry.llm_positive = 0.7
            entry.llm_is_ad = 0.8  # Above threshold
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
            
            # Should return True for all entries (doesn't meet criteria, but not an error)
            assert all(success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_successful_generation(self, worker_config, mock_db_session_factory, sample_entries, sample_llm_response):
        """Test successful image prompt generation."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Mock the LLM call and database update
            with patch.object(worker, '_call_llm', return_value=(sample_llm_response, 'local', 'test-model')) as mock_llm:
                with patch.object(worker, '_update_entry_image_prompt', return_value=True) as mock_update:
                    results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
                    
                    # Verify all entries were processed successfully
                    assert all(success for success in results.values())
                    assert len(results) == len(sample_entries)
                    
                    # Verify LLM calls were made
                    assert mock_llm.call_count == len(sample_entries)
                    
                    # Verify database updates were called
                    assert mock_update.call_count == len(sample_entries)
    
    def test_process_batch_llm_failure(self, worker_config, mock_db_session_factory, sample_entries):
        """Test handling of LLM API failures."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Mock LLM call to fail
            with patch.object(worker, '_call_llm', side_effect=RuntimeError("All LLM providers failed")):
                results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
                
                # Should return False for all entries
                assert all(not success for success in results.values())
                assert len(results) == len(sample_entries)
    
    def test_process_batch_empty_keywords(self, worker_config, mock_db_session_factory, sample_entries):
        """Test handling of empty keywords from LLM."""
        empty_response = {"keywords": ""}  # Empty keywords
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_call_llm', return_value=(empty_response, 'local', 'test-model')):
                results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
                
                # Should return False for all entries (empty keywords)
                assert all(not success for success in results.values())
                assert len(results) == len(sample_entries)
    
    def test_process_batch_single_keyword(self, worker_config, mock_db_session_factory, sample_entries):
        """Test handling of single keyword (no commas) from LLM."""
        single_keyword_response = {"keywords": "news"}  # Single keyword, no commas
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_call_llm', return_value=(single_keyword_response, 'local', 'test-model')):
                with patch.object(worker, '_update_entry_image_prompt', return_value=True):
                    results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
                    
                    # Should still succeed (single keyword is valid)
                    assert all(success for success in results.values())
                    assert len(results) == len(sample_entries)
    
    def test_process_batch_rate_limiting(self, worker_config, mock_db_session_factory, sample_entries):
        """Test rate limiting functionality."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Mock rate limiting to return False
            with patch.object(worker, '_can_make_llm_call', return_value=False):
                results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_IMAGE_PROMPTS)
                
                # Should return False for all entries (rate limited)
                assert all(not success for success in results.values())
                assert len(results) == len(sample_entries)
    
    def test_process_batch_text_truncation(self, worker_config, mock_db_session_factory, sample_llm_response):
        """Test input text truncation for long content."""
        # Create entry with very long text
        entry = Mock(spec=Entry)
        entry.entry_id = "test-entry-long"
        entry.title = "Short Title"
        entry.image_prompt = None
        entry.full_text = "A" * 5000  # Very long text (exceeds max_input_length)
        entry.llm_positive = 0.7
        entry.llm_is_ad = 0.2
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_call_llm', return_value=(sample_llm_response, 'local', 'test-model')) as mock_llm:
                with patch.object(worker, '_update_entry_image_prompt', return_value=True):
                    results = worker.process_batch([entry], ProcessingStage.GENERATE_IMAGE_PROMPTS)
                    
                    # Verify processing succeeded
                    assert results[entry.entry_id] is True
                    
                    # Verify LLM was called with truncated text
                    mock_llm.assert_called_once()
                    call_args = mock_llm.call_args[1]
                    input_text = call_args['input_text']
                    assert len(input_text) <= worker.max_input_length
    
    @patch('backend.app.distributed.workers.image_prompt_worker.requests.post')
    def test_call_local_provider_success(self, mock_post, worker_config, mock_db_session_factory):
        """Test successful local LLM provider call."""
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": '{"keywords": "news, positive, uplifting"}'
                }
            }]
        }
        mock_post.return_value = mock_response
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            provider_cfg = {
                'provider': 'local',
                'base_url': 'http://localhost:1234/v1',
                'model': 'test-model'
            }
            
            result, model = worker._call_local_provider(
                provider_cfg, "test prompt", worker._llm_schema, "test input", 200, 0.7, 0.95
            )
            
            assert result == {"keywords": "news, positive, uplifting"}
            assert model == "test-model"
            mock_post.assert_called_once()
    
    @patch('backend.app.distributed.workers.image_prompt_worker.requests.post')
    def test_call_openai_provider_success(self, mock_post, worker_config, mock_db_session_factory):
        """Test successful OpenAI provider call."""
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": '{"keywords": "news, positive, uplifting"}'
                }
            }]
        }
        mock_post.return_value = mock_response
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            provider_cfg = {
                'provider': 'openai',
                'api_key': 'test-key',
                'model': 'gpt-3.5-turbo'
            }
            
            result, model = worker._call_openai_provider(
                provider_cfg, "test prompt", worker._llm_schema, "test input", 200, 0.7, 0.95
            )
            
            assert result == {"keywords": "news, positive, uplifting"}
            assert model == "gpt-3.5-turbo"
            mock_post.assert_called_once()
    
    @patch('backend.app.distributed.workers.image_prompt_worker.requests.post')
    def test_call_anthropic_provider_success(self, mock_post, worker_config, mock_db_session_factory):
        """Test successful Anthropic provider call."""
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "content": [{
                "text": '{"keywords": "news, positive, uplifting"}'
            }]
        }
        mock_post.return_value = mock_response
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            provider_cfg = {
                'provider': 'anthropic',
                'api_key': 'test-key',
                'model': 'claude-3-haiku-20240307'
            }
            
            result, model = worker._call_anthropic_provider(
                provider_cfg, "test prompt", worker._llm_schema, "test input", 200, 0.7, 0.95
            )
            
            assert result == {"keywords": "news, positive, uplifting"}
            assert model == "claude-3-haiku-20240307"
            mock_post.assert_called_once()
    
    def test_call_llm_provider_unsupported(self, worker_config, mock_db_session_factory):
        """Test unsupported provider error."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            provider_cfg = {'provider': 'unsupported'}
            
            with pytest.raises(ValueError, match="Unsupported LLM provider: unsupported"):
                worker._call_llm_provider(
                    provider_cfg, "test prompt", worker._llm_schema, "test input"
                )
    
    def test_call_llm_with_fallback(self, worker_config, mock_db_session_factory):
        """Test LLM call with provider fallback."""
        # Configure multiple providers
        worker_config.stage_configs[ProcessingStage.GENERATE_IMAGE_PROMPTS]['llm_providers'] = [
            {'provider': 'openai', 'api_key': 'test-key1', 'model': 'gpt-3.5-turbo'},
            {'provider': 'local', 'base_url': 'http://localhost:1234/v1', 'model': 'test-model'}
        ]
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Mock first provider to fail, second to succeed
            with patch.object(worker, '_call_llm_provider') as mock_provider:
                mock_provider.side_effect = [
                    Exception("First provider failed"),
                    ({"keywords": "news, positive, uplifting"}, "test-model")
                ]
                
                result, provider, model = worker._call_llm(
                    "test prompt", worker._llm_schema, "test input"
                )
                
                assert result == {"keywords": "news, positive, uplifting"}
                assert provider == 'local'
                assert model == "test-model"
                assert mock_provider.call_count == 2
    
    def test_call_llm_all_providers_fail(self, worker_config, mock_db_session_factory):
        """Test LLM call when all providers fail."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Mock all providers to fail
            with patch.object(worker, '_call_llm_provider', side_effect=Exception("Provider failed")):
                with pytest.raises(RuntimeError, match="All LLM providers failed"):
                    worker._call_llm("test prompt", worker._llm_schema, "test input")
    
    def test_update_entry_image_prompt_success(self, worker_config, mock_db_session_factory):
        """Test successful database update for entry image prompt."""
        mock_session = mock_db_session_factory()
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_image_prompt("test-entry", "news, positive, uplifting")
            
            assert result is True
            mock_session.query.assert_called_once_with(Entry)
            mock_session.commit.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_image_prompt_database_error(self, worker_config, mock_db_session_factory):
        """Test database error handling in entry update."""
        mock_session = mock_db_session_factory()
        mock_session.commit.side_effect = Exception("Database error")
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_image_prompt("test-entry", "news, positive, uplifting")
            
            assert result is False
            mock_session.rollback.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_image_prompt_session_error(self, worker_config):
        """Test session creation error in entry update."""
        def failing_session_factory():
            raise Exception("Session creation failed")
        
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, failing_session_factory)
            
            result = worker._update_entry_image_prompt("test-entry", "news, positive, uplifting")
            
            assert result is False
    
    def test_can_make_llm_call(self, worker_config, mock_db_session_factory):
        """Test rate limiting check."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Currently always returns True (simple implementation)
            assert worker._can_make_llm_call() is True
    
    def test_get_worker_specific_health(self, worker_config, mock_db_session_factory):
        """Test worker-specific health information."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.get_health_status') as mock_health:
                mock_health.return_value = {'status': 'healthy'}
                
                worker = ImagePromptWorker(worker_config, mock_db_session_factory)
                health = worker.get_worker_specific_health()
                
                assert health['worker_type'] == 'ImagePromptWorker'
                assert health['sentiment_threshold'] == 0.5
                assert health['ad_threshold'] == 0.5
                assert health['max_input_length'] == 4000
                assert health['max_tokens'] == 200
                assert health['temperature'] == 0.7
                assert health['batch_optimize_llm'] is False
                assert health['llm_providers_count'] == 1
                assert ProcessingStage.GENERATE_IMAGE_PROMPTS.value in health['supported_stages']
    
    def test_cleanup_resources(self, worker_config, mock_db_session_factory):
        """Test resource cleanup."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Should not raise any exceptions
            worker.cleanup_resources()
    
    def test_local_provider_json_extraction(self, worker_config, mock_db_session_factory):
        """Test JSON extraction from local provider response with extra text."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            # Mock response with extra text around JSON
            with patch('backend.app.distributed.workers.image_prompt_worker.requests.post') as mock_post:
                mock_response = Mock()
                mock_response.raise_for_status.return_value = None
                mock_response.json.return_value = {
                    "choices": [{
                        "message": {
                            "content": 'Here are the keywords: {"keywords": "news, positive, uplifting"} Hope this helps!'
                        }
                    }]
                }
                mock_post.return_value = mock_response
                
                provider_cfg = {
                    'provider': 'local',
                    'base_url': 'http://localhost:1234/v1',
                    'model': 'test-model'
                }
                
                result, model = worker._call_local_provider(
                    provider_cfg, "test prompt", worker._llm_schema, "test input", 200, 0.7, 0.95
                )
                
                assert result == {"keywords": "news, positive, uplifting"}
                assert model == "test-model"
    
    def test_openai_provider_missing_api_key(self, worker_config, mock_db_session_factory):
        """Test OpenAI provider with missing API key."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            provider_cfg = {'provider': 'openai'}  # Missing api_key
            
            with pytest.raises(ValueError, match="OpenAI API key not provided"):
                worker._call_openai_provider(
                    provider_cfg, "test prompt", worker._llm_schema, "test input", 200, 0.7, 0.95
                )
    
    def test_anthropic_provider_missing_api_key(self, worker_config, mock_db_session_factory):
        """Test Anthropic provider with missing API key."""
        with patch('backend.app.distributed.workers.image_prompt_worker.DistributedWorker.__init__'):
            worker = ImagePromptWorker(worker_config, mock_db_session_factory)
            
            provider_cfg = {'provider': 'anthropic'}  # Missing api_key
            
            with pytest.raises(ValueError, match="Anthropic API key not provided"):
                worker._call_anthropic_provider(
                    provider_cfg, "test prompt", worker._llm_schema, "test input", 200, 0.7, 0.95
                )