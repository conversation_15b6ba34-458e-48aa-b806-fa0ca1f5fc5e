#!/usr/bin/env python3
"""
Simple test to verify operational tools are working.
"""

import sys
import os
import json
from datetime import datetime

# Add the project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

def test_cli_imports():
    """Test that CLI imports work correctly."""
    print("=== Testing CLI Imports ===")
    
    try:
        from backend.app.distributed.cli.worker_cli import WorkerCLI
        print("✓ WorkerCLI imported successfully")
        
        cli = WorkerCLI()
        parser = cli.create_parser()
        print("✓ CLI parser created successfully")
        
        # Test parsing some arguments
        args = parser.parse_args(['list', '--format', 'json'])
        print(f"✓ Parsed args: command={args.command}, format={args.format}")
        
    except Exception as e:
        print(f"✗ CLI import failed: {e}")
        return False
    
    return True

def test_health_checker_basic():
    """Test basic health checker functionality."""
    print("\n=== Testing Health Checker Basics ===")
    
    try:
        from backend.app.distributed.diagnostics.health_checker import HealthStatus, SystemMetrics
        print("✓ Health checker types imported successfully")
        
        # Test enum values
        print(f"✓ Health statuses: {[status.value for status in HealthStatus]}")
        
        # Test SystemMetrics creation
        metrics = SystemMetrics(
            cpu_percent=45.0,
            memory_percent=60.0,
            memory_used_mb=2000.0,
            memory_available_mb=1000.0,
            disk_percent=70.0,
            disk_used_gb=50.0,
            disk_free_gb=20.0,
            network_sent_mb=100.0,
            network_recv_mb=200.0,
            load_average=(1.0, 1.5, 2.0),
            uptime_seconds=3600.0
        )
        print(f"✓ SystemMetrics created: CPU={metrics.cpu_percent}%, Memory={metrics.memory_percent}%")
        
    except Exception as e:
        print(f"✗ Health checker test failed: {e}")
        return False
    
    return True

def test_performance_tuner_basic():
    """Test basic performance tuner functionality."""
    print("\n=== Testing Performance Tuner Basics ===")
    
    try:
        from backend.app.distributed.diagnostics.performance_tuner import OptimizationLevel, PerformanceMetric
        print("✓ Performance tuner types imported successfully")
        
        # Test enum values
        print(f"✓ Optimization levels: {[level.value for level in OptimizationLevel]}")
        
        # Test PerformanceMetric creation
        metric = PerformanceMetric(
            name="processing_time",
            value=5.0,
            unit="seconds",
            timestamp=datetime.now()
        )
        print(f"✓ PerformanceMetric created: {metric.name}={metric.value}{metric.unit}")
        
    except Exception as e:
        print(f"✗ Performance tuner test failed: {e}")
        return False
    
    return True

def test_deployment_configs():
    """Test that deployment configurations are valid."""
    print("\n=== Testing Deployment Configurations ===")
    
    try:
        # Test Docker Compose file
        docker_compose_path = os.path.join(project_root, 'backend/deployment/docker/docker-compose.distributed.yml')
        if os.path.exists(docker_compose_path):
            print("✓ Docker Compose configuration exists")
        else:
            print("✗ Docker Compose configuration missing")
            return False
        
        # Test Kubernetes manifests
        k8s_dir = os.path.join(project_root, 'backend/deployment/k8s')
        if os.path.exists(k8s_dir):
            k8s_files = [f for f in os.listdir(k8s_dir) if f.endswith('.yaml')]
            print(f"✓ Found {len(k8s_files)} Kubernetes manifests")
        else:
            print("✗ Kubernetes manifests directory missing")
            return False
        
        # Test environment configs
        config_dir = os.path.join(project_root, 'backend/deployment/config')
        if os.path.exists(config_dir):
            env_files = [f for f in os.listdir(config_dir) if f.endswith('.env')]
            print(f"✓ Found {len(env_files)} environment configurations")
        else:
            print("✗ Environment configurations directory missing")
            return False
        
        # Test operational documentation
        docs_dir = os.path.join(project_root, 'backend/app/distributed/docs')
        if os.path.exists(docs_dir):
            doc_files = [f for f in os.listdir(docs_dir) if f.endswith('.md')]
            print(f"✓ Found {len(doc_files)} documentation files")
        else:
            print("✗ Documentation directory missing")
            return False
        
    except Exception as e:
        print(f"✗ Deployment config test failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("Distributed ETL Operational Tools - Simple Test")
    print("=" * 60)
    
    tests = [
        test_cli_imports,
        test_health_checker_basic,
        test_performance_tuner_basic,
        test_deployment_configs
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Operational tools are working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())