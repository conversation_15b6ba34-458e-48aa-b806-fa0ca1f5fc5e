"""Add image prompt

Revision ID: cdfca92c25f2
Revises: 88f34f365784
Create Date: 2025-04-23 14:43:08.745624

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cdfca92c25f2'
down_revision: Union[str, None] = '88f34f365784'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('entries', sa.Column('image_prompt', sa.CLOB(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('entries', 'image_prompt')
    # ### end Alembic commands ###
