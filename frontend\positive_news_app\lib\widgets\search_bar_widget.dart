import 'package:flutter/material.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/widgets/responsive_news_grid.dart';

class NewsSearchDelegate extends SearchDelegate<String> {
  final ApiService apiService;

  NewsSearchDelegate(this.apiService);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    if (query.trim().isEmpty) {
      return const Center(
        child: Text('Bitte geben Sie einen Suchbegriff ein'),
      );
    }

    return FutureBuilder<PaginatedNewsResponse>(
      future: apiService.searchNews(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Fehler bei der Suche: ${snapshot.error}'),
          );
        }

        final news = snapshot.data?.items ?? [];

        if (news.isEmpty) {
          return Center(
            child: Text('Keine Ergebnisse für "$query" gefunden'),
          );
        }

        return ResponsiveNewsGrid(
          newsList: news,
          showFavoriteButton: true,
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.trim().isEmpty) {
      return const Center(
        child: Text('Suchen Sie nach positiven Nachrichten'),
      );
    }

    // Für Suchvorschläge könnten wir eine einfache Liste von Kategorien anzeigen
    final suggestions = [
      'Politik',
      'Wirtschaft',
      'Sport',
      'Wissenschaft',
      'Gesundheit',
      'Umwelt',
      'Technologie',
      'Unterhaltung',
    ].where((category) => category.toLowerCase().contains(query.toLowerCase())).toList();

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        return ListTile(
          leading: const Icon(Icons.category),
          title: Text(suggestions[index]),
          onTap: () {
            query = suggestions[index];
            showResults(context);
          },
        );
      },
    );
  }
}
