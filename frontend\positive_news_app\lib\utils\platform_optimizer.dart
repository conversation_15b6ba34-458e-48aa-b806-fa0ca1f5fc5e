import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

class PlatformOptimizer {
  // Prüft, ob die App auf iOS läuft
  static bool isIOS() {
    return defaultTargetPlatform == TargetPlatform.iOS;
  }

  // Prüft, ob die App auf Android läuft
  static bool isAndroid() {
    return defaultTargetPlatform == TargetPlatform.android;
  }

  // Prüft, ob die App im Web läuft
  static bool isWeb() {
    return kIsWeb;
  }

  // Gibt plattformspezifische Einstellungen zurück
  static Map<String, dynamic> getPlatformSettings() {
    if (isIOS()) {
      return {
        'animation_duration': const Duration(milliseconds: 300),
        'card_elevation': 2.0,
        'border_radius': 12.0,
        'icon_size': 24.0,
      };
    } else if (isAndroid()) {
      return {
        'animation_duration': const Duration(milliseconds: 250),
        'card_elevation': 4.0,
        'border_radius': 8.0,
        'icon_size': 24.0,
      };
    } else if (isWeb()) {
      return {
        'animation_duration': const Duration(milliseconds: 200),
        'card_elevation': 1.0,
        'border_radius': 4.0,
        'icon_size': 20.0,
      };
    } else {
      // Standardeinstellungen für andere Plattformen
      return {
        'animation_duration': const Duration(milliseconds: 250),
        'card_elevation': 2.0,
        'border_radius': 8.0,
        'icon_size': 22.0,
      };
    }
  }

  // Gibt plattformspezifische UI-Anpassungen zurück
  static Widget getPlatformSpecificWidget({
    required Widget androidWidget,
    required Widget iosWidget,
    required Widget webWidget,
    required Widget defaultWidget,
  }) {
    if (isAndroid()) {
      return androidWidget;
    } else if (isIOS()) {
      return iosWidget;
    } else if (isWeb()) {
      return webWidget;
    } else {
      return defaultWidget;
    }
  }

  // Optimiert die App für die aktuelle Plattform
  static void optimizeForCurrentPlatform(BuildContext context) {
    if (isIOS()) {
      // iOS-spezifische Optimierungen
    } else if (isAndroid()) {
      // Android-spezifische Optimierungen
    } else if (isWeb()) {
      // Web-spezifische Optimierungen
    }
  }
}
