#!/bin/bash
set -e

echo "Setting up development environment..."

# Update system packages quietly
sudo apt-get update -qq

# Install required system dependencies
sudo apt-get install -y -qq \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    wget \
    file

# Install Flutter
echo "Installing Flutter..."
cd $HOME
if [ ! -d "flutter" ]; then
    wget -q -O flutter_linux.tar.xz https://storage.googleapis.com/flutter_infra_release/releases/stable/linux/flutter_linux_3.27.1-stable.tar.xz
    tar xf flutter_linux.tar.xz > /dev/null 2>&1
    rm flutter_linux.tar.xz
fi

# Add Flutter to PATH in .profile
if ! grep -q "export PATH=\"\$HOME/flutter/bin:\$PATH\"" $HOME/.profile; then
    echo 'export PATH="$HOME/flutter/bin:$PATH"' >> $HOME/.profile
fi

# Source the profile to make flutter available in current session
export PATH="$HOME/flutter/bin:$PATH"

# Navigate to Flutter project directory
cd /mnt/persist/workspace/frontend/positive_news_app

# Get Flutter dependencies
echo "Getting Flutter dependencies..."
flutter pub get > /dev/null 2>&1

# Try to generate mock files, but don't fail if it doesn't work
echo "Attempting to generate mock files..."
flutter packages pub run build_runner build --delete-conflicting-outputs > /dev/null 2>&1 || echo "Mock generation failed, continuing..."

echo "Setup completed successfully!"