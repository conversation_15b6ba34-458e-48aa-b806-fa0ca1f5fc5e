import 'package:flutter/material.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/widgets/responsive_news_grid.dart';
import 'package:breakingbright/screens/category/category_view_screen.dart';

class CategoryNewsWidget extends StatelessWidget {
  final CategoryNews categoryNews;
  final Widget? leading;

  const CategoryNewsWidget({
    super.key,
    required this.categoryNews,
    this.leading,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  if (leading != null) leading!,
                  if (leading != null) const SizedBox(width: 8),
                  Text(
                    categoryNews.category,
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ],
              ),
              TextButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CategoryViewScreen(
                        categoryCode: categoryNews.categoryCode,
                        categoryName: categoryNews.category,
                      ),
                    ),
                  );
                },
                child: const Text('Mehr anzeigen'),
              ),
            ],
          ),
        ),
        ResponsiveNewsGrid(
          newsList: categoryNews.news,
          showFavoriteButton: true,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
        ),
        const Divider(height: 32, thickness: 1),
      ],
    );
  }
}
