# Distributed ETL Processing Design

## Overview

The current ETL system uses monolithic threads that process all available entries for a given stage sequentially. This design transforms the system into a distributed architecture where work is divided into smaller batches that can be processed independently by multiple workers. The solution uses a database-based work queue approach with atomic claiming mechanisms to eliminate the need for a central coordinator.

## Architecture

### Current Architecture Issues
- Each thread processes ALL available entries for its stage
- No coordination between multiple instances
- Single points of failure for each processing stage
- Limited horizontal scalability

### New Distributed Architecture
The new architecture introduces:

1. **Work Batch Management**: Entries are processed in configurable batches rather than all-at-once
2. **Atomic Claiming**: Database-level atomic operations ensure only one worker claims a batch
3. **Worker Specialization**: Workers can be configured to handle specific processing stages
4. **Fault Tolerance**: Failed workers automatically release claimed work after timeout
5. **Backward Compatibility**: Existing database schema and processing logic remain unchanged

## Components and Interfaces

### 1. Work Queue Manager
**Purpose**: Manages work distribution and claiming logic

**Key Methods**:
```python
class WorkQueueManager:
    def claim_batch(self, stage: ProcessingStage, batch_size: int, worker_id: str) -> List[str]
    def release_batch(self, entry_ids: List[str], worker_id: str) -> None
    def mark_completed(self, entry_ids: List[str], stage: ProcessingStage) -> None
    def cleanup_stale_claims(self, timeout_minutes: int) -> int
```

**Database Changes**:
- Add `processing_status` CLOB column to store JSON with stage-specific status
- Add `claimed_by` VARCHAR(255) column to track which worker claimed an entry
- Add `claimed_at` TIMESTAMP column for timeout detection
- Add composite indexes for efficient querying

### 2. Distributed Worker
**Purpose**: Replaces monolithic threads with configurable workers

**Key Features**:
- Configurable batch sizes (default: 10-50 entries)
- Stage specialization (can handle one or multiple stages)
- Automatic heartbeat and health monitoring
- Graceful shutdown with work release

**Configuration**:
```python
@dataclass
class WorkerConfig:
    worker_id: str
    stages: List[ProcessingStage]
    batch_size: int = 25
    claim_timeout_minutes: int = 30
    heartbeat_interval_seconds: int = 60
    max_retries: int = 3
```

### 3. Processing Stage Enum
**Purpose**: Define all ETL processing stages

```python
class ProcessingStage(Enum):
    DOWNLOAD_FEEDS = "download_feeds"
    DOWNLOAD_FULL_TEXT = "download_full_text"
    TRANSLATE_TEXT = "translate_text"
    COMPUTE_EMBEDDINGS = "compute_embeddings"
    CLASSIC_SENTIMENT_ANALYSIS = "classic_sentiment_analysis"
    LLM_SENTIMENT_AD = "llm_sentiment_ad"
    IPTC_CLASSIFICATION = "iptc_classification"
    DUPLICATE_CHECK = "duplicate_check"
    GENERATE_DESCRIPTIONS = "generate_descriptions"
    GENERATE_IMAGE_PROMPTS = "generate_image_prompts"
    GENERATE_PREVIEW_IMAGES = "generate_preview_images"
    GENERATE_RSS_FEED = "generate_rss_feed"
```

### 4. Worker Coordinator
**Purpose**: Optional component for monitoring and management

**Features**:
- Worker registration and discovery
- Health monitoring and alerting
- Performance metrics collection
- Automatic scaling recommendations

## Data Models

### Enhanced Entry Model
```python
class Entry(Base):
    # ... existing fields ...
    
    # New fields for distributed processing
    processing_status = Column(CLOB, nullable=True)  # JSON: {"stage": "status", ...}
    claimed_by = Column(String(255), nullable=True)  # Worker ID that claimed this entry
    claimed_at = Column(TIMESTAMP, nullable=True)    # When entry was claimed
    retry_count = Column(Integer, default=0)         # Number of processing retries
    last_error = Column(CLOB, nullable=True)         # Last error message if any
```

### Processing Status JSON Structure
```json
{
  "download_full_text": {
    "status": "completed|in_progress|failed|pending",
    "completed_at": "2025-01-15T10:30:00Z",
    "worker_id": "worker-001",
    "error": "optional error message"
  },
  "sentiment_analysis": {
    "status": "pending"
  }
}
```

## Error Handling

### Worker Failure Recovery
1. **Heartbeat Monitoring**: Workers send periodic heartbeats
2. **Stale Claim Cleanup**: Background process releases claims from inactive workers
3. **Retry Logic**: Failed entries are retried up to configurable limit
4. **Dead Letter Queue**: Entries that fail repeatedly are marked for manual review

### Database Transaction Safety
- All claim operations use database transactions with proper isolation
- Optimistic locking prevents race conditions during batch claiming
- Rollback mechanisms ensure consistency on worker failures

### Graceful Degradation
- Workers can fall back to smaller batch sizes under high contention
- System continues operating even if some workers fail
- Monitoring alerts when worker count drops below threshold

## Testing Strategy

### Unit Tests
- Work queue claiming logic with concurrent access simulation
- Worker configuration validation and stage filtering
- Processing status JSON serialization/deserialization
- Error handling and retry mechanisms

### Integration Tests
- Multi-worker coordination with real database
- End-to-end processing pipeline with distributed workers
- Failure scenarios (worker crashes, database connectivity issues)
- Performance comparison with monolithic approach

### Load Tests
- Concurrent worker performance under various batch sizes
- Database contention handling with high worker counts
- Memory and CPU usage patterns
- Throughput comparison between monolithic and distributed modes

### Compatibility Tests
- Backward compatibility with existing database schema
- Gradual migration scenarios (mixed monolithic/distributed)
- Data consistency verification between processing modes

## Migration Strategy

### Phase 1: Database Schema Enhancement
- Add new columns to Entry table with default values
- Create necessary indexes for efficient querying
- Implement backward-compatible processing status handling

### Phase 2: Work Queue Implementation
- Implement WorkQueueManager with atomic claiming
- Add distributed worker framework
- Create configuration management system

### Phase 3: Gradual Worker Migration
- Replace one processing stage at a time
- Run parallel systems during transition
- Monitor performance and adjust configurations

### Phase 4: Full Distributed Deployment
- Complete migration of all processing stages
- Remove monolithic thread code
- Optimize performance based on production metrics

## Performance Considerations

### Batch Size Optimization
- Small batches (5-10): Lower latency, higher database overhead
- Medium batches (25-50): Balanced performance for most workloads
- Large batches (100+): Higher throughput, increased failure impact

### Database Query Optimization
- Composite indexes on (processing_status, claimed_by, claimed_at)
- Efficient JSON querying for processing status
- Connection pooling for high-concurrency scenarios

### Memory Management
- Workers process batches in memory before committing
- Configurable memory limits per worker
- Garbage collection optimization for long-running workers

## Monitoring and Observability

### Key Metrics
- Worker throughput (entries processed per minute)
- Batch claim success rate and contention levels
- Processing stage completion times
- Error rates and retry patterns
- Database query performance

### Alerting
- Worker health and availability
- Processing pipeline bottlenecks
- High error rates or retry counts
- Database performance degradation

### Dashboards
- Real-time worker status and performance
- Processing pipeline flow visualization
- Historical performance trends
- Resource utilization metrics