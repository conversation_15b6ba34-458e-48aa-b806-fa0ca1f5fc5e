"""
Unit tests for WorkQueueManager class.

Tests atomic work claiming, releasing, completion marking, and stale cleanup
using mock database operations.
"""

import pytest
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from sqlalchemy.exc import SQLAlchemyError

from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.processing_stage import (
    ProcessingStage, ProcessingStatus, ProcessingStatusValue
)
from backend.app.models.models import Entry


class TestWorkQueueManager:
    """Test cases for WorkQueueManager functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.mock_db = Mock()
        self.mock_optimized_queries = Mock()
        self.manager = WorkQueueManager(self.mock_db)
        self.manager.optimized_queries = self.mock_optimized_queries
        self.worker_id = "test-worker-001"
        self.stage = ProcessingStage.DOWNLOAD_FULL_TEXT
        
    def create_mock_entry(self, entry_id: str, 
                         processing_status: str = None,
                         claimed_by: str = None,
                         claimed_at: datetime = None) -> Mock:
        """Create a mock Entry object."""
        entry = Mock(spec=Entry)
        entry.entry_id = entry_id
        entry.processing_status = processing_status
        entry.claimed_by = claimed_by
        entry.claimed_at = claimed_at
        entry.published = datetime.now(timezone.utc)
        entry.last_error = None
        entry.retry_count = 0
        return entry
    
    def test_claim_batch_success(self):
        """Test successful batch claiming."""
        # Setup mock entries
        mock_entries = [
            self.create_mock_entry("entry1"),
            self.create_mock_entry("entry2"),
            self.create_mock_entry("entry3")
        ]

        # Mock optimized queries
        self.mock_optimized_queries.find_claimable_entries_optimized.return_value = mock_entries
        self.mock_optimized_queries.atomic_claim_batch.return_value = ["entry1", "entry2", "entry3"]

        # Test claim_batch
        result = self.manager.claim_batch(self.stage, 3, self.worker_id)

        # Verify results
        assert result == ["entry1", "entry2", "entry3"]

        # Verify optimized queries were called
        self.mock_optimized_queries.find_claimable_entries_optimized.assert_called_once_with(
            self.stage, 3, 3
        )
        self.mock_optimized_queries.atomic_claim_batch.assert_called_once_with(
            ["entry1", "entry2", "entry3"], self.worker_id, self.stage
        )
        
        # Note: Entry updates are now handled by the optimized queries,
        # so we don't need to verify direct entry modifications
    
    def test_claim_batch_no_available_entries(self):
        """Test claiming when no entries are available."""
        # Mock empty result from optimized queries
        self.mock_optimized_queries.find_claimable_entries_optimized.return_value = []

        # Test claim_batch
        result = self.manager.claim_batch(self.stage, 5, self.worker_id)

        # Verify empty result
        assert result == []

        # Verify find was called but atomic_claim_batch was not
        self.mock_optimized_queries.find_claimable_entries_optimized.assert_called_once_with(
            self.stage, 5, 3
        )
        self.mock_optimized_queries.atomic_claim_batch.assert_not_called()
    
    def test_claim_batch_filters_by_stage_availability(self):
        """Test that claim_batch only returns entries available for the stage."""
        # Create available entries (filtering is now done in optimized queries)
        available_entry1 = self.create_mock_entry("entry1")
        available_entry2 = self.create_mock_entry("entry4")

        # Mock optimized queries to return only available entries
        self.mock_optimized_queries.find_claimable_entries_optimized.return_value = [
            available_entry1, available_entry2
        ]
        self.mock_optimized_queries.atomic_claim_batch.return_value = ["entry1", "entry4"]

        # Test claim_batch
        result = self.manager.claim_batch(self.stage, 5, self.worker_id)

        # Should only return available entries
        assert result == ["entry1", "entry4"]
    
    def test_claim_batch_database_error(self):
        """Test handling of database errors during batch claiming."""
        # Mock database error in optimized queries
        self.mock_optimized_queries.find_claimable_entries_optimized.side_effect = SQLAlchemyError("Database connection failed")

        # Test that exception is raised
        with pytest.raises(SQLAlchemyError):
            self.manager.claim_batch(self.stage, 3, self.worker_id)

        # Verify rollback was called
        assert self.mock_db.rollback.called
    
    def test_release_batch_success(self):
        """Test successful batch release."""
        entry_ids = ["entry1", "entry2", "entry3"]
        
        # Mock database update
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.update.return_value = 3  # 3 entries updated
        
        self.mock_db.query.return_value = mock_query
        self.mock_db.begin.return_value.__enter__ = Mock()
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test release_batch
        self.manager.release_batch(entry_ids, self.worker_id)
        
        # Verify database operations
        assert self.mock_db.query.called
        mock_query.update.assert_called_once()
        
        # Verify update parameters
        update_call = mock_query.update.call_args
        update_data = update_call[0][0]
        assert update_data[Entry.claimed_by] is None
        assert update_data[Entry.claimed_at] is None
    
    def test_release_batch_empty_list(self):
        """Test releasing empty batch does nothing."""
        # Test with empty list
        self.manager.release_batch([], self.worker_id)
        
        # Verify no database operations
        assert not self.mock_db.query.called
    
    def test_release_batch_database_error(self):
        """Test handling of database errors during batch release."""
        entry_ids = ["entry1", "entry2"]
        
        # Mock database error
        self.mock_db.begin.side_effect = SQLAlchemyError("Update failed")
        
        # Test that exception is raised
        with pytest.raises(SQLAlchemyError):
            self.manager.release_batch(entry_ids, self.worker_id)
        
        # Verify rollback was called
        assert self.mock_db.rollback.called
    
    def test_mark_completed_success(self):
        """Test successful completion marking."""
        entry_ids = ["entry1", "entry2"]

        # Mock optimized queries
        self.mock_optimized_queries.bulk_update_completion.return_value = 2

        # Test mark_completed
        self.manager.mark_completed(entry_ids, self.stage, self.worker_id)

        # Verify optimized queries were called
        self.mock_optimized_queries.bulk_update_completion.assert_called_once_with(
            entry_ids, self.stage, self.worker_id
        )
    
    def test_mark_failed_success(self):
        """Test successful failure marking."""
        entry_ids = ["entry1"]
        error_message = "Processing failed due to network timeout"
        
        # Create mock entry
        mock_entry = self.create_mock_entry("entry1", claimed_by=self.worker_id)
        
        # Mock database query
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [mock_entry]
        
        self.mock_db.query.return_value = mock_query
        self.mock_db.begin.return_value.__enter__ = Mock()
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test mark_failed
        self.manager.mark_failed(entry_ids, self.stage, self.worker_id, error_message)
        
        # Verify entry was updated
        assert mock_entry.claimed_by is None
        assert mock_entry.claimed_at is None
        assert mock_entry.last_error == error_message
        
        # Verify processing status was updated
        processing_status = ProcessingStatus.from_json(mock_entry.processing_status)
        assert processing_status.is_stage_failed(self.stage)
        assert processing_status.get_retry_count(self.stage) == 1
    
    def test_cleanup_stale_claims_success(self):
        """Test successful stale claim cleanup."""
        timeout_minutes = 30

        # Mock optimized queries
        self.mock_optimized_queries.efficient_stale_cleanup.return_value = 2

        # Test cleanup_stale_claims
        result = self.manager.cleanup_stale_claims(timeout_minutes)

        # Verify result
        assert result == 2

        # Verify optimized queries were called
        self.mock_optimized_queries.efficient_stale_cleanup.assert_called_once_with(timeout_minutes)
    
    def test_cleanup_stale_claims_no_stale_entries(self):
        """Test cleanup when no stale entries exist."""
        # Mock optimized queries
        self.mock_optimized_queries.efficient_stale_cleanup.return_value = 0

        # Test cleanup_stale_claims
        result = self.manager.cleanup_stale_claims(30)

        # Verify no cleanup needed
        assert result == 0

        # Verify optimized queries were called
        self.mock_optimized_queries.efficient_stale_cleanup.assert_called_once_with(30)
    
    def test_get_queue_stats(self):
        """Test queue statistics generation."""
        # Mock optimized queries
        mock_stats = {
            'pending': 2,
            'completed': 1,
            'failed': 1,
            'claimed': 1
        }
        self.mock_optimized_queries.get_queue_metrics_optimized.return_value.get.return_value = mock_stats

        # Test get_queue_stats
        stats = self.manager.get_queue_stats(self.stage)

        # Verify statistics
        assert stats['pending'] == 2
        assert stats['completed'] == 1
        assert stats['failed'] == 1
        assert stats['claimed'] == 1
    
    def test_get_queue_stats_database_error(self):
        """Test queue stats with database error."""
        # Mock database error in optimized queries
        self.mock_optimized_queries.get_queue_metrics_optimized.side_effect = SQLAlchemyError("Query failed")

        # Test get_queue_stats
        stats = self.manager.get_queue_stats(self.stage)

        # Should return empty dict on error
        assert stats == {}


if __name__ == "__main__":
    pytest.main([__file__])