{"buildFiles": ["C:\\PortableUnsafe\\flutterSdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app\\android\\app\\.cxx\\Debug\\2q3l5s4o\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Dokumente\\Private\\Coding\\goodNews\\frontend\\positive_news_app\\android\\app\\.cxx\\Debug\\2q3l5s4o\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}