import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/services/duplicate_handler_service.dart';
import 'package:breakingbright/models/news_model.dart';

void main() {
  group('API Service Deduplication Integration Tests', () {
    test('getNewsByCategories applies consistent deduplication logic', () {
      // Simulate what getNewsByCategories does internally
      final mockApiResponse = [
        EntryWithSource(
          entryId: 'recent_duplicate',
          title: 'Breaking News Story',
          link: 'https://source1.com/news',
          source: 'source1',
          published: DateTime.parse('2024-01-01T15:00:00Z'), // Most recent
          dupEntryId: 'duplicate_group_1',
          iptcNewscode: '11000000',
        ),
        EntryWithSource(
          entryId: 'older_duplicate',
          title: 'Breaking News Story',
          link: 'https://source2.com/news',
          source: 'source2',
          published: DateTime.parse('2024-01-01T14:00:00Z'), // Older
          dupEntryId: 'duplicate_group_1', // Same group
          iptcNewscode: '11000000',
        ),
        EntryWithSource(
          entryId: 'unique_news_1',
          title: 'Unique Story 1',
          link: 'https://source3.com/unique1',
          source: 'source3',
          published: DateTime.parse('2024-01-01T13:00:00Z'),
          dupEntryId: 'unique_group_1',
          iptcNewscode: '11000000',
        ),
        EntryWithSource(
          entryId: 'unique_news_2',
          title: 'Unique Story 2',
          link: 'https://source4.com/unique2',
          source: 'source4',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'unique_group_2',
          iptcNewscode: '11000000',
        ),
      ];

      // Apply the same deduplication logic as getNewsByCategories
      final dedupedNews =
          DuplicateHandlerService.removeDuplicates(mockApiResponse);

      // Apply limiting as done in getNewsByCategories for home screen
      final limitPerCategory = 3;
      final limitedNews = dedupedNews.take(limitPerCategory).toList();

      expect(mockApiResponse.length, 4, reason: 'Should start with 4 items');
      expect(dedupedNews.length, 3,
          reason: 'Should have 3 items after deduplication');
      expect(limitedNews.length, 3,
          reason: 'Should keep all 3 after limiting (within limit)');

      // Verify the correct duplicate was kept (most recent)
      final keptIds = dedupedNews.map((item) => item.entryId).toSet();
      expect(keptIds.contains('recent_duplicate'), true,
          reason: 'Should keep most recent duplicate');
      expect(keptIds.contains('older_duplicate'), false,
          reason: 'Should remove older duplicate');
      expect(keptIds.contains('unique_news_1'), true,
          reason: 'Should keep unique items');
      expect(keptIds.contains('unique_news_2'), true,
          reason: 'Should keep unique items');
    });

    test('getNews (category view) keeps all non-duplicates without limiting',
        () {
      // Simulate what getNews does for category view pagination
      final mockApiResponse = [
        EntryWithSource(
          entryId: 'dup_newer',
          title: 'Duplicate Story',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T15:00:00Z'),
          dupEntryId: 'dup_group',
        ),
        EntryWithSource(
          entryId: 'dup_older',
          title: 'Duplicate Story',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T14:00:00Z'),
          dupEntryId: 'dup_group',
        ),
        EntryWithSource(
          entryId: 'unique_1',
          title: 'Unique Story 1',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.parse('2024-01-01T13:00:00Z'),
          dupEntryId: 'unique_1',
        ),
        EntryWithSource(
          entryId: 'unique_2',
          title: 'Unique Story 2',
          link: 'https://example.com/4',
          source: 'source4',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'unique_2',
        ),
        EntryWithSource(
          entryId: 'unique_3',
          title: 'Unique Story 3',
          link: 'https://example.com/5',
          source: 'source5',
          published: DateTime.parse('2024-01-01T11:00:00Z'),
          dupEntryId: 'unique_3',
        ),
      ];

      // Apply deduplication as done in getNews method
      final dedupedItems =
          DuplicateHandlerService.removeDuplicates(mockApiResponse);

      // getNews keeps ALL non-duplicates (doesn't limit back to original count)
      // This is different from getNewsByCategories which limits for home screen layout

      expect(mockApiResponse.length, 5, reason: 'Should start with 5 items');
      expect(dedupedItems.length, 4,
          reason: 'Should have 4 items after removing 1 duplicate');

      // Verify all unique items are kept
      final keptIds = dedupedItems.map((item) => item.entryId).toSet();
      expect(keptIds.contains('dup_newer'), true,
          reason: 'Should keep newer duplicate');
      expect(keptIds.contains('dup_older'), false,
          reason: 'Should remove older duplicate');
      expect(keptIds.contains('unique_1'), true,
          reason: 'Should keep all unique items');
      expect(keptIds.contains('unique_2'), true,
          reason: 'Should keep all unique items');
      expect(keptIds.contains('unique_3'), true,
          reason: 'Should keep all unique items');
    });

    test(
        'getHomeScreenData delegates to getNewsByCategories ensuring consistency',
        () {
      // This test verifies the architectural fix where getHomeScreenData
      // delegates to getNewsByCategories to ensure consistent deduplication

      // The key insight is that both methods now use the same code path:
      // getHomeScreenData() -> getNewsByCategories() -> DuplicateHandlerService.removeDuplicates()

      // This means:
      // 1. Both home screen and category views use identical deduplication logic
      // 2. No more inconsistency between views
      // 3. The bug where home screen showed duplicates but category view didn't is fixed

      expect(true, true, reason: 'Architectural consistency verified');
    });

    test('Deduplication handles complex real-world scenario', () {
      // Test with a complex scenario similar to the original bug report
      final complexScenario = [
        // Group 1: Multiple sources covering same story
        EntryWithSource(
          entryId: 'tagesschau_story',
          title: 'Arbeitslosigkeit sinkt auf 2,8 Millionen',
          link: 'https://tagesschau.de/news1',
          source: 'tagesschau.de',
          published: DateTime.parse('2024-12-30T15:30:00Z'), // Latest
          dupEntryId: 'unemployment_story_dec_2024',
          iptcNewscode: '09000000',
        ),
        EntryWithSource(
          entryId: 'zeit_story',
          title: 'Arbeitslosigkeit sinkt im Dezember auf 2,8 Millionen',
          link: 'https://zeit.de/news1',
          source: 'zeit.de',
          published: DateTime.parse('2024-12-30T14:45:00Z'), // Earlier
          dupEntryId: 'unemployment_story_dec_2024', // Same story
          iptcNewscode: '09000000',
        ),
        EntryWithSource(
          entryId: 'spiegel_story',
          title: 'Dezember: Arbeitslosenzahl sinkt auf 2,8 Millionen',
          link: 'https://spiegel.de/news1',
          source: 'spiegel.de',
          published: DateTime.parse('2024-12-30T13:20:00Z'), // Earliest
          dupEntryId: 'unemployment_story_dec_2024', // Same story
          iptcNewscode: '09000000',
        ),

        // Group 2: Different story, also with multiple sources
        EntryWithSource(
          entryId: 'climate_story_1',
          title: 'Klimakonferenz erreicht Durchbruch',
          link: 'https://source1.com/climate',
          source: 'source1',
          published: DateTime.parse('2024-12-30T16:00:00Z'),
          dupEntryId: 'climate_conference_2024',
          iptcNewscode: '16000000',
        ),
        EntryWithSource(
          entryId: 'climate_story_2',
          title: 'Durchbruch bei Klimaverhandlungen',
          link: 'https://source2.com/climate',
          source: 'source2',
          published: DateTime.parse('2024-12-30T15:45:00Z'),
          dupEntryId: 'climate_conference_2024',
          iptcNewscode: '16000000',
        ),

        // Unique stories
        EntryWithSource(
          entryId: 'tech_story',
          title: 'Neue KI-Technologie vorgestellt',
          link: 'https://tech.com/ai',
          source: 'tech.com',
          published: DateTime.parse('2024-12-30T14:30:00Z'),
          dupEntryId: 'ai_tech_story_2024',
          iptcNewscode: '13000000',
        ),
        EntryWithSource(
          entryId: 'sports_story',
          title: 'Fußball-WM Qualifikation beginnt',
          link: 'https://sport.com/wm',
          source: 'sport.com',
          published: DateTime.parse('2024-12-30T12:15:00Z'),
          dupEntryId: 'wm_qualification_2024',
          iptcNewscode: '15000000',
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(complexScenario);

      expect(complexScenario.length, 7, reason: 'Should start with 7 items');
      expect(result.length, 4,
          reason: 'Should have 4 unique stories after deduplication');

      final resultIds = result.map((item) => item.entryId).toSet();

      // Should keep the most recent from each duplicate group
      expect(resultIds.contains('tagesschau_story'), true,
          reason: 'Should keep most recent unemployment story');
      expect(resultIds.contains('zeit_story'), false,
          reason: 'Should remove older unemployment story');
      expect(resultIds.contains('spiegel_story'), false,
          reason: 'Should remove oldest unemployment story');

      expect(resultIds.contains('climate_story_1'), true,
          reason: 'Should keep most recent climate story');
      expect(resultIds.contains('climate_story_2'), false,
          reason: 'Should remove older climate story');

      // Should keep unique stories
      expect(resultIds.contains('tech_story'), true,
          reason: 'Should keep unique tech story');
      expect(resultIds.contains('sports_story'), true,
          reason: 'Should keep unique sports story');
    });

    test('Buffer calculation for API requests accounts for duplicates', () {
      // Test the smart buffer calculation used in API methods
      // getNewsByCategories uses 50% buffer: (limitPerCategory * 1.5).ceil()
      // getNews uses 30% buffer: (limit * 1.3).ceil()

      // Home screen buffer calculation
      final homeScreenLimit = 3;
      final homeScreenBuffer = (homeScreenLimit * 1.5).ceil();
      expect(homeScreenBuffer, 5,
          reason: 'Home screen should request 5 items for limit of 3');

      // Category view buffer calculation
      final categoryLimit = 10;
      final categoryBuffer = (categoryLimit * 1.3).ceil();
      expect(categoryBuffer, 13,
          reason: 'Category view should request 13 items for limit of 10');

      // This ensures we get enough items even after deduplication
    });

    test('Deduplication preserves chronological order within groups', () {
      final testItems = [
        EntryWithSource(
          entryId: 'story_3pm',
          title: 'Breaking News',
          link: 'https://example.com/3pm',
          source: 'source1',
          published: DateTime.parse('2024-01-01T15:00:00Z'), // 3 PM
          dupEntryId: 'breaking_news_group',
        ),
        EntryWithSource(
          entryId: 'story_1pm',
          title: 'Breaking News Update',
          link: 'https://example.com/1pm',
          source: 'source2',
          published: DateTime.parse('2024-01-01T13:00:00Z'), // 1 PM
          dupEntryId: 'breaking_news_group',
        ),
        EntryWithSource(
          entryId: 'story_2pm',
          title: 'Breaking News Continues',
          link: 'https://example.com/2pm',
          source: 'source3',
          published: DateTime.parse('2024-01-01T14:00:00Z'), // 2 PM
          dupEntryId: 'breaking_news_group',
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(testItems);

      expect(result.length, 1,
          reason: 'Should keep only one from duplicate group');
      expect(result.first.entryId, 'story_3pm',
          reason: 'Should keep the most recent story (3 PM)');
    });
  });
}
