"""Add two indices as proposed by Jules

Revision ID: 42d06979fb36
Revises: 0dbbdad1dced
Create Date: 2025-08-07 18:39:47.864236

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '42d06979fb36'
down_revision: Union[str, None] = '0dbbdad1dced'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_entries_iptc_llm_published', 'entries', ['iptc_newscode', 'llm_positive', sa.text('published DESC')], unique=False)
    op.create_index('idx_entries_iptc_published_desc', 'entries', ['iptc_newscode', sa.text('published DESC')], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_entries_iptc_published_desc', table_name='entries')
    op.drop_index('idx_entries_iptc_llm_published', table_name='entries')
    # ### end Alembic commands ###
