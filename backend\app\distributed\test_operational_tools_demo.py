#!/usr/bin/env python3
"""
Demo script to test operational tools functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from worker_config import WorkerConfig
from processing_stage import ProcessingStage
from diagnostics.health_checker import <PERSON><PERSON><PERSON><PERSON>
from diagnostics.performance_tuner import PerformanceTuner


async def test_health_checker():
    """Test the health checker functionality."""
    print("=== Testing Health Checker ===")
    
    # Create test configuration
    config = WorkerConfig(
        worker_id="test-worker-001",
        stages=[ProcessingStage.DOWNLOAD_FEEDS, ProcessingStage.DOWNLOAD_FULL_TEXT],
        batch_size=25,
        database_url="sqlite:///test.db"
    )
    
    # Create health checker
    health_checker = HealthChecker(config)
    
    # Test system metrics
    print("\n1. System Metrics:")
    system_metrics = health_checker.get_system_metrics()
    print(f"   CPU: {system_metrics.cpu_percent:.1f}%")
    print(f"   Memory: {system_metrics.memory_percent:.1f}%")
    print(f"   Disk: {system_metrics.disk_percent:.1f}%")
    print(f"   Uptime: {system_metrics.uptime_seconds:.0f} seconds")
    
    # Test individual health checks
    print("\n2. Database Connectivity Check:")
    try:
        db_result = await health_checker.check_database_connectivity()
        print(f"   Status: {db_result.status.value}")
        print(f"   Message: {db_result.message}")
        print(f"   Response Time: {db_result.response_time_ms:.1f}ms")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n3. Work Queue Health Check:")
    try:
        queue_result = await health_checker.check_work_queue_health()
        print(f"   Status: {queue_result.status.value}")
        print(f"   Message: {queue_result.message}")
        print(f"   Response Time: {queue_result.response_time_ms:.1f}ms")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test comprehensive health check
    print("\n4. Comprehensive Health Check:")
    try:
        health_report = await health_checker.run_comprehensive_health_check()
        print(f"   Worker ID: {health_report.worker_id}")
        print(f"   Overall Status: {health_report.overall_status.value}")
        print(f"   Health Checks: {len(health_report.health_checks)}")
        print(f"   Recent Errors: {len(health_report.recent_errors)}")
        
        # Format and display report
        print("\n   Formatted Report:")
        formatted_report = health_checker.format_health_report(health_report)
        print("   " + "\n   ".join(formatted_report.split("\n")[:15]))  # Show first 15 lines
        print("   ...")
        
    except Exception as e:
        print(f"   Error: {e}")


def test_performance_tuner():
    """Test the performance tuner functionality."""
    print("\n=== Testing Performance Tuner ===")
    
    # Create test configuration
    config = WorkerConfig(
        worker_id="test-worker-001",
        stages=[ProcessingStage.DOWNLOAD_FEEDS],
        batch_size=25,
        claim_timeout_minutes=30
    )
    
    # Create performance tuner
    tuner = PerformanceTuner(config)
    
    # Add some test metrics
    print("\n1. Adding Test Metrics:")
    for i in range(15):
        # Simulate slow processing (12 seconds per entry)
        tuner.add_metric("batch_processing_time", 300.0, "seconds")
        tuner.add_metric("throughput_per_minute", 5.0, "entries/min")
        tuner.add_metric("processing_time", 12.0, "seconds", ProcessingStage.DOWNLOAD_FEEDS)
        tuner.add_metric("error_rate", 2.5, "percent", ProcessingStage.DOWNLOAD_FEEDS)
    
    print(f"   Added {len(tuner.metrics_history)} metrics")
    
    # Test batch size analysis
    print("\n2. Batch Size Analysis:")
    batch_recommendations = tuner.analyze_batch_size_performance()
    if batch_recommendations:
        for rec in batch_recommendations:
            print(f"   Title: {rec.title}")
            print(f"   Level: {rec.level.value}")
            print(f"   Current: {rec.current_value} → Recommended: {rec.recommended_value}")
            print(f"   Expected Improvement: {rec.expected_improvement}")
    else:
        print("   No batch size recommendations")
    
    # Test timeout analysis
    print("\n3. Timeout Configuration Analysis:")
    timeout_recommendations = tuner.analyze_timeout_configuration()
    if timeout_recommendations:
        for rec in timeout_recommendations:
            print(f"   Title: {rec.title}")
            print(f"   Level: {rec.level.value}")
            print(f"   Current: {rec.current_value} → Recommended: {rec.recommended_value}")
    else:
        print("   No timeout recommendations")
    
    # Test stage performance analysis
    print("\n4. Stage Performance Analysis:")
    stage_recommendations = tuner.analyze_stage_performance()
    if stage_recommendations:
        for rec in stage_recommendations:
            print(f"   Title: {rec.title}")
            print(f"   Category: {rec.category}")
            print(f"   Level: {rec.level.value}")
    else:
        print("   No stage performance recommendations")


async def main():
    """Main demo function."""
    print("Distributed ETL Operational Tools Demo")
    print("=" * 50)
    
    try:
        # Test health checker
        await test_health_checker()
        
        # Test performance tuner
        test_performance_tuner()
        
        print("\n" + "=" * 50)
        print("Demo completed successfully!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())