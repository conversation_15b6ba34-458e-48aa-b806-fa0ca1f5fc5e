apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker-manager
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: worker-manager
    app.kubernetes.io/part-of: distributed-etl
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: breaking-bright
      app.kubernetes.io/component: worker-manager
  template:
    metadata:
      labels:
        app.kubernetes.io/name: breaking-bright
        app.kubernetes.io/component: worker-manager
        app.kubernetes.io/part-of: distributed-etl
    spec:
      containers:
      - name: worker-manager
        image: robertschulze/breaking-bright-worker:latest
        imagePullPolicy: Always
        command: ["python", "-m", "backend.app.distributed.worker_manager"]
        ports:
        - containerPort: 8080
          name: health-metrics
          protocol: TCP
        env:
        - name: WORKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: WORKER_STAGES
          value: "all"
        - name: WORKER_BATCH_SIZE
          value: "25"
        - name: WORKER_HEALTH_CHECK_PORT
          value: "8080"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: openai-api-key
        - name: IONOS_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: ionos-api-key
        - name: HUGGINGFACE_TOKEN
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: huggingface-token
        - name: STABILITY_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: stability-api-key
        envFrom:
        - configMapRef:
            name: distributed-worker-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: worker-logs
          mountPath: /var/log/worker
      volumes:
      - name: worker-logs
        emptyDir: {}
      imagePullSecrets:
      - name: docker-registry-secret
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: worker-manager-service
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: worker-manager
spec:
  selector:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: worker-manager
  ports:
  - name: health-metrics
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP