{% extends "base.html" %}

{% block title %}Workers - Distributed ETL{% endblock %}

{% block content %}
<div class="row">
    <!-- Worker Status Overview -->
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> Worker Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h3 class="text-success" id="running-count">--</h3>
                        <small class="text-muted">Running</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-secondary" id="stopped-count">--</h3>
                        <small class="text-muted">Stopped</small>
                    </div>
                    <div class="col-4">
                        <h3 class="text-danger" id="error-count">--</h3>
                        <small class="text-muted">Error</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Worker Actions -->
    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-cogs"></i> Worker Management
                </h5>
                <div>
                    <button class="btn btn-sm btn-success" onclick="startAllWorkers()">
                        <i class="fas fa-play"></i> Start All
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="stopAllWorkers()">
                        <i class="fas fa-stop"></i> Stop All
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearErrorWorkers()">
                        <i class="fas fa-trash"></i> Clear Error Workers
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshWorkers()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    Worker management functionality will be implemented in the next phase.
                    Currently showing read-only worker status.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Worker List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> Active Workers
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="cursor: pointer;" onclick="sortWorkers('worker_id')">Worker ID <i class="fas fa-sort"></i></th>
                                <th style="cursor: pointer;" onclick="sortWorkers('status')">Status <i class="fas fa-sort"></i></th>
                                <th>Stages</th>
                                <th style="cursor: pointer;" onclick="sortWorkers('last_heartbeat')">Last Heartbeat <i class="fas fa-sort-down"></i></th>
                                <th style="cursor: pointer;" onclick="sortWorkers('started_at')">Started At <i class="fas fa-sort"></i></th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="workers-table">
                            <tr>
                                <td colspan="6" class="text-center text-muted">Loading workers...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Worker Details Modal -->
<div class="modal fade" id="workerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Worker Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="worker-details">
                <!-- Worker details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let workersData = [];
let sortColumn = 'last_heartbeat';
let sortDirection = 'desc';

async function loadWorkers() {
    try {
        const response = await fetch('/api/workers/status');
        const data = await response.json();
        
        workersData = data.workers;
        updateWorkerOverview(data);
        updateWorkersTable(data.workers);
        
    } catch (error) {
        console.error('Error loading workers:', error);
        showError('Failed to load worker data');
    }
}

function updateWorkerOverview(data) {
    document.getElementById('running-count').textContent = data.running;
    document.getElementById('stopped-count').textContent = data.stopped;
    document.getElementById('error-count').textContent = data.error;
}

function updateWorkersTable(workers) {
    const tbody = document.getElementById('workers-table');

    if (workers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No workers found</td></tr>';
        return;
    }

    // Sort workers
    const sortedWorkers = [...workers].sort((a, b) => {
        let aVal = a[sortColumn];
        let bVal = b[sortColumn];

        // Handle null values
        if (aVal === null && bVal === null) return 0;
        if (aVal === null) return sortDirection === 'asc' ? -1 : 1;
        if (bVal === null) return sortDirection === 'asc' ? 1 : -1;

        // Handle dates
        if (sortColumn === 'last_heartbeat' || sortColumn === 'started_at') {
            aVal = aVal ? new Date(aVal) : new Date(0);
            bVal = bVal ? new Date(bVal) : new Date(0);
        }

        // Compare values
        if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    tbody.innerHTML = sortedWorkers.map(worker => {
        const statusClass = getStatusClass(worker.status);
        const statusIcon = getStatusIcon(worker.status);
        const stages = Array.isArray(worker.stages) ? worker.stages.join(', ') : 'N/A';
        const lastHeartbeat = worker.last_heartbeat ?
            new Date(worker.last_heartbeat).toLocaleString() : 'Never';
        const startedAt = worker.started_at ?
            new Date(worker.started_at).toLocaleString() : 'N/A';

        return `
            <tr>
                <td>
                    <code>${worker.worker_id}</code>
                </td>
                <td>
                    <span class="badge ${statusClass}">
                        <i class="${statusIcon}"></i> ${worker.status}
                    </span>
                </td>
                <td>
                    <small>${stages}</small>
                </td>
                <td>${lastHeartbeat}</td>
                <td>${startedAt}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="showWorkerDetails('${worker.worker_id}')">
                        <i class="fas fa-info"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="restartWorker('${worker.worker_id}')" disabled>
                        <i class="fas fa-redo"></i>
                    </button>
                </td>
            </tr>
        `;
    }).join('');
}

function getStatusClass(status) {
    switch (status) {
        case 'running': return 'bg-success';
        case 'stopped': return 'bg-secondary';
        case 'error': return 'bg-danger';
        default: return 'bg-warning';
    }
}

function getStatusIcon(status) {
    switch (status) {
        case 'running': return 'fas fa-play';
        case 'stopped': return 'fas fa-stop';
        case 'error': return 'fas fa-exclamation-triangle';
        default: return 'fas fa-question';
    }
}

function showWorkerDetails(workerId) {
    const worker = workersData.find(w => w.worker_id === workerId);
    if (!worker) return;

    // Format JSON data for display
    function formatJsonData(data, label) {
        if (!data) return '';
        try {
            const parsed = typeof data === 'string' ? JSON.parse(data) : data;
            return `
                <div class="mt-3">
                    <h6>${label}</h6>
                    <pre class="bg-light p-2 rounded" style="max-height: 300px; overflow-y: auto; font-size: 0.8em;"><code>${JSON.stringify(parsed, null, 2)}</code></pre>
                </div>
            `;
        } catch (e) {
            return `
                <div class="mt-3">
                    <h6>${label}</h6>
                    <div class="text-muted">Invalid JSON data</div>
                </div>
            `;
        }
    }

    const details = `
        <div class="row">
            <div class="col-md-6">
                <h6>Basic Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Worker ID:</strong></td><td><code>${worker.worker_id}</code></td></tr>
                    <tr><td><strong>Status:</strong></td><td><span class="badge ${getStatusClass(worker.status)}">${worker.status}</span></td></tr>
                    <tr><td><strong>Started At:</strong></td><td>${worker.started_at ? new Date(worker.started_at).toLocaleString() : 'N/A'}</td></tr>
                    <tr><td><strong>Last Heartbeat:</strong></td><td>${worker.last_heartbeat ? new Date(worker.last_heartbeat).toLocaleString() : 'Never'}</td></tr>
                    <tr><td><strong>Host:</strong></td><td>${worker.host || 'N/A'}</td></tr>
                    <tr><td><strong>PID:</strong></td><td>${worker.pid || 'N/A'}</td></tr>
                    <tr><td><strong>Version:</strong></td><td>${worker.version || 'N/A'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Processing Stages</h6>
                <div class="d-flex flex-wrap gap-1">
                    ${Array.isArray(worker.stages) ? worker.stages.map(stage =>
                        `<span class="badge bg-primary">${stage}</span>`
                    ).join('') : '<span class="text-muted">No stages configured</span>'}
                </div>

                <h6 class="mt-3">Additional Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Error Message:</strong></td><td>${worker.error_message || 'None'}</td></tr>
                    <tr><td><strong>Error Count:</strong></td><td>${worker.error_count || 0}</td></tr>
                    <tr><td><strong>Last Error:</strong></td><td>${worker.last_error ? new Date(worker.last_error).toLocaleString() : 'Never'}</td></tr>
                </table>
            </div>
        </div>

        ${formatJsonData(worker.progress, 'Progress Data')}
        ${formatJsonData(worker.config, 'Configuration')}
        ${formatJsonData(worker.metadata, 'Metadata')}
    `;

    document.getElementById('worker-details').innerHTML = details;
    new bootstrap.Modal(document.getElementById('workerModal')).show();
}

function sortWorkers(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = column === 'last_heartbeat' ? 'desc' : 'asc';
    }

    // Update table
    updateWorkersTable(workersData);

    // Update sort indicators
    const headers = document.querySelectorAll('#workers-table thead th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (icon) {
            if (header.textContent.toLowerCase().includes(column.replace('_', ' '))) {
                icon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            } else {
                icon.className = 'fas fa-sort';
            }
        }
    });
}

async function clearErrorWorkers() {
    try {
        const response = await fetch('/api/workers/clear_errors', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.error) {
            console.error(`Error clearing workers: ${result.error}`);
            // Show error in status area instead of popup
            showStatus(`Error clearing workers: ${result.error}`, 'error');
        } else {
            // Log success but don't show popup
            console.log(`Cleared ${result.cleared_count} error workers`);
            if (result.cleared_count > 0) {
                showStatus(`Cleared ${result.cleared_count} error workers`, 'success');
            }
            loadWorkers(); // Refresh the table
        }
    } catch (error) {
        console.error(`Error: ${error.message}`);
        showStatus(`Error: ${error.message}`, 'error');
    }
}

function refreshWorkers() {
    loadWorkers();
}

function startAllWorkers() {
    alert('Start All Workers functionality will be implemented in the next phase.');
}

function stopAllWorkers() {
    alert('Stop All Workers functionality will be implemented in the next phase.');
}

function restartWorker(workerId) {
    alert(`Restart Worker ${workerId} functionality will be implemented in the next phase.`);
}

function showError(message) {
    // Simple error display - could be enhanced with toast notifications
    console.error(message);
}

function showStatus(message, type = 'info') {
    // Create or update status message area
    let statusArea = document.getElementById('status-area');
    if (!statusArea) {
        statusArea = document.createElement('div');
        statusArea.id = 'status-area';
        statusArea.className = 'alert alert-dismissible fade show';
        statusArea.style.position = 'fixed';
        statusArea.style.top = '20px';
        statusArea.style.right = '20px';
        statusArea.style.zIndex = '9999';
        statusArea.style.minWidth = '300px';
        document.body.appendChild(statusArea);
    }

    // Set alert type
    statusArea.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;

    statusArea.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Auto-hide after 3 seconds
    setTimeout(() => {
        if (statusArea && statusArea.parentNode) {
            statusArea.remove();
        }
    }, 3000);
}

function updateDashboard(data) {
    // Handle WebSocket updates
    if (data.type === 'worker_update') {
        loadWorkers();
    }
}

// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    loadWorkers();
    
    // Refresh every 30 seconds
    setInterval(loadWorkers, 30000);
});
</script>
{% endblock %}
