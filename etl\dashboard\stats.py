from flask import jsonify, render_template
import logging
from sqlalchemy import create_engine, func, text
from sqlalchemy.pool import NullPool
from sqlalchemy.orm import sessionmaker
from backend.app.models.models import Entry
from backend.app.core.config import settings
from contextlib import contextmanager
from etl.shared.progress_tracker import thread_progress
from datetime import datetime, timezone, timedelta

@contextmanager
def create_session():
    """Context manager for database sessions"""
    engine = create_engine(settings.DATABASE_URL, poolclass=NullPool)
    Session = sessionmaker(bind=engine)
    session = Session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        raise e
    finally:
        session.close()

def register_stats_routes(app, config):
    @app.route('/')
    def dashboard():
        """Serve the main dashboard page."""
        return render_template('dashboard.html')

    @app.route('/progress', methods=['GET'])
    def progress():
        """Return the progress of all threads in JSON format."""
        return jsonify(thread_progress)

    @app.route('/stats/news_by_source', methods=['GET'])
    def news_by_source():
        """Return news counts by date with various sources."""
        try:
            logging.info("Accessing /stats/news_by_source endpoint")
            
            with create_session() as session:
                # Get data for the last 7 days
                end_date = datetime.now(timezone.utc)
                start_date = end_date - timedelta(days=7)

                # Convert dates to date strings for grouping
                date_trunc = func.trunc(Entry.published, 'DD')

                # Query for news counts by source and date
                news_by_source = session.query(
                    date_trunc.label('date'),
                    Entry.source,
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date
                ).group_by(date_trunc, Entry.source).all()

                # Get all unique dates
                dates = sorted(set([row[0].strftime('%Y-%m-%d') for row in news_by_source]))

                # Get all unique sources from the results
                unique_sources = sorted(set([row[1] for row in news_by_source]))

                # Create a nested dictionary: source -> date -> count
                source_date_counts = {source: {} for source in unique_sources}
                for row in news_by_source:
                    date_str = row[0].strftime('%Y-%m-%d')
                    source = row[1]
                    count = row[2]
                    source_date_counts[source][date_str] = count

                # Prepare the datasets for the chart
                datasets = []
                colors = ['#3e95cd', '#8e5ea2', '#3cba9f', '#e8c3b9', '#c45850', 
                         '#ff9f40', '#ffcd56', '#4bc0c0', '#36a2eb', '#9966ff']

                for i, source in enumerate(unique_sources):
                    color_index = i % len(colors)
                    # Use original source identifier instead of translated name
                    display_name = source
                    if len(display_name) > 30:  # Truncate long names
                        display_name = display_name[:27] + '...'

                    datasets.append({
                        'label': display_name,
                        'data': [source_date_counts[source].get(date, 0) for date in dates],
                        'borderColor': colors[color_index],
                        'fill': False
                    })

                # Prepare the response
                result = {
                    'dates': dates,
                    'datasets': datasets
                }

                return jsonify(result)

        except Exception as e:
            logging.error(f"Error in news_by_source: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/stats/positive_news_by_source', methods=['GET'])
    def positive_news_by_source():
        """Return percentage of positive news by source in descending order."""
        try:
            logging.info("Accessing /stats/positive_news_by_source endpoint")

            with create_session() as session:
                # Query for total news count by source
                total_by_source = session.query(
                    Entry.source,
                    func.count(Entry.entry_id).label('total')
                ).filter(
                    Entry.llm_positive != None
                ).group_by(Entry.source).all()

                # Query for positive news count by source
                positive_by_source = session.query(
                    Entry.source,
                    func.count(Entry.entry_id).label('positive')
                ).filter(
                    Entry.llm_positive != None,
                    Entry.llm_is_ad < 0.5,
                    Entry.llm_positive >= 0.7
                ).group_by(Entry.source).all()

                # Create dictionaries for easier lookup
                total_dict = {row[0]: row[1] for row in total_by_source}
                positive_dict = {row[0]: row[1] for row in positive_by_source}

                # Calculate percentages and prepare data
                source_data = []
                for source, total in total_dict.items():
                    if total > 0:
                        positive = positive_dict.get(source, 0)
                        percentage = (positive / total) * 100
                        display_name = source
                        if len(display_name) > 30:
                            display_name = display_name[:27] + '...'

                        source_data.append({
                            'source': display_name,
                            'percentage': percentage,
                            'total': total,
                            'positive': positive
                        })

                # Sort by percentage in descending order
                source_data.sort(key=lambda x: x['percentage'], reverse=True)

                result = {
                    'labels': [item['source'] for item in source_data],
                    'percentages': [item['percentage'] for item in source_data],
                    'totals': [item['total'] for item in source_data],
                    'positives': [item['positive'] for item in source_data]
                }

                return jsonify(result)

        except Exception as e:
            logging.error(f"Error in positive_news_by_source: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/stats/news_by_state', methods=['GET'])
    def news_by_state():
        """Return news counts by date with various states."""
        try:
            with create_session() as session:
                # Get data for the last 7 days
                end_date = datetime.now(timezone.utc)
                start_date = end_date - timedelta(days=7)

                # Convert dates to date strings for grouping
                date_trunc = func.trunc(Entry.published, 'DD')

                # Query for each state separately with dates
                states_data = []
                
                # New entries (only published date)
                new_entries = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    # An entry is considered "new" if it was published on that date AND still doesn't have full_text
                    Entry.full_text.is_(None),
                    Entry.dup_entry_id == Entry.entry_id  # Not a duplicate
                ).group_by(date_trunc).all()
                
                # Downloaded (has full_text)
                downloaded = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.full_text.isnot(None)
                ).group_by(date_trunc).all()

                # Translated
                translated = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.english_text.isnot(None),
                    Entry.embedding.is_(None)
                ).group_by(date_trunc).all()

                # Embedded
                embedded = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.embedding.isnot(None)  # Only check if embedding exists
                ).group_by(date_trunc).all()

                # Advertisements
                ads = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.llm_is_ad == 1
                ).group_by(date_trunc).all()

                # Duplicates
                duplicates = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.dup_entry_id != Entry.entry_id
                ).group_by(date_trunc).all()

                # With Description
                with_desc = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.description.isnot(None)
                ).group_by(date_trunc).all()

                # Fully Processed
                processed = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.llm_positive.isnot(None),
                    Entry.llm_is_ad != 1,
                    Entry.dup_entry_id == Entry.entry_id,
                    Entry.description.isnot(None)
                ).group_by(date_trunc).all()

                # Get all unique dates
                all_dates = session.query(date_trunc.label('date')).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date
                ).group_by(date_trunc).order_by(date_trunc).all()  # Add order_by

                dates = [d[0].strftime('%Y-%m-%d') for d in all_dates]

                # Convert query results to dictionaries
                def query_to_dict(query_result):
                    return {row[0].strftime('%Y-%m-%d'): row[1] for row in query_result}

                new_dict = query_to_dict(new_entries)
                downloaded_dict = query_to_dict(downloaded)
                translated_dict = query_to_dict(translated)
                embedded_dict = query_to_dict(embedded)
                ads_dict = query_to_dict(ads)
                duplicates_dict = query_to_dict(duplicates)
                with_desc_dict = query_to_dict(with_desc)
                processed_dict = query_to_dict(processed)

                # Add total entries query
                total_entries = session.query(
                    date_trunc.label('date'),
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date
                ).group_by(date_trunc).all()

                total_dict = query_to_dict(total_entries)

                # Prepare the response
                result = {
                    'dates': dates,
                    'datasets': [
                        {
                            'label': 'Total',
                            'data': [total_dict.get(date, 0) for date in dates],
                            'borderColor': '#000000',  # Black
                            'fill': False
                        },
                        {
                            'label': 'New',
                            'data': [new_dict.get(date, 0) for date in dates],
                            'borderColor': '#FFB6C1',  # Light Pink
                            'fill': False
                        },
                        {
                            'label': 'Downloaded',
                            'data': [downloaded_dict.get(date, 0) for date in dates],
                            'borderColor': '#ADD8E6',  # Light Blue
                            'fill': False
                        },
                        {
                            'label': 'Translated',
                            'data': [translated_dict.get(date, 0) for date in dates],
                            'borderColor': '#98FB98',  # Pale Green
                            'fill': False
                        },
                        {
                            'label': 'Embedded',
                            'data': [embedded_dict.get(date, 0) for date in dates],
                            'borderColor': '#DDA0DD',  # Plum
                            'fill': False
                        },
                        {
                            'label': 'Advertisements',
                            'data': [ads_dict.get(date, 0) for date in dates],
                            'borderColor': '#F0E68C',  # Khaki
                            'fill': False
                        },
                        {
                            'label': 'Duplicates',
                            'data': [duplicates_dict.get(date, 0) for date in dates],
                            'borderColor': '#D3D3D3',  # Light Gray
                            'fill': False
                        },
                        {
                            'label': 'With Description',
                            'data': [with_desc_dict.get(date, 0) for date in dates],
                            'borderColor': '#FFA07A',  # Light Salmon
                            'fill': False
                        },
                        {
                            'label': 'Processed',
                            'data': [processed_dict.get(date, 0) for date in dates],
                            'borderColor': '#B0C4DE',  # Light Steel Blue
                            'fill': False
                        }
                    ]
                }

                return jsonify(result)

        except Exception as e:
            logging.error(f"Error in news_by_state: {e}")
            return jsonify({'error': str(e)}), 500

    @app.route('/stats/duplicates_by_source', methods=['GET'])
    def duplicates_by_source():
        """Return duplicate counts by date and source."""
        try:
            with create_session() as session:
                # Get data for the last 7 days
                end_date = datetime.now(timezone.utc)
                start_date = end_date - timedelta(days=7)

                # Convert dates to date strings for grouping
                date_trunc = func.trunc(Entry.published, 'DD')

                # Query for duplicate counts by source and date
                duplicates = session.query(
                    date_trunc.label('date'),
                    Entry.source,
                    func.count(Entry.entry_id).label('count')
                ).filter(
                    Entry.published >= start_date,
                    Entry.published <= end_date,
                    Entry.entry_id != Entry.dup_entry_id  # This identifies duplicates
                ).group_by(date_trunc, Entry.source).all()

                # Get all unique dates and sources
                dates = sorted(set([row[0].strftime('%Y-%m-%d') for row in duplicates]))
                unique_sources = sorted(set([row[1] for row in duplicates]))

                # Create a nested dictionary: source -> date -> count
                source_date_counts = {source: {} for source in unique_sources}
                for row in duplicates:
                    date_str = row[0].strftime('%Y-%m-%d')
                    source = row[1]
                    count = row[2]
                    source_date_counts[source][date_str] = count

                # Prepare the datasets
                colors = ['#3e95cd', '#8e5ea2', '#3cba9f', '#e8c3b9', '#c45850', 
                         '#ff9f40', '#ffcd56', '#4bc0c0', '#36a2eb', '#9966ff']
                
                datasets = []
                for i, source in enumerate(unique_sources):
                    color_index = i % len(colors)
                    display_name = source
                    if len(display_name) > 30:
                        display_name = display_name[:27] + '...'

                    datasets.append({
                        'label': display_name,
                        'data': [source_date_counts[source].get(date, 0) for date in dates],
                        'borderColor': colors[color_index],
                        'fill': False
                    })

                return jsonify({
                    'dates': dates,
                    'datasets': datasets
                })

        except Exception as e:
            logging.error(f"Error in duplicates_by_source: {e}")
            return jsonify({'error': str(e)}), 500

    return app
















