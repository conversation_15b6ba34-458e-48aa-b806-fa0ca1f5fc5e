"""
Tests for FeedDownloadWorker.

This module provides comprehensive tests for the FeedDownloadWorker class,
including unit tests and integration tests with sample RSS feeds.
"""

import pytest
import tempfile
import os
import yaml
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
import pandas as pd
import feedparser

from backend.app.distributed.workers.feed_download_worker import FeedDownloadWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestFeedDownloadWorker:
    """Test suite for FeedDownloadWorker."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.bind = Mock()
        mock_session.execute.return_value.rowcount = 5
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        return WorkerConfig(
            worker_id="test-feed-worker",
            stages=[ProcessingStage.DOWNLOAD_FEEDS],
            batch_size=10,
            stage_configs={
                ProcessingStage.DOWNLOAD_FEEDS.value: {
                    'feed_timeout': 15,
                    'max_concurrent_feeds': 2
                }
            }
        )
    
    @pytest.fixture
    def sample_rss_sources(self):
        """Create sample RSS sources for testing."""
        return [
            {
                'url': 'https://example.com/feed1.rss',
                'clean_name': 'Example News',
                'entry_id_from': 'link,published'
            },
            {
                'url': 'https://golem.de/feed.rss',
                'clean_name': 'Golem',
                'entry_id_from': 'link,published'
            }
        ]
    
    @pytest.fixture
    def temp_rss_config_file(self, sample_rss_sources):
        """Create a temporary RSS configuration file."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump({'rss_sources': sample_rss_sources}, f)
            temp_file = f.name
        
        yield temp_file
        
        # Cleanup
        os.unlink(temp_file)
    
    @patch('backend.app.distributed.workers.feed_download_worker.settings')
    def test_worker_initialization(self, mock_settings, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test worker initialization."""
        mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
        
        worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        assert worker.worker_id == "test-feed-worker"
        assert ProcessingStage.DOWNLOAD_FEEDS in worker.config.stages
        assert worker.feed_timeout == 15
        assert worker.max_concurrent_feeds == 2
        assert len(worker.rss_sources) == 2
    
    def test_worker_initialization_invalid_stage(self, mock_db_session_factory):
        """Test worker initialization with invalid stage."""
        invalid_config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT]  # Wrong stage
        )
        
        with pytest.raises(ValueError, match="FeedDownloadWorker requires DOWNLOAD_FEEDS stage"):
            FeedDownloadWorker(invalid_config, mock_db_session_factory)
    
    @patch('backend.app.distributed.workers.feed_download_worker.settings')
    def test_load_rss_sources_file_not_found(self, mock_settings, worker_config, mock_db_session_factory):
        """Test RSS sources loading when file doesn't exist."""
        mock_settings.NEWS_RSS_SOURCES_FILE = 'nonexistent.yaml'
        
        worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        assert worker.rss_sources == []
    
    def test_generate_entry_id(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test entry ID generation."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        # Mock RSS entry
        mock_entry = Mock()
        mock_entry.link = "https://example.com/article1"
        mock_entry.published = "2024-01-01"
        
        source_config = {'entry_id_from': 'link,published'}
        
        entry_id = worker._generate_entry_id(mock_entry, source_config)
        
        assert isinstance(entry_id, str)
        assert len(entry_id) == 64  # SHA256 hash length
    
    def test_clean_html_content(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test HTML content cleaning."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        html_content = "<p>This is a <br>test article</p><div>with multiple paragraphs</div>"
        cleaned = worker._clean_html_content(html_content)
        
        assert "<p>" not in cleaned
        assert "<br>" not in cleaned
        assert "<div>" not in cleaned
        assert "test article" in cleaned
        assert "multiple paragraphs" in cleaned
    
    def test_clean_html_content_empty(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test HTML content cleaning with empty input."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        assert worker._clean_html_content("") == ""
        assert worker._clean_html_content(None) == ""
    
    def test_preprocess_entries_golem(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test entry preprocessing for Golem source."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        # Create test DataFrame
        df = pd.DataFrame([
            {'title': 'Regular article', 'entry_id': '1'},
            {'title': 'Anzeige: Sponsored content', 'entry_id': '2'},
            {'title': 'Premium article (g+)', 'entry_id': '3'},
            {'title': 'Another regular article', 'entry_id': '4'}
        ])
        
        source = {'clean_name': 'Golem'}
        processed_df = worker._preprocess_entries(df, source)
        
        # Should remove entries with "Anzeige:" and "(g+)"
        assert len(processed_df) == 2
        assert 'Anzeige:' not in processed_df['title'].values[0]
        assert '(g+)' not in processed_df['title'].values[1]
    
    def test_preprocess_entries_stiftung_warentest(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test entry preprocessing for Stiftung Warentest source."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        # Create test DataFrame
        df = pd.DataFrame([
            {'title': 'Test results for smartphones', 'entry_id': '1'},
            {'title': 'Tagesgeld rates comparison', 'entry_id': '2'},
            {'title': 'Festgeld investment guide', 'entry_id': '3'},
            {'title': 'Product safety review', 'entry_id': '4'}
        ])
        
        source = {'clean_name': 'Stiftung Warentest'}
        processed_df = worker._preprocess_entries(df, source)
        
        # Should remove financial entries
        assert len(processed_df) == 2
        remaining_titles = processed_df['title'].tolist()
        assert 'Test results for smartphones' in remaining_titles
        assert 'Product safety review' in remaining_titles
    
    @patch('backend.app.distributed.workers.feed_download_worker.feedparser')
    def test_download_and_process_feed_success(self, mock_feedparser, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test successful feed download and processing."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        # Mock feedparser response
        mock_feed = Mock()
        mock_feed.bozo = False
        mock_entry = Mock()
        mock_entry.title = "Test Article"
        mock_entry.link = "https://example.com/article1"
        mock_entry.description = "<p>Test description</p>"
        mock_entry.published_parsed = (2024, 1, 1, 12, 0, 0, 0, 1, -1)
        mock_feed.entries = [mock_entry]
        mock_feedparser.parse.return_value = mock_feed
        
        # Mock _save_entries to return success
        with patch.object(worker, '_save_entries', return_value=1):
            source = {
                'url': 'https://example.com/feed.rss',
                'clean_name': 'Example',
                'entry_id_from': 'link,published'
            }
            
            result = worker._download_and_process_feed(source)
            
            assert result == 1
            mock_feedparser.parse.assert_called_once_with('https://example.com/feed.rss')
    
    @patch('backend.app.distributed.workers.feed_download_worker.feedparser')
    def test_download_and_process_feed_bozo_error(self, mock_feedparser, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test feed download with parsing error."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        # Mock feedparser response with error
        mock_feed = Mock()
        mock_feed.bozo = True
        mock_feed.bozo_exception = "Invalid XML"
        mock_feedparser.parse.return_value = mock_feed
        
        source = {
            'url': 'https://example.com/bad-feed.rss',
            'clean_name': 'Example',
            'entry_id_from': 'link'
        }
        
        result = worker._download_and_process_feed(source)
        
        assert result is None
    
    @patch('backend.app.distributed.workers.feed_download_worker.feedparser')
    def test_download_and_process_feed_no_entries(self, mock_feedparser, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test feed download with no entries."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        # Mock feedparser response with no entries
        mock_feed = Mock()
        mock_feed.bozo = False
        mock_feed.entries = []
        mock_feedparser.parse.return_value = mock_feed
        
        source = {
            'url': 'https://example.com/empty-feed.rss',
            'clean_name': 'Example',
            'entry_id_from': 'link'
        }
        
        result = worker._download_and_process_feed(source)
        
        assert result == 0
    
    def test_save_entries_empty_dataframe(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test saving empty DataFrame."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        empty_df = pd.DataFrame()
        result = worker._save_entries(empty_df)
        
        assert result == 0
    
    def test_save_entries_database_error(self, worker_config, temp_rss_config_file):
        """Test saving entries with database error."""
        # Create a mock session factory that raises an exception
        def failing_session_factory():
            raise Exception("Database connection failed")
        
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, failing_session_factory)
        
        df = pd.DataFrame([
            {'entry_id': '1', 'title': 'Test', 'link': 'http://example.com', 'source': 'test'}
        ])
        
        result = worker._save_entries(df)
        
        assert result == 0
    
    def test_process_batch_wrong_stage(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test process_batch with wrong stage."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        entries = []
        result = worker.process_batch(entries, ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        assert result == {}
    
    @patch('backend.app.distributed.workers.feed_download_worker.feedparser')
    def test_process_batch_success(self, mock_feedparser, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test successful batch processing."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        # Mock feedparser to return successful feeds
        mock_feed = Mock()
        mock_feed.bozo = False
        mock_entry = Mock()
        mock_entry.title = "Test Article"
        mock_entry.link = "https://example.com/article1"
        mock_entry.description = "Test description"
        mock_entry.published_parsed = (2024, 1, 1, 12, 0, 0, 0, 1, -1)
        mock_feed.entries = [mock_entry]
        mock_feedparser.parse.return_value = mock_feed
        
        # Mock _save_entries to return success
        with patch.object(worker, '_save_entries', return_value=1):
            entries = []  # Not used for feed download
            result = worker.process_batch(entries, ProcessingStage.DOWNLOAD_FEEDS)
            
            # Should have results for all RSS sources
            assert len(result) == len(worker.rss_sources)
            assert all(success for success in result.values())
    
    def test_get_worker_specific_health(self, worker_config, mock_db_session_factory, temp_rss_config_file):
        """Test worker-specific health information."""
        with patch('backend.app.distributed.workers.feed_download_worker.settings') as mock_settings:
            mock_settings.NEWS_RSS_SOURCES_FILE = temp_rss_config_file
            worker = FeedDownloadWorker(worker_config, mock_db_session_factory)
        
        health = worker.get_worker_specific_health()
        
        assert health['worker_type'] == 'FeedDownloadWorker'
        assert health['feed_timeout'] == 15
        assert health['max_concurrent_feeds'] == 2
        assert health['rss_sources_count'] == 2
        assert ProcessingStage.DOWNLOAD_FEEDS.value in health['supported_stages']


class TestFeedDownloadWorkerIntegration:
    """Integration tests for FeedDownloadWorker with real RSS feeds."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.bind = Mock()
        mock_session.execute.return_value.rowcount = 5
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def integration_worker_config(self):
        """Create worker configuration for integration tests."""
        return WorkerConfig(
            worker_id="integration-feed-worker",
            stages=[ProcessingStage.DOWNLOAD_FEEDS],
            batch_size=5,
            stage_configs={
                ProcessingStage.DOWNLOAD_FEEDS.value: {
                    'feed_timeout': 10,
                    'max_concurrent_feeds': 1
                }
            }
        )
    
    @pytest.fixture
    def real_rss_sources(self):
        """Create real RSS sources for integration testing."""
        return [
            {
                'url': 'https://feeds.feedburner.com/oreilly/radar',
                'clean_name': 'O\'Reilly Radar',
                'entry_id_from': 'link,published'
            }
        ]
    
    @pytest.fixture
    def integration_rss_config_file(self, real_rss_sources):
        """Create RSS configuration file with real feeds."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump({'rss_sources': real_rss_sources}, f)
            temp_file = f.name
        
        yield temp_file
        
        # Cleanup
        os.unlink(temp_file)
    
    @pytest.mark.integration
    @patch('backend.app.distributed.workers.feed_download_worker.settings')
    def test_real_feed_download(self, mock_settings, integration_worker_config, mock_db_session_factory, integration_rss_config_file):
        """Test downloading from a real RSS feed."""
        mock_settings.NEWS_RSS_SOURCES_FILE = integration_rss_config_file
        
        worker = FeedDownloadWorker(integration_worker_config, mock_db_session_factory)
        
        # Mock _save_entries to avoid database operations
        with patch.object(worker, '_save_entries', return_value=5):
            entries = []
            result = worker.process_batch(entries, ProcessingStage.DOWNLOAD_FEEDS)
            
            # Should have processed the feed successfully
            assert len(result) == 1
            # Note: This might fail if the feed is down, but that's expected for integration tests


if __name__ == '__main__':
    pytest.main([__file__])