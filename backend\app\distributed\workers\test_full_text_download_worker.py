"""
Integration tests for FullTextDownloadWorker.

Tests the full text download worker with sample entries and various scenarios
including successful downloads, failed downloads, and error handling.
"""

import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
from typing import List

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.app.distributed.workers.full_text_download_worker import FullTextDownloadWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry, Base

logger = logging.getLogger(__name__)


class TestFullTextDownloadWorker:
    """Test suite for FullTextDownloadWorker."""
    
    @pytest.fixture
    def db_session_factory(self):
        """Create an in-memory SQLite database for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        
        def get_session():
            return SessionLocal()
        
        return get_session
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        return WorkerConfig(
            worker_id="test-download-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            batch_size=5,
            heartbeat_interval_seconds=30,
            stage_configs={
                ProcessingStage.DOWNLOAD_FULL_TEXT.value: {
                    'download_timeout': 10,
                    'max_concurrent_downloads': 3
                }
            }
        )
    
    @pytest.fixture
    def sample_entries(self, db_session_factory):
        """Create sample entries for testing."""
        session = db_session_factory()
        
        entries = [
            Entry(
                entry_id="test-entry-1",
                title="Test Article 1",
                link="https://example.com/article1",
                description="Test description 1",
                published=datetime.now(timezone.utc),
                full_text=None
            ),
            Entry(
                entry_id="test-entry-2", 
                title="Test Article 2",
                link="https://example.com/article2",
                description="Test description 2",
                published=datetime.now(timezone.utc),
                full_text=None
            ),
            Entry(
                entry_id="test-entry-3",
                title="Test Article 3",
                link="",  # Empty link to test error handling
                description="Test description 3",
                published=datetime.now(timezone.utc),
                full_text=None
            )
        ]
        
        for entry in entries:
            session.add(entry)
        session.commit()
        
        # Refresh entries to ensure they're loaded
        for entry in entries:
            session.refresh(entry)
        
        # Detach entries from session so they can be used independently
        session.expunge_all()
        session.close()
        
        return entries
    
    def test_worker_initialization(self, worker_config, db_session_factory):
        """Test worker initialization with correct configuration."""
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        
        assert worker.worker_id == "test-download-worker"
        assert ProcessingStage.DOWNLOAD_FULL_TEXT in worker.config.stages
        assert worker.download_timeout == 10
        assert worker.max_concurrent_downloads == 3
    
    def test_worker_initialization_invalid_stage(self, db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="invalid-worker",
            stages=[ProcessingStage.SENTIMENT_ANALYSIS],  # Wrong stage
            batch_size=5
        )
        
        with pytest.raises(ValueError, match="requires DOWNLOAD_FULL_TEXT stage"):
            FullTextDownloadWorker(invalid_config, db_session_factory)
    
    @patch('backend.app.distributed.workers.full_text_download_worker.newspaper.Article')
    def test_successful_batch_processing(self, mock_article_class, worker_config, 
                                       db_session_factory, sample_entries):
        """Test successful batch processing with mock newspaper responses."""
        # Setup mock article responses
        mock_article1 = Mock()
        mock_article1.text = "This is the full text of article 1"
        
        mock_article2 = Mock()
        mock_article2.text = "This is the full text of article 2"
        
        mock_article_class.side_effect = [mock_article1, mock_article2]
        
        # Create worker
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        
        # Process batch (excluding entry with empty link)
        entries_to_process = [entry for entry in sample_entries if entry.link]
        results = worker.process_batch(entries_to_process, ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Verify results
        assert len(results) == 2
        assert results["test-entry-1"] == True
        assert results["test-entry-2"] == True
        
        # Verify database updates
        session = db_session_factory()
        updated_entry1 = session.query(Entry).filter_by(entry_id="test-entry-1").first()
        updated_entry2 = session.query(Entry).filter_by(entry_id="test-entry-2").first()
        
        assert updated_entry1.full_text == "This is the full text of article 1"
        assert updated_entry2.full_text == "This is the full text of article 2"
        
        session.close()
    
    @patch('backend.app.distributed.workers.full_text_download_worker.newspaper.Article')
    def test_empty_text_handling(self, mock_article_class, worker_config, 
                                db_session_factory, sample_entries):
        """Test handling of articles with no extractable text."""
        # Setup mock article with empty text
        mock_article = Mock()
        mock_article.text = ""  # Empty text
        mock_article_class.return_value = mock_article
        
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        
        # Process single entry
        entry = [entry for entry in sample_entries if entry.entry_id == "test-entry-1"][0]
        results = worker.process_batch([entry], ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Should still succeed but with placeholder text
        assert results["test-entry-1"] == True
        
        # Verify placeholder text was stored
        session = db_session_factory()
        updated_entry = session.query(Entry).filter_by(entry_id="test-entry-1").first()
        assert updated_entry.full_text == "<n/a>"
        session.close()
    
    def test_empty_link_handling(self, worker_config, db_session_factory, sample_entries):
        """Test handling of entries with empty or missing links."""
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        
        # Process entry with empty link
        entry = [entry for entry in sample_entries if entry.entry_id == "test-entry-3"][0]
        results = worker.process_batch([entry], ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Should succeed with placeholder text
        assert results["test-entry-3"] == True
        
        # Verify placeholder text was stored
        session = db_session_factory()
        updated_entry = session.query(Entry).filter_by(entry_id="test-entry-3").first()
        assert updated_entry.full_text == "<n/a>"
        session.close()
    
    @patch('backend.app.distributed.workers.full_text_download_worker.newspaper.Article')
    def test_newspaper_exception_handling(self, mock_article_class, worker_config,
                                        db_session_factory, sample_entries):
        """Test handling of newspaper ArticleException."""
        # Setup mock to raise ArticleException
        mock_article_class.side_effect = Exception("Download failed")
        
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        
        # Process entry that will fail
        entry = [entry for entry in sample_entries if entry.entry_id == "test-entry-1"][0]
        results = worker.process_batch([entry], ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Should fail
        assert results["test-entry-1"] == False
    
    def test_wrong_stage_processing(self, worker_config, db_session_factory, sample_entries):
        """Test that worker rejects processing wrong stages."""
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        
        # Try to process with wrong stage
        results = worker.process_batch(sample_entries, ProcessingStage.SENTIMENT_ANALYSIS)
        
        # All entries should fail
        for entry in sample_entries:
            assert results[entry.entry_id] == False
    
    @patch('backend.app.distributed.workers.full_text_download_worker.newspaper.Article')
    def test_concurrent_processing(self, mock_article_class, worker_config,
                                 db_session_factory):
        """Test concurrent processing of multiple entries."""
        # Create multiple entries
        session = db_session_factory()
        entries = []
        
        for i in range(10):
            entry = Entry(
                entry_id=f"concurrent-entry-{i}",
                title=f"Concurrent Article {i}",
                link=f"https://example.com/concurrent{i}",
                description=f"Concurrent description {i}",
                published=datetime.now(timezone.utc),
                full_text=None
            )
            entries.append(entry)
            session.add(entry)
        
        session.commit()
        session.close()
        
        # Setup mock responses
        def create_mock_article():
            mock = Mock()
            mock.text = f"Full text content"
            return mock
        
        mock_article_class.side_effect = [create_mock_article() for _ in range(10)]
        
        # Process batch
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        results = worker.process_batch(entries, ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # All should succeed
        assert len(results) == 10
        assert all(success for success in results.values())
    
    def test_worker_health_status(self, worker_config, db_session_factory):
        """Test worker health status reporting."""
        worker = FullTextDownloadWorker(worker_config, db_session_factory)
        
        health = worker.get_worker_specific_health()
        
        assert health['worker_type'] == 'FullTextDownloadWorker'
        assert health['download_timeout'] == 10
        assert health['max_concurrent_downloads'] == 3
        assert ProcessingStage.DOWNLOAD_FULL_TEXT.value in health['supported_stages']
    
    @patch('backend.app.distributed.workers.full_text_download_worker.newspaper.Article')
    def test_database_error_handling(self, mock_article_class, worker_config, 
                                   db_session_factory, sample_entries):
        """Test handling of database errors during updates."""
        # Setup successful article download
        mock_article = Mock()
        mock_article.text = "Downloaded text"
        mock_article_class.return_value = mock_article
        
        # Create worker with mocked session factory that fails on commit
        def failing_session_factory():
            session = Mock()
            session.query.return_value.filter_by.return_value.update.side_effect = Exception("DB Error")
            return session
        
        worker = FullTextDownloadWorker(worker_config, failing_session_factory)
        
        # Process entry
        entry = sample_entries[0]
        results = worker.process_batch([entry], ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Should fail due to database error
        assert results[entry.entry_id] == False


if __name__ == "__main__":
    # Configure logging for test runs
    logging.basicConfig(level=logging.DEBUG)
    
    # Run tests
    pytest.main([__file__, "-v"])