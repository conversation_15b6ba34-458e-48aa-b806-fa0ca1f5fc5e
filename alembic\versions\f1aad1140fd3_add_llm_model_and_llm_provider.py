"""add_llm_model_and_llm_provider

Revision ID: f1aad1140fd3
Revises: 7fa7b5ad7bea
Create Date: 2025-05-10 00:42:49.086243

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1aad1140fd3'
down_revision: Union[str, None] = '7fa7b5ad7bea'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('entries', sa.Column('llm_model', sa.String(length=255), nullable=True))
    op.add_column('entries', sa.Column('llm_provider', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('entries', 'llm_provider')
    op.drop_column('entries', 'llm_model')
    # ### end Alembic commands ###
