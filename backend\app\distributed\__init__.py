"""
Distributed ETL Processing Module

This module provides the core data structures and utilities for distributed ETL processing,
including processing stage definitions, status management, and worker configuration.
"""

from .processing_stage import (
    ProcessingStage,
    ProcessingStatusValue,
    StageStatus,
    ProcessingStatus,
    create_empty_processing_status,
    get_next_pending_stage,
    can_process_stage,
    get_processable_stages
)

from .worker_config import (
    WorkerConfig,
    WorkerConfigLoader,
    create_download_worker_config,
    create_analysis_worker_config,
    create_general_worker_config
)

__all__ = [
    # Processing stage enums and classes
    "ProcessingStage",
    "ProcessingStatusValue", 
    "StageStatus",
    "ProcessingStatus",
    
    # Processing stage utility functions
    "create_empty_processing_status",
    "get_next_pending_stage",
    "can_process_stage",
    "get_processable_stages",
    
    # Worker configuration classes
    "WorkerConfig",
    "WorkerConfigLoader",
    
    # Worker configuration templates
    "create_download_worker_config",
    "create_analysis_worker_config", 
    "create_general_worker_config"
]