"""
Unit tests for DuplicateCheckWorker.

Tests the duplicate check worker functionality including batch processing,
similarity computation, embedding comparisons, and database operations.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone, timedelta

from backend.app.distributed.workers.duplicate_check_worker import Du<PERSON><PERSON><PERSON>ckWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestDuplicateCheckWorker:
    """Test suite for DuplicateCheckWorker."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.query.return_value.filter.return_value = []
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        config = WorkerConfig(
            worker_id="test-duplicate-worker",
            stages=[ProcessingStage.DUPLICATE_CHECK],
            batch_size=5,
            heartbeat_interval=30,
            claim_timeout=300,
            stage_configs={
                ProcessingStage.DUPLICATE_CHECK: {
                    'similarity_threshold': 0.85,
                    'lookback_days': 30,
                    'batch_optimize_similarity': True
                }
            }
        )
        return config
    
    @pytest.fixture
    def sample_entries(self):
        """Create sample Entry objects with embeddings for testing."""
        entries = []
        for i in range(3):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.description = f"Test Description {i}"
            entry.full_text = f"Test content {i}"
            # Create different embeddings for each entry
            embedding = np.random.rand(384).astype(np.float32)
            entry.embedding = embedding.tobytes()
            entries.append(entry)
        return entries
    
    @pytest.fixture
    def existing_entries_data(self):
        """Create sample existing entries data for comparison."""
        existing_entries = []
        for i in range(2):
            embedding = np.random.rand(384).astype(np.float32)
            existing_entries.append({
                'entry_id': f"existing-entry-{i}",
                'embedding': embedding.tobytes(),
                'dup_entry_id': f"existing-entry-{i}",
                'published': datetime.now(timezone.utc) - timedelta(days=1)
            })
        return existing_entries
    
    def test_worker_initialization(self, worker_config, mock_db_session_factory):
        """Test worker initialization with correct configuration."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            assert worker.similarity_threshold == 0.85
            assert worker.lookback_days == 30
            assert worker.batch_optimize_similarity is True
    
    def test_worker_initialization_invalid_stage(self, mock_db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.FEED_DOWNLOAD],  # Wrong stage
            batch_size=5
        )
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            with pytest.raises(ValueError, match="DuplicateCheckWorker requires DUPLICATE_CHECK stage"):
                DuplicateCheckWorker(invalid_config, mock_db_session_factory)
    
    def test_process_batch_wrong_stage(self, worker_config, mock_db_session_factory, sample_entries):
        """Test process_batch rejects wrong processing stage."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(sample_entries, ProcessingStage.FEED_DOWNLOAD)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(sample_entries)
    
    def test_process_batch_no_embedding(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries without embeddings."""
        # Create entries without embeddings
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.embedding = None  # Missing embedding
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.DUPLICATE_CHECK)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_optimized_with_existing_entries(self, worker_config, mock_db_session_factory, sample_entries, existing_entries_data):
        """Test successful batch-optimized processing with existing entries."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            # Mock the _get_existing_entries method
            with patch.object(worker, '_get_existing_entries', return_value=existing_entries_data):
                # Mock the database update method
                with patch.object(worker, '_update_entry_duplicate_info', return_value=True) as mock_update:
                    results = worker.process_batch(sample_entries, ProcessingStage.DUPLICATE_CHECK)
                    
                    # Verify all entries were processed successfully
                    assert all(success for success in results.values())
                    assert len(results) == len(sample_entries)
                    
                    # Verify database updates were called
                    assert mock_update.call_count == len(sample_entries)
    
    def test_process_batch_no_existing_entries(self, worker_config, mock_db_session_factory, sample_entries):
        """Test processing when no existing entries are found (chicken-and-egg problem)."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            # Mock the _get_existing_entries method to return empty list
            with patch.object(worker, '_get_existing_entries', return_value=[]):
                # Mock the database update method
                with patch.object(worker, '_update_entry_duplicate_info', return_value=True) as mock_update:
                    results = worker.process_batch(sample_entries, ProcessingStage.DUPLICATE_CHECK)
                    
                    # Only first entry should succeed, others should be deferred
                    assert results[sample_entries[0].entry_id] is True
                    for entry in sample_entries[1:]:
                        assert results[entry.entry_id] is False
                    
                    # Only one database update should be called (for first entry)
                    mock_update.assert_called_once_with(
                        sample_entries[0].entry_id, sample_entries[0].entry_id, 1.0
                    )
    
    def test_process_batch_individual_processing(self, worker_config, mock_db_session_factory, sample_entries, existing_entries_data):
        """Test individual processing mode."""
        # Disable batch optimization
        worker_config.stage_configs[ProcessingStage.DUPLICATE_CHECK]['batch_optimize_similarity'] = False
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            # Mock the _get_existing_entries method
            with patch.object(worker, '_get_existing_entries', return_value=existing_entries_data):
                # Mock the database update method
                with patch.object(worker, '_update_entry_duplicate_info', return_value=True) as mock_update:
                    results = worker.process_batch(sample_entries, ProcessingStage.DUPLICATE_CHECK)
                    
                    # Verify all entries were processed successfully
                    assert all(success for success in results.values())
                    assert len(results) == len(sample_entries)
                    
                    # Verify database updates were called
                    assert mock_update.call_count == len(sample_entries)
    
    @patch('backend.app.distributed.workers.duplicate_check_worker.cosine_similarity')
    def test_similarity_computation_high_similarity(self, mock_cosine_similarity, worker_config, mock_db_session_factory, sample_entries, existing_entries_data):
        """Test similarity computation with high similarity (duplicate detected)."""
        # Mock high similarity scores (above threshold)
        mock_cosine_similarity.return_value = np.array([[0.95, 0.80], [0.75, 0.90], [0.85, 0.88]])
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_get_existing_entries', return_value=existing_entries_data):
                with patch.object(worker, '_update_entry_duplicate_info', return_value=True) as mock_update:
                    results = worker.process_batch(sample_entries, ProcessingStage.DUPLICATE_CHECK)
                    
                    # Verify all entries were processed successfully
                    assert all(success for success in results.values())
                    
                    # Check that duplicates were detected (confidence scores should be provided)
                    update_calls = mock_update.call_args_list
                    for call in update_calls:
                        entry_id, dup_entry_id, dup_entry_conf = call[0]
                        # Should have confidence score since similarity is above threshold
                        assert dup_entry_conf is not None
                        assert dup_entry_conf > worker.similarity_threshold
    
    @patch('backend.app.distributed.workers.duplicate_check_worker.cosine_similarity')
    def test_similarity_computation_low_similarity(self, mock_cosine_similarity, worker_config, mock_db_session_factory, sample_entries, existing_entries_data):
        """Test similarity computation with low similarity (unique entries)."""
        # Mock low similarity scores (below threshold)
        mock_cosine_similarity.return_value = np.array([[0.50, 0.40], [0.45, 0.55], [0.60, 0.35]])
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_get_existing_entries', return_value=existing_entries_data):
                with patch.object(worker, '_update_entry_duplicate_info', return_value=True) as mock_update:
                    results = worker.process_batch(sample_entries, ProcessingStage.DUPLICATE_CHECK)
                    
                    # Verify all entries were processed successfully
                    assert all(success for success in results.values())
                    
                    # Check that entries were marked as unique (no confidence scores)
                    update_calls = mock_update.call_args_list
                    for i, call in enumerate(update_calls):
                        entry_id, dup_entry_id, dup_entry_conf = call[0]
                        # Should be assigned as its own duplicate with no confidence
                        assert dup_entry_id == sample_entries[i].entry_id
                        assert dup_entry_conf is None
    
    def test_get_existing_entries_success(self, worker_config, mock_db_session_factory):
        """Test successful retrieval of existing entries."""
        # Create mock query results
        mock_entry1 = Mock()
        mock_entry1.entry_id = "existing-1"
        mock_entry1.embedding = np.random.rand(384).astype(np.float32).tobytes()
        mock_entry1.dup_entry_id = "existing-1"
        mock_entry1.published = datetime.now(timezone.utc) - timedelta(days=1)
        
        mock_entry2 = Mock()
        mock_entry2.entry_id = "existing-2"
        mock_entry2.embedding = np.random.rand(384).astype(np.float32).tobytes()
        mock_entry2.dup_entry_id = "existing-2"
        mock_entry2.published = datetime.now(timezone.utc) - timedelta(days=2)
        
        mock_session = mock_db_session_factory()
        mock_session.query.return_value.filter.return_value = [mock_entry1, mock_entry2]
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            existing_entries = worker._get_existing_entries()
            
            assert len(existing_entries) == 2
            assert existing_entries[0]['entry_id'] == "existing-1"
            assert existing_entries[1]['entry_id'] == "existing-2"
            mock_session.close.assert_called_once()
    
    def test_get_existing_entries_database_error(self, worker_config, mock_db_session_factory):
        """Test handling of database errors when retrieving existing entries."""
        mock_session = mock_db_session_factory()
        mock_session.query.side_effect = Exception("Database error")
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            existing_entries = worker._get_existing_entries()
            
            assert existing_entries == []
            mock_session.close.assert_called_once()
    
    def test_update_entry_duplicate_info_success(self, worker_config, mock_db_session_factory):
        """Test successful database update for entry duplicate information."""
        mock_session = mock_db_session_factory()
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_duplicate_info("test-entry", "duplicate-entry", 0.95)
            
            assert result is True
            mock_session.query.assert_called_once_with(Entry)
            mock_session.commit.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_duplicate_info_database_error(self, worker_config, mock_db_session_factory):
        """Test database error handling in entry update."""
        mock_session = mock_db_session_factory()
        mock_session.commit.side_effect = Exception("Database error")
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_duplicate_info("test-entry", "duplicate-entry", 0.95)
            
            assert result is False
            mock_session.rollback.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_duplicate_info_session_error(self, worker_config):
        """Test session creation error in entry update."""
        def failing_session_factory():
            raise Exception("Session creation failed")
        
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, failing_session_factory)
            
            result = worker._update_entry_duplicate_info("test-entry", "duplicate-entry", 0.95)
            
            assert result is False
    
    def test_handle_no_existing_entries_empty_list(self, worker_config, mock_db_session_factory):
        """Test handling of empty entries list."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            results = worker._handle_no_existing_entries([])
            
            assert results == {}
    
    def test_fallback_to_individual_processing(self, worker_config, mock_db_session_factory, sample_entries, existing_entries_data):
        """Test fallback from batch to individual processing on error."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_get_existing_entries', return_value=existing_entries_data):
                # Mock cosine_similarity to fail on first call (batch) but succeed on individual calls
                with patch('backend.app.distributed.workers.duplicate_check_worker.cosine_similarity') as mock_cosine:
                    mock_cosine.side_effect = [
                        Exception("Batch processing failed"),  # First call fails
                        np.array([[0.50]]),  # Individual calls succeed
                        np.array([[0.60]]),
                        np.array([[0.70]])
                    ]
                    
                    with patch.object(worker, '_update_entry_duplicate_info', return_value=True):
                        results = worker.process_batch(sample_entries, ProcessingStage.DUPLICATE_CHECK)
                        
                        # Should still succeed via individual processing
                        assert all(success for success in results.values())
                        assert len(results) == len(sample_entries)
    
    def test_get_worker_specific_health(self, worker_config, mock_db_session_factory):
        """Test worker-specific health information."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.get_health_status') as mock_health:
                mock_health.return_value = {'status': 'healthy'}
                
                worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
                health = worker.get_worker_specific_health()
                
                assert health['worker_type'] == 'DuplicateCheckWorker'
                assert health['similarity_threshold'] == 0.85
                assert health['lookback_days'] == 30
                assert health['batch_optimize_similarity'] is True
                assert ProcessingStage.DUPLICATE_CHECK.value in health['supported_stages']
    
    def test_cleanup_resources(self, worker_config, mock_db_session_factory):
        """Test resource cleanup."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            # Should not raise any exceptions
            worker.cleanup_resources()
    
    def test_numpy_array_operations(self, worker_config, mock_db_session_factory):
        """Test numpy array operations with real embeddings."""
        with patch('backend.app.distributed.workers.duplicate_check_worker.DistributedWorker.__init__'):
            worker = DuplicateCheckWorker(worker_config, mock_db_session_factory)
            
            # Create test embeddings
            embedding1 = np.random.rand(384).astype(np.float32)
            embedding2 = np.random.rand(384).astype(np.float32)
            
            # Test conversion to/from bytes
            embedding1_bytes = embedding1.tobytes()
            embedding1_restored = np.frombuffer(embedding1_bytes, dtype=np.float32)
            
            # Should be identical
            np.testing.assert_array_equal(embedding1, embedding1_restored)
            
            # Test similarity computation
            from sklearn.metrics.pairwise import cosine_similarity
            similarity = cosine_similarity([embedding1], [embedding2])[0][0]
            
            # Should be a valid similarity score
            assert 0 <= similarity <= 1