#!/bin/bash

echo "Installing Flutter..."
git clone https://github.com/flutter/flutter.git -b stable --depth 50
export PATH="$PATH:`pwd`/flutter/bin"

# Disable Flutter's git version checking to avoid commit lookup issues
export FLUTTER_GIT_URL=""

echo "Flutter version:"
flutter --version || echo "Warning: Flutter version check failed, continuing..."

echo "Enabling web support..."
flutter config --enable-web

echo "Getting dependencies..."
flutter pub get

echo "Building Flutter web app..."
flutter build web --release

echo "Build completed successfully!"
ls -la build/web/