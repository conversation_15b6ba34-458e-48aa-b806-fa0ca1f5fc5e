<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News App - Lazy Loading Example</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .news-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .news-title { font-weight: bold; margin-bottom: 10px; }
        .news-meta { color: #666; font-size: 0.9em; margin-bottom: 10px; }
        .similar-indicator { 
            display: inline-block; 
            padding: 2px 8px; 
            border-radius: 3px; 
            font-size: 0.8em; 
            margin-left: 10px;
        }
        .has-similar { background-color: #e7f3ff; color: #0066cc; }
        .no-similar { background-color: #f0f0f0; color: #666; }
        .loading { background-color: #fff3cd; color: #856404; }
        .load-similar-btn { 
            background-color: #007bff; 
            color: white; 
            border: none; 
            padding: 5px 10px; 
            border-radius: 3px; 
            cursor: pointer; 
            margin-top: 10px;
        }
        .similar-news { 
            margin-top: 10px; 
            padding: 10px; 
            background-color: #f8f9fa; 
            border-radius: 3px; 
        }
    </style>
</head>
<body>
    <h1>News App - Lazy Loading Demo</h1>
    <p>This demonstrates how to load news first, then check for similar articles separately.</p>
    
    <button onclick="loadNews()">Load News</button>
    <button onclick="checkAllSimilar()">Check All for Similar News</button>
    
    <div id="news-container"></div>

    <script>
        const API_BASE = 'http://localhost:8000';
        let currentNews = [];

        async function loadNews() {
            console.log('Loading news...');
            const container = document.getElementById('news-container');
            container.innerHTML = '<p>Loading news...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/news/?limit=10`);
                const data = await response.json();
                currentNews = data.items;
                
                displayNews(currentNews);
                console.log(`Loaded ${currentNews.length} news items`);
            } catch (error) {
                console.error('Error loading news:', error);
                container.innerHTML = '<p>Error loading news</p>';
            }
        }

        function displayNews(newsItems) {
            const container = document.getElementById('news-container');
            container.innerHTML = '';
            
            newsItems.forEach(item => {
                const newsDiv = document.createElement('div');
                newsDiv.className = 'news-item';
                newsDiv.innerHTML = `
                    <div class="news-title">${item.title}</div>
                    <div class="news-meta">
                        Source: ${item.source_name || item.source} | 
                        Category: ${item.category_name || item.iptc_newscode} |
                        Published: ${new Date(item.published).toLocaleString()}
                        <span id="similar-${item.entry_id}" class="similar-indicator loading">Checking...</span>
                    </div>
                    <div class="news-description">${item.description || ''}</div>
                    <button class="load-similar-btn" onclick="loadSimilarNews('${item.entry_id}')" 
                            id="btn-${item.entry_id}" style="display: none;">
                        Load Similar Articles
                    </button>
                    <div id="similar-${item.entry_id}-content" class="similar-news" style="display: none;"></div>
                `;
                container.appendChild(newsDiv);
            });
        }

        async function checkAllSimilar() {
            if (currentNews.length === 0) {
                alert('Load news first!');
                return;
            }

            console.log('Checking for similar news...');
            const entryIds = currentNews.map(item => item.entry_id);
            
            try {
                const response = await fetch(`${API_BASE}/news/check-similar`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(entryIds)
                });
                
                const similarFlags = await response.json();
                console.log('Similar flags received:', similarFlags);
                
                // Update UI with similar indicators
                Object.entries(similarFlags).forEach(([entryId, hasSimilar]) => {
                    const indicator = document.getElementById(`similar-${entryId}`);
                    const button = document.getElementById(`btn-${entryId}`);
                    
                    if (indicator) {
                        indicator.className = `similar-indicator ${hasSimilar ? 'has-similar' : 'no-similar'}`;
                        indicator.textContent = hasSimilar ? 'Has Similar' : 'No Similar';
                    }
                    
                    if (button && hasSimilar) {
                        button.style.display = 'inline-block';
                    }
                });
                
            } catch (error) {
                console.error('Error checking similar news:', error);
            }
        }

        async function loadSimilarNews(entryId) {
            console.log(`Loading similar news for ${entryId}...`);
            const contentDiv = document.getElementById(`similar-${entryId}-content`);
            contentDiv.style.display = 'block';
            contentDiv.innerHTML = '<p>Loading similar articles...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/news/${entryId}/similar`);
                const data = await response.json();
                
                if (data.similar_sources && data.similar_sources.length > 0) {
                    const similarHtml = data.similar_sources.map(similar => `
                        <div style="margin: 5px 0; padding: 5px; border-left: 3px solid #007bff;">
                            <strong>${similar.source_name || similar.source}:</strong> ${similar.title}
                        </div>
                    `).join('');
                    
                    contentDiv.innerHTML = `
                        <h4>Similar Articles:</h4>
                        ${similarHtml}
                    `;
                } else {
                    contentDiv.innerHTML = '<p>No similar articles found.</p>';
                }
                
            } catch (error) {
                console.error('Error loading similar news:', error);
                contentDiv.innerHTML = '<p>Error loading similar articles</p>';
            }
        }

        // Auto-load news on page load
        window.onload = () => {
            loadNews().then(() => {
                // Auto-check for similar news after loading
                setTimeout(checkAllSimilar, 1000);
            });
        };
    </script>
</body>
</html>