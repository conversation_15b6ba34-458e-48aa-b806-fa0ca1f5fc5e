-- Find potential missed duplicates with high similarity but below threshold
-- This query helps identify entries that might be duplicates but weren't marked as such
-- Note: This query assumes the similarity threshold is around 0.8-0.9
WITH entry_pairs AS (
    SELECT 
        e1.entry_id AS entry_id_1,
        e2.entry_id AS entry_id_2,
        e1.title AS title_1,
        e2.title AS title_2,
        e1.source AS source_1,
        e2.source AS source_2,
        e1.published AS published_1,
        e2.published AS published_2,
        e1.dup_entry_conf AS similarity_1,
        e2.dup_entry_conf AS similarity_2
    FROM 
        entries e1
    JOIN 
        entries e2 ON e1.entry_id < e2.entry_id  -- Avoid duplicate pairs
    WHERE 
        e1.dup_entry_id <> e2.dup_entry_id  -- Different duplicate groups
        AND ABS(EXTRACT(DAY FROM (e1.published - e2.published)) * 24 * 60) < 1440  -- Within 24 hours
        AND e1.dup_entry_conf > 0.7  -- High similarity score
        AND e2.dup_entry_conf > 0.7  -- High similarity score
)
SELECT 
    ep.entry_id_1,
    ep.title_1,
    s1.name AS source_name_1,
    ep.published_1,
    ep.entry_id_2,
    ep.title_2,
    s2.name AS source_name_2,
    ep.published_2,
    ABS(EXTRACT(DAY FROM (ep.published_1 - ep.published_2)) * 24 * 60) AS minutes_difference
FROM 
    entry_pairs ep
LEFT JOIN
    sources s1 ON ep.source_1 = s1.source
LEFT JOIN
    sources s2 ON ep.source_2 = s2.source
ORDER BY 
    minutes_difference ASC
FETCH FIRST 50 ROWS ONLY
