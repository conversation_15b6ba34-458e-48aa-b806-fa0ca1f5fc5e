import logging
import sys
from logging.config import fileConfig

from sqlalchemy import engine_from_config, inspect, text
from sqlalchemy import pool, MetaData
from sqlalchemy.dialects import oracle

from alembic import context

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

logging.getLogger('alembic.runtime.migration').setLevel(logging.DEBUG)
logging.getLogger('alembic.autogenerate').setLevel(logging.DEBUG)
logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

logging.basicConfig(level=logging.INFO, stream=sys.stdout)
logger = logging.getLogger('alembic.env')

# add your model's MetaData object here
# for 'autogenerate' support
from backend.app.models.models import Base
target_metadata = Base.metadata

def include_object(object, name, type_, reflected, compare_to):
    """Control which database objects to include in the autogeneration."""
    logger.info(f"\nComparing object: name={name}, type={type_}, reflected={reflected}")
    
    # Ignore specific schemas
    ignored_schemas = {'ssb', 'sh', 'mdsys', 'sys'}
    if hasattr(object, 'schema'):
        logger.info(f"Schema check: object schema = {object.schema}")
        if object.schema in ignored_schemas:
            logger.info(f"Ignoring object due to schema {object.schema} in ignored_schemas")
            return False

    # Ignore system tables and specific tables
    ignored_tables = {
        'alembic_version',
        'dbtools$execution_history',
        'sdo_txn_journal_reg',
        'commit_scn_log$',
        'rdf_parameter',
    }
    
    if type_ == 'table':
        logger.info(f"Table check: {name.lower()}")
        if name.lower() in ignored_tables:
            logger.info(f"Ignoring table {name} as it's in ignored_tables")
            return False
        logger.info(f"Including table {name}")
        logger.info(f"Table schema: {getattr(object, 'schema', None)}")
    elif type_ == 'index':
        logger.info(f"Index check: {name}")
        logger.info(f"Index columns: {[col.name for col in object.columns]}")
        logger.info(f"Including index {name}")
    else:
        logger.info(f"Other object type: {type_}")
    
    logger.info(f"Including object: {name} of type {type_}")
    return True

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=include_object,
        include_schemas=True,
        compare_type=True,
        compare_server_default=True
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    cfg = config.get_section(config.config_ini_section, {})
    
    connectable = engine_from_config(
        cfg,
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        # Set the current schema
        connection.execute(text('ALTER SESSION SET CURRENT_SCHEMA = DEVXPS15'))
        
        # Debug output
        insp = inspect(connection)
        logger.info("\n=== Database Tables ===")
        for table_name in insp.get_table_names():
            logger.info(f"Table: {table_name}")
            indices = insp.get_indexes(table_name)
            for idx in indices:
                logger.info(f"  Index: {idx}")

        logger.info("\n=== Model Tables ===")
        for table in Base.metadata.tables.values():
            logger.info(f"Model table: {table.name}")
            for idx in table.indexes:
                logger.info(f"  Model index: {idx.name} - {[col.name for col in idx.columns]}")

        # Add detailed index comparison
        logger.info("\n=== Comparing Indices ===")
        insp = inspect(connection)
        for table_name in insp.get_table_names(schema='DEVXPS15'):
            logger.info(f"Table: {table_name}")
            
            # Get database indices
            db_indices = insp.get_indexes(table_name, schema='DEVXPS15')
            logger.info("Database indices:")
            for idx in db_indices:
                logger.info(f"  - {idx['name']}: {idx['column_names']}")
            
            # Get model indices - use correct table name format
            table_key = f"{table_name}"  # Remove schema prefix for lookup
            if table_key in [t.name for t in Base.metadata.tables.values()]:
                # Find the table object
                model_table = next(t for t in Base.metadata.tables.values() if t.name == table_key)
                logger.info("Model indices:")
                for idx in model_table.indexes:
                    logger.info(f"  - {idx.name}: {[col.name for col in idx.columns]}")
            else:
                logger.info(f"No model found for table {table_name}")

        logger.info("\n=== Starting Configuration ===")
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_object=include_object,
            include_schemas=True,
            compare_type=True,
            compare_server_default=True,
            version_table_schema='DEVXPS15',
            transaction_per_migration=True,
            as_sql=False
        )
        logger.info("\n=== Configuration Complete ===")

        with context.begin_transaction():
            context.run_migrations()
            connection.commit()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
