# Worker Health Monitoring and Heartbeat System

This document describes the comprehensive health monitoring and heartbeat system implemented for distributed ETL workers.

## Overview

The health monitoring system provides:
- **Database-backed worker registration and heartbeat tracking**
- **HTTP endpoints for health checks and metrics**
- **Automatic detection and recovery from worker failures**
- **Comprehensive monitoring and alerting capabilities**
- **Integration with the distributed worker framework**

## Architecture

### Core Components

1. **WorkerHealthManager** (`worker_health_manager.py`)
   - Database operations for worker lifecycle management
   - Heartbeat tracking and health status monitoring
   - Stale worker detection and recovery coordination

2. **HealthCheckServer** (`health_check_server.py`)
   - HTTP server providing REST endpoints for health monitoring
   - Kubernetes-compatible readiness and liveness probes
   - Prometheus-style metrics endpoint

3. **WorkerRecoverySystem** (`worker_recovery_system.py`)
   - Automatic failure detection and recovery
   - Background monitoring and cleanup processes
   - Configurable recovery policies and thresholds

4. **Worker Database Model** (in `models.py`)
   - Persistent storage for worker state and statistics
   - Heartbeat timestamps and health metrics
   - Error tracking and recovery history

### Integration with DistributedWorker

The health monitoring system is seamlessly integrated with the `DistributedWorker` class:
- Automatic worker registration on startup
- Regular heartbeat transmission during operation
- Health endpoint exposure (if configured)
- Graceful unregistration on shutdown

## Database Schema

### Worker Table

```sql
CREATE TABLE workers (
    worker_id VARCHAR(255) PRIMARY KEY,
    worker_type VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL,
    stages CLOB,  -- JSON array of processing stages
    config CLOB,  -- JSON worker configuration
    
    -- Heartbeat and health tracking
    last_heartbeat TIMESTAMP,
    heartbeat_interval_seconds INTEGER DEFAULT 60,
    
    -- Worker lifecycle
    started_at TIMESTAMP,
    stopped_at TIMESTAMP,
    
    -- Statistics and monitoring
    batches_processed INTEGER DEFAULT 0,
    entries_processed INTEGER DEFAULT 0,
    entries_failed INTEGER DEFAULT 0,
    total_processing_time FLOAT DEFAULT 0.0,
    
    -- Current processing state
    current_batch_size INTEGER DEFAULT 0,
    processing_since TIMESTAMP,
    
    -- Error tracking
    last_error CLOB,
    error_count INTEGER DEFAULT 0,
    
    -- Host information
    hostname VARCHAR(255),
    process_id INTEGER
);
```

### Indexes

```sql
CREATE INDEX idx_workers_status ON workers(status);
CREATE INDEX idx_workers_last_heartbeat ON workers(last_heartbeat);
CREATE INDEX idx_workers_status_heartbeat ON workers(status, last_heartbeat);
CREATE INDEX idx_workers_type ON workers(worker_type);
```

## API Endpoints

### Health Check Endpoints

When a worker is configured with a `health_check_port`, the following HTTP endpoints are available:

#### Basic Health Check
```
GET /health
```
Returns basic health status:
```json
{
  "status": "healthy",
  "timestamp": "2025-09-18T18:48:46.009444+00:00",
  "worker_id": "worker-001",
  "state": "running",
  "heartbeat_stale": false
}
```

#### Detailed Health Information
```
GET /health/detailed
```
Returns comprehensive worker health data including statistics, configuration, and processing state.

#### Kubernetes Readiness Probe
```
GET /health/ready
```
Returns 200 if worker is ready to accept work, 503 otherwise.

#### Kubernetes Liveness Probe
```
GET /health/live
```
Returns 200 if worker is alive (not in error state), 503 otherwise.

#### Prometheus Metrics
```
GET /metrics
```
Returns metrics in Prometheus format:
```
# HELP worker_batches_processed_total Total number of batches processed
# TYPE worker_batches_processed_total counter
worker_batches_processed_total{worker_id="worker-001"} 42

# HELP worker_entries_processed_total Total number of entries processed
# TYPE worker_entries_processed_total counter
worker_entries_processed_total{worker_id="worker-001"} 1050
```

## Configuration

### Worker Configuration

```python
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage

config = WorkerConfig(
    worker_id="my-worker-001",
    stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
    batch_size=25,
    heartbeat_interval_seconds=60,  # Send heartbeat every 60 seconds
    health_check_port=8080,         # Enable HTTP health endpoints
    max_retries=3
)
```

### Recovery System Configuration

```python
from backend.app.distributed.worker_recovery_system import WorkerRecoverySystem

recovery_system = WorkerRecoverySystem(
    db_session_factory=session_factory,
    check_interval_seconds=60,      # Check for failed workers every 60 seconds
    stale_threshold_minutes=5,      # Consider workers stale after 5 minutes
    claim_timeout_minutes=30        # Release claims after 30 minutes
)
```

## Usage Examples

### Basic Worker with Health Monitoring

```python
from backend.app.distributed.distributed_worker import DistributedWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage

class MyWorker(DistributedWorker):
    def process_batch(self, entries, stage):
        # Your processing logic here
        return {entry.entry_id: True for entry in entries}

# Create worker with health monitoring
config = WorkerConfig(
    worker_id="my-worker-001",
    stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
    heartbeat_interval_seconds=30,
    health_check_port=8080
)

worker = MyWorker(config, db_session_factory)
worker.start()  # Automatically registers with health system

# Worker will send heartbeats and expose health endpoints
# Health endpoints available at http://localhost:8080/health
```

### Manual Health Management

```python
from backend.app.distributed.worker_health_manager import WorkerHealthManager

session = db_session_factory()
health_manager = WorkerHealthManager(session)

# Register worker
health_manager.register_worker(
    worker_id="manual-worker",
    worker_type="ManualWorker",
    config=config
)

# Send heartbeat with statistics
health_manager.send_heartbeat(
    worker_id="manual-worker",
    stats={
        'batches_processed': 10,
        'entries_processed': 250,
        'entries_failed': 2
    },
    current_batch_size=15
)

# Get health status
health = health_manager.get_worker_health("manual-worker")
print(f"Worker status: {health['status']}")
```

### Recovery System Setup

```python
from backend.app.distributed.worker_recovery_system import create_default_recovery_system

# Create recovery system with default logging
recovery_system = create_default_recovery_system(
    db_session_factory=session_factory,
    check_interval_seconds=60,
    stale_threshold_minutes=5
)

# Start automatic recovery
recovery_system.start()

# Add custom failure notification
def notify_admin(failed_workers):
    for worker in failed_workers:
        print(f"ALERT: Worker {worker['worker_id']} has failed!")

recovery_system.add_failure_callback(notify_admin)

# Force immediate recovery check
recovery_stats = recovery_system.force_recovery_check()
print(f"Recovered {recovery_stats['entries_released']} entries")
```

## Monitoring and Alerting

### Health Metrics

The system tracks comprehensive metrics for each worker:

- **Lifecycle**: Registration, startup, shutdown times
- **Processing**: Batches processed, entries processed/failed
- **Performance**: Processing times, throughput rates
- **Health**: Heartbeat status, error counts, uptime
- **Resource**: Current batch size, processing state

### Failure Detection

Workers are considered failed when:
1. **Heartbeat timeout**: No heartbeat received within threshold
2. **Error reporting**: Worker reports critical errors
3. **Stale claims**: Worker has old claimed entries

### Recovery Actions

When failures are detected:
1. **Mark worker as failed** in database
2. **Release claimed entries** back to work queue
3. **Clean up stale resources**
4. **Notify configured callbacks**
5. **Log recovery actions**

## Testing

### Unit Tests

```bash
# Run health manager tests
python -m pytest backend/app/distributed/test_worker_health_monitoring.py::TestWorkerHealthManager -v

# Run recovery system tests
python -m pytest backend/app/distributed/test_worker_health_monitoring.py::TestWorkerRecoverySystem -v

# Run integration tests
python -m pytest backend/app/distributed/test_health_integration_simple.py -v
```

### Demo Scripts

```bash
# Run concept demonstration
python backend/app/distributed/simple_health_demo.py

# Run full integration demo (requires proper Python path)
python backend/app/distributed/health_monitoring_demo.py
```

## Deployment Considerations

### Docker Configuration

```dockerfile
# Expose health check port
EXPOSE 8080

# Set health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1
```

### Kubernetes Configuration

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: etl-worker
spec:
  template:
    spec:
      containers:
      - name: worker
        image: etl-worker:latest
        ports:
        - containerPort: 8080
          name: health
        livenessProbe:
          httpGet:
            path: /health/live
            port: health
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: health
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Monitoring Integration

```yaml
# Prometheus scrape configuration
scrape_configs:
- job_name: 'etl-workers'
  static_configs:
  - targets: ['worker-1:8080', 'worker-2:8080']
  metrics_path: /metrics
  scrape_interval: 15s
```

## Performance Considerations

### Database Load

- Heartbeats are lightweight database operations
- Indexes optimize health queries
- Configurable heartbeat intervals balance monitoring vs. load

### Recovery Overhead

- Recovery checks run in background threads
- Configurable check intervals prevent excessive database queries
- Batch operations minimize transaction overhead

### Memory Usage

- Health data is stored in database, not memory
- Minimal memory footprint for health tracking
- HTTP server runs in separate thread with small overhead

## Security Considerations

### Health Endpoint Security

- Health endpoints expose operational data only
- No sensitive configuration or data exposed
- Consider network-level access controls for production

### Database Security

- Worker table contains operational data only
- Standard database security practices apply
- Consider encryption for sensitive deployment information

## Troubleshooting

### Common Issues

1. **Workers not appearing in health system**
   - Check database connectivity
   - Verify worker registration calls
   - Check for database schema issues

2. **Stale worker detection not working**
   - Verify recovery system is started
   - Check stale threshold configuration
   - Review database timestamp handling

3. **Health endpoints not accessible**
   - Verify health_check_port configuration
   - Check firewall and network settings
   - Review server startup logs

### Debug Commands

```python
# Check worker registration
session = db_session_factory()
workers = session.query(Worker).all()
for worker in workers:
    print(f"{worker.worker_id}: {worker.status}")

# Force recovery check
recovery_system.force_recovery_check()

# Get comprehensive status
status = recovery_system.get_current_worker_status()
print(json.dumps(status, indent=2))
```

## Future Enhancements

### Planned Features

1. **Advanced Metrics**: More detailed performance metrics
2. **Alerting Integration**: Direct integration with alerting systems
3. **Dashboard**: Web-based monitoring dashboard
4. **Auto-scaling**: Automatic worker scaling based on load
5. **Health Policies**: Configurable health check policies

### Extension Points

The system is designed for extensibility:
- Custom health providers
- Additional recovery strategies
- Enhanced metrics collection
- Integration with external monitoring systems

## Requirements Satisfied

This implementation satisfies the following requirements from the specification:

- **5.1**: Worker heartbeat tracking and health monitoring
- **5.2**: Failed worker detection within configurable timeouts
- **5.3**: Automatic recovery by releasing claimed work from failed workers

The system provides comprehensive health monitoring with automatic failure detection and recovery, ensuring reliable distributed ETL processing.