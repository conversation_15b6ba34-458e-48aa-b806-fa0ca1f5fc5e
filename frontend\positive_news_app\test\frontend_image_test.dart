import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/models/news_model.dart';

void main() {
  group('Frontend Image Loading Tests', () {
    group('Image URL Generation', () {
      test('generates correct API URLs when hasImage is true', () {
        final apiService = ApiService();
        final url = apiService.getImageUrl('test_123', hasImage: true);

        expect(url, contains('/news/test_123/image'));
        expect(url, startsWith('http'));
      });

      test('generates fallback URLs when hasImage is false', () {
        final apiService = ApiService();
        final url = apiService.getImageUrl('test_456', hasImage: false);

        expect(url, contains('picsum.photos'));
        expect(url, contains('test_456'));
      });

      test('URLs are deterministic for caching', () {
        final apiService = ApiService();

        final url1 = apiService.getImageUrl('same_entry', hasImage: true);
        final url2 = apiService.getImageUrl('same_entry', hasImage: true);

        expect(url1, equals(url2));
      });
    });

    group('News Model Integration', () {
      test('handles hasImage field variations correctly', () {
        final jsonWithImage = {
          'entry_id': 'test_1',
          'title': 'Test News',
          'link': 'https://example.com',
          'source': 'test_source',
          'published': '2024-01-01T12:00:00Z',
          'has_image': true,
        };

        final entry = EntryWithSource.fromJson(jsonWithImage);
        expect(entry.hasImage, true);
      });

      test('handles missing hasImage field', () {
        final jsonMissingImage = {
          'entry_id': 'test_2',
          'title': 'Test News',
          'link': 'https://example.com',
          'source': 'test_source',
          'published': '2024-01-01T12:00:00Z',
        };

        final entry = EntryWithSource.fromJson(jsonMissingImage);
        expect(entry.hasImage, null);
      });
    });

    group('Performance Tests', () {
      test('URL generation is fast for bulk operations', () {
        final apiService = ApiService();
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 1000; i++) {
          apiService.getImageUrl('test_$i', hasImage: i % 2 == 0);
        }

        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });
    });
  });
}
