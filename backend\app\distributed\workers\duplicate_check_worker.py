"""
Duplicate Check worker for distributed ETL processing.

This module provides the DuplicateCheckWorker class that handles checking for
duplicate news entries using embedding similarity comparisons in batch processing mode.
"""

import logging
import numpy as np
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone, timedelta
from sklearn.metrics.pairwise import cosine_similarity

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from backend.app.distributed.distributed_worker import DistributedWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class DuplicateCheckWorker(DistributedWorker):
    """
    Worker specialized in checking for duplicate news entries using embedding similarity.
    
    Adapts the existing check_and_update_duplicates logic for batch processing with
    efficient similarity computation for batches and proper handling of embedding comparisons.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the duplicate check worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.DUPLICATE_CHECK not in config.stages:
            raise ValueError("DuplicateCheckWorker requires DUPLICATE_CHECK stage")
        
        # Configuration for duplicate checking
        self.similarity_threshold = config.get_stage_config(
            ProcessingStage.DUPLICATE_CHECK, 'similarity_threshold', 0.85
        )
        self.lookback_days = config.get_stage_config(
            ProcessingStage.DUPLICATE_CHECK, 'lookback_days', 30
        )
        self.batch_optimize_similarity = config.get_stage_config(
            ProcessingStage.DUPLICATE_CHECK, 'batch_optimize_similarity', True
        )
        
        logger.info(
            f"Initialized DuplicateCheckWorker {self.worker_id} "
            f"(similarity_threshold: {self.similarity_threshold}, "
            f"lookback_days: {self.lookback_days}, "
            f"batch_optimize: {self.batch_optimize_similarity})"
        )
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for duplicate checking.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be DUPLICATE_CHECK)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.DUPLICATE_CHECK:
            logger.error(f"DuplicateCheckWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing batch of {len(entries)} entries for duplicate checking")
        
        # Filter entries that have required embedding field
        valid_entries = []
        results = {}
        
        for entry in entries:
            if not entry.embedding:
                logger.warning(
                    f"Entry {entry.entry_id} missing required embedding field"
                )
                results[entry.entry_id] = False
            else:
                valid_entries.append(entry)
        
        if not valid_entries:
            logger.warning("No valid entries to process in batch")
            return results
        
        # Process valid entries
        if self.batch_optimize_similarity:
            # Batch-optimized processing
            batch_results = self._process_batch_optimized(valid_entries)
        else:
            # Individual processing
            batch_results = self._process_batch_individual(valid_entries)
        
        results.update(batch_results)
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"Completed duplicate check batch: {success_count}/{len(entries)} successful"
        )
        
        return results
    
    def _process_batch_optimized(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries with batch-optimized similarity computation.
        
        Args:
            entries: List of valid entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        try:
            # Get existing entries for comparison
            existing_entries = self._get_existing_entries()
            
            if not existing_entries:
                # Handle the chicken-and-egg problem: no existing entries to compare against
                logger.info("No existing entries found, assigning first entry as its own duplicate")
                return self._handle_no_existing_entries(entries)
            
            # Convert existing embeddings to numpy array
            existing_embeddings = np.array([
                np.frombuffer(entry['embedding'], dtype=np.float32) 
                for entry in existing_entries
            ])
            existing_dup_ids = [entry["dup_entry_id"] for entry in existing_entries]
            
            # Convert new entries embeddings to numpy array
            new_embeddings = np.array([
                np.frombuffer(entry.embedding, dtype=np.float32) 
                for entry in entries
            ])
            
            # Compute similarity matrix between new and existing entries
            logger.debug(f"Computing similarity matrix: {len(entries)} new vs {len(existing_entries)} existing")
            similarity_matrix = cosine_similarity(new_embeddings, existing_embeddings)
            
            # Process each new entry
            for i, entry in enumerate(entries):
                try:
                    similarities = similarity_matrix[i]
                    most_similar_index = np.argmax(similarities)
                    max_similarity = similarities[most_similar_index]
                    
                    if max_similarity > self.similarity_threshold:
                        # Found a duplicate
                        dup_entry_id = existing_dup_ids[most_similar_index]
                        dup_entry_conf = float(max_similarity)
                        logger.debug(
                            f"Entry {entry.entry_id} is duplicate of {dup_entry_id} "
                            f"(similarity: {max_similarity:.3f})"
                        )
                    else:
                        # Not a duplicate, assign itself as the duplicate ID
                        dup_entry_id = entry.entry_id
                        dup_entry_conf = None
                        logger.debug(
                            f"Entry {entry.entry_id} is unique "
                            f"(max similarity: {max_similarity:.3f})"
                        )
                    
                    # Update database
                    success = self._update_entry_duplicate_info(
                        entry.entry_id, dup_entry_id, dup_entry_conf
                    )
                    results[entry.entry_id] = success
                    
                    if success:
                        # Add this entry to existing entries for subsequent comparisons
                        new_embedding = new_embeddings[i:i+1]  # Keep 2D shape
                        existing_embeddings = np.vstack([existing_embeddings, new_embedding])
                        existing_dup_ids.append(dup_entry_id)
                
                except Exception as e:
                    logger.error(f"Error processing entry {entry.entry_id}: {e}")
                    results[entry.entry_id] = False
        
        except Exception as e:
            logger.error(f"Error in batch-optimized duplicate checking: {e}")
            # Fall back to individual processing
            return self._process_batch_individual(entries)
        
        return results
    
    def _process_batch_individual(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries individually (fallback method).
        
        Args:
            entries: List of valid entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        # Get existing entries for comparison
        existing_entries = self._get_existing_entries()
        
        if not existing_entries:
            # Handle the chicken-and-egg problem
            return self._handle_no_existing_entries(entries)
        
        # Convert existing embeddings to numpy array
        existing_embeddings = np.array([
            np.frombuffer(entry['embedding'], dtype=np.float32) 
            for entry in existing_entries
        ])
        existing_dup_ids = [entry["dup_entry_id"] for entry in existing_entries]
        
        for entry in entries:
            try:
                # Convert entry embedding to numpy array
                new_embedding = np.frombuffer(entry.embedding, dtype=np.float32)
                
                # Compute similarities with all existing entries
                similarities = cosine_similarity([new_embedding], existing_embeddings)[0]
                most_similar_index = np.argmax(similarities)
                max_similarity = similarities[most_similar_index]
                
                if max_similarity > self.similarity_threshold:
                    # Found a duplicate
                    dup_entry_id = existing_dup_ids[most_similar_index]
                    dup_entry_conf = float(max_similarity)
                    logger.debug(
                        f"Entry {entry.entry_id} is duplicate of {dup_entry_id} "
                        f"(similarity: {max_similarity:.3f})"
                    )
                else:
                    # Not a duplicate, assign itself as the duplicate ID
                    dup_entry_id = entry.entry_id
                    dup_entry_conf = None
                    logger.debug(
                        f"Entry {entry.entry_id} is unique "
                        f"(max similarity: {max_similarity:.3f})"
                    )
                
                # Update database
                success = self._update_entry_duplicate_info(
                    entry.entry_id, dup_entry_id, dup_entry_conf
                )
                results[entry.entry_id] = success
                
                if success:
                    # Add this entry to existing entries for subsequent comparisons
                    existing_embeddings = np.vstack([existing_embeddings, [new_embedding]])
                    existing_dup_ids.append(dup_entry_id)
            
            except Exception as e:
                logger.error(f"Error processing entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
        
        return results
    
    def _get_existing_entries(self) -> List[Dict]:
        """
        Get existing entries with embeddings and duplicate IDs for comparison.
        
        Returns:
            List of dictionaries with entry data
        """
        try:
            db_session = self.db_session_factory()
            
            try:
                # Calculate lookback date
                lookback_date = datetime.now(timezone.utc) - timedelta(days=self.lookback_days)
                
                # Query existing entries with embeddings and duplicate IDs
                query = db_session.query(Entry).filter(
                    and_(
                        Entry.embedding.isnot(None),
                        Entry.dup_entry_id.isnot(None),
                        Entry.published >= lookback_date
                    )
                )
                
                existing_entries = []
                for entry in query:
                    existing_entries.append({
                        'entry_id': entry.entry_id,
                        'embedding': entry.embedding,
                        'dup_entry_id': entry.dup_entry_id,
                        'published': entry.published
                    })
                
                logger.debug(f"Found {len(existing_entries)} existing entries for comparison")
                return existing_entries
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Error fetching existing entries: {e}")
            return []
    
    def _handle_no_existing_entries(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Handle the case where no existing entries are found (chicken-and-egg problem).
        
        Args:
            entries: List of entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        # Assign the first entry as its own duplicate to break the chicken-and-egg problem
        if entries:
            first_entry = entries[0]
            success = self._update_entry_duplicate_info(
                first_entry.entry_id, first_entry.entry_id, 1.0
            )
            results[first_entry.entry_id] = success
            
            if success:
                logger.info(f"Assigned first entry {first_entry.entry_id} as its own duplicate")
            
            # Mark remaining entries as failed for this batch - they'll be processed in the next batch
            for entry in entries[1:]:
                results[entry.entry_id] = False
                logger.debug(f"Deferring entry {entry.entry_id} to next batch")
        
        return results
    
    def _update_entry_duplicate_info(self, entry_id: str, dup_entry_id: str, dup_entry_conf: Optional[float]) -> bool:
        """
        Update the entry's duplicate information in the database.
        
        Args:
            entry_id: ID of the entry to update
            dup_entry_id: ID of the duplicate entry (or self if unique)
            dup_entry_conf: Confidence score for duplicate detection (None if unique)
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create a new database session for this update
            db_session = self.db_session_factory()
            
            try:
                # Update duplicate information fields
                db_session.query(Entry).filter_by(entry_id=entry_id).update({
                    "dup_entry_id": dup_entry_id,
                    "dup_entry_conf": dup_entry_conf
                })
                db_session.commit()
                
                logger.debug(f"Updated duplicate info for entry {entry_id}")
                return True
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error updating entry {entry_id}: {e}")
                return False
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for entry {entry_id}: {e}")
            return False
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to duplicate check worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add duplicate check worker specific metrics
        health.update({
            'worker_type': 'DuplicateCheckWorker',
            'similarity_threshold': self.similarity_threshold,
            'lookback_days': self.lookback_days,
            'batch_optimize_similarity': self.batch_optimize_similarity,
            'supported_stages': [ProcessingStage.DUPLICATE_CHECK.value]
        })
        
        return health
    
    def cleanup_resources(self) -> None:
        """Clean up resources when worker shuts down."""
        logger.info("Cleaning up duplicate check worker resources")
        
        # No specific resources to clean up for this worker
        logger.info("Duplicate check worker resources cleaned up")