"""add_singleton_stage_executions_table

Revision ID: e819f59fa192
Revises: 819c0f44868c
Create Date: 2025-09-20 23:52:03.165126

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e819f59fa192'
down_revision: Union[str, None] = '819c0f44868c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create singleton_stage_executions table to track last execution times
    op.create_table(
        'singleton_stage_executions',
        sa.Column('stage_name', sa.String(100), primary_key=True, nullable=False),
        sa.Column('last_execution_time', sa.DateTime(timezone=True), nullable=False),
        sa.Column('worker_id', sa.String(255), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False)
    )

    # Create index on last_execution_time for efficient cooldown checks
    op.create_index('idx_singleton_stage_executions_last_execution', 'singleton_stage_executions', ['last_execution_time'])


def downgrade() -> None:
    # Drop the table and its indexes
    op.drop_index('idx_singleton_stage_executions_last_execution', table_name='singleton_stage_executions')
    op.drop_table('singleton_stage_executions')
