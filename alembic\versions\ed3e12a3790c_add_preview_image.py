"""Add preview image

Revision ID: ed3e12a3790c
Revises: cdfca92c25f2
Create Date: 2025-04-24 09:56:42.946547

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ed3e12a3790c'
down_revision: Union[str, None] = 'cdfca92c25f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('entries', sa.Column('preview_img', sa.LargeBinary(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('entries', 'preview_img')
    # ### end Alembic commands ###
