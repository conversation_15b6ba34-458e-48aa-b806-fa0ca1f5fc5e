import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/widgets/category_news_widget.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/services/similar_news_service.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/widgets/news_card.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences_platform_interface/shared_preferences_platform_interface.dart';

@GenerateMocks([ApiService])
import 'service_test.mocks.dart';

void main() {
  late MockApiService mockApiService;
  late SimilarNewsService similarNewsService;
  late FavoritesService favoritesService;
  late SettingsProvider settingsProvider;

  // Use platform interface for mocking SharedPreferences
  SharedPreferencesStorePlatform.instance = InMemorySharedPreferencesStore.empty();

  setUp(() {
    mockApiService = MockApiService();
    similarNewsService = SimilarNewsService(mockApiService);
    favoritesService = FavoritesService();
    settingsProvider = SettingsProvider();

    // Default stubs
    when(mockApiService.getImageUrl(any, hasImage: anyNamed('hasImage')))
        .thenReturn('https://picsum.photos/seed/test/400/200');
    when(mockApiService.checkSimilarNews(any)).thenAnswer((_) async => {});
  });

  Future<void> pumpWidgetWithProviders(WidgetTester tester, Widget widget) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: MultiProvider(
            providers: [
              ChangeNotifierProvider.value(value: favoritesService),
              ChangeNotifierProvider.value(value: settingsProvider),
              Provider<ApiService>.value(value: mockApiService),
              ChangeNotifierProvider.value(value: similarNewsService),
            ],
            child: widget,
          ),
        ),
      ),
    );
  }

  group('UI-Tests für NewsCard', () {
    testWidgets('NewsCard zeigt Titel und Quelle korrekt an', (WidgetTester tester) async {
      when(mockApiService.checkSimilarNews(any)).thenAnswer((_) async => {});
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason',
        hasSimilar: null, // Updated for lazy loading
      );

      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
        ),
      );

      expect(find.text('Test Nachricht'), findsOneWidget);
      expect(find.text('Test Quelle'), findsOneWidget);
    });

    testWidgets('NewsCard zeigt "Ähnliche Nachrichten verfügbar" an, wenn hasSimilar true ist', (WidgetTester tester) async {
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason 1; Test reason 2',
        hasSimilar: true, // Explicitly set to true
      );

      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
        ),
      );

      // Verify that the text is displayed correctly
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
    });

    testWidgets('NewsCard zeigt Ladeindikator für null hasSimilar', (WidgetTester tester) async {
      // Mock the API calls
      when(mockApiService.checkSimilarNews(any)).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 200));
        return {'test_id': true};
      });

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason',
        hasSimilar: null, // Null for lazy loading
      );

      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
        ),
      );

      // Trigger a frame to ensure post-frame callbacks are executed
      await tester.pump();

      // Initially should show loading indicator
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsOneWidget);

      // Wait for the future to complete
      await tester.pump(const Duration(milliseconds: 250));

      // Should now show the similar news button
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsNothing);
    });
  });

  group('UI-Tests für CategoryNewsWidget', () {
    testWidgets('CategoryNewsWidget zeigt Kategorienamen und Nachrichten an', (WidgetTester tester) async {
      when(mockApiService.checkSimilarNews(any)).thenAnswer((_) async => {});
      final news1 = EntryWithSource(
        entryId: 'test_id_1',
        title: 'Test Nachricht 1',
        link: 'https://example.com/1',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason 1',
        hasSimilar: null, // Updated for lazy loading
      );

      final news2 = EntryWithSource(
        entryId: 'test_id_2',
        title: 'Test Nachricht 2',
        link: 'https://example.com/2',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.92,
        llmReasonList: 'Test reason 2',
        hasSimilar: null, // Updated for lazy loading
      );

      final categoryNews = CategoryNews(
        category: 'Politik',
        categoryCode: 'POL',
        news: [news1, news2],
      );

      await pumpWidgetWithProviders(
        tester,
        CategoryNewsWidget(
          categoryNews: categoryNews,
        ),
      );

      expect(find.text('Politik'), findsOneWidget);
      expect(find.text('Test Nachricht 1'), findsOneWidget);
      expect(find.text('Test Nachricht 2'), findsOneWidget);
    });
  });

  group('UI-Tests für NewsCard (Optimized)', () {
    testWidgets('NewsCard zeigt Titel und Quelle korrekt an (Optimized)', (WidgetTester tester) async {
      when(mockApiService.checkSimilarNews(any)).thenAnswer((_) async => {});
      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht Optimized',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle Optimized',
        published: DateTime.now(),
        llmPositive: 0.95,
        llmReasonList: 'Test reason',
        hasSimilar: null,
      );

      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
        ),
      );

      expect(find.text('Test Nachricht Optimized'), findsOneWidget);
      expect(find.text('Test Quelle Optimized'), findsOneWidget);
    });

    testWidgets('NewsCard verwendet SimilarNewsService für cached Flags', (WidgetTester tester) async {
      // Pre-cache a similar flag
      when(mockApiService.checkSimilarNews(['test_id'])).thenAnswer((_) async => {'test_id': true});

      await similarNewsService.loadSimilarFlags(['test_id']);

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        llmPositive: 0.95,
        hasSimilar: null, // Null, but should use cached value
      );

      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
        ),
      );

      await tester.pump();

      // Should show similar news button immediately (no loading)
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsOneWidget);
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsNothing);
    });

    testWidgets('OptimizedNewsCard zeigt Ladeindikator während Service lädt', (WidgetTester tester) async {
      // Mock delayed response
      when(mockApiService.checkSimilarNews(any)).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 200));
        return {'test_id': false};
      });

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        published: DateTime.now(),
        hasSimilar: null,
      );

      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
          showSimilarMessage: true,
        ),
      );

      // Trigger the loading
      await tester.pump();

      // Trigger a frame to ensure post-frame callbacks are executed
      await tester.pump();

      // Should show loading initially
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsOneWidget);

      // Wait for completion
      await tester.pump(const Duration(milliseconds: 250));

      // Should hide the button (no similar news)
      expect(find.text('Prüfe ähnliche Nachrichten...'), findsNothing);
      expect(find.text('Ähnliche Nachrichten verfügbar'), findsNothing);
    });
  });

  group('Lazy Loading Integration Widget Tests', () {
    testWidgets('NewsCard verhält sich konsistent mit optimierter Implementierung', (WidgetTester tester) async {
      when(mockApiService.hasSimilarNews('test_id')).thenAnswer((_) async => true);
      when(mockApiService.checkSimilarNews(['test_id'])).thenAnswer((_) async => {'test_id': true});

      final news = EntryWithSource(
        entryId: 'test_id',
        title: 'Test Nachricht',
        link: 'https://example.com',
        source: 'test_source',
        sourceName: 'Test Quelle',
        published: DateTime.now(),
        hasSimilar: null,
      );

      // Test NewsCard
      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
        ),
      );

      await tester.pump();
      expect(find.text('Ähnliche Nachrichten'), findsOneWidget);

      // Test NewsCard (now consolidated)
      await pumpWidgetWithProviders(
        tester,
        NewsCard(
          news: news,
          showFavoriteButton: true,
        ),
      );

      await tester.pump();
      expect(find.text('Ähnliche Nachrichten'), findsOneWidget);
    });
  });
}
