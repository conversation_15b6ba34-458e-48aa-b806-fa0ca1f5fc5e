"""
Monitoring and observability tests for NewsAggregator
Tests logging, metrics, health checks, and monitoring capabilities
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import os
import sys
import time
import json
from datetime import datetime, timezone, timedelta

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)


class TestLoggingAndMetrics(unittest.TestCase):
    """Test logging and metrics collection"""

    def test_structured_logging(self):
        """Test structured logging format"""
        log_entry = self._create_structured_log(
            level="INFO",
            message="RSS feed processed successfully",
            component="feed_processor",
            feed_url="https://example.com/rss",
            entries_processed=25,
            processing_time=1.5
        )
        
        # Should contain required fields
        self.assertIn('timestamp', log_entry)
        self.assertIn('level', log_entry)
        self.assertIn('message', log_entry)
        self.assertIn('component', log_entry)
        
        # Should contain contextual information
        self.assertEqual(log_entry['feed_url'], "https://example.com/rss")
        self.assertEqual(log_entry['entries_processed'], 25)
        self.assertEqual(log_entry['processing_time'], 1.5)
        
        # Should be valid JSON
        json_str = json.dumps(log_entry)
        parsed = json.loads(json_str)
        self.assertEqual(parsed['level'], "INFO")
    
    def _create_structured_log(self, level, message, component, **kwargs):
        """Create a structured log entry"""
        log_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'level': level,
            'message': message,
            'component': component,
            **kwargs
        }
        return log_entry

    def test_performance_metrics_collection(self):
        """Test collection of performance metrics"""
        metrics_collector = self._create_metrics_collector()
        
        # Simulate processing operations
        with metrics_collector.timer('rss_processing'):
            time.sleep(0.01)  # Simulate work
        
        with metrics_collector.timer('sentiment_analysis'):
            time.sleep(0.02)  # Simulate work
        
        # Increment counters
        metrics_collector.increment('articles_processed', 10)
        metrics_collector.increment('errors_occurred', 2)
        
        # Check collected metrics
        metrics = metrics_collector.get_metrics()
        
        self.assertIn('rss_processing_duration', metrics)
        self.assertIn('sentiment_analysis_duration', metrics)
        self.assertEqual(metrics['articles_processed'], 10)
        self.assertEqual(metrics['errors_occurred'], 2)
        
        # Timing metrics should be reasonable
        self.assertGreater(metrics['rss_processing_duration'], 0.005)
        self.assertGreater(metrics['sentiment_analysis_duration'], 0.015)
    
    def _create_metrics_collector(self):
        """Create a metrics collector for testing"""
        class MetricsCollector:
            def __init__(self):
                self.metrics = {}
                self.timers = {}
            
            def timer(self, name):
                return self.Timer(self, name)
            
            def increment(self, name, value=1):
                self.metrics[name] = self.metrics.get(name, 0) + value
            
            def get_metrics(self):
                return self.metrics.copy()
            
            class Timer:
                def __init__(self, collector, name):
                    self.collector = collector
                    self.name = name
                    self.start_time = None
                
                def __enter__(self):
                    self.start_time = time.time()
                    return self
                
                def __exit__(self, exc_type, exc_val, exc_tb):
                    duration = time.time() - self.start_time
                    self.collector.metrics[f'{self.name}_duration'] = duration
        
        return MetricsCollector()

    def test_error_tracking(self):
        """Test error tracking and categorization"""
        error_tracker = self._create_error_tracker()
        
        # Track different types of errors
        error_tracker.track_error('RSS_FETCH_ERROR', 'Failed to fetch RSS feed', 
                                 {'url': 'https://example.com/rss', 'status_code': 404})
        
        error_tracker.track_error('TRANSLATION_ERROR', 'Translation service unavailable',
                                 {'service': 'google_translate', 'text_length': 500})
        
        error_tracker.track_error('DATABASE_ERROR', 'Connection timeout',
                                 {'operation': 'insert', 'table': 'entries'})
        
        # Get error summary
        error_summary = error_tracker.get_error_summary()
        
        self.assertEqual(error_summary['total_errors'], 3)
        self.assertEqual(error_summary['error_types']['RSS_FETCH_ERROR'], 1)
        self.assertEqual(error_summary['error_types']['TRANSLATION_ERROR'], 1)
        self.assertEqual(error_summary['error_types']['DATABASE_ERROR'], 1)
    
    def _create_error_tracker(self):
        """Create an error tracker for testing"""
        class ErrorTracker:
            def __init__(self):
                self.errors = []
                self.error_counts = {}
            
            def track_error(self, error_type, message, context=None):
                error_entry = {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'type': error_type,
                    'message': message,
                    'context': context or {}
                }
                self.errors.append(error_entry)
                self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
            
            def get_error_summary(self):
                return {
                    'total_errors': len(self.errors),
                    'error_types': self.error_counts.copy(),
                    'recent_errors': self.errors[-10:]  # Last 10 errors
                }
        
        return ErrorTracker()


class TestHealthChecks(unittest.TestCase):
    """Test health check and system status monitoring"""

    def test_database_health_check(self):
        """Test database connectivity health check"""
        # Mock database connection
        with patch('news_aggregator.create_engine') as mock_engine:
            mock_connection = Mock()
            mock_engine.return_value.connect.return_value = mock_connection
            
            health_checker = self._create_health_checker()
            db_health = health_checker.check_database_health()
            
            self.assertTrue(db_health['healthy'])
            self.assertEqual(db_health['component'], 'database')
            self.assertIn('response_time', db_health)
    
    def test_external_service_health_check(self):
        """Test external service health checks"""
        health_checker = self._create_health_checker()
        
        # Mock successful service response
        with patch('requests.get') as mock_get:
            mock_response = Mock()
            mock_response.status_code = 200
            mock_response.elapsed.total_seconds.return_value = 0.5
            mock_get.return_value = mock_response
            
            service_health = health_checker.check_external_service_health(
                'translation_service', 'https://translate.googleapis.com'
            )
            
            self.assertTrue(service_health['healthy'])
            self.assertEqual(service_health['response_time'], 0.5)
    
    def test_system_resource_health_check(self):
        """Test system resource health monitoring"""
        health_checker = self._create_health_checker()
        resource_health = health_checker.check_system_resources()
        
        # Should check various system resources
        self.assertIn('memory_usage', resource_health)
        self.assertIn('disk_usage', resource_health)
        self.assertIn('cpu_usage', resource_health)
        
        # All values should be reasonable percentages
        self.assertGreaterEqual(resource_health['memory_usage'], 0)
        self.assertLessEqual(resource_health['memory_usage'], 100)
    
    def _create_health_checker(self):
        """Create a health checker for testing"""
        class HealthChecker:
            def check_database_health(self):
                start_time = time.time()
                try:
                    # Simulate database check
                    time.sleep(0.01)
                    response_time = time.time() - start_time
                    return {
                        'healthy': True,
                        'component': 'database',
                        'response_time': response_time,
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
                except Exception as e:
                    return {
                        'healthy': False,
                        'component': 'database',
                        'error': str(e),
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
            
            def check_external_service_health(self, service_name, url):
                import requests
                start_time = time.time()
                try:
                    response = requests.get(url, timeout=5)
                    return {
                        'healthy': response.status_code == 200,
                        'service': service_name,
                        'status_code': response.status_code,
                        'response_time': response.elapsed.total_seconds(),
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
                except Exception as e:
                    return {
                        'healthy': False,
                        'service': service_name,
                        'error': str(e),
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
            
            def check_system_resources(self):
                try:
                    import psutil
                    return {
                        'memory_usage': psutil.virtual_memory().percent,
                        'disk_usage': psutil.disk_usage('/').percent,
                        'cpu_usage': psutil.cpu_percent(interval=0.1),
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
                except ImportError:
                    # Fallback if psutil not available
                    return {
                        'memory_usage': 50.0,  # Mock values
                        'disk_usage': 30.0,
                        'cpu_usage': 25.0,
                        'timestamp': datetime.now(timezone.utc).isoformat()
                    }
        
        return HealthChecker()


class TestAlertingAndNotifications(unittest.TestCase):
    """Test alerting and notification systems"""

    def test_threshold_based_alerting(self):
        """Test threshold-based alerting system"""
        alert_manager = self._create_alert_manager()
        
        # Configure thresholds
        alert_manager.set_threshold('error_rate', 5.0)  # 5% error rate
        alert_manager.set_threshold('processing_time', 30.0)  # 30 seconds
        alert_manager.set_threshold('memory_usage', 80.0)  # 80% memory
        
        # Test normal conditions (no alerts)
        alert_manager.check_metric('error_rate', 2.0)
        alert_manager.check_metric('processing_time', 15.0)
        alert_manager.check_metric('memory_usage', 60.0)
        
        alerts = alert_manager.get_active_alerts()
        self.assertEqual(len(alerts), 0)
        
        # Test threshold violations (should trigger alerts)
        alert_manager.check_metric('error_rate', 8.0)
        alert_manager.check_metric('memory_usage', 85.0)
        
        alerts = alert_manager.get_active_alerts()
        self.assertEqual(len(alerts), 2)
        
        # Check alert details
        error_rate_alert = next(a for a in alerts if a['metric'] == 'error_rate')
        self.assertEqual(error_rate_alert['severity'], 'WARNING')
        self.assertEqual(error_rate_alert['current_value'], 8.0)
        self.assertEqual(error_rate_alert['threshold'], 5.0)
    
    def _create_alert_manager(self):
        """Create an alert manager for testing"""
        class AlertManager:
            def __init__(self):
                self.thresholds = {}
                self.active_alerts = {}
            
            def set_threshold(self, metric_name, threshold_value, severity='WARNING'):
                self.thresholds[metric_name] = {
                    'value': threshold_value,
                    'severity': severity
                }
            
            def check_metric(self, metric_name, current_value):
                if metric_name in self.thresholds:
                    threshold = self.thresholds[metric_name]
                    
                    if current_value > threshold['value']:
                        # Trigger alert
                        self.active_alerts[metric_name] = {
                            'metric': metric_name,
                            'current_value': current_value,
                            'threshold': threshold['value'],
                            'severity': threshold['severity'],
                            'timestamp': datetime.now(timezone.utc).isoformat()
                        }
                    else:
                        # Clear alert if it exists
                        self.active_alerts.pop(metric_name, None)
            
            def get_active_alerts(self):
                return list(self.active_alerts.values())
        
        return AlertManager()

    def test_notification_delivery(self):
        """Test notification delivery mechanisms"""
        notification_service = self._create_notification_service()
        
        # Test different notification types
        email_result = notification_service.send_notification(
            'email',
            recipient='<EMAIL>',
            subject='High Error Rate Alert',
            message='Error rate has exceeded 5% threshold'
        )
        
        slack_result = notification_service.send_notification(
            'slack',
            channel='#alerts',
            message='🚨 Memory usage is at 85%'
        )
        
        webhook_result = notification_service.send_notification(
            'webhook',
            url='https://hooks.example.com/alert',
            payload={'alert': 'high_cpu', 'value': 90}
        )
        
        # All notifications should be sent successfully
        self.assertTrue(email_result['success'])
        self.assertTrue(slack_result['success'])
        self.assertTrue(webhook_result['success'])
    
    def _create_notification_service(self):
        """Create a notification service for testing"""
        class NotificationService:
            def send_notification(self, notification_type, **kwargs):
                # Simulate notification sending
                if notification_type == 'email':
                    return self._send_email(kwargs.get('recipient'), 
                                          kwargs.get('subject'), 
                                          kwargs.get('message'))
                elif notification_type == 'slack':
                    return self._send_slack(kwargs.get('channel'), 
                                          kwargs.get('message'))
                elif notification_type == 'webhook':
                    return self._send_webhook(kwargs.get('url'), 
                                            kwargs.get('payload'))
                else:
                    return {'success': False, 'error': 'Unknown notification type'}
            
            def _send_email(self, recipient, subject, message):
                # Mock email sending
                return {
                    'success': True,
                    'type': 'email',
                    'recipient': recipient,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
            
            def _send_slack(self, channel, message):
                # Mock Slack sending
                return {
                    'success': True,
                    'type': 'slack',
                    'channel': channel,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
            
            def _send_webhook(self, url, payload):
                # Mock webhook sending
                return {
                    'success': True,
                    'type': 'webhook',
                    'url': url,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
        
        return NotificationService()


class TestPerformanceMonitoring(unittest.TestCase):
    """Test performance monitoring and profiling"""

    def test_processing_time_tracking(self):
        """Test tracking of processing times for different operations"""
        perf_monitor = self._create_performance_monitor()
        
        # Track different operations
        with perf_monitor.track_operation('rss_fetch'):
            time.sleep(0.01)
        
        with perf_monitor.track_operation('content_extraction'):
            time.sleep(0.02)
        
        with perf_monitor.track_operation('sentiment_analysis'):
            time.sleep(0.03)
        
        # Get performance statistics
        stats = perf_monitor.get_performance_stats()
        
        self.assertIn('rss_fetch', stats)
        self.assertIn('content_extraction', stats)
        self.assertIn('sentiment_analysis', stats)
        
        # Check that times are reasonable
        self.assertGreater(stats['rss_fetch']['avg_time'], 0.005)
        self.assertGreater(stats['content_extraction']['avg_time'], 0.015)
        self.assertGreater(stats['sentiment_analysis']['avg_time'], 0.025)
    
    def _create_performance_monitor(self):
        """Create a performance monitor for testing"""
        class PerformanceMonitor:
            def __init__(self):
                self.operation_times = {}
            
            def track_operation(self, operation_name):
                return self.OperationTracker(self, operation_name)
            
            def record_time(self, operation_name, duration):
                if operation_name not in self.operation_times:
                    self.operation_times[operation_name] = []
                self.operation_times[operation_name].append(duration)
            
            def get_performance_stats(self):
                stats = {}
                for operation, times in self.operation_times.items():
                    stats[operation] = {
                        'count': len(times),
                        'avg_time': sum(times) / len(times),
                        'min_time': min(times),
                        'max_time': max(times),
                        'total_time': sum(times)
                    }
                return stats
            
            class OperationTracker:
                def __init__(self, monitor, operation_name):
                    self.monitor = monitor
                    self.operation_name = operation_name
                    self.start_time = None
                
                def __enter__(self):
                    self.start_time = time.time()
                    return self
                
                def __exit__(self, exc_type, exc_val, exc_tb):
                    duration = time.time() - self.start_time
                    self.monitor.record_time(self.operation_name, duration)
        
        return PerformanceMonitor()

    def test_throughput_monitoring(self):
        """Test monitoring of system throughput"""
        throughput_monitor = self._create_throughput_monitor()
        
        # Simulate processing items over time
        for i in range(100):
            throughput_monitor.record_processed_item('articles')
            if i % 10 == 0:
                time.sleep(0.001)  # Small delay every 10 items
        
        for i in range(50):
            throughput_monitor.record_processed_item('translations')
        
        # Get throughput statistics
        stats = throughput_monitor.get_throughput_stats(time_window=60)
        
        self.assertEqual(stats['articles']['count'], 100)
        self.assertEqual(stats['translations']['count'], 50)
        self.assertGreater(stats['articles']['rate_per_second'], 0)
        self.assertGreater(stats['translations']['rate_per_second'], 0)
    
    def _create_throughput_monitor(self):
        """Create a throughput monitor for testing"""
        class ThroughputMonitor:
            def __init__(self):
                self.processed_items = {}
            
            def record_processed_item(self, item_type):
                current_time = time.time()
                if item_type not in self.processed_items:
                    self.processed_items[item_type] = []
                self.processed_items[item_type].append(current_time)
            
            def get_throughput_stats(self, time_window=60):
                current_time = time.time()
                cutoff_time = current_time - time_window
                
                stats = {}
                for item_type, timestamps in self.processed_items.items():
                    recent_items = [t for t in timestamps if t >= cutoff_time]
                    count = len(recent_items)
                    rate = count / time_window if time_window > 0 else 0
                    
                    stats[item_type] = {
                        'count': count,
                        'rate_per_second': rate,
                        'time_window': time_window
                    }
                
                return stats
        
        return ThroughputMonitor()


if __name__ == '__main__':
    unittest.main(verbosity=2)