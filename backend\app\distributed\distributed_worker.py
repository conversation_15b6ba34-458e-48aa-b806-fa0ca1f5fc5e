"""
Base distributed worker implementation for ETL processing.

This module provides the core DistributedWorker class that handles worker lifecycle,
batch processing, error handling, and graceful shutdown for distributed ETL operations.
"""

import asyncio
import logging
import signal
import threading
import time
from abc import ABC, abstractmethod
from datetime import datetime, timezone
from enum import Enum
from typing import List, Optional, Dict, Any, Callable
from contextlib import contextmanager

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus, stage_requires_singleton, stage_requires_entry_retrieval
from backend.app.distributed.worker_health_manager import WorkerHealthManager
from backend.app.distributed.health_check_server import WorkerHealthEndpoints
from backend.app.distributed.progress_reporter import ProgressReporter
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class WorkerState(Enum):
    """Worker lifecycle states."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class DistributedWorker(ABC):
    """
    Base class for distributed ETL workers.
    
    Provides worker lifecycle management, batch processing loop, error handling,
    and graceful shutdown with work release. Subclasses must implement the
    process_batch method for stage-specific processing logic.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory: Callable[[], Session]):
        """
        Initialize the distributed worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        self.config = config
        self.db_session_factory = db_session_factory
        self.worker_id = config.worker_id
        
        # Worker state management
        self._state = WorkerState.STOPPED
        self._shutdown_event = threading.Event()
        self._worker_thread: Optional[threading.Thread] = None
        self._heartbeat_thread: Optional[threading.Thread] = None
        
        # Processing state
        self._current_batch: List[str] = []
        self._processing_start_time: Optional[datetime] = None
        self._last_heartbeat: Optional[datetime] = None
        self._last_error: Optional[str] = None
        
        # Statistics
        self._stats = {
            'batches_processed': 0,
            'entries_processed': 0,
            'entries_failed': 0,
            'total_processing_time': 0.0,
            'last_batch_size': 0,
            'last_batch_duration': 0.0
        }
        
        # Health manager for database heartbeats
        self._health_manager: Optional[WorkerHealthManager] = None

        # Health check endpoints
        self._health_endpoints: Optional[WorkerHealthEndpoints] = None

        # Fix SQLAlchemy logging issues first (before any database operations)
        from backend.app.distributed.logging_config import fix_sqlalchemy_logging
        fix_sqlalchemy_logging()

        # Progress reporter for dashboard
        self._progress_reporter = ProgressReporter(self.worker_id, db_session_factory)

        # Setup logging
        self._setup_logging()

        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()

        logger.info(f"Initialized worker {self.worker_id} for stages: {[s.value for s in config.stages]}")
    
    def _setup_logging(self) -> None:
        """Configure logging for the worker."""
        # Set log level from config
        numeric_level = getattr(logging, self.config.log_level.upper(), None)
        if isinstance(numeric_level, int):
            logger.setLevel(numeric_level)

        # Add worker ID to log messages
        formatter = logging.Formatter(
            f'%(asctime)s - %(name)s - [{self.worker_id}] - %(levelname)s - %(message)s'
        )

        # Update existing handlers
        for handler in logger.handlers:
            handler.setFormatter(formatter)

        # Fix SQLAlchemy logging format errors by disabling problematic loggers
        sqlalchemy_loggers = [
            'sqlalchemy.engine',
            'sqlalchemy.engine.Engine',
            'sqlalchemy.pool',
            'sqlalchemy.dialects',
            'sqlalchemy.orm',
            'sqlalchemy.engine.base',
            'sqlalchemy.engine.base.Engine'
        ]

        for logger_name in sqlalchemy_loggers:
            sql_logger = logging.getLogger(logger_name)
            sql_logger.setLevel(logging.ERROR)
            sql_logger.propagate = False
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self.stop()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    @property
    def state(self) -> WorkerState:
        """Get current worker state."""
        return self._state
    
    @property
    def is_running(self) -> bool:
        """Check if worker is currently running."""
        return self._state == WorkerState.RUNNING
    
    @property
    def stats(self) -> Dict[str, Any]:
        """Get worker statistics."""
        stats = self._stats.copy()
        stats.update({
            'worker_id': self.worker_id,
            'state': self._state.value,
            'current_batch_size': len(self._current_batch),
            'last_heartbeat': self._last_heartbeat.isoformat() if self._last_heartbeat else None,
            'uptime_seconds': self._get_uptime_seconds()
        })
        return stats
    
    def start(self) -> None:
        """
        Start the worker in a separate thread.
        
        Raises:
            RuntimeError: If worker is already running
        """
        if self._state != WorkerState.STOPPED:
            raise RuntimeError(f"Cannot start worker in state {self._state.value}")
        
        logger.info(f"Starting worker {self.worker_id}...")
        self._state = WorkerState.STARTING
        
        try:
            # Initialize health manager and register worker
            self._register_worker()
            
            # Start health check endpoints
            self._start_health_endpoints()
            
            # Reset shutdown event
            self._shutdown_event.clear()
            
            # Start worker thread
            self._worker_thread = threading.Thread(
                target=self._worker_loop,
                name=f"worker-{self.worker_id}",
                daemon=False
            )
            self._worker_thread.start()
            
            # Start heartbeat thread
            self._heartbeat_thread = threading.Thread(
                target=self._heartbeat_loop,
                name=f"heartbeat-{self.worker_id}",
                daemon=True
            )
            self._heartbeat_thread.start()
            
            self._state = WorkerState.RUNNING
            logger.info(f"Worker {self.worker_id} started successfully")
            
        except Exception as e:
            self._state = WorkerState.ERROR
            self._last_error = str(e)
            logger.error(f"Failed to start worker {self.worker_id}: {e}")
            raise
    
    def stop(self, timeout: Optional[int] = None) -> None:
        """
        Stop the worker gracefully.
        
        Args:
            timeout: Maximum time to wait for shutdown (uses config default if None)
        """
        if self._state in (WorkerState.STOPPED, WorkerState.STOPPING):
            return
        
        timeout = timeout or self.config.shutdown_timeout_seconds
        logger.info(f"Stopping worker {self.worker_id} (timeout: {timeout}s)...")
        
        self._state = WorkerState.STOPPING
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Wait for worker thread to finish
        if self._worker_thread and self._worker_thread.is_alive():
            self._worker_thread.join(timeout=timeout)
            
            if self._worker_thread.is_alive():
                logger.warning(f"Worker {self.worker_id} did not stop within {timeout}s")
        
        # Wait for heartbeat thread
        if self._heartbeat_thread and self._heartbeat_thread.is_alive():
            self._heartbeat_thread.join(timeout=5)
        
        # Stop health check endpoints
        self._stop_health_endpoints()
        
        # Unregister worker from health tracking
        self._unregister_worker()
        
        self._state = WorkerState.STOPPED
        logger.info(f"Worker {self.worker_id} stopped")
    
    def _worker_loop(self) -> None:
        """Main worker processing loop."""
        logger.info(f"Worker {self.worker_id} entering main processing loop")
        
        try:
            while not self._shutdown_event.is_set():
                try:
                    # Process each configured stage
                    work_found = False

                    for stage in self.config.stages:
                        if self._shutdown_event.is_set():
                            break

                        batch_processed = self._process_stage_batch(stage)
                        if batch_processed:
                            work_found = True

                        # Small delay between stages to prevent rapid cycling
                        if not self._shutdown_event.is_set():
                            self._shutdown_event.wait(self.config.processing_delay_seconds)

                    # Report progress for all stages periodically (every 10 iterations or when work is found)
                    if work_found or (self._stats['batches_processed'] % 10 == 0):
                        try:
                            self._progress_reporter.calculate_and_report_progress(self.config.stages)
                        except Exception as e:
                            logger.warning(f"Error reporting progress: {e}")

                    # If no work was found, wait before next iteration
                    if not work_found:
                        self._shutdown_event.wait(self.config.no_work_delay_seconds)
                
                except Exception as e:
                    logger.error(f"Error in worker loop: {e}", exc_info=True)
                    self._last_error = str(e)
                    # Continue processing after error
                    self._shutdown_event.wait(1.0)
        
        except Exception as e:
            logger.error(f"Fatal error in worker loop: {e}", exc_info=True)
            self._state = WorkerState.ERROR
            self._last_error = str(e)
        
        finally:
            # Release any claimed work before shutting down
            self._release_current_batch()
            logger.info(f"Worker {self.worker_id} exited processing loop")

    def _process_singleton_stage(self, work_queue: WorkQueueManager, stage: ProcessingStage) -> bool:
        """
        Process a singleton stage that doesn't require entry retrieval.

        Args:
            work_queue: WorkQueueManager instance for stage claiming
            stage: Singleton processing stage to work on

        Returns:
            True if the stage was processed, False if already claimed by another worker
        """
        try:
            # Check if the stage is in cooldown period
            if work_queue.is_stage_in_cooldown(stage):
                logger.debug(f"Singleton stage {stage.value} is in cooldown period, skipping")
                return False

            # Add small random delay to prevent race conditions when cooldown ends
            # This spreads out the attempts from multiple workers
            import random
            import time
            delay = random.uniform(0.1, 2.0)  # Random delay between 100ms and 2 seconds
            time.sleep(delay)

            # Re-check cooldown after delay (another worker might have started processing)
            if work_queue.is_stage_in_cooldown(stage):
                logger.debug(f"Singleton stage {stage.value} entered cooldown during delay, skipping")
                return False

            # Check if another worker is already processing this singleton stage
            if work_queue.is_singleton_stage_claimed(stage):
                logger.debug(f"Singleton stage {stage.value} already claimed by another worker")
                return False

            # Attempt to claim the singleton stage
            if not work_queue.claim_singleton_stage(stage, self.worker_id):
                logger.debug(f"Failed to claim singleton stage {stage.value}")
                return False

            logger.info(f"Processing singleton stage {stage.value}")

            # Track processing start time
            self._processing_start_time = datetime.now(timezone.utc)

            try:
                # Create separate session for processing
                process_session = self.db_session_factory()

                # For singleton stages, we don't process existing entries
                # Instead, we call the process_batch method with an empty list
                success_results = self.process_batch([], stage)

                # Close processing session
                process_session.close()

                # Log results
                if success_results:
                    logger.info(f"Singleton stage {stage.value} completed successfully")
                else:
                    logger.warning(f"Singleton stage {stage.value} completed with no results")

                # Record the execution for cooldown tracking
                work_queue.record_stage_execution(stage, self.worker_id)

                return True

            except Exception as e:
                logger.error(f"Error processing singleton stage {stage.value}: {e}", exc_info=True)
                return False

            finally:
                # Always release the singleton stage claim
                try:
                    work_queue.release_singleton_stage(stage, self.worker_id)
                except Exception as e:
                    logger.error(f"Error releasing singleton stage {stage.value}: {e}")

                # Clear processing tracking
                self._processing_start_time = None

        except Exception as e:
            logger.error(f"Fatal error in singleton stage processing for {stage.value}: {e}", exc_info=True)
            return False
    
    def _process_stage_batch(self, stage: ProcessingStage) -> bool:
        """
        Process a batch of entries for a specific stage.

        Args:
            stage: Processing stage to work on

        Returns:
            True if a batch was processed, False if no work available
        """
        claim_session = None
        process_session = None

        try:
            # Create separate database session for claiming work
            claim_session = self.db_session_factory()
            work_queue = WorkQueueManager(claim_session)

            # Handle singleton stages differently
            if stage_requires_singleton(stage):
                return self._process_singleton_stage(work_queue, stage)

            # For regular stages, claim a batch of work
            entry_ids = work_queue.claim_batch(
                stage=stage,
                batch_size=self.config.batch_size,
                worker_id=self.worker_id,
                max_retries=self.config.max_retries
            )

            # Close the claim session to avoid conflicts
            claim_session.close()
            claim_session = None

            if not entry_ids:
                return False
            
            logger.info(f"[{stage.value}] Processing batch of {len(entry_ids)} entries for stage {stage.value}")
            
            # Track current batch for cleanup
            self._current_batch = entry_ids
            self._processing_start_time = datetime.now(timezone.utc)
            
            # Create separate session for processing
            process_session = self.db_session_factory()
            
            # Process the batch
            success_ids, failed_ids = self._process_batch_with_error_handling(
                process_session, entry_ids, stage
            )
            
            # Close processing session
            process_session.close()
            process_session = None
            
            # Create separate session for completion updates
            completion_session = self.db_session_factory()
            completion_queue = WorkQueueManager(completion_session)
            
            # Update completion status
            if success_ids:
                completion_queue.mark_completed(success_ids, stage, self.worker_id)
            
            if failed_ids:
                completion_queue.mark_failed(
                    failed_ids, stage, self.worker_id, 
                    "Batch processing failed"
                )
            
            # Close completion session
            completion_session.close()
            
            # Update statistics
            self._update_stats(len(entry_ids), len(success_ids), len(failed_ids))
            
            # Clear current batch
            self._current_batch = []
            self._processing_start_time = None
            
            return True
        
        except Exception as e:
            logger.error(f"Error processing batch for stage {stage.value}: {e}", exc_info=True)
            
            # Release claimed work on error
            if self._current_batch:
                try:
                    cleanup_session = self.db_session_factory()
                    cleanup_queue = WorkQueueManager(cleanup_session)
                    cleanup_queue.release_batch(self._current_batch, self.worker_id)
                    cleanup_session.close()
                except Exception as release_error:
                    logger.error(f"Failed to release batch on error: {release_error}")
            
            self._current_batch = []
            self._processing_start_time = None
            return False
        
        finally:
            # Clean up any remaining sessions
            for session in [claim_session, process_session]:
                if session:
                    try:
                        session.close()
                    except Exception:
                        pass
    
    def _process_batch_with_error_handling(self, 
                                         db_session: Session,
                                         entry_ids: List[str], 
                                         stage: ProcessingStage) -> tuple[List[str], List[str]]:
        """
        Process a batch with comprehensive error handling.
        
        Args:
            db_session: Database session
            entry_ids: List of entry IDs to process
            stage: Processing stage
            
        Returns:
            Tuple of (successful_ids, failed_ids)
        """
        success_ids = []
        failed_ids = []
        
        try:
            # Get entries from database
            entries = (
                db_session.query(Entry)
                .filter(Entry.entry_id.in_(entry_ids))
                .all()
            )
            
            # Process the batch using subclass implementation
            processed_entries = self.process_batch(entries, stage)
            
            # Categorize results
            for entry_id, success in processed_entries.items():
                if success:
                    success_ids.append(entry_id)
                else:
                    failed_ids.append(entry_id)
        
        except Exception as e:
            logger.error(f"Batch processing failed: {e}", exc_info=True)
            # Mark all entries as failed
            failed_ids = entry_ids
        
        return success_ids, failed_ids
    
    @abstractmethod
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for a specific stage.
        
        This method must be implemented by subclasses to provide stage-specific
        processing logic.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage to execute
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        pass
    
    def _heartbeat_loop(self) -> None:
        """Heartbeat loop to indicate worker is alive."""
        logger.debug(f"Starting heartbeat loop for worker {self.worker_id}")
        
        while not self._shutdown_event.is_set():
            try:
                self._send_heartbeat()
                self._shutdown_event.wait(self.config.heartbeat_interval_seconds)
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}", exc_info=True)
                self._shutdown_event.wait(5.0)  # Wait before retry
    
    def _send_heartbeat(self) -> None:
        """Send heartbeat to indicate worker is alive."""
        self._last_heartbeat = datetime.now(timezone.utc)
        
        # Send heartbeat to database if health manager is available
        if self._health_manager:
            try:
                self._health_manager.send_heartbeat(
                    worker_id=self.worker_id,
                    stats=self._stats,
                    current_batch_size=len(self._current_batch),
                    processing_since=self._processing_start_time,
                    error_message=self._last_error
                )
                # Clear error after successful heartbeat
                if self._last_error and self._state != WorkerState.ERROR:
                    self._last_error = None
            except Exception as e:
                logger.error(f"Failed to send database heartbeat: {e}")
        
        # Log heartbeat with current status
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                f"Heartbeat - State: {self._state.value}, "
                f"Batch size: {len(self._current_batch)}, "
                f"Processed: {self._stats['entries_processed']}"
            )
    
    def _release_current_batch(self) -> None:
        """Release any currently claimed batch."""
        if not self._current_batch:
            return
        
        try:
            db_session = self.db_session_factory()
            work_queue = WorkQueueManager(db_session)
            work_queue.release_batch(self._current_batch, self.worker_id)
            
            logger.info(f"Released {len(self._current_batch)} entries during shutdown")
            self._current_batch = []
            
        except Exception as e:
            logger.error(f"Failed to release current batch: {e}")
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def _update_stats(self, total_entries: int, success_count: int, failed_count: int) -> None:
        """Update worker statistics."""
        processing_duration = 0.0
        if self._processing_start_time:
            processing_duration = (
                datetime.now(timezone.utc) - self._processing_start_time
            ).total_seconds()
        
        self._stats['batches_processed'] += 1
        self._stats['entries_processed'] += success_count
        self._stats['entries_failed'] += failed_count
        self._stats['total_processing_time'] += processing_duration
        self._stats['last_batch_size'] = total_entries
        self._stats['last_batch_duration'] = processing_duration
    
    def _get_uptime_seconds(self) -> float:
        """Get worker uptime in seconds."""
        if self._state == WorkerState.STOPPED:
            return 0.0
        
        # This is a simplified implementation
        # In a real implementation, you'd track start time
        return self._stats['total_processing_time']
    
    def _register_worker(self) -> None:
        """Register worker with health monitoring system."""
        try:
            db_session = self.db_session_factory()
            self._health_manager = WorkerHealthManager(db_session)
            
            # Register worker in database
            self._health_manager.register_worker(
                worker_id=self.worker_id,
                worker_type=self.__class__.__name__,
                config=self.config
            )
            
            logger.info(f"Registered worker {self.worker_id} with health monitoring")
            
        except Exception as e:
            logger.error(f"Failed to register worker with health monitoring: {e}")
            # Don't fail startup if health registration fails
            self._health_manager = None
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def _unregister_worker(self) -> None:
        """Unregister worker from health monitoring system."""
        if not self._health_manager:
            return
            
        try:
            db_session = self.db_session_factory()
            health_manager = WorkerHealthManager(db_session)
            
            # Unregister worker from database
            health_manager.unregister_worker(self.worker_id)
            
            logger.info(f"Unregistered worker {self.worker_id} from health monitoring")
            
        except Exception as e:
            logger.error(f"Failed to unregister worker from health monitoring: {e}")
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def _start_health_endpoints(self) -> None:
        """Start health check endpoints if configured."""
        try:
            self._health_endpoints = WorkerHealthEndpoints(self)
            self._health_endpoints.start()
        except Exception as e:
            logger.warning(f"Failed to start health endpoints: {e}")
            # Don't fail worker startup if health endpoints fail
            self._health_endpoints = None
    
    def _stop_health_endpoints(self) -> None:
        """Stop health check endpoints."""
        if self._health_endpoints:
            try:
                self._health_endpoints.stop()
            except Exception as e:
                logger.warning(f"Error stopping health endpoints: {e}")
            finally:
                self._health_endpoints = None
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get comprehensive health status for monitoring.
        
        Returns:
            Dictionary with health information
        """
        # Try to get health from database first
        if self._health_manager:
            try:
                db_health = self._health_manager.get_worker_health(self.worker_id)
                if db_health:
                    return db_health
            except Exception as e:
                logger.warning(f"Failed to get database health status: {e}")
        
        # Fallback to local health status
        now = datetime.now(timezone.utc)
        
        health = {
            'worker_id': self.worker_id,
            'worker_type': self.__class__.__name__,
            'state': self._state.value,
            'healthy': self._state == WorkerState.RUNNING,
            'last_heartbeat': self._last_heartbeat.isoformat() if self._last_heartbeat else None,
            'current_batch_size': len(self._current_batch),
            'processing_since': self._processing_start_time.isoformat() if self._processing_start_time else None,
            'last_error': self._last_error,
            'stats': self.stats,
            'config': {
                'stages': [s.value for s in self.config.stages],
                'batch_size': self.config.batch_size,
                'heartbeat_interval': self.config.heartbeat_interval_seconds
            }
        }
        
        # Add heartbeat freshness
        if self._last_heartbeat:
            heartbeat_age = (now - self._last_heartbeat).total_seconds()
            health['heartbeat_age_seconds'] = heartbeat_age
            health['heartbeat_stale'] = heartbeat_age > (self.config.heartbeat_interval_seconds * 2)
        
        return health


class WorkerManager:
    """
    Manages multiple distributed workers.
    
    Provides utilities for starting, stopping, and monitoring multiple workers
    in a coordinated fashion.
    """
    
    def __init__(self):
        """Initialize the worker manager."""
        self.workers: Dict[str, DistributedWorker] = {}
        self._shutdown_event = threading.Event()
    
    def add_worker(self, worker: DistributedWorker) -> None:
        """Add a worker to the manager."""
        self.workers[worker.worker_id] = worker
        logger.info(f"Added worker {worker.worker_id} to manager")
    
    def start_all(self) -> None:
        """Start all managed workers."""
        logger.info(f"Starting {len(self.workers)} workers...")
        
        for worker_id, worker in self.workers.items():
            try:
                worker.start()
                logger.info(f"Started worker {worker_id}")
            except Exception as e:
                logger.error(f"Failed to start worker {worker_id}: {e}")
    
    def stop_all(self, timeout: Optional[int] = None) -> None:
        """Stop all managed workers."""
        logger.info(f"Stopping {len(self.workers)} workers...")
        
        # Signal all workers to stop
        for worker in self.workers.values():
            try:
                worker.stop(timeout)
            except Exception as e:
                logger.error(f"Error stopping worker {worker.worker_id}: {e}")
        
        logger.info("All workers stopped")
    
    def get_worker_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all managed workers."""
        return {
            worker_id: worker.get_health_status()
            for worker_id, worker in self.workers.items()
        }
    
    def get_running_workers(self) -> List[DistributedWorker]:
        """Get list of currently running workers."""
        return [worker for worker in self.workers.values() if worker.is_running]
    
    def cleanup_failed_workers(self) -> None:
        """Remove workers that are in error state."""
        failed_workers = [
            worker_id for worker_id, worker in self.workers.items()
            if worker.state == WorkerState.ERROR
        ]
        
        for worker_id in failed_workers:
            logger.warning(f"Removing failed worker {worker_id}")
            del self.workers[worker_id]
