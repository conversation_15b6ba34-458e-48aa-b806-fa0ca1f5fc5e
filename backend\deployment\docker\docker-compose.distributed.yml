version: '3.8'

services:
  # Backend API service (unchanged)
  backend:
    build:
      context: ../../..
      dockerfile: backend/Dockerfile
    image: robertschulze/breaking-bright-backend:latest
    ports:
      - "8000:8000"
    env_file:
      - ../../../.env
    restart: always
    volumes:
      - ../../../backend/app:/backend/app
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    environment:
      - PYTHONPATH=/
    networks:
      - etl-network

  # Worker Manager - coordinates distributed workers
  worker-manager:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=manager-001
      - WORKER_STAGES=all
      - WORKER_BATCH_SIZE=25
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8080
      - WORKER_METRICS_ENABLED=true
      - PYTHONPATH=/
    ports:
      - "8080:8080"  # Health check and metrics endpoint
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network
    command: ["python", "-m", "backend.app.distributed.worker_manager"]

  # Feed Download Workers - specialized for RSS feed processing
  feed-worker-1:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=feed-worker-001
      - WORKER_STAGES=download_feeds
      - WORKER_BATCH_SIZE=50
      - WORKER_CLAIM_TIMEOUT_MINUTES=15
      - WORKER_MAX_RETRIES=5
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8081
      - PYTHONPATH=/
    ports:
      - "8081:8081"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  feed-worker-2:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=feed-worker-002
      - WORKER_STAGES=download_feeds
      - WORKER_BATCH_SIZE=50
      - WORKER_CLAIM_TIMEOUT_MINUTES=15
      - WORKER_MAX_RETRIES=5
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8082
      - PYTHONPATH=/
    ports:
      - "8082:8082"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  # Full Text Download Workers - specialized for article content extraction
  fulltext-worker-1:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=fulltext-worker-001
      - WORKER_STAGES=download_full_text
      - WORKER_BATCH_SIZE=10
      - WORKER_CLAIM_TIMEOUT_MINUTES=20
      - WORKER_MAX_RETRIES=3
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8083
      - PYTHONPATH=/
    ports:
      - "8083:8083"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  fulltext-worker-2:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=fulltext-worker-002
      - WORKER_STAGES=download_full_text
      - WORKER_BATCH_SIZE=10
      - WORKER_CLAIM_TIMEOUT_MINUTES=20
      - WORKER_MAX_RETRIES=3
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8084
      - PYTHONPATH=/
    ports:
      - "8084:8084"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  # Analysis Workers - specialized for sentiment analysis and classification
  analysis-worker-1:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=analysis-worker-001
      - WORKER_STAGES=sentiment_analysis,llm_analysis,iptc_classification
      - WORKER_BATCH_SIZE=5
      - WORKER_CLAIM_TIMEOUT_MINUTES=60
      - WORKER_MAX_RETRIES=3
      - WORKER_PROCESSING_DELAY_SECONDS=0.5
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8085
      - PYTHONPATH=/
    ports:
      - "8085:8085"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  analysis-worker-2:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=analysis-worker-002
      - WORKER_STAGES=sentiment_analysis,llm_analysis,iptc_classification
      - WORKER_BATCH_SIZE=5
      - WORKER_CLAIM_TIMEOUT_MINUTES=60
      - WORKER_MAX_RETRIES=3
      - WORKER_PROCESSING_DELAY_SECONDS=0.5
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8086
      - PYTHONPATH=/
    ports:
      - "8086:8086"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  # Translation and Embedding Workers - specialized for ML tasks
  ml-worker-1:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=ml-worker-001
      - WORKER_STAGES=translate_text,compute_embeddings
      - WORKER_BATCH_SIZE=8
      - WORKER_CLAIM_TIMEOUT_MINUTES=45
      - WORKER_MAX_RETRIES=3
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8087
      - PYTHONPATH=/
    ports:
      - "8087:8087"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  # Content Generation Workers - specialized for description and image generation
  generation-worker-1:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=generation-worker-001
      - WORKER_STAGES=generate_descriptions,generate_image_prompts,generate_preview_images
      - WORKER_BATCH_SIZE=3
      - WORKER_CLAIM_TIMEOUT_MINUTES=90
      - WORKER_MAX_RETRIES=2
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8088
      - PYTHONPATH=/
    ports:
      - "8088:8088"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  # Utility Workers - specialized for duplicate checking and RSS generation
  utility-worker-1:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=utility-worker-001
      - WORKER_STAGES=duplicate_check,generate_rss_feed
      - WORKER_BATCH_SIZE=15
      - WORKER_CLAIM_TIMEOUT_MINUTES=30
      - WORKER_MAX_RETRIES=3
      - WORKER_LOG_LEVEL=INFO
      - WORKER_HEALTH_CHECK_PORT=8089
      - PYTHONPATH=/
    ports:
      - "8089:8089"
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network

  # Stale Work Cleanup Service
  cleanup-service:
    build:
      context: ../../..
      dockerfile: backend/deployment/docker/Dockerfile.distributed-worker
    image: robertschulze/breaking-bright-worker:latest
    env_file:
      - ../../../.env
    environment:
      - WORKER_ID=cleanup-service-001
      - CLEANUP_INTERVAL_MINUTES=5
      - STALE_TIMEOUT_MINUTES=60
      - WORKER_LOG_LEVEL=INFO
      - PYTHONPATH=/
    restart: always
    depends_on:
      - backend
    networks:
      - etl-network
    command: ["python", "-m", "backend.app.distributed.stale_work_cleanup"]

  # Web frontend (unchanged)
  web:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ../../../frontend/positive_news_app/build/web:/usr/share/nginx/html
      - ../../../nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
    restart: always
    networks:
      - etl-network

networks:
  etl-network:
    driver: bridge

volumes:
  worker-logs:
    driver: local