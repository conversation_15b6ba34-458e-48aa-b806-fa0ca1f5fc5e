import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/screens/detail/detail_screen.dart';
import 'package:breakingbright/screens/web_view/web_view_screen.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/services/similar_news_service.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/widgets/llm_score_popup.dart';
import 'package:url_launcher/url_launcher.dart';

class NewsCard extends StatefulWidget {
  // Make constants static and public (remove underscore)
  static const double bottomBarHeight = 48.0;
  static const double iconSize = 20.0;
  static const double imageHeight = 200.0;

  final EntryWithSource news;
  final bool showFavoriteButton;
  final bool showSimilarMessage;
  final double? width;

  const NewsCard({
    super.key,
    required this.news,
    this.showFavoriteButton = true,
    this.showSimilarMessage = true,
    this.width,
  });

  @override
  State<NewsCard> createState() => _NewsCardState();
}

class _NewsCardState extends State<NewsCard> {
  @override
  void initState() {
    super.initState();
    // Trigger loading of similar flag if not already loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.news.hasSimilar == null && widget.showSimilarMessage) {
        final similarService = Provider.of<SimilarNewsService>(context, listen: false);
        similarService.loadSimilarFlag(widget.news.entryId);
      }
    });
  }

  Future<void> _openNewsLink(BuildContext context, String url,
      [bool newTab = false]) async {
    final Uri uri = Uri.parse(url);

    if (kIsWeb && newTab) {
      try {
        await launchUrl(
          uri,
          mode: LaunchMode
              .platformDefault, // For web, use platform default (new tab)
        );
        return;
      } catch (e) {
        debugPrint('Failed to open URL in new tab: $e');
      }
    }

    // Check if context is still mounted before proceeding
    if (!context.mounted) return;

    // Try WebView approach first - no async gap before using context
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => WebViewScreen(
            url: url,
            title: widget.news.title,
          ),
        ),
      );
      return; // Exit if successful
    } catch (e) {
      debugPrint('Custom WebView failed: $e');
      // If WebView fails, continue to next approach
    }

    // For the remaining approaches, we'll use async operations
    // so we need to handle context carefully
    _tryAlternativeApproaches(context, uri, url);
  }

  // Separate method to handle alternative URL opening approaches
  Future<void> _tryAlternativeApproaches(
      BuildContext context, Uri uri, String url) async {
    try {
      // Try in-app browser
      final bool launched = await launchUrl(
        uri,
        mode: LaunchMode.inAppWebView,
        webViewConfiguration: const WebViewConfiguration(
          enableJavaScript: true,
          enableDomStorage: true,
        ),
      );

      if (!launched) {
        throw Exception('Could not launch URL in in-app browser');
      }
    } catch (e) {
      // Try external browser as final fallback
      try {
        final bool launched = await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );

        if (!launched) {
          throw Exception('Could not launch URL in external browser');
        }
      } catch (fallbackError) {
        // Check if context is still mounted before showing snackbar
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Konnte Link nicht öffnen: $url')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      child: IntrinsicHeight(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top content section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image with fixed height and score overlay
                SizedBox(
                  height: NewsCard.imageHeight,
                  child: Stack(
                    children: [
                      // Image with click/touch handling
                      GestureDetector(
                        onTap: () => kIsWeb
                            ? _openNewsLink(context, widget.news.link, false)
                            : _openNewsLink(context, widget.news.link),
                        behavior: HitTestBehavior.translucent,
                        child: ClipRRect(
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(4.0),
                            topRight: Radius.circular(4.0),
                          ),
                          child: CachedNetworkImage(
                            imageUrl:
                                Provider.of<ApiService>(context, listen: false)
                                    .getImageUrl(widget.news.entryId,
                                        hasImage: widget.news.hasImage == true),
                            height: NewsCard.imageHeight,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              height: NewsCard.imageHeight,
                              color: Colors.grey[300],
                              child: const Center(
                                  child: CircularProgressIndicator()),
                            ),
                            errorWidget: (context, url, error) => Container(
                              height: NewsCard.imageHeight,
                              color: Colors.grey[300],
                              child: const Icon(Icons.error),
                            ),
                          ),
                        ),
                      ),

                      // Score overlay
                      Consumer<SettingsProvider>(
                        builder: (context, settings, child) {
                          if (!settings.showLlmScore ||
                              widget.news.llmPositive == null) {
                            return const SizedBox.shrink();
                          }

                          return Positioned(
                            top: 8,
                            right: 8,
                            child: GestureDetector(
                              onTap: () => LlmScorePopup.show(
                                context,
                                widget.news.llmReasonList,
                                widget.news.llmPositive,
                                widget.news.entryId,
                              ),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.black.withValues(alpha: 0.7),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  '${(widget.news.llmPositive! * 100).round()}%',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    shadows: [
                                      Shadow(
                                        offset: Offset(1, 1),
                                        blurRadius: 2,
                                        color: Colors.black,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),

                // Content area
                kIsWeb
                    ? GestureDetector(
                        onTap: () => _openNewsLink(context, widget.news.link, false),
                        behavior: HitTestBehavior.translucent,
                        child: Listener(
                          onPointerDown: (PointerDownEvent event) {
                            // Check if Ctrl key is pressed or right mouse button for new tab
                            if (event.kind == PointerDeviceKind.mouse &&
                                (event.buttons & kSecondaryMouseButton != 0 ||
                                    event.down &&
                                        event.buttons == kPrimaryMouseButton &&
                                        (HardwareKeyboard
                                            .instance.isControlPressed))) {
                              _openNewsLink(context, widget.news.link, true);
                            }
                          },
                          child: _buildContentArea(context),
                        ),
                      )
                    : GestureDetector(
                        onTap: () => _openNewsLink(context, widget.news.link),
                        // Allow scrolling by not consuming pan gestures
                        behavior: HitTestBehavior.translucent,
                        child: _buildContentArea(context),
                      ),
              ],
            ),

            // Fixed height bottom bar
            SizedBox(
              height: NewsCard.bottomBarHeight,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Similar news button with optimized loading
                    if (widget.showSimilarMessage)
                      Expanded(
                        child: SizedBox(
                          height: NewsCard.bottomBarHeight,
                          child: _buildOptimizedSimilarButton(context),
                        ),
                      )
                    else
                      const Spacer(),

                    // Favorite button
                    if (widget.showFavoriteButton)
                      SizedBox(
                        width: NewsCard.bottomBarHeight,
                        height: NewsCard.bottomBarHeight,
                        child: Consumer<FavoritesService>(
                          builder: (context, favoritesService, child) {
                            final isFavorite =
                                favoritesService.isFavorite(widget.news);
                            return IconButton(
                              icon: Icon(
                                isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                color: isFavorite ? Colors.red : null,
                                size: NewsCard.iconSize,
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                              onPressed: () {
                                if (isFavorite) {
                                  favoritesService.removeFavorite(widget.news);
                                } else {
                                  favoritesService.addFavorite(widget.news);
                                }
                              },
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptimizedSimilarButton(BuildContext context) {
    return Consumer<SimilarNewsService>(
      builder: (context, similarService, child) {
        // Check if we have a cached result
        final cachedFlag = similarService.getSimilarFlag(widget.news.entryId);
        final isLoading = similarService.isLoading(widget.news.entryId);

        // Use cached result if available, otherwise use the original value
        final hasSimilar = cachedFlag ?? widget.news.hasSimilar;

        if (isLoading || (hasSimilar == null && !similarService.isLoaded(widget.news.entryId))) {
          return TextButton.icon(
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              minimumSize: Size.zero,
              alignment: Alignment.centerLeft,
              foregroundColor: Colors.grey,
            ),
            icon: const SizedBox(
              width: NewsCard.iconSize,
              height: NewsCard.iconSize,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            label: const Text(
              'Prüfe ähnliche Nachrichten...',
              style: TextStyle(fontSize: 12.0),
              overflow: TextOverflow.ellipsis,
            ),
            onPressed: null,
          );
        }

        if (hasSimilar == true) {
          return TextButton.icon(
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              minimumSize: Size.zero,
              alignment: Alignment.centerLeft,
            ),
            icon: const Icon(
              Icons.article,
              size: NewsCard.iconSize,
            ),
            label: const Text(
              'Ähnliche Nachrichten',
              style: TextStyle(fontSize: 12.0),
              overflow: TextOverflow.ellipsis,
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => DetailScreen(news: widget.news),
                ),
              );
            },
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildContentArea(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.news.title,
            style: Theme.of(context).textTheme.titleLarge,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.news.sourceName ?? widget.news.source,
                  style: Theme.of(context).textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                widget.news.formattedDate,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          if (widget.news.description != null && widget.news.description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              widget.news.formattedDescription!,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}
