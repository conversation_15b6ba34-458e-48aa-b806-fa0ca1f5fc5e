"""
Integration tests for DistributedWorker with real database components.
"""

import pytest
import time
from unittest.mock import Mock, patch
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.app.distributed.distributed_worker import DistributedWorker, WorkerState
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestDistributedWorkerIntegration:
    """Integration tests for DistributedWorker."""
    
    @pytest.fixture
    def in_memory_db(self):
        """Create an in-memory SQLite database for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        
        # Create tables (simplified for testing)
        from backend.app.models.models import Base
        Base.metadata.create_all(engine)
        
        SessionLocal = sessionmaker(bind=engine)
        return SessionLocal
    
    @pytest.fixture
    def test_worker_config(self):
        """Create test worker configuration."""
        return WorkerConfig(
            worker_id="integration-test-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            batch_size=5,
            heartbeat_interval_seconds=1,
            processing_delay_seconds=0.1,
            shutdown_timeout_seconds=2
        )
    
    @pytest.fixture
    def concrete_worker(self, test_worker_config, in_memory_db):
        """Create a concrete worker implementation for testing."""
        
        class IntegrationTestWorker(DistributedWorker):
            def __init__(self, config, db_session_factory):
                super().__init__(config, db_session_factory)
                self.processed_entries = []
            
            def process_batch(self, entries, stage):
                """Mock processing that always succeeds."""
                results = {}
                for entry in entries:
                    self.processed_entries.append(entry.entry_id)
                    results[entry.entry_id] = True
                    time.sleep(0.01)  # Simulate processing time
                return results
        
        return IntegrationTestWorker(test_worker_config, in_memory_db)
    
    def test_worker_initialization_with_real_config(self, concrete_worker):
        """Test worker initialization with real configuration."""
        assert concrete_worker.worker_id == "integration-test-worker"
        assert concrete_worker.state == WorkerState.STOPPED
        assert not concrete_worker.is_running
        assert len(concrete_worker.config.stages) == 1
        assert ProcessingStage.DOWNLOAD_FULL_TEXT in concrete_worker.config.stages
    
    def test_worker_health_status_comprehensive(self, concrete_worker):
        """Test comprehensive health status reporting."""
        # Send a heartbeat first
        concrete_worker._send_heartbeat()
        
        health = concrete_worker.get_health_status()
        
        # Verify all expected fields are present
        expected_fields = [
            'worker_id', 'state', 'healthy', 'last_heartbeat',
            'current_batch_size', 'processing_since', 'stats', 'config'
        ]
        
        for field in expected_fields:
            assert field in health, f"Missing field: {field}"
        
        # Verify config details
        assert health['config']['batch_size'] == 5
        assert health['config']['heartbeat_interval'] == 1
        assert len(health['config']['stages']) == 1
        assert health['config']['stages'][0] == 'download_full_text'
        
        # Verify stats structure
        stats = health['stats']
        expected_stats = [
            'batches_processed', 'entries_processed', 'entries_failed',
            'worker_id', 'state', 'current_batch_size'
        ]
        
        for stat in expected_stats:
            assert stat in stats, f"Missing stat: {stat}"
    
    @patch('backend.app.distributed.distributed_worker.WorkQueueManager')
    def test_worker_batch_processing_flow(self, mock_queue_manager_class, 
                                        concrete_worker, in_memory_db):
        """Test the complete batch processing flow."""
        # Setup mock work queue manager
        mock_queue_manager = Mock()
        mock_queue_manager_class.return_value = mock_queue_manager
        
        # Mock claiming a batch of work
        entry_ids = ["entry1", "entry2", "entry3"]
        mock_queue_manager.claim_batch.return_value = entry_ids
        
        # Create mock database session with entries
        mock_session = Mock()
        mock_entries = [Mock(entry_id=eid) for eid in entry_ids]
        mock_session.query.return_value.filter.return_value.all.return_value = mock_entries
        
        # Mock the session factory to return our mock session
        with patch.object(concrete_worker, 'db_session_factory', return_value=mock_session):
            # Process a batch
            result = concrete_worker._process_stage_batch(ProcessingStage.DOWNLOAD_FULL_TEXT)
            
            # Verify the batch was processed
            assert result is True
            
            # Verify work queue interactions
            mock_queue_manager.claim_batch.assert_called_once()
            mock_queue_manager.mark_completed.assert_called_once_with(
                entry_ids, ProcessingStage.DOWNLOAD_FULL_TEXT, "integration-test-worker"
            )
            
            # Verify entries were processed
            assert len(concrete_worker.processed_entries) == 3
            assert set(concrete_worker.processed_entries) == set(entry_ids)
            
            # Verify statistics were updated
            stats = concrete_worker.stats
            assert stats['batches_processed'] == 1
            assert stats['entries_processed'] == 3
            assert stats['entries_failed'] == 0
            assert stats['last_batch_size'] == 3
    
    def test_worker_error_handling_and_recovery(self, concrete_worker):
        """Test worker error handling and recovery mechanisms."""
        # Test statistics update with processing time
        from datetime import datetime, timezone
        concrete_worker._processing_start_time = datetime.now(timezone.utc)
        time.sleep(0.02)  # Small delay to get measurable duration
        
        concrete_worker._update_stats(total_entries=10, success_count=8, failed_count=2)
        
        stats = concrete_worker.stats
        assert stats['batches_processed'] == 1
        assert stats['entries_processed'] == 8
        assert stats['entries_failed'] == 2
        assert stats['last_batch_size'] == 10
        assert stats['last_batch_duration'] > 0
    
    def test_worker_graceful_shutdown_simulation(self, concrete_worker):
        """Test worker graceful shutdown behavior."""
        # Set up a current batch to test release functionality
        concrete_worker._current_batch = ["entry1", "entry2", "entry3"]
        
        # Mock the database session and work queue for batch release
        mock_session = Mock()
        mock_queue_manager = Mock()
        
        with patch.object(concrete_worker, 'db_session_factory', return_value=mock_session), \
             patch('backend.app.distributed.distributed_worker.WorkQueueManager', 
                   return_value=mock_queue_manager):
            
            # Test batch release
            concrete_worker._release_current_batch()
            
            # Verify batch was released
            mock_queue_manager.release_batch.assert_called_once_with(
                ["entry1", "entry2", "entry3"], "integration-test-worker"
            )
            
            # Verify current batch was cleared
            assert concrete_worker._current_batch == []
    
    def test_worker_configuration_validation_integration(self):
        """Test that worker properly validates configuration on initialization."""
        # Test with invalid configuration
        with pytest.raises(ValueError, match="Configuration validation failed"):
            invalid_config = WorkerConfig(
                worker_id="",  # Invalid empty worker_id
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                batch_size=-1  # Invalid negative batch_size
            )
        
        # Test with valid configuration
        valid_config = WorkerConfig(
            worker_id="valid-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS],
            batch_size=25,
            heartbeat_interval_seconds=30
        )
        
        # Should not raise any exception
        assert valid_config.worker_id == "valid-worker"
        assert len(valid_config.stages) == 2


if __name__ == "__main__":
    pytest.main([__file__, "-v"])