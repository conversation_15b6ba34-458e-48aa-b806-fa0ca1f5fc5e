"""
Tests for comprehensive error handling and retry system.
"""

import pytest
import json
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from backend.app.distributed.error_handling import (
    ErrorClassifier, ErrorCategory, Error<PERSON>everity, ErrorClassification,
    RetryCalculator, DeadLetterQueue, ComprehensiveErrorHandler,
    RetryAttempt, FailureRecord
)
from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus
from backend.app.models.models import Entry


class TestErrorClassifier:
    """Test error classification functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.classifier = ErrorClassifier()
    
    def test_classify_network_error(self):
        """Test classification of network errors."""
        classification = self.classifier.classify_error("Connection refused")
        
        assert classification.category == ErrorCategory.NETWORK
        assert classification.severity == ErrorSeverity.TRANSIENT
        assert classification.retry_strategy == "exponential_backoff"
        assert classification.max_retries == 5
    
    def test_classify_timeout_error(self):
        """Test classification of timeout errors."""
        classification = self.classifier.classify_error("Operation timed out")
        
        assert classification.category == ErrorCategory.TIMEOUT
        assert classification.severity == ErrorSeverity.TRANSIENT
        assert classification.max_retries == 3
    
    def test_classify_rate_limit_error(self):
        """Test classification of rate limit errors."""
        classification = self.classifier.classify_error("Rate limit exceeded")
        
        assert classification.category == ErrorCategory.RATE_LIMIT
        assert classification.severity == ErrorSeverity.PERSISTENT
        assert classification.max_retries == 10
        assert classification.base_delay_seconds == 60.0
    
    def test_classify_authentication_error(self):
        """Test classification of authentication errors."""
        classification = self.classifier.classify_error("Unauthorized access")
        
        assert classification.category == ErrorCategory.AUTHENTICATION
        assert classification.severity == ErrorSeverity.FATAL
        assert classification.retry_strategy == "no_retry"
        assert classification.max_retries == 0
    
    def test_classify_memory_error(self):
        """Test classification of memory errors."""
        classification = self.classifier.classify_error("Out of memory")
        
        assert classification.category == ErrorCategory.RESOURCE
        assert classification.severity == ErrorSeverity.PERSISTENT
        assert classification.max_retries == 3
    
    def test_classify_data_format_error(self):
        """Test classification of data format errors."""
        classification = self.classifier.classify_error("Invalid JSON format")
        
        assert classification.category == ErrorCategory.DATA_FORMAT
        assert classification.severity == ErrorSeverity.FATAL
        assert classification.max_retries == 0
    
    def test_classify_unknown_error(self):
        """Test classification of unknown errors defaults to processing."""
        classification = self.classifier.classify_error("Some unknown error")
        
        assert classification.category == ErrorCategory.PROCESSING
        assert classification.severity == ErrorSeverity.PERSISTENT
        assert classification.max_retries == 2
    
    def test_add_custom_rule(self):
        """Test adding custom classification rules."""
        custom_rule = ErrorClassification(
            category=ErrorCategory.EXTERNAL_SERVICE,
            severity=ErrorSeverity.TRANSIENT,
            retry_strategy="exponential_backoff",
            max_retries=7,
            base_delay_seconds=15.0,
            max_delay_seconds=1200.0,
            backoff_multiplier=1.8,
            description="Custom service error"
        )
        
        self.classifier.add_classification_rule("custom_error", custom_rule)
        
        classification = self.classifier.classify_error("custom_error occurred")
        assert classification.max_retries == 7
        assert classification.base_delay_seconds == 15.0


class TestRetryCalculator:
    """Test retry delay calculation functionality."""
    
    def test_exponential_backoff(self):
        """Test exponential backoff calculation."""
        # Test without jitter for predictable results
        delay1 = RetryCalculator.exponential_backoff(1, 2.0, 300.0, 2.0, jitter=False)
        delay2 = RetryCalculator.exponential_backoff(2, 2.0, 300.0, 2.0, jitter=False)
        delay3 = RetryCalculator.exponential_backoff(3, 2.0, 300.0, 2.0, jitter=False)
        
        assert delay1 == 2.0
        assert delay2 == 4.0
        assert delay3 == 8.0
    
    def test_exponential_backoff_max_delay(self):
        """Test exponential backoff respects max delay."""
        delay = RetryCalculator.exponential_backoff(10, 2.0, 100.0, 2.0, jitter=False)
        assert delay == 100.0
    
    def test_exponential_backoff_with_jitter(self):
        """Test exponential backoff with jitter."""
        base_delay = 10.0
        delay = RetryCalculator.exponential_backoff(1, base_delay, 300.0, 2.0, jitter=True)
        
        # Should be within ±25% of base delay
        assert 7.5 <= delay <= 12.5
    
    def test_linear_backoff(self):
        """Test linear backoff calculation."""
        delay1 = RetryCalculator.linear_backoff(1, 5.0, 100.0)
        delay2 = RetryCalculator.linear_backoff(2, 5.0, 100.0)
        delay3 = RetryCalculator.linear_backoff(3, 5.0, 100.0)
        
        assert delay1 == 5.0
        assert delay2 == 10.0
        assert delay3 == 15.0
    
    def test_linear_backoff_custom_increment(self):
        """Test linear backoff with custom increment."""
        delay1 = RetryCalculator.linear_backoff(1, 5.0, 100.0, increment=3.0)
        delay2 = RetryCalculator.linear_backoff(2, 5.0, 100.0, increment=3.0)
        
        assert delay1 == 5.0
        assert delay2 == 8.0
    
    def test_fixed_delay(self):
        """Test fixed delay calculation."""
        delay1 = RetryCalculator.fixed_delay(1, 10.0, 100.0)
        delay2 = RetryCalculator.fixed_delay(5, 10.0, 100.0)
        
        assert delay1 == 10.0
        assert delay2 == 10.0


class TestDeadLetterQueue:
    """Test dead letter queue functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_db = Mock(spec=Session)
        self.dead_letter_queue = DeadLetterQueue(self.mock_db)
        
        # Create mock entry
        self.mock_entry = Mock(spec=Entry)
        self.mock_entry.entry_id = "test-entry-1"
        self.mock_entry.processing_status = None
        self.mock_entry.last_error = None
        self.mock_entry.retry_count = 0
    
    def test_add_to_dead_letter_queue(self):
        """Test adding entry to dead letter queue."""
        # Setup mocks
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Create failure record
        failure_record = FailureRecord(
            entry_id="test-entry-1",
            stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
            first_failure_at=datetime.now(timezone.utc),
            last_failure_at=datetime.now(timezone.utc),
            total_attempts=3,
            retry_attempts=[],
            error_classification=ErrorClassification(
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.TRANSIENT,
                retry_strategy="exponential_backoff",
                max_retries=3,
                base_delay_seconds=2.0,
                max_delay_seconds=300.0,
                backoff_multiplier=2.0,
                description="Network error"
            )
        )
        
        # Test adding to dead letter queue
        self.dead_letter_queue.add_to_dead_letter_queue(
            "test-entry-1",
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            failure_record,
            "Max retries exceeded"
        )
        
        # Verify entry was updated
        assert self.mock_entry.last_error == "DEAD_LETTER: Max retries exceeded"
        assert self.mock_entry.retry_count == 3
        
        # Verify processing status was updated
        status_data = json.loads(self.mock_entry.processing_status)
        assert "dead_letter_info" in status_data
        assert status_data["dead_letter_info"]["is_dead_letter"] is True
    
    def test_get_dead_letter_entries(self):
        """Test retrieving dead letter entries."""
        # Setup mock entries
        mock_entries = [
            Mock(
                entry_id="dead-1",
                title="Dead Entry 1",
                published=datetime.now(timezone.utc),
                last_error="DEAD_LETTER: Max retries exceeded",
                retry_count=5,
                processing_status='{"dead_letter_info": {"reason": "Max retries"}}'
            ),
            Mock(
                entry_id="dead-2",
                title="Dead Entry 2",
                published=datetime.now(timezone.utc),
                last_error="DEAD_LETTER: Fatal error",
                retry_count=1,
                processing_status='{"dead_letter_info": {"reason": "Fatal error"}}'
            )
        ]
        
        self.mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = mock_entries
        
        # Test getting dead letter entries
        entries = self.dead_letter_queue.get_dead_letter_entries()
        
        assert len(entries) == 2
        assert entries[0]["entry_id"] == "dead-1"
        assert entries[1]["entry_id"] == "dead-2"
        assert "dead_letter_info" in entries[0]
    
    def test_retry_dead_letter_entry(self):
        """Test retrying a dead letter entry."""
        # Setup mocks
        self.mock_entry.processing_status = '{"dead_letter_info": {"reason": "Test"}}'
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test retrying entry
        result = self.dead_letter_queue.retry_dead_letter_entry(
            "test-entry-1",
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            reset_retry_count=True
        )
        
        assert result is True
        assert self.mock_entry.last_error is None
        assert self.mock_entry.retry_count == 0
        assert self.mock_entry.claimed_by is None
        
        # Verify dead letter info was removed
        status_data = json.loads(self.mock_entry.processing_status)
        assert "dead_letter_info" not in status_data


class TestComprehensiveErrorHandler:
    """Test comprehensive error handling functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_db = Mock(spec=Session)
        self.error_handler = ComprehensiveErrorHandler(self.mock_db)
        
        # Create mock entry
        self.mock_entry = Mock(spec=Entry)
        self.mock_entry.entry_id = "test-entry-1"
        self.mock_entry.processing_status = None
        self.mock_entry.last_error = None
        self.mock_entry.retry_count = 0
        self.mock_entry.claimed_by = None
        self.mock_entry.claimed_at = None
    
    def test_handle_transient_error_should_retry(self):
        """Test handling transient error that should be retried."""
        # Setup mocks
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test handling transient error
        should_retry, delay = self.error_handler.handle_processing_error(
            "test-entry-1",
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            "Connection refused",
            "worker-001"
        )
        
        assert should_retry is True
        assert delay is not None
        assert delay > 0
        
        # Verify failure record was created
        failure_key = "test-entry-1:download_full_text"
        assert failure_key in self.error_handler._failure_records
        
        failure_record = self.error_handler._failure_records[failure_key]
        assert failure_record.total_attempts == 1
        assert len(failure_record.retry_attempts) == 1
    
    def test_handle_fatal_error_no_retry(self):
        """Test handling fatal error that should not be retried."""
        # Setup mocks for dead letter queue
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test handling fatal error
        should_retry, delay = self.error_handler.handle_processing_error(
            "test-entry-1",
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            "Unauthorized access",
            "worker-001"
        )
        
        assert should_retry is False
        assert delay is None
        
        # Verify entry was added to dead letter queue
        assert self.mock_entry.last_error.startswith("DEAD_LETTER:")
    
    def test_handle_max_retries_exceeded(self):
        """Test handling error when max retries are exceeded."""
        # Setup mocks
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Create existing failure record with max attempts
        failure_key = "test-entry-1:download_full_text"
        classification = self.error_handler.classifier.classify_error("Connection refused")
        
        self.error_handler._failure_records[failure_key] = FailureRecord(
            entry_id="test-entry-1",
            stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
            first_failure_at=datetime.now(timezone.utc),
            last_failure_at=datetime.now(timezone.utc),
            total_attempts=classification.max_retries,  # Already at max
            retry_attempts=[],
            error_classification=classification
        )
        
        # Test handling error when max retries exceeded
        should_retry, delay = self.error_handler.handle_processing_error(
            "test-entry-1",
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            "Connection refused",
            "worker-001"
        )
        
        assert should_retry is False
        assert delay is None
    
    def test_get_retry_ready_entries(self):
        """Test getting entries ready for retry."""
        # Setup mock entries with retry info
        past_time = datetime.now(timezone.utc) - timedelta(minutes=5)
        future_time = datetime.now(timezone.utc) + timedelta(minutes=5)
        
        mock_entries = [
            Mock(
                entry_id="ready-1",
                claimed_by=None,
                processing_status=json.dumps({
                    "download_full_text": {
                        "retry_info": {
                            "next_retry_at": past_time.isoformat()
                        }
                    }
                })
            ),
            Mock(
                entry_id="not-ready-1",
                claimed_by=None,
                processing_status=json.dumps({
                    "download_full_text": {
                        "retry_info": {
                            "next_retry_at": future_time.isoformat()
                        }
                    }
                })
            ),
            Mock(
                entry_id="ready-2",
                claimed_by=None,
                processing_status=json.dumps({
                    "download_full_text": {
                        "retry_info": {
                            "next_retry_at": past_time.isoformat()
                        }
                    }
                })
            )
        ]
        
        self.mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = mock_entries
        
        # Test getting retry ready entries
        ready_entries = self.error_handler.get_retry_ready_entries(ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        assert len(ready_entries) == 2
        assert "ready-1" in ready_entries
        assert "ready-2" in ready_entries
        assert "not-ready-1" not in ready_entries
    
    def test_exponential_backoff_progression(self):
        """Test that retry delays follow exponential backoff."""
        # Setup mocks
        self.mock_db.query.return_value.filter.return_value.first.return_value = self.mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        delays = []
        
        # Simulate multiple failures
        for i in range(3):
            should_retry, delay = self.error_handler.handle_processing_error(
                "test-entry-1",
                ProcessingStage.DOWNLOAD_FULL_TEXT,
                "Connection refused",
                "worker-001"
            )
            
            if should_retry:
                delays.append(delay)
        
        # Verify delays increase exponentially (approximately)
        assert len(delays) == 3
        assert delays[1] > delays[0]
        assert delays[2] > delays[1]
        
        # Should roughly follow 2x pattern (with jitter)
        assert delays[1] >= delays[0] * 1.5  # Account for jitter
        assert delays[2] >= delays[1] * 1.5
    
    def test_get_error_statistics(self):
        """Test getting error statistics."""
        # Create some failure records
        classification = self.error_handler.classifier.classify_error("Connection refused")
        
        self.error_handler._failure_records["entry1:stage1"] = FailureRecord(
            entry_id="entry1",
            stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
            first_failure_at=datetime.now(timezone.utc),
            last_failure_at=datetime.now(timezone.utc),
            total_attempts=2,
            retry_attempts=[],
            error_classification=classification
        )
        
        self.error_handler._failure_records["entry2:stage2"] = FailureRecord(
            entry_id="entry2",
            stage=ProcessingStage.SENTIMENT_ANALYSIS,
            first_failure_at=datetime.now(timezone.utc),
            last_failure_at=datetime.now(timezone.utc),
            total_attempts=1,
            retry_attempts=[],
            error_classification=classification
        )
        
        # Mock dead letter queue stats
        with patch.object(self.error_handler.dead_letter_queue, 'get_dead_letter_stats') as mock_stats:
            mock_stats.return_value = {"total_dead_letter_entries": 5}
            
            stats = self.error_handler.get_error_statistics()
            
            assert stats["failure_records"]["total_failure_records"] == 2
            assert "download_full_text" in stats["failure_records"]["by_stage"]
            assert "sentiment_analysis" in stats["failure_records"]["by_stage"]
            assert "network" in stats["failure_records"]["by_error_category"]
            assert stats["dead_letter_queue"]["total_dead_letter_entries"] == 5
    
    def test_cleanup_old_failure_records(self):
        """Test cleanup of old failure records."""
        # Create old and new failure records
        old_time = datetime.now(timezone.utc) - timedelta(hours=25)
        new_time = datetime.now(timezone.utc) - timedelta(hours=1)
        
        classification = self.error_handler.classifier.classify_error("Connection refused")
        
        # Old record (should be cleaned up)
        self.error_handler._failure_records["old:stage"] = FailureRecord(
            entry_id="old",
            stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
            first_failure_at=old_time,
            last_failure_at=old_time,
            total_attempts=1,
            retry_attempts=[],
            error_classification=classification
        )
        
        # New record (should be kept)
        self.error_handler._failure_records["new:stage"] = FailureRecord(
            entry_id="new",
            stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
            first_failure_at=new_time,
            last_failure_at=new_time,
            total_attempts=1,
            retry_attempts=[],
            error_classification=classification
        )
        
        # Test cleanup
        cleaned_count = self.error_handler.cleanup_old_failure_records(max_age_hours=24)
        
        assert cleaned_count == 1
        assert "old:stage" not in self.error_handler._failure_records
        assert "new:stage" in self.error_handler._failure_records


class TestErrorHandlingIntegration:
    """Integration tests for error handling system."""
    
    def test_full_retry_cycle(self):
        """Test complete retry cycle from error to dead letter queue."""
        mock_db = Mock(spec=Session)
        error_handler = ComprehensiveErrorHandler(mock_db)
        
        # Setup mock entry
        mock_entry = Mock(spec=Entry)
        mock_entry.entry_id = "integration-test"
        mock_entry.processing_status = None
        mock_entry.last_error = None
        mock_entry.retry_count = 0
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_entry
        mock_db.begin.return_value.__enter__ = Mock(return_value=mock_db)
        mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Simulate multiple failures until dead letter
        entry_id = "integration-test"
        stage = ProcessingStage.DOWNLOAD_FULL_TEXT
        error_msg = "Connection refused"
        worker_id = "worker-001"
        
        retry_count = 0
        while True:
            should_retry, delay = error_handler.handle_processing_error(
                entry_id, stage, error_msg, worker_id
            )
            
            if not should_retry:
                break
            
            retry_count += 1
            assert delay is not None
            assert delay > 0
            
            # Prevent infinite loop
            if retry_count > 10:
                pytest.fail("Too many retries, should have reached dead letter queue")
        
        # Verify entry was added to dead letter queue
        assert mock_entry.last_error.startswith("DEAD_LETTER:")
        
        # Verify failure record exists
        failure_key = f"{entry_id}:{stage.value}"
        assert failure_key in error_handler._failure_records
        
        failure_record = error_handler._failure_records[failure_key]
        assert failure_record.total_attempts > 0
        assert len(failure_record.retry_attempts) == failure_record.total_attempts
    
    def test_different_error_types_different_strategies(self):
        """Test that different error types get different retry strategies."""
        mock_db = Mock(spec=Session)
        error_handler = ComprehensiveErrorHandler(mock_db)
        
        # Setup mock entry
        mock_entry = Mock(spec=Entry)
        mock_entry.entry_id = "strategy-test"
        mock_entry.processing_status = None
        mock_entry.last_error = None
        mock_entry.retry_count = 0
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_entry
        mock_db.begin.return_value.__enter__ = Mock(return_value=mock_db)
        mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test network error (should retry)
        should_retry_network, delay_network = error_handler.handle_processing_error(
            "strategy-test", ProcessingStage.DOWNLOAD_FULL_TEXT, "Connection refused", "worker-001"
        )
        
        # Test authentication error (should not retry)
        should_retry_auth, delay_auth = error_handler.handle_processing_error(
            "strategy-test", ProcessingStage.SENTIMENT_ANALYSIS, "Unauthorized", "worker-001"
        )
        
        # Test rate limit error (should retry with longer delay)
        should_retry_rate, delay_rate = error_handler.handle_processing_error(
            "strategy-test", ProcessingStage.COMPUTE_EMBEDDINGS, "Rate limit exceeded", "worker-001"
        )
        
        # Verify different strategies
        assert should_retry_network is True
        assert should_retry_auth is False
        assert should_retry_rate is True
        
        assert delay_network is not None
        assert delay_auth is None
        assert delay_rate is not None
        
        # Rate limit should have longer delay
        assert delay_rate > delay_network