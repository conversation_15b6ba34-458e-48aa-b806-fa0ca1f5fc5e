"""
Migration utilities for transitioning from monolithic to distributed ETL processing.

This module provides utilities for:
- Database migration scripts for smooth transition
- Data validation between processing modes
- Rollback mechanisms for failed migrations
- Migration state management and validation
"""

import json
import logging
import time
from datetime import datetime, timezone, timedelta
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict
from contextlib import contextmanager

from sqlalchemy import text, func, and_, or_
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from backend.app.models.models import Entry, Worker
from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus
from backend.app.distributed.work_queue_manager import WorkQueueManager

logger = logging.getLogger(__name__)


class MigrationMode(Enum):
    """Migration modes for transitioning between processing approaches."""
    MONOLITHIC = "monolithic"
    DISTRIBUTED = "distributed"
    HYBRID = "hybrid"  # Both systems running in parallel


class MigrationState(Enum):
    """States of the migration process."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


@dataclass
class MigrationConfig:
    """Configuration for migration process."""
    target_mode: MigrationMode
    batch_size: int = 1000
    validation_sample_size: int = 100
    timeout_minutes: int = 30
    backup_before_migration: bool = True
    validate_data_integrity: bool = True
    rollback_on_failure: bool = True
    parallel_validation: bool = False
    
    # Stage-specific migration settings
    stages_to_migrate: List[ProcessingStage] = None
    preserve_processing_history: bool = True
    
    def __post_init__(self):
        if self.stages_to_migrate is None:
            self.stages_to_migrate = list(ProcessingStage)


@dataclass
class MigrationResult:
    """Result of a migration operation."""
    success: bool
    state: MigrationState
    message: str
    entries_migrated: int = 0
    entries_validated: int = 0
    validation_errors: List[str] = None
    rollback_performed: bool = False
    duration_seconds: float = 0.0
    
    def __post_init__(self):
        if self.validation_errors is None:
            self.validation_errors = []


class MigrationValidator:
    """Validates data consistency between monolithic and distributed processing modes."""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.logger = logging.getLogger(f"{__name__}.MigrationValidator")
    
    def validate_processing_status_consistency(self, entry_ids: List[str]) -> Dict[str, Any]:
        """
        Validate that processing status is consistent between old and new formats.
        
        Args:
            entry_ids: List of entry IDs to validate
            
        Returns:
            Dictionary with validation results
        """
        self.logger.info(f"Validating processing status consistency for {len(entry_ids)} entries")
        
        validation_result = {
            'total_entries': len(entry_ids),
            'consistent_entries': 0,
            'inconsistent_entries': 0,
            'errors': [],
            'inconsistencies': []
        }
        
        try:
            # Get entries with processing status
            entries = (
                self.db_session.query(Entry)
                .filter(Entry.entry_id.in_(entry_ids))
                .all()
            )
            
            for entry in entries:
                try:
                    # Validate processing status JSON structure
                    if entry.processing_status:
                        processing_status = json.loads(entry.processing_status)
                        
                        # Check if status structure is valid
                        if self._validate_processing_status_structure(processing_status):
                            # Cross-validate with legacy fields
                            inconsistencies = self._cross_validate_legacy_fields(entry, processing_status)
                            
                            if inconsistencies:
                                validation_result['inconsistent_entries'] += 1
                                validation_result['inconsistencies'].extend(inconsistencies)
                            else:
                                validation_result['consistent_entries'] += 1
                        else:
                            validation_result['inconsistent_entries'] += 1
                            validation_result['errors'].append(
                                f"Entry {entry.entry_id}: Invalid processing status structure"
                            )
                    else:
                        # Entry without processing status - check if it should have one
                        if self._should_have_processing_status(entry):
                            validation_result['inconsistent_entries'] += 1
                            validation_result['errors'].append(
                                f"Entry {entry.entry_id}: Missing processing status"
                            )
                        else:
                            validation_result['consistent_entries'] += 1
                
                except json.JSONDecodeError as e:
                    validation_result['inconsistent_entries'] += 1
                    validation_result['errors'].append(
                        f"Entry {entry.entry_id}: Invalid JSON in processing_status: {e}"
                    )
                except Exception as e:
                    validation_result['inconsistent_entries'] += 1
                    validation_result['errors'].append(
                        f"Entry {entry.entry_id}: Validation error: {e}"
                    )
        
        except Exception as e:
            validation_result['errors'].append(f"Database query error: {e}")
            self.logger.error(f"Error validating processing status consistency: {e}")
        
        self.logger.info(
            f"Validation complete: {validation_result['consistent_entries']} consistent, "
            f"{validation_result['inconsistent_entries']} inconsistent entries"
        )
        
        return validation_result
    
    def _validate_processing_status_structure(self, processing_status: Dict) -> bool:
        """Validate the structure of processing status JSON."""
        if not isinstance(processing_status, dict):
            return False
        
        # Check that all stage entries have required fields
        for stage_name, stage_data in processing_status.items():
            if not isinstance(stage_data, dict):
                return False
            
            # Required fields for each stage
            if 'status' not in stage_data:
                return False
            
            # Valid status values
            valid_statuses = {'pending', 'in_progress', 'completed', 'failed'}
            if stage_data['status'] not in valid_statuses:
                return False
        
        return True
    
    def _cross_validate_legacy_fields(self, entry: Entry, processing_status: Dict) -> List[str]:
        """Cross-validate processing status with legacy database fields."""
        inconsistencies = []
        
        # Map legacy fields to processing stages
        legacy_field_mappings = {
            ProcessingStage.DOWNLOAD_FULL_TEXT: ['full_text'],
            ProcessingStage.TRANSLATE_TEXT: ['english_text'],
            ProcessingStage.COMPUTE_EMBEDDINGS: ['embedding'],
            ProcessingStage.SENTIMENT_ANALYSIS: ['vader_pos', 'vader_neu', 'vader_neg'],
            ProcessingStage.LLM_ANALYSIS: ['llm_positive', 'llm_neutral', 'llm_negative'],
            ProcessingStage.IPTC_CLASSIFICATION: ['iptc_newscode'],
            ProcessingStage.DUPLICATE_CHECK: ['dup_entry_id'],
            ProcessingStage.GENERATE_DESCRIPTIONS: ['description_auto_generated'],
            ProcessingStage.GENERATE_IMAGE_PROMPTS: ['image_prompt'],
            ProcessingStage.GENERATE_PREVIEW_IMAGES: ['preview_img']
        }
        
        for stage, fields in legacy_field_mappings.items():
            stage_name = stage.value
            
            # Check if stage status matches field presence
            if stage_name in processing_status:
                stage_status = processing_status[stage_name]['status']
                
                # Check if completed status matches field presence
                if stage_status == 'completed':
                    for field in fields:
                        field_value = getattr(entry, field, None)
                        if field_value is None or (isinstance(field_value, str) and not field_value.strip()):
                            inconsistencies.append(
                                f"Entry {entry.entry_id}: Stage {stage_name} marked completed but {field} is empty"
                            )
                
                elif stage_status == 'pending':
                    # If pending, fields should generally be empty (except for some cases)
                    for field in fields:
                        field_value = getattr(entry, field, None)
                        if field_value is not None and isinstance(field_value, str) and field_value.strip():
                            # This might be okay - could be from previous processing
                            pass
        
        return inconsistencies
    
    def _should_have_processing_status(self, entry: Entry) -> bool:
        """Determine if an entry should have processing status based on its data."""
        # If entry has any processed data, it should have processing status
        processed_fields = [
            entry.full_text, entry.english_text, entry.embedding,
            entry.vader_pos, entry.llm_positive, entry.iptc_newscode,
            entry.dup_entry_id, entry.image_prompt, entry.preview_img
        ]
        
        return any(field is not None for field in processed_fields)
    
    def validate_work_queue_integrity(self) -> Dict[str, Any]:
        """Validate work queue integrity and consistency."""
        self.logger.info("Validating work queue integrity")
        
        validation_result = {
            'total_claimed_entries': 0,
            'stale_claims': 0,
            'orphaned_claims': 0,
            'invalid_claims': 0,
            'errors': []
        }
        
        try:
            # Check for stale claims (claimed but worker not active)
            stale_timeout = datetime.now(timezone.utc) - timedelta(hours=1)
            
            stale_claims = (
                self.db_session.query(Entry)
                .filter(
                    and_(
                        Entry.claimed_by.isnot(None),
                        Entry.claimed_at < stale_timeout
                    )
                )
                .count()
            )
            
            validation_result['stale_claims'] = stale_claims
            
            # Check for orphaned claims (claimed by non-existent workers)
            claimed_entries = (
                self.db_session.query(Entry.claimed_by)
                .filter(Entry.claimed_by.isnot(None))
                .distinct()
                .all()
            )
            
            active_workers = (
                self.db_session.query(Worker.worker_id)
                .filter(Worker.status == 'running')
                .all()
            )
            
            claimed_worker_ids = {row[0] for row in claimed_entries}
            active_worker_ids = {row[0] for row in active_workers}
            
            orphaned_worker_ids = claimed_worker_ids - active_worker_ids
            
            if orphaned_worker_ids:
                orphaned_count = (
                    self.db_session.query(Entry)
                    .filter(Entry.claimed_by.in_(orphaned_worker_ids))
                    .count()
                )
                validation_result['orphaned_claims'] = orphaned_count
            
            # Total claimed entries
            total_claimed = (
                self.db_session.query(Entry)
                .filter(Entry.claimed_by.isnot(None))
                .count()
            )
            validation_result['total_claimed_entries'] = total_claimed
            
        except Exception as e:
            validation_result['errors'].append(f"Work queue validation error: {e}")
            self.logger.error(f"Error validating work queue integrity: {e}")
        
        return validation_result
    
    def compare_processing_results(self, entry_ids: List[str], 
                                 monolithic_results: Dict[str, Any],
                                 distributed_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compare processing results between monolithic and distributed modes.
        
        Args:
            entry_ids: List of entry IDs to compare
            monolithic_results: Results from monolithic processing
            distributed_results: Results from distributed processing
            
        Returns:
            Comparison results
        """
        self.logger.info(f"Comparing processing results for {len(entry_ids)} entries")
        
        comparison_result = {
            'total_entries': len(entry_ids),
            'matching_results': 0,
            'differing_results': 0,
            'differences': [],
            'errors': []
        }
        
        try:
            for entry_id in entry_ids:
                mono_result = monolithic_results.get(entry_id, {})
                dist_result = distributed_results.get(entry_id, {})
                
                # Compare key processing fields
                differences = self._compare_entry_results(entry_id, mono_result, dist_result)
                
                if differences:
                    comparison_result['differing_results'] += 1
                    comparison_result['differences'].extend(differences)
                else:
                    comparison_result['matching_results'] += 1
        
        except Exception as e:
            comparison_result['errors'].append(f"Result comparison error: {e}")
            self.logger.error(f"Error comparing processing results: {e}")
        
        return comparison_result
    
    def _compare_entry_results(self, entry_id: str, 
                             mono_result: Dict[str, Any],
                             dist_result: Dict[str, Any]) -> List[str]:
        """Compare results for a single entry."""
        differences = []
        
        # Fields to compare (with tolerance for floating point values)
        comparable_fields = {
            'vader_pos': 'float',
            'vader_neu': 'float', 
            'vader_neg': 'float',
            'llm_positive': 'float',
            'llm_neutral': 'float',
            'llm_negative': 'float',
            'iptc_newscode': 'string',
            'dup_entry_conf': 'float'
        }
        
        for field, field_type in comparable_fields.items():
            mono_value = mono_result.get(field)
            dist_value = dist_result.get(field)
            
            if field_type == 'float':
                if not self._float_values_equal(mono_value, dist_value):
                    differences.append(
                        f"Entry {entry_id}: {field} differs - mono: {mono_value}, dist: {dist_value}"
                    )
            elif field_type == 'string':
                if mono_value != dist_value:
                    differences.append(
                        f"Entry {entry_id}: {field} differs - mono: {mono_value}, dist: {dist_value}"
                    )
        
        return differences
    
    def _float_values_equal(self, val1: Optional[float], val2: Optional[float], 
                          tolerance: float = 1e-6) -> bool:
        """Compare float values with tolerance."""
        if val1 is None and val2 is None:
            return True
        if val1 is None or val2 is None:
            return False
        return abs(val1 - val2) < tolerance


class MigrationManager:
    """Manages the migration process from monolithic to distributed processing."""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
        self.validator = MigrationValidator(db_session)
        self.logger = logging.getLogger(f"{__name__}.MigrationManager")
    
    def migrate_to_distributed(self, config: MigrationConfig) -> MigrationResult:
        """
        Migrate from monolithic to distributed processing mode.
        
        Args:
            config: Migration configuration
            
        Returns:
            Migration result
        """
        start_time = time.time()
        self.logger.info(f"Starting migration to distributed mode: {config.target_mode.value}")
        
        result = MigrationResult(
            success=False,
            state=MigrationState.IN_PROGRESS,
            message="Migration in progress"
        )
        
        try:
            # Step 1: Validate current state
            self.logger.info("Step 1: Validating current state")
            if not self._validate_migration_prerequisites(config):
                result.state = MigrationState.FAILED
                result.message = "Migration prerequisites not met"
                return result
            
            # Step 2: Create backup if requested
            if config.backup_before_migration:
                self.logger.info("Step 2: Creating backup")
                backup_success = self._create_migration_backup()
                if not backup_success:
                    result.state = MigrationState.FAILED
                    result.message = "Failed to create backup"
                    return result
            
            # Step 3: Migrate processing status for existing entries
            self.logger.info("Step 3: Migrating processing status")
            migration_stats = self._migrate_processing_status(config)
            result.entries_migrated = migration_stats['migrated_count']
            
            if migration_stats['failed_count'] > 0:
                self.logger.warning(f"Failed to migrate {migration_stats['failed_count']} entries")
            
            # Step 4: Validate migrated data
            if config.validate_data_integrity:
                self.logger.info("Step 4: Validating migrated data")
                validation_result = self._validate_migrated_data(config)
                result.entries_validated = validation_result['validated_count']
                result.validation_errors = validation_result['errors']
                
                if validation_result['critical_errors'] > 0:
                    if config.rollback_on_failure:
                        self.logger.error("Critical validation errors found, rolling back")
                        rollback_result = self._rollback_migration()
                        result.rollback_performed = rollback_result
                        result.state = MigrationState.ROLLED_BACK
                        result.message = "Migration rolled back due to validation errors"
                        return result
            
            # Step 5: Update system configuration
            self.logger.info("Step 5: Updating system configuration")
            self._update_system_configuration(config.target_mode)
            
            # Migration completed successfully
            result.success = True
            result.state = MigrationState.COMPLETED
            result.message = "Migration completed successfully"
            
            self.logger.info(
                f"Migration completed: {result.entries_migrated} entries migrated, "
                f"{result.entries_validated} entries validated"
            )
        
        except Exception as e:
            self.logger.error(f"Migration failed with error: {e}", exc_info=True)
            result.state = MigrationState.FAILED
            result.message = f"Migration failed: {str(e)}"
            
            if config.rollback_on_failure:
                try:
                    rollback_result = self._rollback_migration()
                    result.rollback_performed = rollback_result
                    if rollback_result:
                        result.state = MigrationState.ROLLED_BACK
                        result.message += " (rolled back)"
                except Exception as rollback_error:
                    self.logger.error(f"Rollback failed: {rollback_error}")
                    result.message += f" (rollback failed: {rollback_error})"
        
        finally:
            result.duration_seconds = time.time() - start_time
        
        return result
    
    def _validate_migration_prerequisites(self, config: MigrationConfig) -> bool:
        """Validate that migration prerequisites are met."""
        try:
            # Check database schema is up to date
            if not self._check_database_schema():
                self.logger.error("Database schema is not up to date")
                return False
            
            # Check no active workers are running if migrating to distributed
            if config.target_mode == MigrationMode.DISTRIBUTED:
                active_workers = (
                    self.db_session.query(Worker)
                    .filter(Worker.status == 'running')
                    .count()
                )
                
                if active_workers > 0:
                    self.logger.error(f"Found {active_workers} active workers, stop them before migration")
                    return False
            
            # Check for pending work that might be lost
            pending_work = (
                self.db_session.query(Entry)
                .filter(Entry.claimed_by.isnot(None))
                .count()
            )
            
            if pending_work > 0:
                self.logger.warning(f"Found {pending_work} entries with pending work")
                # This is a warning, not a blocker
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error validating migration prerequisites: {e}")
            return False
    
    def _check_database_schema(self) -> bool:
        """Check if database schema supports distributed processing."""
        try:
            # Check if required columns exist
            required_columns = [
                'processing_status', 'claimed_by', 'claimed_at', 
                'retry_count', 'last_error'
            ]
            
            # Use SQLAlchemy inspector to check columns
            from sqlalchemy import inspect
            inspector = inspect(self.db_session.bind)
            columns = [col['name'] for col in inspector.get_columns('entries')]
            
            missing_columns = [col for col in required_columns if col not in columns]
            
            if missing_columns:
                self.logger.error(f"Missing required columns: {missing_columns}")
                return False
            
            # Check if workers table exists
            tables = inspector.get_table_names()
            if 'workers' not in tables:
                self.logger.error("Workers table does not exist")
                return False
            
            return True
        
        except Exception as e:
            self.logger.error(f"Error checking database schema: {e}")
            return False
    
    def _create_migration_backup(self) -> bool:
        """Create a backup before migration."""
        try:
            # Create a backup table with current processing state
            backup_table_name = f"entries_backup_{int(time.time())}"
            
            # Create backup table with relevant columns
            backup_sql = f"""
            CREATE TABLE {backup_table_name} AS 
            SELECT entry_id, processing_status, claimed_by, claimed_at, 
                   retry_count, last_error, full_text, english_text, 
                   embedding, vader_pos, vader_neu, vader_neg, vader_compound,
                   llm_positive, llm_neutral, llm_negative, iptc_newscode,
                   dup_entry_id, description_auto_generated, image_prompt, preview_img
            FROM entries
            WHERE processing_status IS NOT NULL 
               OR claimed_by IS NOT NULL 
               OR full_text IS NOT NULL
            """
            
            self.db_session.execute(text(backup_sql))
            self.db_session.commit()
            
            self.logger.info(f"Created backup table: {backup_table_name}")
            return True
        
        except Exception as e:
            self.logger.error(f"Failed to create backup: {e}")
            return False
    
    def _migrate_processing_status(self, config: MigrationConfig) -> Dict[str, int]:
        """Migrate processing status from legacy fields to new format."""
        stats = {'migrated_count': 0, 'failed_count': 0, 'skipped_count': 0}
        
        try:
            # Get entries that need migration in batches
            offset = 0
            
            while True:
                entries = (
                    self.db_session.query(Entry)
                    .filter(
                        or_(
                            Entry.processing_status.is_(None),
                            Entry.processing_status == ''
                        )
                    )
                    .offset(offset)
                    .limit(config.batch_size)
                    .all()
                )
                
                if not entries:
                    break
                
                for entry in entries:
                    try:
                        # Generate processing status from legacy fields
                        processing_status = self._generate_processing_status_from_legacy(entry)
                        
                        if processing_status:
                            entry.processing_status = json.dumps(processing_status)
                            stats['migrated_count'] += 1
                        else:
                            stats['skipped_count'] += 1
                    
                    except Exception as e:
                        self.logger.error(f"Failed to migrate entry {entry.entry_id}: {e}")
                        stats['failed_count'] += 1
                
                # Commit batch
                self.db_session.commit()
                offset += config.batch_size
                
                self.logger.info(f"Migrated batch: {stats['migrated_count']} total entries")
        
        except Exception as e:
            self.logger.error(f"Error during processing status migration: {e}")
            self.db_session.rollback()
        
        return stats
    
    def _generate_processing_status_from_legacy(self, entry: Entry) -> Optional[Dict[str, Any]]:
        """Generate processing status JSON from legacy database fields."""
        processing_status = {}
        
        # Map legacy fields to processing stages
        stage_mappings = [
            (ProcessingStage.DOWNLOAD_FEEDS, lambda e: e.title is not None),
            (ProcessingStage.DOWNLOAD_FULL_TEXT, lambda e: e.full_text is not None and e.full_text.strip()),
            (ProcessingStage.TRANSLATE_TEXT, lambda e: e.english_text is not None and e.english_text.strip()),
            (ProcessingStage.COMPUTE_EMBEDDINGS, lambda e: e.embedding is not None),
            (ProcessingStage.SENTIMENT_ANALYSIS, lambda e: e.vader_pos is not None),
            (ProcessingStage.LLM_ANALYSIS, lambda e: e.llm_positive is not None),
            (ProcessingStage.IPTC_CLASSIFICATION, lambda e: e.iptc_newscode is not None and e.iptc_newscode.strip()),
            (ProcessingStage.DUPLICATE_CHECK, lambda e: e.dup_entry_id is not None),
            (ProcessingStage.GENERATE_DESCRIPTIONS, lambda e: e.description_auto_generated is True),
            (ProcessingStage.GENERATE_IMAGE_PROMPTS, lambda e: e.image_prompt is not None and e.image_prompt.strip()),
            (ProcessingStage.GENERATE_PREVIEW_IMAGES, lambda e: e.preview_img is not None),
        ]
        
        has_any_processing = False
        
        for stage, condition in stage_mappings:
            if condition(entry):
                processing_status[stage.value] = {
                    'status': 'completed',
                    'completed_at': entry.published.isoformat() if entry.published else None,
                    'worker_id': 'legacy_migration'
                }
                has_any_processing = True
            else:
                # Only add pending status if entry has some processing
                # (to avoid creating status for completely unprocessed entries)
                if has_any_processing or self._entry_needs_processing(entry, stage):
                    processing_status[stage.value] = {
                        'status': 'pending'
                    }
        
        return processing_status if processing_status else None
    
    def _entry_needs_processing(self, entry: Entry, stage: ProcessingStage) -> bool:
        """Determine if an entry needs processing for a specific stage."""
        # Basic logic - entry needs processing if it has basic content
        if stage == ProcessingStage.DOWNLOAD_FEEDS:
            return entry.title is not None
        elif stage == ProcessingStage.DOWNLOAD_FULL_TEXT:
            return entry.link is not None
        else:
            # Other stages depend on previous stages being completed
            return entry.full_text is not None and entry.full_text.strip()
    
    def _validate_migrated_data(self, config: MigrationConfig) -> Dict[str, Any]:
        """Validate migrated data integrity."""
        validation_stats = {
            'validated_count': 0,
            'errors': [],
            'critical_errors': 0,
            'warnings': 0
        }
        
        try:
            # Sample entries for validation
            sample_size = min(config.validation_sample_size, 1000)
            
            sample_entries = (
                self.db_session.query(Entry.entry_id)
                .filter(Entry.processing_status.isnot(None))
                .limit(sample_size)
                .all()
            )
            
            entry_ids = [row[0] for row in sample_entries]
            
            # Validate processing status consistency
            consistency_result = self.validator.validate_processing_status_consistency(entry_ids)
            validation_stats['validated_count'] = consistency_result['total_entries']
            validation_stats['errors'].extend(consistency_result['errors'])
            validation_stats['critical_errors'] += len(consistency_result['errors'])
            
            # Validate work queue integrity
            queue_result = self.validator.validate_work_queue_integrity()
            validation_stats['errors'].extend(queue_result['errors'])
            
            if queue_result['stale_claims'] > 0:
                validation_stats['warnings'] += 1
                validation_stats['errors'].append(f"Found {queue_result['stale_claims']} stale claims")
            
            if queue_result['orphaned_claims'] > 0:
                validation_stats['warnings'] += 1
                validation_stats['errors'].append(f"Found {queue_result['orphaned_claims']} orphaned claims")
        
        except Exception as e:
            validation_stats['errors'].append(f"Validation error: {e}")
            validation_stats['critical_errors'] += 1
            self.logger.error(f"Error during data validation: {e}")
        
        return validation_stats
    
    def _update_system_configuration(self, target_mode: MigrationMode) -> None:
        """Update system configuration for the target mode."""
        # This would update configuration files or environment variables
        # to indicate the current processing mode
        self.logger.info(f"System configuration updated to {target_mode.value} mode")
        
        # In a real implementation, this might:
        # - Update configuration files
        # - Set environment variables
        # - Update database configuration tables
        # - Send notifications to monitoring systems
    
    def _rollback_migration(self) -> bool:
        """Rollback migration changes."""
        try:
            self.logger.info("Starting migration rollback")
            
            # Clear processing status for entries that were migrated
            rollback_sql = """
            UPDATE entries 
            SET processing_status = NULL,
                claimed_by = NULL,
                claimed_at = NULL,
                retry_count = 0,
                last_error = NULL
            WHERE processing_status IS NOT NULL
            """
            
            self.db_session.execute(text(rollback_sql))
            self.db_session.commit()
            
            self.logger.info("Migration rollback completed successfully")
            return True
        
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            self.db_session.rollback()
            return False
    
    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status."""
        try:
            # Count entries with processing status
            entries_with_status = (
                self.db_session.query(Entry)
                .filter(Entry.processing_status.isnot(None))
                .count()
            )
            
            # Count total entries
            total_entries = self.db_session.query(Entry).count()
            
            # Count active workers
            active_workers = (
                self.db_session.query(Worker)
                .filter(Worker.status == 'running')
                .count()
            )
            
            # Count claimed entries
            claimed_entries = (
                self.db_session.query(Entry)
                .filter(Entry.claimed_by.isnot(None))
                .count()
            )
            
            # Determine current mode
            if entries_with_status == 0:
                current_mode = MigrationMode.MONOLITHIC
            elif entries_with_status == total_entries:
                current_mode = MigrationMode.DISTRIBUTED
            else:
                current_mode = MigrationMode.HYBRID
            
            return {
                'current_mode': current_mode.value,
                'total_entries': total_entries,
                'entries_with_processing_status': entries_with_status,
                'migration_percentage': (entries_with_status / total_entries * 100) if total_entries > 0 else 0,
                'active_workers': active_workers,
                'claimed_entries': claimed_entries,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        
        except Exception as e:
            self.logger.error(f"Error getting migration status: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }


@contextmanager
def migration_session(db_session_factory):
    """Context manager for migration operations with proper session handling."""
    session = db_session_factory()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()