"""
Tests for MetricsCollector and monitoring functionality.
"""

import pytest
import time
import threading
from unittest.mock import <PERSON><PERSON>, MagicMock, patch
from datetime import datetime, timezone, timedelta

from backend.app.distributed.metrics_collector import (
    MetricsCollector, MetricPoint, Alert, WorkerMetrics, SystemMetrics,
    MetricType, AlertSeverity
)
from backend.app.distributed.processing_stage import ProcessingStage


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    session = Mock()
    session.begin.return_value.__enter__ = Mock(return_value=session)
    session.begin.return_value.__exit__ = Mock(return_value=None)
    return session


@pytest.fixture
def db_session_factory(mock_db_session):
    """Mock database session factory."""
    return lambda: mock_db_session


@pytest.fixture
def metrics_collector(db_session_factory):
    """Create a metrics collector for testing."""
    collector = MetricsCollector(db_session_factory)
    # Reduce collection interval for faster testing
    collector._collection_interval = 0.1
    return collector


class TestMetricPoint:
    """Test cases for MetricPoint dataclass."""
    
    def test_metric_point_creation(self):
        """Test creating a metric point."""
        timestamp = datetime.now(timezone.utc)
        labels = {'worker_id': 'test-worker', 'stage': 'download'}
        
        point = MetricPoint(
            name='worker_entries_processed',
            value=100.0,
            timestamp=timestamp,
            labels=labels,
            metric_type=MetricType.COUNTER
        )
        
        assert point.name == 'worker_entries_processed'
        assert point.value == 100.0
        assert point.timestamp == timestamp
        assert point.labels == labels
        assert point.metric_type == MetricType.COUNTER
    
    def test_metric_point_to_dict(self):
        """Test converting metric point to dictionary."""
        timestamp = datetime.now(timezone.utc)
        point = MetricPoint(
            name='test_metric',
            value=42.5,
            timestamp=timestamp,
            labels={'key': 'value'}
        )
        
        result = point.to_dict()
        
        assert result['name'] == 'test_metric'
        assert result['value'] == 42.5
        assert result['timestamp'] == timestamp.isoformat()
        assert result['labels'] == {'key': 'value'}
        assert result['type'] == MetricType.GAUGE.value


class TestAlert:
    """Test cases for Alert dataclass."""
    
    def test_alert_creation(self):
        """Test creating an alert."""
        timestamp = datetime.now(timezone.utc)
        
        alert = Alert(
            id='test-alert-1',
            name='Test Alert',
            severity=AlertSeverity.WARNING,
            message='This is a test alert',
            timestamp=timestamp,
            labels={'component': 'worker'}
        )
        
        assert alert.id == 'test-alert-1'
        assert alert.name == 'Test Alert'
        assert alert.severity == AlertSeverity.WARNING
        assert alert.message == 'This is a test alert'
        assert alert.timestamp == timestamp
        assert alert.labels == {'component': 'worker'}
        assert not alert.resolved
        assert alert.resolved_at is None
    
    def test_alert_to_dict(self):
        """Test converting alert to dictionary."""
        timestamp = datetime.now(timezone.utc)
        alert = Alert(
            id='test-alert',
            name='Test',
            severity=AlertSeverity.ERROR,
            message='Test message',
            timestamp=timestamp
        )
        
        result = alert.to_dict()
        
        assert result['id'] == 'test-alert'
        assert result['name'] == 'Test'
        assert result['severity'] == 'error'
        assert result['message'] == 'Test message'
        assert result['timestamp'] == timestamp.isoformat()
        assert result['resolved'] is False
        assert result['resolved_at'] is None


class TestWorkerMetrics:
    """Test cases for WorkerMetrics dataclass."""
    
    def test_worker_metrics_creation(self):
        """Test creating worker metrics."""
        metrics = WorkerMetrics(
            worker_id='test-worker',
            worker_type='TestWorker',
            state='running',
            healthy=True,
            batches_processed=10,
            entries_processed=100,
            entries_failed=5,
            total_processing_time=60.0
        )
        
        assert metrics.worker_id == 'test-worker'
        assert metrics.worker_type == 'TestWorker'
        assert metrics.state == 'running'
        assert metrics.healthy is True
        assert metrics.batches_processed == 10
        assert metrics.entries_processed == 100
        assert metrics.entries_failed == 5
        assert metrics.total_processing_time == 60.0
    
    def test_worker_metrics_to_dict(self):
        """Test converting worker metrics to dictionary."""
        metrics = WorkerMetrics(
            worker_id='test-worker',
            worker_type='TestWorker',
            state='running',
            healthy=True
        )
        
        result = metrics.to_dict()
        
        assert isinstance(result, dict)
        assert result['worker_id'] == 'test-worker'
        assert result['worker_type'] == 'TestWorker'
        assert result['state'] == 'running'
        assert result['healthy'] is True


class TestSystemMetrics:
    """Test cases for SystemMetrics dataclass."""
    
    def test_system_metrics_creation(self):
        """Test creating system metrics."""
        timestamp = datetime.now(timezone.utc)
        
        metrics = SystemMetrics(
            timestamp=timestamp,
            total_workers=5,
            running_workers=4,
            healthy_workers=3,
            total_entries_processed=1000,
            overall_success_rate=95.0
        )
        
        assert metrics.timestamp == timestamp
        assert metrics.total_workers == 5
        assert metrics.running_workers == 4
        assert metrics.healthy_workers == 3
        assert metrics.total_entries_processed == 1000
        assert metrics.overall_success_rate == 95.0
    
    def test_system_metrics_to_dict(self):
        """Test converting system metrics to dictionary."""
        timestamp = datetime.now(timezone.utc)
        metrics = SystemMetrics(timestamp=timestamp, total_workers=3)
        
        result = metrics.to_dict()
        
        assert isinstance(result, dict)
        assert result['timestamp'] == timestamp.isoformat()
        assert result['total_workers'] == 3


class TestMetricsCollector:
    """Test cases for MetricsCollector."""
    
    def test_initialization(self, db_session_factory):
        """Test metrics collector initialization."""
        collector = MetricsCollector(db_session_factory)
        
        assert collector.db_session_factory == db_session_factory
        assert not collector.is_running
        assert len(collector._worker_metrics) == 0
        assert len(collector._system_metrics_history) == 0
        assert len(collector._metric_points) == 0
        assert len(collector._alerts) == 0
        assert len(collector._alert_rules) > 0  # Default rules should be added
    
    def test_collect_worker_metrics(self, metrics_collector):
        """Test collecting metrics from worker health data."""
        health_data = {
            'worker_id': 'test-worker',
            'worker_type': 'TestWorker',
            'state': 'running',
            'healthy': True,
            'stats': {
                'batches_processed': 10,
                'entries_processed': 100,
                'entries_failed': 5,
                'total_processing_time': 60.0
            },
            'current_batch_size': 5,
            'last_heartbeat': datetime.now(timezone.utc).isoformat(),
            'heartbeat_age_seconds': 30.0
        }
        
        metrics_collector.collect_worker_metrics('test-worker', health_data)
        
        # Verify worker metrics were stored
        worker_metrics = metrics_collector.get_worker_metrics('test-worker')
        assert 'test-worker' in worker_metrics
        
        worker = worker_metrics['test-worker']
        assert worker.worker_id == 'test-worker'
        assert worker.worker_type == 'TestWorker'
        assert worker.state == 'running'
        assert worker.healthy is True
        assert worker.batches_processed == 10
        assert worker.entries_processed == 100
        assert worker.entries_failed == 5
        assert abs(worker.success_rate - 95.24) < 0.1  # 100/(100+5) * 100
        
        # Verify metric points were created
        metric_points = metrics_collector.get_metric_points()
        assert len(metric_points) > 0
        
        # Check for specific metrics
        metric_names = [p.name for p in metric_points]
        assert 'worker_batches_processed_total' in metric_names
        assert 'worker_entries_processed_total' in metric_names
        assert 'worker_healthy' in metric_names
    
    @patch('backend.app.distributed.metrics_collector.WorkerHealthManager')
    @patch('backend.app.distributed.metrics_collector.WorkQueueManager')
    def test_collect_system_metrics(self, mock_work_queue_class, mock_health_manager_class, metrics_collector):
        """Test collecting system-wide metrics."""
        # Add some worker metrics first
        metrics_collector._worker_metrics = {
            'worker-1': WorkerMetrics(
                worker_id='worker-1',
                worker_type='TestWorker',
                state='running',
                healthy=True,
                batches_processed=10,
                entries_processed=100,
                entries_failed=5,
                total_processing_time=60.0
            ),
            'worker-2': WorkerMetrics(
                worker_id='worker-2',
                worker_type='TestWorker',
                state='error',
                healthy=False,
                batches_processed=5,
                entries_processed=40,
                entries_failed=10,
                total_processing_time=30.0
            )
        }
        
        # Mock queue metrics
        mock_work_queue = Mock()
        mock_work_queue_class.return_value = mock_work_queue
        mock_work_queue.get_comprehensive_metrics.return_value = {
            'download_full_text': {'pending': 50, 'in_progress': 10, 'completed': 100}
        }
        
        # Collect system metrics
        system_metrics = metrics_collector.collect_system_metrics()
        
        assert system_metrics.total_workers == 2
        assert system_metrics.running_workers == 1
        assert system_metrics.healthy_workers == 1
        assert system_metrics.error_workers == 1
        assert system_metrics.total_entries_processed == 140
        assert system_metrics.total_entries_failed == 15
        assert system_metrics.total_batches_processed == 15
        assert abs(system_metrics.overall_success_rate - 90.32) < 0.1  # 140/(140+15) * 100
        
        # Verify it was stored in history
        history = metrics_collector.get_system_metrics_history()
        assert len(history) == 1
        assert history[0] == system_metrics
    
    def test_get_worker_metrics_filtering(self, metrics_collector):
        """Test getting worker metrics with filtering."""
        # Add test worker metrics
        metrics_collector._worker_metrics = {
            'worker-1': WorkerMetrics(worker_id='worker-1', worker_type='Type1', state='running', healthy=True),
            'worker-2': WorkerMetrics(worker_id='worker-2', worker_type='Type2', state='stopped', healthy=False)
        }
        
        # Get all metrics
        all_metrics = metrics_collector.get_worker_metrics()
        assert len(all_metrics) == 2
        assert 'worker-1' in all_metrics
        assert 'worker-2' in all_metrics
        
        # Get specific worker metrics
        specific_metrics = metrics_collector.get_worker_metrics('worker-1')
        assert len(specific_metrics) == 1
        assert 'worker-1' in specific_metrics
        assert 'worker-2' not in specific_metrics
        
        # Get non-existent worker
        empty_metrics = metrics_collector.get_worker_metrics('nonexistent')
        assert len(empty_metrics) == 0
    
    def test_get_metric_points_filtering(self, metrics_collector):
        """Test getting metric points with filtering."""
        timestamp1 = datetime.now(timezone.utc)
        timestamp2 = timestamp1 + timedelta(minutes=1)
        timestamp3 = timestamp1 + timedelta(minutes=2)
        
        # Add test metric points
        points = [
            MetricPoint('metric1', 10.0, timestamp1, {'worker_id': 'worker-1'}),
            MetricPoint('metric1', 20.0, timestamp2, {'worker_id': 'worker-2'}),
            MetricPoint('metric2', 30.0, timestamp3, {'worker_id': 'worker-1'}),
        ]
        metrics_collector._metric_points.extend(points)
        
        # Get all points
        all_points = metrics_collector.get_metric_points()
        assert len(all_points) == 3
        
        # Filter by metric name
        metric1_points = metrics_collector.get_metric_points(metric_name='metric1')
        assert len(metric1_points) == 2
        assert all(p.name == 'metric1' for p in metric1_points)
        
        # Filter by labels
        worker1_points = metrics_collector.get_metric_points(labels={'worker_id': 'worker-1'})
        assert len(worker1_points) == 2
        assert all(p.labels.get('worker_id') == 'worker-1' for p in worker1_points)
        
        # Filter by timestamp
        since_points = metrics_collector.get_metric_points(since=timestamp2)
        assert len(since_points) == 2
        assert all(p.timestamp >= timestamp2 for p in since_points)
        
        # Filter with limit
        limited_points = metrics_collector.get_metric_points(limit=2)
        assert len(limited_points) == 2
    
    def test_alerts_management(self, metrics_collector):
        """Test alert creation and management."""
        # Create test alerts
        alert1 = Alert(
            id='alert-1',
            name='Test Alert 1',
            severity=AlertSeverity.WARNING,
            message='Test message 1',
            timestamp=datetime.now(timezone.utc)
        )
        alert2 = Alert(
            id='alert-2',
            name='Test Alert 2',
            severity=AlertSeverity.ERROR,
            message='Test message 2',
            timestamp=datetime.now(timezone.utc)
        )
        
        # Add alerts
        metrics_collector._alerts['alert-1'] = alert1
        metrics_collector._alerts['alert-2'] = alert2
        
        # Get all alerts
        all_alerts = metrics_collector.get_alerts()
        assert len(all_alerts) == 2
        
        # Filter by severity
        error_alerts = metrics_collector.get_alerts(severity=AlertSeverity.ERROR)
        assert len(error_alerts) == 1
        assert error_alerts[0].severity == AlertSeverity.ERROR
        
        # Filter by resolved status
        unresolved_alerts = metrics_collector.get_alerts(resolved=False)
        assert len(unresolved_alerts) == 2
        
        # Resolve an alert
        success = metrics_collector.resolve_alert('alert-1')
        assert success is True
        assert metrics_collector._alerts['alert-1'].resolved is True
        assert metrics_collector._alerts['alert-1'].resolved_at is not None
        
        # Try to resolve non-existent alert
        success = metrics_collector.resolve_alert('nonexistent')
        assert success is False
        
        # Check resolved filtering
        resolved_alerts = metrics_collector.get_alerts(resolved=True)
        assert len(resolved_alerts) == 1
        assert resolved_alerts[0].id == 'alert-1'
    
    def test_add_custom_alert_rule(self, metrics_collector):
        """Test adding custom alert rules."""
        initial_rules_count = len(metrics_collector._alert_rules)
        
        def custom_rule(system_metrics, worker_metrics):
            return [Alert(
                id='custom-alert',
                name='Custom Alert',
                severity=AlertSeverity.INFO,
                message='Custom alert message',
                timestamp=datetime.now(timezone.utc)
            )]
        
        metrics_collector.add_alert_rule(custom_rule)
        
        assert len(metrics_collector._alert_rules) == initial_rules_count + 1
    
    def test_get_dashboard_data(self, metrics_collector):
        """Test getting dashboard data."""
        # Add some test data
        metrics_collector._worker_metrics = {
            'worker-1': WorkerMetrics(worker_id='worker-1', worker_type='Test', state='running', healthy=True)
        }
        
        system_metrics = SystemMetrics(
            timestamp=datetime.now(timezone.utc),
            total_workers=1,
            running_workers=1,
            healthy_workers=1
        )
        metrics_collector._system_metrics_history.append(system_metrics)
        
        alert = Alert(
            id='test-alert',
            name='Test Alert',
            severity=AlertSeverity.WARNING,
            message='Test message',
            timestamp=datetime.now(timezone.utc)
        )
        metrics_collector._alerts['test-alert'] = alert
        
        # Get dashboard data
        dashboard_data = metrics_collector.get_dashboard_data()
        
        assert 'timestamp' in dashboard_data
        assert 'system_metrics' in dashboard_data
        assert 'system_metrics_history' in dashboard_data
        assert 'worker_metrics' in dashboard_data
        assert 'active_alerts' in dashboard_data
        assert 'alert_summary' in dashboard_data
        
        # Check alert summary
        alert_summary = dashboard_data['alert_summary']
        assert alert_summary['total_alerts'] == 1
        assert alert_summary['active_alerts'] == 1
        assert alert_summary['warning_alerts'] == 1
    
    @patch('backend.app.distributed.metrics_collector.WorkerHealthManager')
    @patch('backend.app.distributed.metrics_collector.WorkQueueManager')
    def test_lifecycle_management(self, mock_work_queue_class, mock_health_manager_class, metrics_collector):
        """Test metrics collector lifecycle."""
        # Mock dependencies
        mock_health_manager = Mock()
        mock_health_manager_class.return_value = mock_health_manager
        mock_health_manager.get_all_workers_health.return_value = []
        
        mock_work_queue = Mock()
        mock_work_queue_class.return_value = mock_work_queue
        mock_work_queue.get_comprehensive_metrics.return_value = {}
        
        # Start collector
        assert not metrics_collector.is_running
        metrics_collector.start()
        assert metrics_collector.is_running
        
        # Let it run briefly
        time.sleep(0.2)
        
        # Stop collector
        metrics_collector.stop(timeout=5)
        assert not metrics_collector.is_running
    
    def test_default_alert_rules(self, metrics_collector):
        """Test that default alert rules are working."""
        # Create test data that should trigger alerts
        system_metrics = SystemMetrics(
            timestamp=datetime.now(timezone.utc),
            total_workers=2,
            running_workers=1,
            healthy_workers=0,
            stale_workers=1,
            overall_success_rate=70.0,  # Below 90%
            total_entries_processed=200
        )
        
        worker_metrics = {
            'worker-1': WorkerMetrics(
                worker_id='worker-1',
                worker_type='Test',
                state='error',
                healthy=False,
                success_rate=60.0,  # Below 80%
                entries_processed=100
            )
        }
        
        # Manually trigger alert rule checking
        metrics_collector._worker_metrics = worker_metrics
        metrics_collector._check_alert_rules(system_metrics)
        
        # Check that alerts were generated
        alerts = metrics_collector.get_alerts(resolved=False)
        assert len(alerts) > 0
        
        # Check for specific alert types
        alert_names = [a.name for a in alerts]
        assert any('Worker Unhealthy' in name for name in alert_names)
        assert any('High System Failure Rate' in name for name in alert_names)
        assert any('Stale Workers Detected' in name for name in alert_names)


class TestMetricsCollectorIntegration:
    """Integration tests for MetricsCollector."""
    
    @patch('backend.app.distributed.metrics_collector.WorkerHealthManager')
    @patch('backend.app.distributed.metrics_collector.WorkQueueManager')
    def test_full_collection_cycle(self, mock_work_queue_class, mock_health_manager_class, db_session_factory):
        """Test a complete metrics collection cycle."""
        # Setup mocks
        mock_health_manager = Mock()
        mock_health_manager_class.return_value = mock_health_manager
        mock_health_manager.get_all_workers_health.return_value = [
            {
                'worker_id': 'test-worker',
                'worker_type': 'TestWorker',
                'state': 'running',
                'healthy': True,
                'stats': {
                    'batches_processed': 5,
                    'entries_processed': 50,
                    'entries_failed': 2,
                    'total_processing_time': 30.0
                },
                'current_batch_size': 3,
                'last_heartbeat': datetime.now(timezone.utc).isoformat(),
                'heartbeat_age_seconds': 15.0
            }
        ]
        
        mock_work_queue = Mock()
        mock_work_queue_class.return_value = mock_work_queue
        mock_work_queue.get_comprehensive_metrics.return_value = {
            'download_full_text': {'pending': 25, 'in_progress': 5, 'completed': 70}
        }
        
        # Create collector with fast collection interval
        collector = MetricsCollector(db_session_factory)
        collector._collection_interval = 0.1
        
        try:
            # Start collection
            collector.start()
            
            # Wait for at least one collection cycle
            time.sleep(0.3)
            
            # Verify data was collected
            worker_metrics = collector.get_worker_metrics()
            assert len(worker_metrics) == 1
            assert 'test-worker' in worker_metrics
            
            system_metrics_history = collector.get_system_metrics_history()
            assert len(system_metrics_history) > 0
            
            metric_points = collector.get_metric_points()
            assert len(metric_points) > 0
            
            # Verify dashboard data is complete
            dashboard_data = collector.get_dashboard_data()
            assert dashboard_data['system_metrics'] is not None
            assert len(dashboard_data['worker_metrics']) == 1
            
        finally:
            collector.stop(timeout=5)
    
    def test_concurrent_operations(self, metrics_collector):
        """Test concurrent access to metrics collector."""
        def add_worker_metrics():
            for i in range(10):
                health_data = {
                    'worker_id': f'worker-{i}',
                    'worker_type': 'TestWorker',
                    'state': 'running',
                    'healthy': True,
                    'stats': {
                        'batches_processed': i,
                        'entries_processed': i * 10,
                        'entries_failed': i,
                        'total_processing_time': i * 5.0
                    },
                    'current_batch_size': i % 5
                }
                metrics_collector.collect_worker_metrics(f'worker-{i}', health_data)
                time.sleep(0.01)
        
        def read_metrics():
            for _ in range(20):
                metrics_collector.get_worker_metrics()
                metrics_collector.get_metric_points(limit=10)
                time.sleep(0.01)
        
        # Run operations concurrently
        threads = [
            threading.Thread(target=add_worker_metrics),
            threading.Thread(target=add_worker_metrics),
            threading.Thread(target=read_metrics),
            threading.Thread(target=read_metrics)
        ]
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join(timeout=5)
        
        # Verify final state is consistent
        worker_metrics = metrics_collector.get_worker_metrics()
        assert len(worker_metrics) == 10
        
        metric_points = metrics_collector.get_metric_points()
        assert len(metric_points) > 0