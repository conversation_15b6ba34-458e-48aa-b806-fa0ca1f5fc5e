# Deployment-Anleitung für Positive News App

Diese Anleitung beschreibt die Schritte zur Vorbereitung und Veröffentlichung der Positive News App für iOS, Android und Web.

## Voraussetzungen

- Flutter SDK (Version 3.19.3 oder höher)
- Xcode (für iOS-Deployment)
- Android Studio (für Android-Deployment)
- Firebase-Konto (für Web-Deployment)
- Apple Developer Account (für iOS App Store)
- Google Play Developer Account (für Google Play Store)

## 1. App-Konfiguration

### 1.1 App-Identifikation

- iOS Bundle ID: `de.positivenews.positive_news_app`
- Android Package Name: `de.positivenews.positive_news_app`
- Web Domain: `positivenews.de`

### 1.2 App-Version

- Version: `1.0.0`
- Build-Nummer: `1`

## 2. Backend-Deployment

### 2.1 FastAPI-Backend

1. Erstellen Sie eine `.env`-Datei im Backend-Verzeichnis mit den Oracle Cloud Autonomous Database-Anmeldedaten:
   ```
   DB_USER=your_oracle_username
   DB_PASSWORD=your_oracle_password
   DB_HOST=your_oracle_host
   DB_PORT=1521
   DB_SERVICE=your_oracle_service_name
   ```

2. Deployment auf einem Server (z.B. mit Docker):
   ```bash
   cd /home/<USER>/positive_news_app/backend
   docker build -t positive-news-api .
   docker run -d -p 8000:8000 --env-file .env --name positive-news-api positive-news-api
   ```

3. Alternativ: Deployment auf Cloud-Plattformen wie Oracle Cloud, AWS, Google Cloud oder Azure.

## 3. Frontend-Deployment

### 3.1 Konfiguration der Produktions-API-URL

Aktualisieren Sie die API-URL in der Datei `api_service.dart`:

```dart
final String _productionBaseUrl = 'https://api.positivenews.de/api';
```

### 3.2 iOS-Deployment

1. Erstellen Sie ein iOS-Build:
   ```bash
   cd /home/<USER>/positive_news_app/frontend/positive_news_app
   flutter build ios --release
   ```

2. Öffnen Sie das Xcode-Projekt:
   ```bash
   open ios/Runner.xcworkspace
   ```

3. Konfigurieren Sie die Signing-Einstellungen in Xcode mit Ihrem Apple Developer Account.

4. Erstellen Sie ein Archiv und laden Sie es in den App Store Connect hoch.

### 3.3 Android-Deployment

1. Erstellen Sie einen Signing-Key:
   ```bash
   keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
   ```

2. Konfigurieren Sie die Signing-Einstellungen in `android/app/build.gradle`.

3. Erstellen Sie ein Android-Build:
   ```bash
   cd /home/<USER>/positive_news_app/frontend/positive_news_app
   flutter build appbundle --release
   ```

4. Laden Sie die App Bundle-Datei (`build/app/outputs/bundle/release/app-release.aab`) in die Google Play Console hoch.

### 3.4 Web-Deployment

1. Erstellen Sie ein Web-Build:
   ```bash
   cd /home/<USER>/positive_news_app/frontend/positive_news_app
   flutter build web --release
   ```

2. Deployment auf Firebase Hosting:
   ```bash
   firebase init hosting
   firebase deploy --only hosting
   ```

3. Alternativ: Deployment auf anderen Hosting-Plattformen wie Netlify, Vercel oder GitHub Pages.

## 4. App Store-Einreichung

### 4.1 iOS App Store

1. Erstellen Sie einen App-Eintrag in App Store Connect.
2. Füllen Sie alle erforderlichen Metadaten aus:
   - App-Name: Positive News
   - Beschreibung: Eine plattformübergreifende Nachrichten-App, die ausschließlich positive Nachrichten anzeigt.
   - Keywords: Nachrichten, positiv, gute Nachrichten, Optimismus
   - Screenshots für verschiedene Geräte
   - App-Icon
   - Datenschutzrichtlinie-URL
3. Laden Sie das iOS-Build hoch und reichen Sie es zur Überprüfung ein.

### 4.2 Google Play Store

1. Erstellen Sie einen App-Eintrag in der Google Play Console.
2. Füllen Sie alle erforderlichen Metadaten aus:
   - App-Name: Positive News
   - Kurze Beschreibung: Nur positive Nachrichten aus aller Welt.
   - Vollständige Beschreibung: Eine plattformübergreifende Nachrichten-App, die ausschließlich positive Nachrichten anzeigt.
   - Screenshots für verschiedene Geräte
   - App-Icon
   - Datenschutzrichtlinie-URL
3. Laden Sie das Android App Bundle hoch und reichen Sie es zur Überprüfung ein.

### 4.3 Progressive Web App (PWA)

1. Stellen Sie sicher, dass die Web-Version als PWA konfiguriert ist:
   - Manifest-Datei (`web/manifest.json`)
   - Service Worker für Offline-Funktionalität
   - Icons in verschiedenen Größen
2. Testen Sie die PWA mit Lighthouse in Chrome DevTools.
3. Veröffentlichen Sie die PWA auf Ihrer Domain.

## 5. Post-Launch-Aktivitäten

1. Überwachen Sie Crash-Berichte und Fehler.
2. Sammeln Sie Nutzerfeedback.
3. Planen Sie regelmäßige Updates und Verbesserungen.
4. Implementieren Sie Analytics, um die Nutzung der App zu verfolgen.

## 6. Nützliche Befehle

```bash
# Flutter-Version überprüfen
flutter --version

# Flutter-Abhängigkeiten aktualisieren
flutter pub get

# Flutter-Projekt analysieren
flutter analyze

# Flutter-Tests ausführen
flutter test

# Flutter-Build für alle Plattformen
flutter build all
```
