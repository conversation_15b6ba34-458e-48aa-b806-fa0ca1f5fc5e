"""
Worker factory for creating specialized distributed workers.

This module provides a factory pattern for instantiating the appropriate
specialized worker based on processing stages.
"""

import logging
from typing import Optional, Callable
from sqlalchemy.orm import Session

from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.distributed_worker import DistributedWorker

# Import specialized workers
from backend.app.distributed.workers.feed_download_worker import FeedDownloadWorker
from backend.app.distributed.workers.full_text_download_worker import FullTextDownloadWorker
# from backend.app.distributed.workers.sentiment_analysis_worker import SentimentAnalysisWorker
from backend.app.distributed.workers.llm_analysis_worker import LLMAnalysisWorker
from backend.app.distributed.workers.embedding_worker import EmbeddingWorker
from backend.app.distributed.workers.iptc_classification_worker import IPTCClassificationWorker
from backend.app.distributed.workers.duplicate_check_worker import DuplicateCheckWorker
from backend.app.distributed.workers.description_generation_worker import DescriptionGenerationWorker
from backend.app.distributed.workers.image_prompt_worker import ImagePromptWorker
from backend.app.distributed.workers.preview_image_worker import PreviewImageWorker
# from backend.app.distributed.workers.rss_feed_generation_worker import RSSFeedGenerationWorker

logger = logging.getLogger(__name__)


class WorkerFactory:
    """Factory for creating specialized distributed workers."""
    
    # Map processing stages to their corresponding worker classes
    WORKER_CLASSES = {
        ProcessingStage.DOWNLOAD_FEEDS: FeedDownloadWorker,
        ProcessingStage.DOWNLOAD_FULL_TEXT: FullTextDownloadWorker,
        # ProcessingStage.SENTIMENT_ANALYSIS: SentimentAnalysisWorker,  # Not implemented yet
        ProcessingStage.LLM_ANALYSIS: LLMAnalysisWorker,
        ProcessingStage.COMPUTE_EMBEDDINGS: EmbeddingWorker,
        ProcessingStage.IPTC_CLASSIFICATION: IPTCClassificationWorker,
        ProcessingStage.DUPLICATE_CHECK: DuplicateCheckWorker,
        ProcessingStage.GENERATE_DESCRIPTIONS: DescriptionGenerationWorker,
        ProcessingStage.GENERATE_IMAGE_PROMPTS: ImagePromptWorker,
        ProcessingStage.GENERATE_PREVIEW_IMAGES: PreviewImageWorker,
    }
    
    @classmethod
    def create_worker(cls, 
                     config: WorkerConfig, 
                     db_session_factory: Optional[Callable[[], Session]] = None) -> DistributedWorker:
        """
        Create a specialized worker based on the configuration.
        
        Args:
            config: Worker configuration
            db_session_factory: Optional database session factory
            
        Returns:
            Specialized worker instance
            
        Raises:
            ValueError: If configuration is invalid or worker type not supported
        """
        if not config.stages:
            raise ValueError("Worker configuration must specify at least one processing stage")
        
        # Use default session factory if none provided
        if db_session_factory is None:
            from backend.app.core.database import SessionLocal
            db_session_factory = SessionLocal
        
        # For single-stage workers, create the specialized worker directly
        if len(config.stages) == 1:
            stage = config.stages[0]
            worker_class = cls.WORKER_CLASSES.get(stage)
            
            if not worker_class:
                raise ValueError(f"No specialized worker available for stage: {stage.value}")
            
            logger.info(f"Creating {worker_class.__name__} for stage {stage.value}")
            return worker_class(config, db_session_factory)
        
        # For multi-stage workers, we need a coordinator
        else:
            return cls._create_multi_stage_worker(config, db_session_factory)
    
    @classmethod
    def _create_multi_stage_worker(cls, 
                                  config: WorkerConfig, 
                                  db_session_factory: Callable[[], Session]) -> DistributedWorker:
        """
        Create a worker that can handle multiple stages.
        
        For now, this creates a simple coordinator that processes stages sequentially.
        In the future, this could be enhanced with more sophisticated coordination.
        
        Args:
            config: Worker configuration with multiple stages
            db_session_factory: Database session factory
            
        Returns:
            Multi-stage worker instance
        """
        # Check if all stages have available workers
        unsupported_stages = [stage for stage in config.stages if stage not in cls.WORKER_CLASSES]
        if unsupported_stages:
            raise ValueError(f"No workers available for stages: {[s.value for s in unsupported_stages]}")
        
        # For now, create a simple multi-stage coordinator
        from backend.app.distributed.multi_stage_worker import MultiStageWorker
        logger.info(f"Creating MultiStageWorker for stages: {[s.value for s in config.stages]}")
        return MultiStageWorker(config, db_session_factory)
    
    @classmethod
    def get_supported_stages(cls) -> list[ProcessingStage]:
        """
        Get list of processing stages that have specialized workers available.
        
        Returns:
            List of supported processing stages
        """
        return list(cls.WORKER_CLASSES.keys())
    
    @classmethod
    def is_stage_supported(cls, stage: ProcessingStage) -> bool:
        """
        Check if a processing stage has a specialized worker available.
        
        Args:
            stage: Processing stage to check
            
        Returns:
            True if stage is supported, False otherwise
        """
        return stage in cls.WORKER_CLASSES