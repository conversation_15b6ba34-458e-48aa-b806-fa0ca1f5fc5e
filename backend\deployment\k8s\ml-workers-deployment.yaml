apiVersion: apps/v1
kind: Deployment
metadata:
  name: ml-workers
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: ml-worker
    app.kubernetes.io/part-of: distributed-etl
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: breaking-bright
      app.kubernetes.io/component: ml-worker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: breaking-bright
        app.kubernetes.io/component: ml-worker
        app.kubernetes.io/part-of: distributed-etl
    spec:
      containers:
      - name: ml-worker
        image: robertschulze/breaking-bright-worker:latest
        imagePullPolicy: Always
        command: ["python", "-m", "backend.app.distributed.distributed_worker"]
        ports:
        - containerPort: 8080
          name: health-check
          protocol: TCP
        env:
        - name: WORKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: WORKER_STAGES
          value: "translate_text,compute_embeddings"
        - name: WORKER_BATCH_SIZE
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: ML_WORKER_BATCH_SIZE
        - name: WORKER_CLAIM_TIMEOUT_MINUTES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: ML_WORKER_CLAIM_TIMEOUT_MINUTES
        - name: WORKER_MAX_RETRIES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: ML_WORKER_MAX_RETRIES
        - name: WORKER_HEALTH_CHECK_PORT
          value: "8080"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: openai-api-key
        - name: IONOS_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: ionos-api-key
        - name: HUGGINGFACE_TOKEN
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: huggingface-token
        envFrom:
        - configMapRef:
            name: distributed-worker-config
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 90
          periodSeconds: 30
          timeoutSeconds: 15
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        volumeMounts:
        - name: worker-logs
          mountPath: /var/log/worker
        - name: model-cache
          mountPath: /root/.cache
      volumes:
      - name: worker-logs
        emptyDir: {}
      - name: model-cache
        emptyDir:
          sizeLimit: 10Gi
      imagePullSecrets:
      - name: docker-registry-secret
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: ml-workers-service
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: ml-worker
spec:
  selector:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: ml-worker
  ports:
  - name: health-check
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP