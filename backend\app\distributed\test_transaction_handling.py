"""
Tests for transaction handling in singleton stage management.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.exc import InvalidRequestError

from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Worker


def test_claim_singleton_stage_with_existing_transaction():
    """Test that claim_singleton_stage works when a transaction is already active."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = True  # Transaction already active

    # Mock worker query - need to handle two separate queries
    mock_worker = Mock(spec=Worker)
    mock_worker.worker_id = "test-worker"
    mock_worker.singleton_stages = None

    # First query (checking for existing workers) returns None
    # Second query (getting current worker) returns the worker
    def mock_query_chain(*args):
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter

        # Track call count to differentiate between queries
        if not hasattr(mock_query_chain, 'call_count'):
            mock_query_chain.call_count = 0
        mock_query_chain.call_count += 1

        if mock_query_chain.call_count == 1:
            # First query: check for existing workers (should return None)
            mock_filter.first.return_value = None
        else:
            # Second query: get current worker
            mock_filter.first.return_value = mock_worker

        return mock_query

    mock_session.query.side_effect = mock_query_chain

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Test claiming singleton stage
    result = work_queue.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")

    # Verify success
    assert result is True

    # Verify that begin() was not called (since transaction already active)
    mock_session.begin.assert_not_called()

    # Verify worker's singleton_stages was updated
    assert mock_worker.singleton_stages == "download_feeds"


def test_claim_singleton_stage_without_existing_transaction():
    """Test that claim_singleton_stage works when no transaction is active."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = False  # No transaction active

    # Mock context manager for begin()
    mock_transaction = Mock()
    mock_session.begin.return_value.__enter__ = Mock(return_value=mock_transaction)
    mock_session.begin.return_value.__exit__ = Mock(return_value=None)

    # Mock worker query - need to handle two separate queries
    mock_worker = Mock(spec=Worker)
    mock_worker.worker_id = "test-worker"
    mock_worker.singleton_stages = None

    # First query (checking for existing workers) returns None
    # Second query (getting current worker) returns the worker
    def mock_query_chain(*args):
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter

        # Track call count to differentiate between queries
        if not hasattr(mock_query_chain, 'call_count'):
            mock_query_chain.call_count = 0
        mock_query_chain.call_count += 1

        if mock_query_chain.call_count == 1:
            # First query: check for existing workers (should return None)
            mock_filter.first.return_value = None
        else:
            # Second query: get current worker
            mock_filter.first.return_value = mock_worker

        return mock_query

    mock_session.query.side_effect = mock_query_chain

    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)

    # Test claiming singleton stage
    result = work_queue.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")

    # Verify success
    assert result is True

    # Verify that begin() was called (since no transaction was active)
    mock_session.begin.assert_called_once()

    # Verify worker's singleton_stages was updated
    assert mock_worker.singleton_stages == "download_feeds"


def test_release_singleton_stage_with_existing_transaction():
    """Test that release_singleton_stage works when a transaction is already active."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = True  # Transaction already active
    
    # Mock worker query
    mock_worker = Mock(spec=Worker)
    mock_worker.worker_id = "test-worker"
    mock_worker.singleton_stages = "download_feeds,other_stage"
    
    mock_session.query.return_value.filter.return_value.first.return_value = mock_worker
    
    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)
    
    # Test releasing singleton stage
    work_queue.release_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")
    
    # Verify that begin() was not called (since transaction already active)
    mock_session.begin.assert_not_called()
    
    # Verify worker's singleton_stages was updated (download_feeds removed)
    assert mock_worker.singleton_stages == "other_stage"


def test_release_singleton_stage_without_existing_transaction():
    """Test that release_singleton_stage works when no transaction is active."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = False  # No transaction active
    
    # Mock context manager for begin()
    mock_transaction = Mock()
    mock_session.begin.return_value.__enter__ = Mock(return_value=mock_transaction)
    mock_session.begin.return_value.__exit__ = Mock(return_value=None)
    
    # Mock worker query
    mock_worker = Mock(spec=Worker)
    mock_worker.worker_id = "test-worker"
    mock_worker.singleton_stages = "download_feeds"
    
    mock_session.query.return_value.filter.return_value.first.return_value = mock_worker
    
    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)
    
    # Test releasing singleton stage
    work_queue.release_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")
    
    # Verify that begin() was called (since no transaction was active)
    mock_session.begin.assert_called_once()
    
    # Verify worker's singleton_stages was updated (download_feeds removed)
    assert mock_worker.singleton_stages is None


def test_claim_singleton_stage_handles_transaction_error():
    """Test that claim_singleton_stage handles transaction errors gracefully."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = False
    
    # Mock begin() to raise InvalidRequestError
    mock_session.begin.side_effect = InvalidRequestError("A transaction is already begun on this Session.")
    
    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)
    
    # Test that the error is properly raised
    with pytest.raises(InvalidRequestError):
        work_queue.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")


def test_claim_singleton_stage_already_claimed_by_other_worker():
    """Test that claim_singleton_stage returns False when stage is already claimed."""
    # Create mock database session
    mock_session = Mock()
    mock_session.in_transaction.return_value = True
    
    # Mock existing worker with the stage already claimed
    mock_existing_worker = Mock(spec=Worker)
    mock_existing_worker.worker_id = "other-worker"
    mock_existing_worker.singleton_stages = "download_feeds"
    
    # Mock current worker
    mock_current_worker = Mock(spec=Worker)
    mock_current_worker.worker_id = "test-worker"
    mock_current_worker.singleton_stages = None
    
    # Setup query mocks
    def mock_query_side_effect(*args):
        mock_query = Mock()
        mock_filter = Mock()
        mock_query.filter.return_value = mock_filter
        
        # First call (checking for existing worker) returns existing worker
        # Second call (getting current worker) returns current worker
        if mock_query.filter.call_count == 1:
            mock_filter.first.return_value = mock_existing_worker
        else:
            mock_filter.first.return_value = mock_current_worker
        
        return mock_query
    
    mock_session.query.side_effect = mock_query_side_effect
    
    # Create work queue manager
    work_queue = WorkQueueManager(mock_session)
    
    # Test claiming singleton stage when already claimed
    result = work_queue.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker")
    
    # Verify that claim failed
    assert result is False
    
    # Verify that current worker's singleton_stages was not modified
    assert mock_current_worker.singleton_stages is None


if __name__ == "__main__":
    pytest.main([__file__])
