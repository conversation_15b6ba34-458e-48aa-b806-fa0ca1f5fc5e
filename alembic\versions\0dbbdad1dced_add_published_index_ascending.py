"""Add published index ascending

Revision ID: 0dbbdad1dced
Revises: 022b0510e688
Create Date: 2025-06-15 23:26:11.570133

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0dbbdad1dced'
down_revision: Union[str, None] = '022b0510e688'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_entries_published_asc', 'entries', ['published'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_entries_published_asc', table_name='entries')
    # ### end Alembic commands ###
