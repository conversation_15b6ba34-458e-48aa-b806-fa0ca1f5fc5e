"""
Example usage of the EmbeddingWorker for distributed ETL processing.

This script demonstrates how to configure and use the EmbeddingWorker
to compute embeddings for news articles in a distributed manner.
"""

import logging
from typing import Callable
from sqlalchemy.orm import Session

from backend.app.distributed.workers.embedding_worker import EmbeddingWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_embedding_worker(worker_id: str, db_session_factory: Callable[[], Session]) -> EmbeddingWorker:
    """
    Create and configure an EmbeddingWorker.
    
    Args:
        worker_id: Unique identifier for the worker
        db_session_factory: Factory function to create database sessions
        
    Returns:
        Configured EmbeddingWorker instance
    """
    # Create worker configuration
    config = WorkerConfig(
        worker_id=worker_id,
        stages=[ProcessingStage.COMPUTE_EMBEDDINGS],
        batch_size=25,  # Process 25 entries at a time
        heartbeat_interval_seconds=60,
        max_retries=3
    )
    
    # Configure embedding-specific settings
    config.stage_configs = {
        ProcessingStage.COMPUTE_EMBEDDINGS.value: {
            'model_name': 'BAAI/bge-m3',  # Multilingual embedding model
            'batch_optimize_model': True,  # Enable batch processing optimization
            'preload_model': False,  # Lazy load model to save memory
            'max_text_length': 8192  # Maximum text length to process
        }
    }
    
    # Create and return worker
    return EmbeddingWorker(config, db_session_factory)


def example_usage():
    """
    Example of how to use the EmbeddingWorker.
    
    Note: This is a demonstration - in practice you would have a real
    database session factory and would start the worker in a separate process.
    """
    logger.info("EmbeddingWorker Example Usage")
    
    # Mock database session factory (replace with real implementation)
    def mock_db_session_factory():
        # In practice, this would return a real SQLAlchemy session
        # connected to your database
        from unittest.mock import Mock
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.commit.return_value = None
        mock_session.close.return_value = None
        return mock_session
    
    # Create worker
    worker = create_embedding_worker("embedding-worker-001", mock_db_session_factory)
    
    # Display worker configuration
    logger.info(f"Created worker: {worker.worker_id}")
    logger.info(f"Model: {worker.model_name}")
    logger.info(f"Batch size: {worker.config.batch_size}")
    logger.info(f"Batch optimization: {worker.batch_optimize_model}")
    
    # Create sample entries (in practice these would come from the database)
    sample_entries = []
    for i in range(3):
        entry = Entry()
        entry.entry_id = f"sample-entry-{i}"
        entry.title = f"Sample News Article {i}"
        entry.full_text = f"This is the full text content of sample news article {i}. " * 20
        sample_entries.append(entry)
    
    logger.info(f"Processing {len(sample_entries)} sample entries...")
    
    # Process the batch (this would normally be done automatically by the worker)
    try:
        results = worker.process_batch(sample_entries, ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Display results
        success_count = sum(1 for success in results.values() if success)
        logger.info(f"Processing completed: {success_count}/{len(sample_entries)} successful")
        
        for entry_id, success in results.items():
            status = "SUCCESS" if success else "FAILED"
            logger.info(f"  {entry_id}: {status}")
    
    except Exception as e:
        logger.error(f"Error processing batch: {e}")
    
    # Get worker health status
    health = worker.get_worker_specific_health()
    logger.info("Worker Health Status:")
    logger.info(f"  Type: {health['worker_type']}")
    logger.info(f"  Model loaded: {health['model_loaded']}")
    logger.info(f"  Supported stages: {health['supported_stages']}")
    
    # Cleanup resources
    worker.cleanup_resources()
    logger.info("Worker resources cleaned up")


if __name__ == "__main__":
    example_usage()