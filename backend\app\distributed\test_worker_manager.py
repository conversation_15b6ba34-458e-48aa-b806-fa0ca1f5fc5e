"""
Tests for WorkerManager orchestration functionality.
"""

import pytest
import threading
import time
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timezone

from backend.app.distributed.worker_manager import (
    WorkerManager, WorkerManagerState, WorkerAssignment, ScalingDecision
)
from backend.app.distributed.distributed_worker import Distri<PERSON><PERSON>or<PERSON>, WorkerState
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage


class MockDistributedWorker(DistributedWorker):
    """Mock distributed worker for testing."""
    
    def __init__(self, config: WorkerConfig):
        # Initialize without calling parent __init__ to avoid database dependencies
        self.config = config
        self.worker_id = config.worker_id
        self._state = WorkerState.STOPPED
        self._current_batch = []
        self._processing_start_time = None
        self._last_heartbeat = None
        self._last_error = None
        self._stats = {
            'batches_processed': 0,
            'entries_processed': 0,
            'entries_failed': 0,
            'total_processing_time': 0.0,
            'last_batch_size': 0,
            'last_batch_duration': 0.0
        }
        
        # Mock methods
        self.start = Mock()
        self.stop = Mock()
    
    @property
    def state(self):
        return self._state
    
    @property
    def is_running(self):
        return self._state == WorkerState.RUNNING
    
    def get_health_status(self):
        return {
            'worker_id': self.worker_id,
            'worker_type': self.__class__.__name__,
            'state': self._state.value,
            'healthy': self._state == WorkerState.RUNNING,
            'last_heartbeat': self._last_heartbeat.isoformat() if self._last_heartbeat else None,
            'current_batch_size': len(self._current_batch),
            'processing_since': self._processing_start_time.isoformat() if self._processing_start_time else None,
            'last_error': self._last_error,
            'stats': self._stats.copy(),
            'config': {
                'stages': [s.value for s in self.config.stages],
                'batch_size': self.config.batch_size,
                'heartbeat_interval': self.config.heartbeat_interval_seconds
            }
        }
    
    def process_batch(self, entries, stage):
        # Mock implementation
        return {entry.entry_id: True for entry in entries}


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    session = Mock()
    session.begin.return_value.__enter__ = Mock(return_value=session)
    session.begin.return_value.__exit__ = Mock(return_value=None)
    return session


@pytest.fixture
def db_session_factory(mock_db_session):
    """Mock database session factory."""
    return lambda: mock_db_session


@pytest.fixture
def worker_config():
    """Create a test worker configuration."""
    return WorkerConfig(
        worker_id="test-worker-1",
        stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
        batch_size=10,
        heartbeat_interval_seconds=30
    )


@pytest.fixture
def mock_worker(worker_config):
    """Create a mock distributed worker."""
    return MockDistributedWorker(worker_config)


@pytest.fixture
def worker_manager(db_session_factory):
    """Create a worker manager for testing."""
    manager = WorkerManager(db_session_factory)
    # Disable auto-scaling for most tests
    manager.auto_scaling_enabled = False
    # Reduce intervals for faster testing
    manager.management_interval_seconds = 0.1
    manager.scaling_interval_seconds = 0.1
    return manager


class TestWorkerManager:
    """Test cases for WorkerManager."""
    
    def test_initialization(self, db_session_factory):
        """Test worker manager initialization."""
        manager = WorkerManager(db_session_factory)
        
        assert manager.state == WorkerManagerState.STOPPED
        assert not manager.is_running
        assert len(manager.workers) == 0
        assert len(manager.worker_assignments) == 0
        assert manager.auto_scaling_enabled is True
    
    def test_register_worker(self, worker_manager, mock_worker):
        """Test worker registration."""
        # Register worker
        worker_manager.register_worker(mock_worker)
        
        # Verify registration
        assert mock_worker.worker_id in worker_manager.workers
        assert mock_worker.worker_id in worker_manager.worker_assignments
        assert mock_worker.worker_id in worker_manager.worker_configs
        
        # Check assignment
        assignment = worker_manager.worker_assignments[mock_worker.worker_id]
        assert assignment.worker_id == mock_worker.worker_id
        assert assignment.stages == mock_worker.config.stages
    
    def test_register_worker_with_custom_assignment(self, worker_manager, mock_worker):
        """Test worker registration with custom assignment."""
        custom_assignment = WorkerAssignment(
            worker_id=mock_worker.worker_id,
            stages=[ProcessingStage.SENTIMENT_ANALYSIS],
            priority=1,
            load_factor=0.5
        )
        
        worker_manager.register_worker(mock_worker, custom_assignment)
        
        # Verify custom assignment
        assignment = worker_manager.worker_assignments[mock_worker.worker_id]
        assert assignment.stages == [ProcessingStage.SENTIMENT_ANALYSIS]
        assert assignment.priority == 1
        assert assignment.load_factor == 0.5
    
    def test_unregister_worker(self, worker_manager, mock_worker):
        """Test worker unregistration."""
        # Set worker as running so stop will be called
        mock_worker._state = WorkerState.RUNNING
        
        # Register and then unregister
        worker_manager.register_worker(mock_worker)
        worker_manager.unregister_worker(mock_worker.worker_id)
        
        # Verify unregistration
        assert mock_worker.worker_id not in worker_manager.workers
        assert mock_worker.worker_id not in worker_manager.worker_assignments
        assert mock_worker.worker_id not in worker_manager.worker_configs
        
        # Verify stop was called
        mock_worker.stop.assert_called_once()
    
    def test_unregister_nonexistent_worker(self, worker_manager):
        """Test unregistering a worker that doesn't exist."""
        # Should not raise an exception
        worker_manager.unregister_worker("nonexistent-worker")
    
    def test_start_worker(self, worker_manager, mock_worker):
        """Test starting a specific worker."""
        worker_manager.register_worker(mock_worker)
        
        # Mock worker as not running
        mock_worker._state = WorkerState.STOPPED
        
        # Start worker
        result = worker_manager.start_worker(mock_worker.worker_id)
        
        assert result is True
        mock_worker.start.assert_called_once()
        assert worker_manager._metrics['workers_started'] == 1
    
    def test_start_already_running_worker(self, worker_manager, mock_worker):
        """Test starting a worker that's already running."""
        worker_manager.register_worker(mock_worker)
        
        # Mock worker as running
        mock_worker._state = WorkerState.RUNNING
        
        # Start worker
        result = worker_manager.start_worker(mock_worker.worker_id)
        
        assert result is True
        mock_worker.start.assert_not_called()
    
    def test_start_nonexistent_worker(self, worker_manager):
        """Test starting a worker that doesn't exist."""
        result = worker_manager.start_worker("nonexistent-worker")
        assert result is False
    
    def test_start_worker_failure(self, worker_manager, mock_worker):
        """Test handling worker start failure."""
        worker_manager.register_worker(mock_worker)
        
        # Mock start to raise exception
        mock_worker.start.side_effect = Exception("Start failed")
        mock_worker._state = WorkerState.STOPPED
        
        # Start worker
        result = worker_manager.start_worker(mock_worker.worker_id)
        
        assert result is False
        assert worker_manager._metrics['workers_failed'] == 1
    
    def test_stop_worker(self, worker_manager, mock_worker):
        """Test stopping a specific worker."""
        worker_manager.register_worker(mock_worker)
        
        # Mock worker as running
        mock_worker._state = WorkerState.RUNNING
        
        # Stop worker
        result = worker_manager.stop_worker(mock_worker.worker_id, timeout=30)
        
        assert result is True
        mock_worker.stop.assert_called_once_with(30)
        assert worker_manager._metrics['workers_stopped'] == 1
    
    def test_stop_nonexistent_worker(self, worker_manager):
        """Test stopping a worker that doesn't exist."""
        result = worker_manager.stop_worker("nonexistent-worker")
        assert result is False
    
    def test_start_all_workers(self, worker_manager):
        """Test starting all registered workers."""
        # Create multiple mock workers
        workers = []
        for i in range(3):
            config = WorkerConfig(
                worker_id=f"test-worker-{i}",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                batch_size=10
            )
            worker = MockDistributedWorker(config)
            worker._state = WorkerState.STOPPED
            workers.append(worker)
            worker_manager.register_worker(worker)
        
        # Start all workers
        results = worker_manager.start_all_workers()
        
        # Verify all workers were started
        assert len(results) == 3
        assert all(results.values())
        
        for worker in workers:
            worker.start.assert_called_once()
    
    def test_stop_all_workers(self, worker_manager):
        """Test stopping all registered workers."""
        # Create multiple mock workers
        workers = []
        for i in range(3):
            config = WorkerConfig(
                worker_id=f"test-worker-{i}",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                batch_size=10
            )
            worker = MockDistributedWorker(config)
            worker._state = WorkerState.RUNNING
            workers.append(worker)
            worker_manager.register_worker(worker)
        
        # Stop all workers
        results = worker_manager.stop_all_workers(timeout=30)
        
        # Verify all workers were stopped
        assert len(results) == 3
        assert all(results.values())
        
        for worker in workers:
            worker.stop.assert_called_once_with(30)
    
    def test_get_worker_status(self, worker_manager, mock_worker):
        """Test getting worker status."""
        worker_manager.register_worker(mock_worker)
        
        # Mock health status
        mock_health = {
            'worker_id': mock_worker.worker_id,
            'healthy': True,
            'state': 'running'
        }
        mock_worker.get_health_status = Mock(return_value=mock_health)
        
        # Get status
        status = worker_manager.get_worker_status()
        
        assert mock_worker.worker_id in status
        worker_status = status[mock_worker.worker_id]
        assert worker_status['worker_id'] == mock_worker.worker_id
        assert worker_status['healthy'] is True
        assert 'assignment' in worker_status
    
    def test_get_running_workers(self, worker_manager):
        """Test getting list of running workers."""
        # Create workers with different states
        configs = [
            WorkerConfig(worker_id="running-1", stages=[ProcessingStage.DOWNLOAD_FULL_TEXT], batch_size=10),
            WorkerConfig(worker_id="running-2", stages=[ProcessingStage.SENTIMENT_ANALYSIS], batch_size=10),
            WorkerConfig(worker_id="stopped-1", stages=[ProcessingStage.COMPUTE_EMBEDDINGS], batch_size=10)
        ]
        
        workers = []
        for config in configs:
            worker = MockDistributedWorker(config)
            workers.append(worker)
            worker_manager.register_worker(worker)
        
        # Set states
        workers[0]._state = WorkerState.RUNNING
        workers[1]._state = WorkerState.RUNNING
        workers[2]._state = WorkerState.STOPPED
        
        # Get running workers
        running_workers = worker_manager.get_running_workers()
        
        assert len(running_workers) == 2
        running_ids = [w.worker_id for w in running_workers]
        assert "running-1" in running_ids
        assert "running-2" in running_ids
        assert "stopped-1" not in running_ids
    
    def test_get_workers_by_stage(self, worker_manager):
        """Test getting workers by processing stage."""
        # Create workers with different stage assignments
        configs = [
            WorkerConfig(worker_id="download-worker", stages=[ProcessingStage.DOWNLOAD_FULL_TEXT], batch_size=10),
            WorkerConfig(worker_id="analysis-worker", stages=[ProcessingStage.SENTIMENT_ANALYSIS], batch_size=10),
            WorkerConfig(worker_id="multi-worker", stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS], batch_size=10)
        ]
        
        workers = []
        for config in configs:
            worker = MockDistributedWorker(config)
            worker._state = WorkerState.RUNNING
            workers.append(worker)
            worker_manager.register_worker(worker)
        
        # Get workers for download stage
        download_workers = worker_manager.get_workers_by_stage(ProcessingStage.DOWNLOAD_FULL_TEXT)
        download_ids = [w.worker_id for w in download_workers]
        
        assert len(download_workers) == 2
        assert "download-worker" in download_ids
        assert "multi-worker" in download_ids
        assert "analysis-worker" not in download_ids
        
        # Get workers for sentiment analysis stage
        analysis_workers = worker_manager.get_workers_by_stage(ProcessingStage.SENTIMENT_ANALYSIS)
        analysis_ids = [w.worker_id for w in analysis_workers]
        
        assert len(analysis_workers) == 2
        assert "analysis-worker" in analysis_ids
        assert "multi-worker" in analysis_ids
        assert "download-worker" not in analysis_ids
    
    @patch('backend.app.distributed.worker_manager.WorkQueueManager')
    def test_optimize_worker_assignments(self, mock_work_queue_class, worker_manager, mock_db_session, mock_worker):
        """Test worker assignment optimization."""
        # Setup mock work queue
        mock_work_queue = Mock()
        mock_work_queue_class.return_value = mock_work_queue
        mock_work_queue.get_comprehensive_metrics.return_value = {
            'download_full_text': {'pending': 100, 'in_progress': 10, 'completed': 50},
            'sentiment_analysis': {'pending': 20, 'in_progress': 5, 'completed': 30}
        }
        
        # Register a worker
        worker_manager.register_worker(mock_worker)
        
        # Mock worker status
        with patch.object(worker_manager, 'get_worker_status') as mock_get_status:
            mock_get_status.return_value = {
                mock_worker.worker_id: {'healthy': True}
            }
            
            # Optimize assignments
            assignments = worker_manager.optimize_worker_assignments()
            
            assert mock_worker.worker_id in assignments
            assert len(assignments[mock_worker.worker_id]) > 0
    
    @patch('backend.app.distributed.worker_manager.WorkQueueManager')
    def test_analyze_scaling_needs(self, mock_work_queue_class, worker_manager, mock_db_session):
        """Test scaling needs analysis."""
        # Setup mock work queue
        mock_work_queue = Mock()
        mock_work_queue_class.return_value = mock_work_queue
        mock_work_queue.get_comprehensive_metrics.return_value = {
            'download_full_text': {'pending': 200, 'in_progress': 10, 'completed': 50, 'failed': 5},
            'sentiment_analysis': {'pending': 0, 'in_progress': 0, 'completed': 100, 'failed': 0}
        }
        
        # Register workers
        download_config = WorkerConfig(
            worker_id="download-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            batch_size=10
        )
        download_worker = MockDistributedWorker(download_config)
        download_worker._state = WorkerState.RUNNING
        worker_manager.register_worker(download_worker)
        
        # Analyze scaling needs
        scaling_decisions = worker_manager.analyze_scaling_needs()
        
        # Should recommend scaling up for download stage (high workload)
        # and potentially scaling down for sentiment analysis (no work)
        assert len(scaling_decisions) >= 1
        
        # Find scaling decision for download stage
        download_decision = None
        for decision in scaling_decisions:
            if decision.stage == ProcessingStage.DOWNLOAD_FULL_TEXT:
                download_decision = decision
                break
        
        assert download_decision is not None
        assert download_decision.recommended_workers > download_decision.current_workers
    
    def test_cleanup_failed_workers(self, worker_manager):
        """Test cleanup of failed workers."""
        # Create workers with different states
        configs = [
            WorkerConfig(worker_id="healthy-worker", stages=[ProcessingStage.DOWNLOAD_FULL_TEXT], batch_size=10),
            WorkerConfig(worker_id="failed-worker-1", stages=[ProcessingStage.SENTIMENT_ANALYSIS], batch_size=10),
            WorkerConfig(worker_id="failed-worker-2", stages=[ProcessingStage.COMPUTE_EMBEDDINGS], batch_size=10)
        ]
        
        workers = []
        for config in configs:
            worker = MockDistributedWorker(config)
            workers.append(worker)
            worker_manager.register_worker(worker)
        
        # Set states
        workers[0]._state = WorkerState.RUNNING
        workers[1]._state = WorkerState.ERROR
        workers[2]._state = WorkerState.ERROR
        
        # Cleanup failed workers
        cleaned_count = worker_manager.cleanup_failed_workers()
        
        assert cleaned_count == 2
        assert "healthy-worker" in worker_manager.workers
        assert "failed-worker-1" not in worker_manager.workers
        assert "failed-worker-2" not in worker_manager.workers
        assert worker_manager._metrics['workers_failed'] == 2
    
    def test_get_manager_metrics(self, worker_manager):
        """Test getting manager metrics."""
        # Register some workers
        for i in range(3):
            config = WorkerConfig(
                worker_id=f"worker-{i}",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                batch_size=10
            )
            worker = MockDistributedWorker(config)
            if i < 2:
                worker._state = WorkerState.RUNNING
            worker_manager.register_worker(worker)
        
        # Mock worker status for healthy workers
        with patch.object(worker_manager, 'get_worker_status') as mock_get_status:
            mock_get_status.return_value = {
                'worker-0': {'healthy': True},
                'worker-1': {'healthy': True},
                'worker-2': {'healthy': False}
            }
            
            metrics = worker_manager.get_manager_metrics()
            
            assert metrics['manager_state'] == WorkerManagerState.STOPPED.value
            assert metrics['total_workers'] == 3
            assert metrics['running_workers'] == 2
            assert metrics['healthy_workers'] == 2
            assert 'stage_distribution' in metrics
            assert 'metrics' in metrics
            assert 'configuration' in metrics
    
    @patch('backend.app.distributed.worker_manager.WorkerHealthManager')
    def test_manager_lifecycle(self, mock_health_manager_class, worker_manager, mock_db_session):
        """Test manager start/stop lifecycle."""
        # Mock health manager
        mock_health_manager = Mock()
        mock_health_manager_class.return_value = mock_health_manager
        
        # Start manager
        worker_manager.start()
        
        assert worker_manager.state == WorkerManagerState.RUNNING
        assert worker_manager.is_running
        assert worker_manager._management_thread is not None
        assert worker_manager._management_thread.is_alive()
        
        # Stop manager
        worker_manager.stop(timeout=5)
        
        assert worker_manager.state == WorkerManagerState.STOPPED
        assert not worker_manager.is_running
    
    def test_manager_start_already_running(self, worker_manager):
        """Test starting manager when already running."""
        worker_manager._state = WorkerManagerState.RUNNING
        
        with pytest.raises(RuntimeError, match="Cannot start manager in state running"):
            worker_manager.start()
    
    def test_scaling_decision_creation(self):
        """Test ScalingDecision dataclass."""
        decision = ScalingDecision(
            stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
            current_workers=2,
            recommended_workers=4,
            reason="High workload",
            urgency=2
        )
        
        assert decision.stage == ProcessingStage.DOWNLOAD_FULL_TEXT
        assert decision.current_workers == 2
        assert decision.recommended_workers == 4
        assert decision.reason == "High workload"
        assert decision.urgency == 2
    
    def test_worker_assignment_creation(self):
        """Test WorkerAssignment dataclass."""
        assignment = WorkerAssignment(
            worker_id="test-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS],
            priority=1,
            load_factor=0.8
        )
        
        assert assignment.worker_id == "test-worker"
        assert len(assignment.stages) == 2
        assert ProcessingStage.DOWNLOAD_FULL_TEXT in assignment.stages
        assert ProcessingStage.SENTIMENT_ANALYSIS in assignment.stages
        assert assignment.priority == 1
        assert assignment.load_factor == 0.8


class TestWorkerManagerIntegration:
    """Integration tests for WorkerManager."""
    
    @patch('backend.app.distributed.worker_manager.WorkerHealthManager')
    @patch('backend.app.distributed.worker_manager.WorkQueueManager')
    def test_full_worker_lifecycle_management(self, mock_work_queue_class, mock_health_manager_class, db_session_factory):
        """Test complete worker lifecycle management."""
        # Setup mocks
        mock_health_manager = Mock()
        mock_health_manager_class.return_value = mock_health_manager
        mock_work_queue = Mock()
        mock_work_queue_class.return_value = mock_work_queue
        
        # Create manager
        manager = WorkerManager(db_session_factory)
        manager.auto_scaling_enabled = False
        manager.management_interval_seconds = 0.1
        
        try:
            # Start manager
            manager.start()
            assert manager.is_running
            
            # Register workers
            workers = []
            for i in range(2):
                config = WorkerConfig(
                    worker_id=f"integration-worker-{i}",
                    stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                    batch_size=10
                )
                worker = MockDistributedWorker(config)
                workers.append(worker)
                manager.register_worker(worker)
            
            # Start all workers
            results = manager.start_all_workers()
            assert all(results.values())
            
            # Verify workers are tracked
            assert len(manager.get_running_workers()) == 0  # Mock workers don't actually change state
            
            # Get status
            status = manager.get_worker_status()
            assert len(status) == 2
            
            # Stop all workers
            stop_results = manager.stop_all_workers()
            assert all(stop_results.values())
            
        finally:
            # Stop manager
            manager.stop(timeout=5)
            assert not manager.is_running
    
    def test_concurrent_worker_operations(self, worker_manager):
        """Test concurrent worker operations."""
        # Register multiple workers
        workers = []
        for i in range(5):
            config = WorkerConfig(
                worker_id=f"concurrent-worker-{i}",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                batch_size=10
            )
            worker = MockDistributedWorker(config)
            workers.append(worker)
            worker_manager.register_worker(worker)
        
        # Perform concurrent operations
        def start_workers():
            worker_manager.start_all_workers()
        
        def stop_workers():
            time.sleep(0.1)  # Small delay
            worker_manager.stop_all_workers()
        
        def get_status():
            for _ in range(10):
                worker_manager.get_worker_status()
                time.sleep(0.01)
        
        # Run operations concurrently
        threads = [
            threading.Thread(target=start_workers),
            threading.Thread(target=stop_workers),
            threading.Thread(target=get_status)
        ]
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join(timeout=5)
        
        # Verify no exceptions occurred and state is consistent
        assert len(worker_manager.workers) == 5