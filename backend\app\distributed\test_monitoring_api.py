"""
Tests for MonitoringAPI and related functionality.
"""

import pytest
import json
import threading
import time
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime, timezone
from http.client import HTTPConnection

from backend.app.distributed.monitoring_api import (
    MonitoringAPIServer, <PERSON>ertManager, log_notification_handler
)
from backend.app.distributed.metrics_collector import (
    MetricsCollector, Alert, AlertSeverity, WorkerMetrics, SystemMetrics
)
from backend.app.distributed.worker_manager import WorkerManager


@pytest.fixture
def mock_metrics_collector():
    """Mock metrics collector."""
    collector = Mock(spec=MetricsCollector)
    collector.is_running = True
    collector.get_system_metrics_history.return_value = [
        SystemMetrics(
            timestamp=datetime.now(timezone.utc),
            total_workers=3,
            running_workers=2,
            healthy_workers=2,
            total_entries_processed=1000,
            overall_success_rate=95.0
        )
    ]
    collector.get_worker_metrics.return_value = {
        'worker-1': WorkerMetrics(
            worker_id='worker-1',
            worker_type='TestWorker',
            state='running',
            healthy=True,
            batches_processed=10,
            entries_processed=100
        )
    }
    collector.get_alerts.return_value = [
        Alert(
            id='test-alert',
            name='Test Alert',
            severity=AlertSeverity.WARNING,
            message='Test alert message',
            timestamp=datetime.now(timezone.utc)
        )
    ]
    collector.get_dashboard_data.return_value = {
        'timestamp': datetime.now(timezone.utc).isoformat(),
        'system_metrics': {'total_workers': 3},
        'worker_metrics': {'worker-1': {'worker_id': 'worker-1'}},
        'active_alerts': [],
        'alert_summary': {'total_alerts': 0, 'active_alerts': 0}
    }
    collector.resolve_alert.return_value = True
    return collector


@pytest.fixture
def mock_worker_manager():
    """Mock worker manager."""
    manager = Mock(spec=WorkerManager)
    manager.is_running = True
    manager.get_worker_status.return_value = {
        'worker-1': {
            'worker_id': 'worker-1',
            'state': 'running',
            'healthy': True
        }
    }
    manager.get_manager_metrics.return_value = {
        'total_workers': 3,
        'running_workers': 2,
        'healthy_workers': 2
    }
    manager.analyze_scaling_needs.return_value = []
    manager.start_all_workers.return_value = {'worker-1': True}
    manager.stop_all_workers.return_value = {'worker-1': True}
    manager.start_worker.return_value = True
    manager.stop_worker.return_value = True
    return manager


@pytest.fixture
def monitoring_server(mock_metrics_collector, mock_worker_manager):
    """Create monitoring API server for testing."""
    # Use port 0 to get any available port
    server = MonitoringAPIServer(
        port=0,
        metrics_collector=mock_metrics_collector,
        worker_manager=mock_worker_manager
    )
    return server


class TestMonitoringAPIServer:
    """Test cases for MonitoringAPIServer."""
    
    def test_initialization(self, mock_metrics_collector, mock_worker_manager):
        """Test monitoring API server initialization."""
        server = MonitoringAPIServer(
            port=8080,
            metrics_collector=mock_metrics_collector,
            worker_manager=mock_worker_manager
        )
        
        assert server.port == 8080
        assert server.metrics_collector == mock_metrics_collector
        assert server.worker_manager == mock_worker_manager
        assert not server.is_running
    
    def test_get_api_endpoints(self):
        """Test getting API endpoint URLs."""
        server = MonitoringAPIServer(port=8080)
        endpoints = server.get_api_endpoints()
        
        assert 'health' in endpoints
        assert 'system_metrics' in endpoints
        assert 'worker_metrics' in endpoints
        assert 'alerts' in endpoints
        assert 'dashboard' in endpoints
        
        # Check URL format
        assert endpoints['health'] == 'http://localhost:8080/api/health'
        assert endpoints['dashboard'] == 'http://localhost:8080/api/dashboard'
    
    def test_server_lifecycle(self, monitoring_server):
        """Test server start/stop lifecycle."""
        # Start server
        monitoring_server.start()
        
        # Give server time to start
        time.sleep(0.1)
        
        assert monitoring_server.is_running
        assert monitoring_server.server is not None
        assert monitoring_server.server_thread is not None
        
        # Get actual port (since we used 0)
        actual_port = monitoring_server.server.server_address[1]
        monitoring_server.port = actual_port
        
        # Test basic connectivity
        try:
            conn = HTTPConnection('localhost', actual_port, timeout=1)
            conn.request('GET', '/api/health')
            response = conn.getresponse()
            assert response.status in [200, 503]  # Either healthy or degraded
        except Exception:
            pass  # Connection might fail in test environment
        finally:
            if 'conn' in locals():
                conn.close()
        
        # Stop server
        monitoring_server.stop()
        assert not monitoring_server.is_running


class TestMonitoringAPIEndpoints:
    """Test cases for monitoring API endpoints."""
    
    def _make_request(self, server, path, method='GET', data=None):
        """Helper to make HTTP requests to the server."""
        if not server.is_running:
            server.start()
            time.sleep(0.1)
        
        actual_port = server.server.server_address[1]
        
        try:
            conn = HTTPConnection('localhost', actual_port, timeout=2)
            
            if method == 'POST' and data:
                headers = {'Content-Type': 'application/json'}
                conn.request(method, path, json.dumps(data), headers)
            else:
                conn.request(method, path)
            
            response = conn.getresponse()
            response_data = response.read().decode('utf-8')
            
            return response.status, response_data
            
        except Exception as e:
            pytest.skip(f"HTTP request failed: {e}")
        finally:
            if 'conn' in locals():
                conn.close()
    
    def test_health_endpoint(self, monitoring_server):
        """Test health check endpoint."""
        status, data = self._make_request(monitoring_server, '/api/health')
        
        assert status in [200, 503]
        
        response_json = json.loads(data)
        assert 'status' in response_json
        assert 'timestamp' in response_json
        assert 'components' in response_json
        
        components = response_json['components']
        assert 'metrics_collector' in components
        assert 'worker_manager' in components
    
    def test_system_metrics_endpoint(self, monitoring_server):
        """Test system metrics endpoint."""
        status, data = self._make_request(monitoring_server, '/api/metrics/system')
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'metrics' in response_json
        assert 'count' in response_json
        assert isinstance(response_json['metrics'], list)
    
    def test_worker_metrics_endpoint(self, monitoring_server):
        """Test worker metrics endpoint."""
        status, data = self._make_request(monitoring_server, '/api/metrics/workers')
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'workers' in response_json
        assert 'count' in response_json
        assert isinstance(response_json['workers'], dict)
    
    def test_alerts_endpoint(self, monitoring_server):
        """Test alerts endpoint."""
        status, data = self._make_request(monitoring_server, '/api/alerts')
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'alerts' in response_json
        assert 'count' in response_json
        assert isinstance(response_json['alerts'], list)
    
    def test_dashboard_endpoint(self, monitoring_server):
        """Test dashboard data endpoint."""
        status, data = self._make_request(monitoring_server, '/api/dashboard')
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'timestamp' in response_json
        assert 'system_metrics' in response_json
        assert 'worker_metrics' in response_json
        assert 'active_alerts' in response_json
        assert 'alert_summary' in response_json
    
    def test_workers_status_endpoint(self, monitoring_server):
        """Test workers status endpoint."""
        status, data = self._make_request(monitoring_server, '/api/workers')
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'workers' in response_json
        assert 'summary' in response_json
        
        summary = response_json['summary']
        assert 'total_workers' in summary
        assert 'running_workers' in summary
        assert 'healthy_workers' in summary
    
    def test_scaling_analysis_endpoint(self, monitoring_server):
        """Test scaling analysis endpoint."""
        status, data = self._make_request(monitoring_server, '/api/workers/scaling')
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'scaling_decisions' in response_json
        assert 'count' in response_json
        assert isinstance(response_json['scaling_decisions'], list)
    
    def test_worker_detail_endpoint(self, monitoring_server):
        """Test individual worker detail endpoint."""
        status, data = self._make_request(monitoring_server, '/api/workers/worker-1')
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'worker_id' in response_json
        assert response_json['worker_id'] == 'worker-1'
    
    def test_worker_not_found(self, monitoring_server):
        """Test worker detail endpoint for non-existent worker."""
        status, data = self._make_request(monitoring_server, '/api/workers/nonexistent')
        
        assert status == 404
        
        response_json = json.loads(data)
        assert 'error' in response_json
    
    def test_resolve_alert_endpoint(self, monitoring_server):
        """Test alert resolution endpoint."""
        status, data = self._make_request(
            monitoring_server, 
            '/api/alerts/test-alert/resolve', 
            method='POST'
        )
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'message' in response_json
        assert 'test-alert' in response_json['message']
    
    def test_start_workers_endpoint(self, monitoring_server):
        """Test start all workers endpoint."""
        status, data = self._make_request(
            monitoring_server, 
            '/api/workers/start', 
            method='POST'
        )
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'message' in response_json
        assert 'results' in response_json
        assert 'successful' in response_json
        assert 'total' in response_json
    
    def test_stop_workers_endpoint(self, monitoring_server):
        """Test stop all workers endpoint."""
        status, data = self._make_request(
            monitoring_server, 
            '/api/workers/stop', 
            method='POST'
        )
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'message' in response_json
        assert 'results' in response_json
    
    def test_start_individual_worker_endpoint(self, monitoring_server):
        """Test start individual worker endpoint."""
        status, data = self._make_request(
            monitoring_server, 
            '/api/workers/worker-1/start', 
            method='POST'
        )
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'message' in response_json
        assert 'worker-1' in response_json['message']
    
    def test_stop_individual_worker_endpoint(self, monitoring_server):
        """Test stop individual worker endpoint."""
        status, data = self._make_request(
            monitoring_server, 
            '/api/workers/worker-1/stop', 
            method='POST'
        )
        
        assert status == 200
        
        response_json = json.loads(data)
        assert 'message' in response_json
        assert 'worker-1' in response_json['message']
    
    def test_endpoint_not_found(self, monitoring_server):
        """Test 404 for non-existent endpoint."""
        status, data = self._make_request(monitoring_server, '/api/nonexistent')
        
        assert status == 404
        
        response_json = json.loads(data)
        assert 'error' in response_json
    
    def test_query_parameters(self, monitoring_server):
        """Test endpoints with query parameters."""
        # Test system metrics with limit
        status, data = self._make_request(monitoring_server, '/api/metrics/system?limit=5')
        assert status == 200
        
        # Test worker metrics with worker_id filter
        status, data = self._make_request(monitoring_server, '/api/metrics/workers?worker_id=worker-1')
        assert status == 200
        
        # Test alerts with severity filter
        status, data = self._make_request(monitoring_server, '/api/alerts?severity=warning')
        assert status == 200
        
        # Test alerts with resolved filter
        status, data = self._make_request(monitoring_server, '/api/alerts?resolved=false')
        assert status == 200


class TestAlertManager:
    """Test cases for AlertManager."""
    
    def test_initialization(self, mock_metrics_collector):
        """Test alert manager initialization."""
        alert_manager = AlertManager(mock_metrics_collector)
        
        assert alert_manager.metrics_collector == mock_metrics_collector
        assert not alert_manager._is_running
        assert len(alert_manager._notification_handlers) == 0
    
    def test_add_notification_handler(self, mock_metrics_collector):
        """Test adding notification handlers."""
        alert_manager = AlertManager(mock_metrics_collector)
        
        def test_handler(alert):
            pass
        
        alert_manager.add_notification_handler(test_handler)
        
        assert len(alert_manager._notification_handlers) == 1
        assert alert_manager._notification_handlers[0] == test_handler
    
    def test_lifecycle_management(self, mock_metrics_collector):
        """Test alert manager lifecycle."""
        alert_manager = AlertManager(mock_metrics_collector)
        
        # Start alert manager
        alert_manager.start()
        assert alert_manager._is_running
        assert alert_manager._alert_thread is not None
        
        # Let it run briefly
        time.sleep(0.1)
        
        # Stop alert manager
        alert_manager.stop(timeout=5)
        assert not alert_manager._is_running
    
    def test_alert_processing(self, mock_metrics_collector):
        """Test alert processing with notification handlers."""
        alert_manager = AlertManager(mock_metrics_collector)
        
        # Track notifications
        notifications_received = []
        
        def test_handler(alert):
            notifications_received.append(alert)
        
        alert_manager.add_notification_handler(test_handler)
        
        # Mock metrics collector to return new alerts
        test_alert = Alert(
            id='test-alert',
            name='Test Alert',
            severity=AlertSeverity.ERROR,
            message='Test message',
            timestamp=datetime.now(timezone.utc)
        )
        
        mock_metrics_collector.get_alerts.return_value = [test_alert]
        
        # Process alert manually
        alert_manager._process_alert(test_alert)
        
        # Verify notification was sent
        assert len(notifications_received) == 1
        assert notifications_received[0] == test_alert


class TestNotificationHandlers:
    """Test cases for notification handlers."""
    
    def test_log_notification_handler(self, caplog):
        """Test log-based notification handler."""
        alert = Alert(
            id='test-alert',
            name='Test Alert',
            severity=AlertSeverity.WARNING,
            message='Test alert message',
            timestamp=datetime.now(timezone.utc)
        )
        
        # Call handler
        log_notification_handler(alert)
        
        # Check that log message was created
        assert len(caplog.records) > 0
        log_record = caplog.records[-1]
        assert 'ALERT: Test Alert - Test alert message' in log_record.message
        assert log_record.levelno == 30  # WARNING level
    
    @patch('requests.post')
    def test_webhook_notification_handler(self, mock_post):
        """Test webhook notification handler."""
        from backend.app.distributed.monitoring_api import webhook_notification_handler
        
        # Mock successful webhook response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_post.return_value = mock_response
        
        # Create webhook handler
        webhook_url = 'http://example.com/webhook'
        handler = webhook_notification_handler(webhook_url)
        
        # Create test alert
        alert = Alert(
            id='test-alert',
            name='Test Alert',
            severity=AlertSeverity.ERROR,
            message='Test message',
            timestamp=datetime.now(timezone.utc)
        )
        
        # Call handler
        handler(alert)
        
        # Verify webhook was called
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        
        assert call_args[0][0] == webhook_url
        assert 'json' in call_args[1]
        assert 'timeout' in call_args[1]
        
        # Check payload structure
        payload = call_args[1]['json']
        assert 'alert' in payload
        assert 'timestamp' in payload
        assert payload['alert']['id'] == 'test-alert'
    
    @patch('requests.post')
    def test_webhook_notification_handler_failure(self, mock_post, caplog):
        """Test webhook notification handler with failure."""
        from backend.app.distributed.monitoring_api import webhook_notification_handler
        
        # Mock failed webhook response
        mock_post.side_effect = Exception("Connection failed")
        
        # Create webhook handler
        handler = webhook_notification_handler('http://example.com/webhook')
        
        # Create test alert
        alert = Alert(
            id='test-alert',
            name='Test Alert',
            severity=AlertSeverity.ERROR,
            message='Test message',
            timestamp=datetime.now(timezone.utc)
        )
        
        # Call handler (should not raise exception)
        handler(alert)
        
        # Check that error was logged
        assert any('Failed to send webhook notification' in record.message for record in caplog.records)


class TestMonitoringAPIIntegration:
    """Integration tests for monitoring API components."""
    
    def test_full_monitoring_stack(self, mock_metrics_collector, mock_worker_manager):
        """Test complete monitoring stack integration."""
        # Create monitoring server
        server = MonitoringAPIServer(
            port=0,  # Use any available port
            metrics_collector=mock_metrics_collector,
            worker_manager=mock_worker_manager
        )
        
        # Create alert manager
        alert_manager = AlertManager(mock_metrics_collector)
        
        # Track notifications
        notifications = []
        alert_manager.add_notification_handler(lambda alert: notifications.append(alert))
        
        try:
            # Start components
            server.start()
            alert_manager.start()
            
            # Give components time to start
            time.sleep(0.1)
            
            # Verify components are running
            assert server.is_running
            assert alert_manager._is_running
            
            # Test API endpoint
            actual_port = server.server.server_address[1]
            
            try:
                conn = HTTPConnection('localhost', actual_port, timeout=1)
                conn.request('GET', '/api/health')
                response = conn.getresponse()
                assert response.status in [200, 503]
            except Exception:
                pass  # Connection might fail in test environment
            finally:
                if 'conn' in locals():
                    conn.close()
            
        finally:
            # Stop components
            server.stop()
            alert_manager.stop(timeout=5)
    
    def test_concurrent_api_requests(self, monitoring_server):
        """Test concurrent API requests."""
        def make_requests():
            for _ in range(5):
                try:
                    self._make_request(monitoring_server, '/api/health')
                    self._make_request(monitoring_server, '/api/dashboard')
                    time.sleep(0.01)
                except Exception:
                    pass  # Ignore connection errors in test environment
        
        # Start server
        monitoring_server.start()
        time.sleep(0.1)
        
        # Make concurrent requests
        threads = [threading.Thread(target=make_requests) for _ in range(3)]
        
        for thread in threads:
            thread.start()
        
        for thread in threads:
            thread.join(timeout=5)
        
        # Server should still be running
        assert monitoring_server.is_running
        
        monitoring_server.stop()
    
    def _make_request(self, server, path):
        """Helper method for making requests."""
        if not server.is_running:
            return
        
        actual_port = server.server.server_address[1]
        
        try:
            conn = HTTPConnection('localhost', actual_port, timeout=1)
            conn.request('GET', path)
            response = conn.getresponse()
            response.read()  # Consume response
            return response.status
        except Exception:
            pass  # Ignore connection errors
        finally:
            if 'conn' in locals():
                conn.close()