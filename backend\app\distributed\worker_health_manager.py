"""
Worker Health Manager for distributed ETL processing.

This module provides health monitoring, heartbeat tracking, and automatic
recovery functionality for distributed workers.
"""

import json
import logging
import os
import socket
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Set
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text
from sqlalchemy.exc import SQLAlchemyError

from backend.app.models.models import Worker, Entry
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.worker_config import WorkerConfig

logger = logging.getLogger(__name__)


class WorkerHealthManager:
    """
    Manages worker health monitoring, heartbeat tracking, and recovery operations.
    
    Provides database operations for tracking worker lifecycle, detecting failed
    workers, and automatically recovering from worker failures.
    """
    
    def __init__(self, db_session: Session):
        """Initialize with database session."""
        self.db = db_session
    
    def register_worker(self, 
                       worker_id: str,
                       worker_type: str,
                       config: WorkerConfig) -> None:
        """
        Register a new worker in the database.
        
        Args:
            worker_id: Unique worker identifier
            worker_type: Type/class name of the worker
            config: Worker configuration
            
        Raises:
            SQLAlchemyError: If database operation fails
        """
        try:
            with self.db.begin():
                # Check if worker already exists
                existing_worker = (
                    self.db.query(Worker)
                    .filter(Worker.worker_id == worker_id)
                    .first()
                )
                
                if existing_worker:
                    # Update existing worker
                    existing_worker.worker_type = worker_type
                    existing_worker.status = 'running'
                    existing_worker.stages = json.dumps([s.value for s in config.stages])
                    existing_worker.config = json.dumps(config.to_dict())
                    existing_worker.heartbeat_interval_seconds = config.heartbeat_interval_seconds
                    existing_worker.started_at = datetime.now(timezone.utc)
                    existing_worker.stopped_at = None
                    existing_worker.hostname = socket.gethostname()
                    existing_worker.process_id = os.getpid()
                    existing_worker.last_error = None
                    existing_worker.singleton_stages = None  # Clear singleton stages on restart
                    
                    logger.info(f"Updated existing worker registration: {worker_id}")
                else:
                    # Create new worker
                    worker = Worker(
                        worker_id=worker_id,
                        worker_type=worker_type,
                        status='running',
                        stages=json.dumps([s.value for s in config.stages]),
                        config=json.dumps(config.to_dict()),
                        heartbeat_interval_seconds=config.heartbeat_interval_seconds,
                        started_at=datetime.now(timezone.utc),
                        hostname=socket.gethostname(),
                        process_id=os.getpid(),
                        singleton_stages=None  # Initialize singleton stages tracking
                    )
                    
                    self.db.add(worker)
                    logger.info(f"Registered new worker: {worker_id}")
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to register worker {worker_id}: {e}")
            self.db.rollback()
            raise
    
    def unregister_worker(self, worker_id: str) -> None:
        """
        Unregister a worker and mark it as stopped.
        
        Args:
            worker_id: Worker identifier to unregister
            
        Raises:
            SQLAlchemyError: If database operation fails
        """
        try:
            with self.db.begin():
                worker = (
                    self.db.query(Worker)
                    .filter(Worker.worker_id == worker_id)
                    .first()
                )
                
                if worker:
                    worker.status = 'stopped'
                    worker.stopped_at = datetime.now(timezone.utc)
                    worker.current_batch_size = 0
                    worker.processing_since = None
                    worker.singleton_stages = None  # Clear singleton stages on shutdown
                    
                    logger.info(f"Unregistered worker: {worker_id}")
                else:
                    logger.warning(f"Attempted to unregister non-existent worker: {worker_id}")
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to unregister worker {worker_id}: {e}")
            self.db.rollback()
            raise
    
    def send_heartbeat(self, 
                      worker_id: str,
                      stats: Optional[Dict[str, Any]] = None,
                      current_batch_size: int = 0,
                      processing_since: Optional[datetime] = None,
                      error_message: Optional[str] = None) -> None:
        """
        Send a heartbeat for a worker.
        
        Args:
            worker_id: Worker identifier
            stats: Optional worker statistics to update
            current_batch_size: Size of currently processing batch
            processing_since: When current processing started
            error_message: Optional error message if worker encountered an error
            
        Raises:
            SQLAlchemyError: If database operation fails
        """
        try:
            with self.db.begin():
                worker = (
                    self.db.query(Worker)
                    .filter(Worker.worker_id == worker_id)
                    .first()
                )
                
                if not worker:
                    logger.warning(f"Heartbeat for non-existent worker: {worker_id}")
                    return
                
                # Update heartbeat timestamp
                worker.last_heartbeat = datetime.now(timezone.utc)
                
                # Update current processing state
                worker.current_batch_size = current_batch_size
                worker.processing_since = processing_since
                
                # Update statistics if provided
                if stats:
                    worker.batches_processed = stats.get('batches_processed', worker.batches_processed)
                    worker.entries_processed = stats.get('entries_processed', worker.entries_processed)
                    worker.entries_failed = stats.get('entries_failed', worker.entries_failed)
                    worker.total_processing_time = stats.get('total_processing_time', worker.total_processing_time)
                
                # Handle error reporting
                if error_message:
                    worker.last_error = error_message
                    worker.error_count = (worker.error_count or 0) + 1
                    worker.status = 'error'
                elif worker.status == 'error':
                    # Clear error status if no error reported
                    worker.status = 'running'
                
                logger.debug(f"Heartbeat received from worker {worker_id}")
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to send heartbeat for worker {worker_id}: {e}")
            self.db.rollback()
            raise
    
    def get_worker_health(self, worker_id: str) -> Optional[Dict[str, Any]]:
        """
        Get health information for a specific worker.
        
        Args:
            worker_id: Worker identifier
            
        Returns:
            Dictionary with worker health information or None if not found
        """
        try:
            worker = (
                self.db.query(Worker)
                .filter(Worker.worker_id == worker_id)
                .first()
            )
            
            if not worker:
                return None
            
            now = datetime.now(timezone.utc)
            
            # Calculate heartbeat age and staleness
            heartbeat_age_seconds = None
            heartbeat_stale = False
            
            if worker.last_heartbeat:
                # Ensure both datetimes are timezone-aware for comparison
                last_heartbeat = worker.last_heartbeat
                if last_heartbeat.tzinfo is None:
                    last_heartbeat = last_heartbeat.replace(tzinfo=timezone.utc)
                
                heartbeat_age_seconds = (now - last_heartbeat).total_seconds()
                heartbeat_stale = heartbeat_age_seconds > (worker.heartbeat_interval_seconds * 2)
            
            # Calculate uptime
            uptime_seconds = None
            if worker.started_at:
                # Ensure timezone-aware comparison
                started_at = worker.started_at
                if started_at.tzinfo is None:
                    started_at = started_at.replace(tzinfo=timezone.utc)
                
                end_time = worker.stopped_at or now
                if end_time.tzinfo is None:
                    end_time = end_time.replace(tzinfo=timezone.utc)
                
                uptime_seconds = (end_time - started_at).total_seconds()
            
            return {
                'worker_id': worker.worker_id,
                'worker_type': worker.worker_type,
                'status': worker.status,
                'healthy': worker.status == 'running' and not heartbeat_stale,
                'stages': self._parse_json_field(worker.stages, []),
                'config': self._parse_json_field(worker.config, {}),
                
                # Heartbeat information
                'last_heartbeat': worker.last_heartbeat.isoformat() if worker.last_heartbeat else None,
                'heartbeat_interval_seconds': worker.heartbeat_interval_seconds,
                'heartbeat_age_seconds': heartbeat_age_seconds,
                'heartbeat_stale': heartbeat_stale,
                
                # Lifecycle information
                'started_at': worker.started_at.isoformat() if worker.started_at else None,
                'stopped_at': worker.stopped_at.isoformat() if worker.stopped_at else None,
                'uptime_seconds': uptime_seconds,
                
                # Processing statistics
                'batches_processed': worker.batches_processed,
                'entries_processed': worker.entries_processed,
                'entries_failed': worker.entries_failed,
                'total_processing_time': worker.total_processing_time,
                'current_batch_size': worker.current_batch_size,
                'processing_since': worker.processing_since.isoformat() if worker.processing_since else None,
                
                # Error information
                'last_error': worker.last_error,
                'error_count': worker.error_count,
                
                # Host information
                'hostname': worker.hostname,
                'process_id': worker.process_id
            }
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get worker health for {worker_id}: {e}")
            return None
    
    def get_all_workers_health(self) -> List[Dict[str, Any]]:
        """
        Get health information for all registered workers.
        
        Returns:
            List of dictionaries with worker health information
        """
        try:
            workers = self.db.query(Worker).all()
            return [
                self.get_worker_health(worker.worker_id)
                for worker in workers
            ]
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get all workers health: {e}")
            return []
    
    def detect_stale_workers(self, 
                           stale_threshold_minutes: int = 5) -> List[Dict[str, Any]]:
        """
        Detect workers that haven't sent heartbeats recently.
        
        Args:
            stale_threshold_minutes: Minutes after which a worker is considered stale
            
        Returns:
            List of stale worker information
        """
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=stale_threshold_minutes)
            
            stale_workers = (
                self.db.query(Worker)
                .filter(
                    and_(
                        Worker.status.in_(['running', 'active']),  # Check both running and active workers
                        or_(
                            Worker.last_heartbeat < cutoff_time,
                            Worker.last_heartbeat.is_(None)
                        )
                    )
                )
                .all()
            )
            
            return [
                self.get_worker_health(worker.worker_id)
                for worker in stale_workers
            ]
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to detect stale workers: {e}")
            return []
    
    def recover_failed_workers(self, 
                              stale_threshold_minutes: int = 5,
                              claim_timeout_minutes: int = 30) -> Dict[str, int]:
        """
        Automatically recover from failed workers by releasing their claimed work.
        
        Args:
            stale_threshold_minutes: Minutes after which a worker is considered failed
            claim_timeout_minutes: Minutes after which claims are released
            
        Returns:
            Dictionary with recovery statistics
        """
        recovery_stats = {
            'stale_workers_detected': 0,
            'workers_marked_failed': 0,
            'entries_released': 0
        }
        
        try:
            with self.db.begin():
                # Detect stale workers
                stale_workers = self.detect_stale_workers(stale_threshold_minutes)
                recovery_stats['stale_workers_detected'] = len(stale_workers)
                
                if not stale_workers:
                    return recovery_stats
                
                stale_worker_ids = [worker['worker_id'] for worker in stale_workers]
                
                # Mark workers as failed
                (
                    self.db.query(Worker)
                    .filter(Worker.worker_id.in_(stale_worker_ids))
                    .update({
                        Worker.status: 'error',
                        Worker.last_error: f'Worker became unresponsive (no heartbeat for {stale_threshold_minutes} minutes)',
                        Worker.stopped_at: datetime.now(timezone.utc),
                        Worker.current_batch_size: 0,
                        Worker.processing_since: None,
                        Worker.singleton_stages: None  # Clear singleton stages for failed workers
                    }, synchronize_session=False)
                )
                
                recovery_stats['workers_marked_failed'] = len(stale_worker_ids)
                
                # Release claimed entries from failed workers
                claim_cutoff = datetime.now(timezone.utc) - timedelta(minutes=claim_timeout_minutes)
                
                released_entries = (
                    self.db.query(Entry)
                    .filter(
                        and_(
                            Entry.claimed_by.in_(stale_worker_ids),
                            Entry.claimed_at < claim_cutoff
                        )
                    )
                    .update({
                        Entry.claimed_by: None,
                        Entry.claimed_at: None
                    }, synchronize_session=False)
                )
                
                recovery_stats['entries_released'] = released_entries
                
                logger.warning(
                    f"Recovered from {len(stale_worker_ids)} failed workers, "
                    f"released {released_entries} entries"
                )
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to recover from failed workers: {e}")
            self.db.rollback()
            raise
        
        return recovery_stats
    
    def cleanup_old_workers(self, 
                           retention_days: int = 7) -> int:
        """
        Clean up old worker records to prevent database bloat.
        
        Args:
            retention_days: Number of days to retain stopped worker records
            
        Returns:
            Number of worker records cleaned up
        """
        try:
            with self.db.begin():
                cutoff_time = datetime.now(timezone.utc) - timedelta(days=retention_days)
                
                # Delete old stopped workers
                deleted_count = (
                    self.db.query(Worker)
                    .filter(
                        and_(
                            Worker.status.in_(['stopped', 'error']),
                            Worker.stopped_at < cutoff_time
                        )
                    )
                    .delete(synchronize_session=False)
                )
                
                logger.info(f"Cleaned up {deleted_count} old worker records")
                return deleted_count
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to cleanup old workers: {e}")
            self.db.rollback()
            raise
    
    def get_worker_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive metrics about all workers.
        
        Returns:
            Dictionary with worker metrics and statistics
        """
        try:
            # Get basic counts
            total_workers = self.db.query(Worker).count()
            running_workers = (
                self.db.query(Worker)
                .filter(Worker.status == 'running')
                .count()
            )
            stopped_workers = (
                self.db.query(Worker)
                .filter(Worker.status == 'stopped')
                .count()
            )
            error_workers = (
                self.db.query(Worker)
                .filter(Worker.status == 'error')
                .count()
            )
            
            # Get processing statistics
            processing_stats = (
                self.db.query(
                    func.sum(Worker.batches_processed).label('total_batches'),
                    func.sum(Worker.entries_processed).label('total_entries'),
                    func.sum(Worker.entries_failed).label('total_failed'),
                    func.sum(Worker.total_processing_time).label('total_time'),
                    func.avg(Worker.total_processing_time).label('avg_time')
                )
                .filter(Worker.status == 'running')
                .first()
            )
            
            # Get stale worker count
            stale_workers = len(self.detect_stale_workers())
            
            return {
                'total_workers': total_workers,
                'running_workers': running_workers,
                'stopped_workers': stopped_workers,
                'error_workers': error_workers,
                'stale_workers': stale_workers,
                'healthy_workers': running_workers - stale_workers,
                
                'processing_stats': {
                    'total_batches_processed': processing_stats.total_batches or 0,
                    'total_entries_processed': processing_stats.total_entries or 0,
                    'total_entries_failed': processing_stats.total_failed or 0,
                    'total_processing_time': processing_stats.total_time or 0.0,
                    'average_processing_time': processing_stats.avg_time or 0.0
                }
            }
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get worker metrics: {e}")
            return {}
    
    def get_workers_by_stage(self, stage: ProcessingStage) -> List[Dict[str, Any]]:
        """
        Get all workers that can process a specific stage.
        
        Args:
            stage: Processing stage to filter by
            
        Returns:
            List of worker health information for workers handling the stage
        """
        try:
            workers = (
                self.db.query(Worker)
                .filter(Worker.status == 'running')
                .all()
            )
            
            stage_workers = []
            for worker in workers:
                if worker.stages:
                    worker_stages = self._parse_json_field(worker.stages, [])
                    if stage.value in worker_stages:
                        stage_workers.append(self.get_worker_health(worker.worker_id))
            
            return [w for w in stage_workers if w is not None]
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get workers by stage {stage.value}: {e}")
            return []

    def cleanup_old_error_workers(self,
                                  error_age_hours: int = 24) -> Dict[str, int]:
        """
        Remove workers that have been in error state for too long.

        Args:
            error_age_hours: Hours after which error workers are removed

        Returns:
            Dictionary with cleanup statistics
        """
        cleanup_stats = {
            'old_error_workers_found': 0,
            'workers_removed': 0
        }

        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=error_age_hours)

            # Find old error workers
            old_error_workers = (
                self.db.query(Worker)
                .filter(
                    and_(
                        Worker.status == 'error',
                        or_(
                            Worker.stopped_at < cutoff_time,
                            and_(
                                Worker.stopped_at.is_(None),
                                Worker.started_at < cutoff_time
                            )
                        )
                    )
                )
                .all()
            )

            cleanup_stats['old_error_workers_found'] = len(old_error_workers)

            if old_error_workers:
                # Remove old error workers
                old_worker_ids = [worker.worker_id for worker in old_error_workers]

                deleted_count = (
                    self.db.query(Worker)
                    .filter(Worker.worker_id.in_(old_worker_ids))
                    .delete(synchronize_session=False)
                )

                self.db.commit()
                cleanup_stats['workers_removed'] = deleted_count

                logger.info(f"Removed {deleted_count} old error workers older than {error_age_hours} hours")

            return cleanup_stats

        except SQLAlchemyError as e:
            logger.error(f"Failed to cleanup old error workers: {e}")
            self.db.rollback()
            raise

    def _parse_json_field(self, field_value: Optional[str], default_value):
        """
        Safely parse a JSON field, handling malformed or non-JSON values.

        Args:
            field_value: The field value to parse
            default_value: Default value to return if parsing fails

        Returns:
            Parsed JSON value or default_value
        """
        if not field_value:
            return default_value

        try:
            return json.loads(field_value)
        except (json.JSONDecodeError, TypeError):
            # Handle cases where field contains non-JSON strings like 'download_feeds'
            if isinstance(field_value, str):
                # If it's a simple string that looks like a stage name, wrap it in a list
                if field_value in ['download_feeds', 'download_full_text', 'compute_embeddings', 'sentiment_analysis', 'duplicate_detection']:
                    return [field_value]

            logger.warning(f"Failed to parse JSON field: {repr(field_value)}, using default: {default_value}")
            return default_value