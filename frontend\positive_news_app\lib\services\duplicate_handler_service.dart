import 'package:flutter/foundation.dart';
import 'package:breakingbright/models/news_model.dart';

/// Service to handle duplicate news entries on the frontend
/// This compensates for the simplified backend queries that may return duplicates
class DuplicateHandlerService {
  /// Remove duplicate news entries based on dup_entry_id
  /// Keeps the most recent entry for each duplicate group
  static List<EntryWithSource> removeDuplicates(
      List<EntryWithSource> newsItems) {
    if (newsItems.isEmpty) return newsItems;

    debugPrint('DuplicateHandler: Processing ${newsItems.length} news items');

    // Group by dup_entry_id, keeping track of the most recent entry
    final Map<String?, EntryWithSource> uniqueEntries = {};

    for (final entry in newsItems) {
      final dupId = entry.dupEntryId;

      // If no dup_entry_id, treat as unique
      if (dupId == null || dupId.isEmpty) {
        uniqueEntries[entry.entryId] = entry;
        continue;
      }

      // Check if we already have an entry for this dup_entry_id
      final existing = uniqueEntries[dupId];
      if (existing == null) {
        // First entry for this dup_entry_id
        uniqueEntries[dupId] = entry;
      } else {
        // Keep the more recent entry
        if (entry.published.isAfter(existing.published)) {
          uniqueEntries[dupId] = entry;
        }
        // If same publish time, keep the one with smaller entry_id (tie-breaker)
        else if (entry.published.isAtSameMomentAs(existing.published) &&
            entry.entryId.compareTo(existing.entryId) < 0) {
          uniqueEntries[dupId] = entry;
        }
      }
    }

    final result = uniqueEntries.values.toList();

    // Sort by published date (most recent first)
    result.sort((a, b) => b.published.compareTo(a.published));

    debugPrint(
        'DuplicateHandler: Removed ${newsItems.length - result.length} duplicates, ${result.length} unique entries remain');

    return result;
  }

  /// Remove duplicates within each category while preserving category structure
  static Map<String, List<EntryWithSource>> removeDuplicatesByCategory(
      Map<String, List<EntryWithSource>> categorizedNews) {
    debugPrint(
        'DuplicateHandler: Processing ${categorizedNews.length} categories');

    final result = <String, List<EntryWithSource>>{};

    for (final entry in categorizedNews.entries) {
      final categoryCode = entry.key;
      final newsItems = entry.value;

      result[categoryCode] = removeDuplicates(newsItems);
    }

    return result;
  }

  /// Check if a news item is likely a duplicate of another
  static bool areLikelyDuplicates(
      EntryWithSource item1, EntryWithSource item2) {
    // Same dup_entry_id is a definite duplicate
    if (item1.dupEntryId != null &&
        item1.dupEntryId == item2.dupEntryId &&
        item1.dupEntryId!.isNotEmpty) {
      return true;
    }

    // Similar titles and same category might be duplicates
    if (item1.iptcNewscode == item2.iptcNewscode) {
      final title1 = item1.title.toLowerCase().trim();
      final title2 = item2.title.toLowerCase().trim();

      // Very similar titles (simple similarity check)
      if (_calculateSimilarity(title1, title2) > 0.8) {
        return true;
      }
    }

    return false;
  }

  /// Simple string similarity calculation (Jaccard similarity)
  static double _calculateSimilarity(String str1, String str2) {
    if (str1 == str2) return 1.0;
    if (str1.isEmpty || str2.isEmpty) return 0.0;

    // Split into words
    final words1 = str1.split(' ').where((w) => w.length > 2).toSet();
    final words2 = str2.split(' ').where((w) => w.length > 2).toSet();

    if (words1.isEmpty || words2.isEmpty) return 0.0;

    // Calculate Jaccard similarity
    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;

    return intersection / union;
  }

  /// Get statistics about duplicates in a news list
  static Map<String, int> getDuplicateStats(List<EntryWithSource> newsItems) {
    final duplicateGroups = <String, int>{};
    final uniqueItems = <String>{};

    for (final item in newsItems) {
      final dupId = item.dupEntryId;
      if (dupId != null && dupId.isNotEmpty) {
        duplicateGroups[dupId] = (duplicateGroups[dupId] ?? 0) + 1;
      } else {
        uniqueItems.add(item.entryId);
      }
    }

    final duplicateCount = duplicateGroups.values
        .where((count) => count > 1)
        .fold(
            0,
            (sum, count) =>
                sum + count - 1); // -1 because we keep one from each group

    return {
      'total_items': newsItems.length,
      'unique_items': uniqueItems.length,
      'duplicate_groups': duplicateGroups.length,
      'duplicate_items': duplicateCount,
      'final_count': newsItems.length - duplicateCount,
    };
  }

  /// Smart deduplication that preserves diversity
  /// Keeps duplicates if they're from different sources to show multiple perspectives
  static List<EntryWithSource> smartDeduplication(
    List<EntryWithSource> newsItems, {
    bool preserveSourceDiversity = true,
    int maxPerDuplicateGroup = 2,
  }) {
    if (newsItems.isEmpty) return newsItems;

    debugPrint(
        'DuplicateHandler: Smart deduplication of ${newsItems.length} items');

    // Group by dup_entry_id
    final groups = <String, List<EntryWithSource>>{};
    final uniqueItems = <EntryWithSource>[];

    for (final item in newsItems) {
      final dupId = item.dupEntryId;
      if (dupId == null || dupId.isEmpty) {
        uniqueItems.add(item);
      } else {
        groups[dupId] = (groups[dupId] ?? [])..add(item);
      }
    }

    final result = <EntryWithSource>[];
    result.addAll(uniqueItems);

    // Process each duplicate group
    for (final group in groups.values) {
      if (group.length == 1) {
        result.add(group.first);
        continue;
      }

      // Sort by published date (most recent first)
      group.sort((a, b) => b.published.compareTo(a.published));

      if (preserveSourceDiversity) {
        // Keep up to maxPerDuplicateGroup items from different sources
        final seenSources = <String>{};
        var added = 0;

        for (final item in group) {
          if (added >= maxPerDuplicateGroup) break;

          if (!seenSources.contains(item.source)) {
            result.add(item);
            seenSources.add(item.source);
            added++;
          }
        }

        // If we haven't reached the limit and there are more items, add the most recent ones
        if (added < maxPerDuplicateGroup) {
          for (final item in group) {
            if (added >= maxPerDuplicateGroup) break;
            if (!result.contains(item)) {
              result.add(item);
              added++;
            }
          }
        }
      } else {
        // Just keep the most recent one
        result.add(group.first);
      }
    }

    // Sort final result by published date
    result.sort((a, b) => b.published.compareTo(a.published));

    debugPrint(
        'DuplicateHandler: Smart deduplication result: ${result.length} items');

    return result;
  }
}
