"""
Tests for stale work cleanup and recovery system.
"""

import pytest
import json
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session

from backend.app.distributed.stale_work_cleanup import (
    StaleWorkCleanupSystem, ManualInterventionTools, CleanupReason, RecoveryAction,
    CleanupResult, StuckEntry, log_cleanup_results, log_stuck_entries
)
from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus, ProcessingStatusValue
from backend.app.models.models import Entry


class TestStaleWorkCleanupSystem:
    """Test stale work cleanup system functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_db_factory = Mock()
        self.mock_db = Mock(spec=Session)
        self.mock_db_factory.return_value = self.mock_db
        
        self.cleanup_system = StaleWorkCleanupSystem(
            db_session_factory=self.mock_db_factory,
            cleanup_interval_seconds=60,
            stale_claim_timeout_minutes=30,
            stuck_processing_timeout_minutes=120,
            max_retry_age_hours=24
        )
    
    def test_initialization(self):
        """Test cleanup system initialization."""
        assert self.cleanup_system.cleanup_interval_seconds == 60
        assert self.cleanup_system.stale_claim_timeout_minutes == 30
        assert self.cleanup_system.stuck_processing_timeout_minutes == 120
        assert self.cleanup_system.max_retry_age_hours == 24
        assert not self.cleanup_system.is_running
    
    def test_cleanup_stale_claims(self):
        """Test cleanup of stale claims."""
        # Setup mock stale entries
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=30)
        
        stale_entries = [
            Mock(
                entry_id="stale-1",
                claimed_by="worker-001",
                claimed_at=cutoff_time - timedelta(minutes=10)
            ),
            Mock(
                entry_id="stale-2",
                claimed_by="worker-002",
                claimed_at=cutoff_time - timedelta(minutes=5)
            )
        ]
        
        # Setup database mocks
        self.mock_db.query.return_value.filter.return_value.all.return_value = stale_entries
        self.mock_db.query.return_value.filter.return_value.update.return_value = 2
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test cleanup
        result = self.cleanup_system._cleanup_stale_claims(self.mock_db)
        
        assert result.entries_processed == 2
        assert result.entries_released == 2
        assert result.cleanup_reason == CleanupReason.STALE_CLAIM
        assert RecoveryAction.RELEASE_CLAIM in result.recovery_actions
    
    def test_cleanup_stale_claims_no_stale_entries(self):
        """Test cleanup when no stale entries exist."""
        # Setup database mocks for no stale entries
        self.mock_db.query.return_value.filter.return_value.all.return_value = []
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test cleanup
        result = self.cleanup_system._cleanup_stale_claims(self.mock_db)
        
        assert result.entries_processed == 0
        assert result.entries_released == 0
        assert result.cleanup_reason == CleanupReason.STALE_CLAIM
    
    @patch('backend.app.distributed.stale_work_cleanup.WorkerHealthManager')
    def test_recover_from_worker_failures(self, mock_health_manager_class):
        """Test recovery from worker failures."""
        # Setup mock health manager
        mock_health_manager = Mock()
        mock_health_manager_class.return_value = mock_health_manager
        
        # Mock failed workers
        failed_workers = [
            {'worker_id': 'failed-worker-1', 'last_heartbeat': '2025-01-01T10:00:00Z'},
            {'worker_id': 'failed-worker-2', 'last_heartbeat': '2025-01-01T09:30:00Z'}
        ]
        mock_health_manager.detect_stale_workers.return_value = failed_workers
        
        # Mock entries from failed workers
        failed_worker_entries = [
            Mock(entry_id="entry-1", claimed_by="failed-worker-1"),
            Mock(entry_id="entry-2", claimed_by="failed-worker-2"),
            Mock(entry_id="entry-3", claimed_by="failed-worker-1")
        ]
        
        self.mock_db.query.return_value.filter.return_value.all.return_value = failed_worker_entries
        self.mock_db.query.return_value.filter.return_value.update.return_value = 3
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        mock_health_manager.recover_failed_workers.return_value = {
            'workers_marked_failed': 2,
            'entries_released': 3
        }
        
        # Test recovery
        result = self.cleanup_system._recover_from_worker_failures(self.mock_db)
        
        assert result.entries_processed == 3
        assert result.entries_released == 3
        assert result.cleanup_reason == CleanupReason.WORKER_FAILURE
        assert 'failed_workers' in result.details
        assert result.details['failed_workers'] == ['failed-worker-1', 'failed-worker-2']
    
    @patch('backend.app.distributed.stale_work_cleanup.WorkerHealthManager')
    def test_recover_from_worker_failures_no_failures(self, mock_health_manager_class):
        """Test recovery when no worker failures exist."""
        # Setup mock health manager
        mock_health_manager = Mock()
        mock_health_manager_class.return_value = mock_health_manager
        mock_health_manager.detect_stale_workers.return_value = []
        
        # Test recovery
        result = self.cleanup_system._recover_from_worker_failures(self.mock_db)
        
        assert result.entries_processed == 0
        assert result.entries_released == 0
        assert result.cleanup_reason == CleanupReason.WORKER_FAILURE
    
    def test_cleanup_stuck_processing(self):
        """Test cleanup of stuck processing entries."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=120)
        
        # Create mock stuck entries
        stuck_entries = [
            Mock(
                entry_id="stuck-1",
                claimed_by="worker-001",
                claimed_at=cutoff_time - timedelta(minutes=30),
                retry_count=1,
                processing_status='{"download_full_text": {"status": "in_progress"}}'
            ),
            Mock(
                entry_id="stuck-2",
                claimed_by="worker-002",
                claimed_at=cutoff_time - timedelta(minutes=60),
                retry_count=4,  # High retry count - should fail
                processing_status='{"sentiment_analysis": {"status": "in_progress"}}'
            )
        ]
        
        self.mock_db.query.return_value.filter.return_value.all.return_value = stuck_entries
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test cleanup
        result = self.cleanup_system._cleanup_stuck_processing(self.mock_db)
        
        assert result.entries_processed == 2
        assert result.entries_reset >= 0
        assert result.entries_failed >= 0
        assert result.cleanup_reason == CleanupReason.STUCK_PROCESSING
    
    def test_cleanup_old_retry_entries(self):
        """Test cleanup of old retry entries."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        
        # Create mock entries with old retry info
        old_retry_entries = [
            Mock(
                entry_id="old-retry-1",
                claimed_by=None,
                claimed_at=cutoff_time - timedelta(hours=1),
                processing_status=json.dumps({
                    "download_full_text": {
                        "retry_info": {
                            "next_retry_at": (cutoff_time - timedelta(hours=2)).isoformat()
                        }
                    }
                })
            ),
            Mock(
                entry_id="old-retry-2",
                claimed_by=None,
                claimed_at=cutoff_time - timedelta(hours=3),
                processing_status=json.dumps({
                    "sentiment_analysis": {
                        "retry_info": {
                            "next_retry_at": (cutoff_time - timedelta(hours=4)).isoformat()
                        }
                    }
                })
            )
        ]
        
        self.mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = old_retry_entries
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test cleanup
        result = self.cleanup_system._cleanup_old_retry_entries(self.mock_db)
        
        assert result.entries_processed == 2
        assert result.cleanup_reason == CleanupReason.TIMEOUT
        
        # Verify retry info was cleaned from entries
        for entry in old_retry_entries:
            status_data = json.loads(entry.processing_status)
            for stage_data in status_data.values():
                assert 'retry_info' not in stage_data
    
    def test_find_stuck_entries(self):
        """Test finding entries that require manual intervention."""
        # Create mock high retry entries
        high_retry_entries = [
            Mock(
                entry_id="high-retry-1",
                retry_count=8,
                claimed_by=None,
                claimed_at=None,
                last_error="Connection refused",
                processing_status='{"download_full_text": {"status": "failed"}}'
            ),
            Mock(
                entry_id="high-retry-2",
                retry_count=12,  # Very high - should suggest dead letter
                claimed_by=None,
                claimed_at=None,
                last_error="Unauthorized access",
                processing_status='{"sentiment_analysis": {"status": "failed"}}'
            )
        ]
        
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = high_retry_entries
        
        # Test finding stuck entries
        stuck_entries = self.cleanup_system._find_stuck_entries(self.mock_db)
        
        assert len(stuck_entries) == 2
        assert all(isinstance(entry, StuckEntry) for entry in stuck_entries)
        assert stuck_entries[0].entry_id == "high-retry-1"
        assert stuck_entries[1].entry_id == "high-retry-2"
        assert stuck_entries[1].suggested_action == RecoveryAction.DEAD_LETTER  # High retry count
    
    def test_analyze_stuck_entry_scenarios(self):
        """Test different stuck entry analysis scenarios."""
        # Test high retry count
        high_retry_entry = Mock(
            retry_count=15,
            last_error=None,
            claimed_by=None,
            claimed_at=None
        )
        reason, action = self.cleanup_system._analyze_stuck_entry(high_retry_entry)
        assert reason == "Too many retries"
        assert action == RecoveryAction.DEAD_LETTER
        
        # Test authentication error
        auth_error_entry = Mock(
            retry_count=3,
            last_error="Unauthorized access denied",
            claimed_by=None,
            claimed_at=None
        )
        reason, action = self.cleanup_system._analyze_stuck_entry(auth_error_entry)
        assert reason == "Authentication error"
        assert action == RecoveryAction.MANUAL_REVIEW
        
        # Test not found error
        not_found_entry = Mock(
            retry_count=2,
            last_error="Resource not found (404)",
            claimed_by=None,
            claimed_at=None
        )
        reason, action = self.cleanup_system._analyze_stuck_entry(not_found_entry)
        assert reason == "Resource not found"
        assert action == RecoveryAction.MARK_FAILED
        
        # Test long running claim
        long_claim_entry = Mock(
            retry_count=2,
            last_error=None,
            claimed_by="worker-001",
            claimed_at=datetime.now(timezone.utc) - timedelta(hours=2)
        )
        reason, action = self.cleanup_system._analyze_stuck_entry(long_claim_entry)
        assert reason == "Long running claim"
        assert action == RecoveryAction.RELEASE_CLAIM
    
    def test_force_cleanup(self):
        """Test forcing immediate cleanup."""
        # Mock the cleanup system as running
        self.cleanup_system._running = True
        
        # Mock successful cleanup cycle
        with patch.object(self.cleanup_system, '_perform_cleanup_cycle') as mock_cleanup:
            result = self.cleanup_system.force_cleanup()
            
            assert result['status'] == 'success'
            assert 'timestamp' in result
            assert 'stats' in result
            mock_cleanup.assert_called_once()
    
    def test_force_cleanup_not_running(self):
        """Test forcing cleanup when system is not running."""
        # Ensure system is not running
        self.cleanup_system._running = False
        
        with pytest.raises(RuntimeError, match="Cleanup system is not running"):
            self.cleanup_system.force_cleanup()
    
    def test_get_cleanup_stats(self):
        """Test getting cleanup statistics."""
        # Update some stats
        self.cleanup_system._stats['cleanup_cycles'] = 5
        self.cleanup_system._stats['total_entries_cleaned'] = 100
        
        stats = self.cleanup_system.get_cleanup_stats()
        
        assert stats['running'] == self.cleanup_system._running
        assert stats['cleanup_interval_seconds'] == 60
        assert stats['stats']['cleanup_cycles'] == 5
        assert stats['stats']['total_entries_cleaned'] == 100
    
    def test_callback_notifications(self):
        """Test callback notifications."""
        # Setup callbacks
        cleanup_callback = Mock()
        stuck_callback = Mock()
        
        self.cleanup_system.add_cleanup_callback(cleanup_callback)
        self.cleanup_system.add_stuck_entry_callback(stuck_callback)
        
        # Test cleanup callback
        cleanup_result = CleanupResult(
            entries_processed=5,
            entries_released=3,
            entries_reset=2,
            entries_failed=0,
            entries_dead_lettered=0,
            cleanup_reason=CleanupReason.STALE_CLAIM,
            recovery_actions=[RecoveryAction.RELEASE_CLAIM],
            timestamp=datetime.now(timezone.utc),
            details={}
        )
        
        self.cleanup_system._notify_cleanup_callbacks(cleanup_result)
        cleanup_callback.assert_called_once_with(cleanup_result)
        
        # Test stuck entry callback
        stuck_entries = [
            StuckEntry(
                entry_id="stuck-1",
                stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
                claimed_by=None,
                claimed_at=None,
                last_error="Error",
                retry_count=5,
                processing_status={},
                stuck_reason="High retry count",
                suggested_action=RecoveryAction.MANUAL_REVIEW,
                manual_intervention_required=True
            )
        ]
        
        self.cleanup_system._notify_stuck_entries(stuck_entries)
        stuck_callback.assert_called_once_with(stuck_entries)


class TestManualInterventionTools:
    """Test manual intervention tools functionality."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.mock_db = Mock(spec=Session)
        self.intervention_tools = ManualInterventionTools(self.mock_db)
    
    def test_get_stuck_entries(self):
        """Test getting stuck entries."""
        # Setup mock entries
        stuck_entries = [
            Mock(
                entry_id="stuck-1",
                retry_count=5,
                claimed_by=None,
                claimed_at=None,
                last_error="Connection refused",
                processing_status='{"download_full_text": {"status": "failed"}}'
            ),
            Mock(
                entry_id="stuck-2",
                retry_count=8,
                claimed_by="worker-001",
                claimed_at=datetime.now(timezone.utc) - timedelta(hours=3),
                last_error="Timeout",
                processing_status='{"sentiment_analysis": {"status": "in_progress"}}'
            )
        ]
        
        self.mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = stuck_entries
        
        # Test getting stuck entries
        result = self.intervention_tools.get_stuck_entries(min_retry_count=3)
        
        assert len(result) == 2
        assert all(isinstance(entry, StuckEntry) for entry in result)
        assert result[0].entry_id == "stuck-1"
        assert result[1].entry_id == "stuck-2"
    
    def test_reset_entry_processing(self):
        """Test resetting entry processing."""
        # Setup mock entry
        mock_entry = Mock(
            entry_id="test-entry",
            processing_status='{"download_full_text": {"status": "failed"}}',
            claimed_by="worker-001",
            claimed_at=datetime.now(timezone.utc),
            last_error="Some error",
            retry_count=3
        )
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test reset
        result = self.intervention_tools.reset_entry_processing("test-entry", reset_retry_count=True)
        
        assert result is True
        assert mock_entry.claimed_by is None
        assert mock_entry.claimed_at is None
        assert mock_entry.last_error is None
        assert mock_entry.retry_count == 0
    
    def test_reset_entry_processing_specific_stage(self):
        """Test resetting specific stage processing."""
        # Setup mock entry
        mock_entry = Mock(
            entry_id="test-entry",
            processing_status='{"download_full_text": {"status": "completed"}, "sentiment_analysis": {"status": "failed"}}',
            claimed_by=None,
            claimed_at=None,
            last_error="Stage error",
            retry_count=2
        )
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test reset specific stage
        result = self.intervention_tools.reset_entry_processing(
            "test-entry", 
            stage=ProcessingStage.SENTIMENT_ANALYSIS,
            reset_retry_count=False
        )
        
        assert result is True
        assert mock_entry.retry_count == 2  # Should not be reset
    
    def test_reset_entry_processing_not_found(self):
        """Test resetting processing for non-existent entry."""
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        result = self.intervention_tools.reset_entry_processing("non-existent")
        
        assert result is False
    
    def test_mark_entry_failed(self):
        """Test marking entry as failed."""
        # Setup mock entry
        mock_entry = Mock(
            entry_id="test-entry",
            processing_status='{"download_full_text": {"status": "in_progress"}}',
            claimed_by="worker-001",
            claimed_at=datetime.now(timezone.utc),
            last_error=None,
            retry_count=2
        )
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test marking as failed
        result = self.intervention_tools.mark_entry_failed(
            "test-entry",
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            "Manual intervention required"
        )
        
        assert result is True
        assert mock_entry.claimed_by is None
        assert mock_entry.claimed_at is None
        assert mock_entry.last_error == "MANUAL_FAILURE: Manual intervention required"
        assert mock_entry.retry_count == 3
    
    def test_release_entry_claim(self):
        """Test releasing entry claim."""
        # Setup mock entry
        mock_entry = Mock(
            entry_id="test-entry",
            claimed_by="worker-001",
            claimed_at=datetime.now(timezone.utc)
        )
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_entry
        self.mock_db.begin.return_value.__enter__ = Mock(return_value=self.mock_db)
        self.mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Test release
        result = self.intervention_tools.release_entry_claim("test-entry")
        
        assert result is True
        assert mock_entry.claimed_by is None
        assert mock_entry.claimed_at is None
    
    def test_get_entry_details(self):
        """Test getting detailed entry information."""
        # Setup mock entry
        mock_entry = Mock(
            entry_id="test-entry",
            title="Test Entry",
            published=datetime.now(timezone.utc),
            claimed_by="worker-001",
            claimed_at=datetime.now(timezone.utc),
            last_error="Some error",
            retry_count=3,
            processing_status='{"download_full_text": {"status": "completed"}, "sentiment_analysis": {"status": "failed"}}'
        )
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_entry
        
        # Test getting details
        details = self.intervention_tools.get_entry_details("test-entry")
        
        assert details is not None
        assert details['entry_id'] == "test-entry"
        assert details['title'] == "Test Entry"
        assert details['retry_count'] == 3
        assert 'stage_summary' in details
        assert 'download_full_text' in details['stage_summary']
        assert 'sentiment_analysis' in details['stage_summary']
    
    def test_get_entry_details_not_found(self):
        """Test getting details for non-existent entry."""
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        details = self.intervention_tools.get_entry_details("non-existent")
        
        assert details is None


class TestDefaultHandlers:
    """Test default notification handlers."""
    
    def test_log_cleanup_results(self):
        """Test cleanup results logging handler."""
        cleanup_result = CleanupResult(
            entries_processed=10,
            entries_released=5,
            entries_reset=3,
            entries_failed=2,
            entries_dead_lettered=0,
            cleanup_reason=CleanupReason.STALE_CLAIM,
            recovery_actions=[RecoveryAction.RELEASE_CLAIM],
            timestamp=datetime.now(timezone.utc),
            details={}
        )
        
        # Test that it doesn't raise an exception
        log_cleanup_results(cleanup_result)
    
    def test_log_stuck_entries(self):
        """Test stuck entries logging handler."""
        stuck_entries = [
            StuckEntry(
                entry_id="stuck-1",
                stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
                claimed_by=None,
                claimed_at=None,
                last_error="Error",
                retry_count=5,
                processing_status={},
                stuck_reason="High retry count",
                suggested_action=RecoveryAction.MANUAL_REVIEW,
                manual_intervention_required=True
            ),
            StuckEntry(
                entry_id="stuck-2",
                stage=ProcessingStage.SENTIMENT_ANALYSIS,
                claimed_by=None,
                claimed_at=None,
                last_error="Another error",
                retry_count=8,
                processing_status={},
                stuck_reason="Too many retries",
                suggested_action=RecoveryAction.DEAD_LETTER,
                manual_intervention_required=True
            )
        ]
        
        # Test that it doesn't raise an exception
        log_stuck_entries(stuck_entries)
    
    def test_log_stuck_entries_empty(self):
        """Test stuck entries logging with empty list."""
        # Test that it doesn't raise an exception
        log_stuck_entries([])


class TestCleanupSystemIntegration:
    """Integration tests for cleanup system."""
    
    def test_full_cleanup_cycle(self):
        """Test complete cleanup cycle."""
        mock_db_factory = Mock()
        mock_db = Mock(spec=Session)
        mock_db_factory.return_value = mock_db
        
        cleanup_system = StaleWorkCleanupSystem(
            db_session_factory=mock_db_factory,
            cleanup_interval_seconds=1,  # Fast for testing
            stale_claim_timeout_minutes=1,
            stuck_processing_timeout_minutes=2,
            max_retry_age_hours=1
        )
        
        # Setup mocks for all cleanup operations
        mock_db.begin.return_value.__enter__ = Mock(return_value=mock_db)
        mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Mock stale claims
        mock_db.query.return_value.filter.return_value.all.return_value = []
        mock_db.query.return_value.filter.return_value.update.return_value = 0
        mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = []
        mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = []
        
        # Mock health manager
        with patch('backend.app.distributed.stale_work_cleanup.WorkerHealthManager') as mock_health_manager_class:
            mock_health_manager = Mock()
            mock_health_manager_class.return_value = mock_health_manager
            mock_health_manager.detect_stale_workers.return_value = []
            mock_health_manager.recover_failed_workers.return_value = {'workers_marked_failed': 0, 'entries_released': 0}
            
            # Test single cleanup cycle
            cleanup_system._perform_cleanup_cycle()
            
            # Verify database was accessed
            assert mock_db_factory.called
            assert mock_db.close.called
    
    def test_cleanup_with_callbacks(self):
        """Test cleanup system with callbacks."""
        mock_db_factory = Mock()
        mock_db = Mock(spec=Session)
        mock_db_factory.return_value = mock_db
        
        cleanup_system = StaleWorkCleanupSystem(db_session_factory=mock_db_factory)
        
        # Setup callbacks
        cleanup_callback = Mock()
        stuck_callback = Mock()
        
        cleanup_system.add_cleanup_callback(cleanup_callback)
        cleanup_system.add_stuck_entry_callback(stuck_callback)
        
        # Setup mocks
        mock_db.begin.return_value.__enter__ = Mock(return_value=mock_db)
        mock_db.begin.return_value.__exit__ = Mock(return_value=None)
        
        # Mock some stale entries to trigger callback
        stale_entries = [Mock(entry_id="stale-1", claimed_by="worker-001")]
        mock_db.query.return_value.filter.return_value.all.return_value = stale_entries
        mock_db.query.return_value.filter.return_value.update.return_value = 1
        mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = []
        mock_db.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = []
        
        # Mock health manager
        with patch('backend.app.distributed.stale_work_cleanup.WorkerHealthManager') as mock_health_manager_class:
            mock_health_manager = Mock()
            mock_health_manager_class.return_value = mock_health_manager
            mock_health_manager.detect_stale_workers.return_value = []
            
            # Test cleanup cycle
            cleanup_system._perform_cleanup_cycle()
            
            # Verify callback was called
            assert cleanup_callback.called
            
            # Get the cleanup result that was passed to callback
            cleanup_result = cleanup_callback.call_args[0][0]
            assert isinstance(cleanup_result, CleanupResult)
            assert cleanup_result.entries_processed == 1