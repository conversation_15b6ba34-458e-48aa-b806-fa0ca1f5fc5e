import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/widgets/category_news_widget.dart';
import 'package:breakingbright/widgets/search_bar_widget.dart';
import 'package:breakingbright/widgets/responsive_news_grid.dart';
import 'package:breakingbright/utils/category_utils.dart';
import 'package:provider/provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/screens/category/category_view_screen.dart';
import 'package:breakingbright/screens/settings/settings_screen.dart';
import 'package:breakingbright/providers/settings_provider.dart';

class HomeScreen extends StatefulWidget {
  final Function(ThemeMode) setThemeMode;
  final ThemeMode currentThemeMode;

  const HomeScreen({
    super.key,
    required this.setThemeMode,
    required this.currentThemeMode,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ApiService _apiService = ApiService();
  NewsResponse? _newsData;
  bool _isLoading = false;
  int _selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    debugPrint('HomeScreen initialized');
    _loadNews();
  }

  Future<void> _loadNews() async {
    if (_isLoading) return;

    debugPrint('Loading news data...');
    setState(() {
      _isLoading = true;
    });

    try {
      final newsResponse = await _apiService.getHomeScreenData(
        context: context,
        minPositive: 0.7,
        limitPerCategory: 3,
      );
      if (mounted) {
        // Update CategoryUtils with discovered category names from API
        final codeToNameMap = <String, String>{};
        for (final category in newsResponse.categories) {
          codeToNameMap[category.categoryCode] = category.category;
        }
        CategoryUtils.updateCategoryNames(codeToNameMap);

        // Ensure any new categories from the API are included in saved order
        final settingsProvider =
            Provider.of<SettingsProvider>(context, listen: false);
        final availableCodes =
            newsResponse.categories.map((c) => c.categoryCode).toList();
        await settingsProvider.ensureCategoriesIncluded(availableCodes);

        setState(() {
          _newsData = newsResponse;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _selectedIndex == 0, // Only allow pop if we're on the home tab
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _selectedIndex != 0) {
          // If we're not on the home tab, switch to home tab instead of exiting
          setState(() {
            _selectedIndex = 0;
          });
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Row(
            children: [
              SvgPicture.asset(
                'assets/icons/logo.svg',
                height: 32,
                width: 32,
                placeholderBuilder: (BuildContext context) => SizedBox(
                  height: 32,
                  width: 32,
                  child: const Icon(Icons.image),
                ),
              ),
              const SizedBox(width: 8),
              const Text('Breaking Bright'),
            ],
          ),
          actions: [
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () async {
                final settingsChanged = await Navigator.push<bool>(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SettingsScreen(
                      availableCategories: _newsData?.categories,
                    ),
                  ),
                );

                if (settingsChanged == true && mounted) {
                  setState(() {
                    _selectedIndex = 0; // Reset to home tab
                  });
                  await _loadNews();
                }
              },
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                showSearch(
                  context: context,
                  delegate: NewsSearchDelegate(_apiService),
                );
              },
            ),
            IconButton(
              icon: Icon(
                widget.currentThemeMode == ThemeMode.dark
                    ? Icons.light_mode
                    : Icons.dark_mode,
              ),
              onPressed: () {
                widget.setThemeMode(
                  widget.currentThemeMode == ThemeMode.dark
                      ? ThemeMode.light
                      : ThemeMode.dark,
                );
              },
            ),
          ],
        ),
        body: _buildBody(),
        bottomNavigationBar: BottomNavigationBar(
          currentIndex: _selectedIndex,
          onTap: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.favorite),
              label: 'Favoriten',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.category),
              label: 'Kategorien',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_selectedIndex == 1) {
      return _buildFavoritesTab();
    } else if (_selectedIndex == 2) {
      return _buildCategoriesTab();
    } else {
      return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_newsData == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Fehler beim Laden der Nachrichten'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadNews,
              child: const Text('Erneut versuchen'),
            ),
          ],
        ),
      );
    }

    if (_newsData!.categories.isEmpty) {
      return const Center(
        child: Text('Keine Nachrichten verfügbar'),
      );
    }

    // Build a map for quick access
    final categoriesByCode = {
      for (final c in _newsData!.categories) c.categoryCode: c
    };

    return RefreshIndicator(
      onRefresh: _loadNews,
      child: Builder(builder: (context) {
        final settingsProvider = Provider.of<SettingsProvider>(context);
        final availableCodes =
            _newsData!.categories.map((c) => c.categoryCode).toList();
        final preferredOrder = settingsProvider.categoryOrder;
        final sortedCodes = <String>[];
        for (final code in preferredOrder) {
          if (availableCodes.contains(code)) sortedCodes.add(code);
        }
        for (final code in availableCodes) {
          if (!sortedCodes.contains(code)) sortedCodes.add(code);
        }

        return ReorderableListView.builder(
          buildDefaultDragHandles: false,
          itemCount: sortedCodes.length,
          onReorder: (oldIndex, newIndex) async {
            if (newIndex > oldIndex) newIndex -= 1;
            final updated = List<String>.from(sortedCodes);
            final moved = updated.removeAt(oldIndex);
            updated.insert(newIndex, moved);

            // Preserve positions of missing categories during reordering
            final current = settingsProvider.categoryOrder;
            final newOrder = <String>[];
            int visibleIndex = 0;

            for (final code in current) {
              if (updated.contains(code)) {
                // Use the reordered position for visible categories
                newOrder.add(updated[visibleIndex]);
                visibleIndex++;
              } else {
                // Keep missing categories in their original positions
                newOrder.add(code);
              }
            }

            await settingsProvider.setCategoryOrder(newOrder);
          },
          itemBuilder: (context, index) {
            final code = sortedCodes[index];
            final category = categoriesByCode[code];
            if (category == null) {
              return const SizedBox.shrink(key: ValueKey('missing'));
            }
            return Container(
              key: ValueKey(code),
              child: CategoryNewsWidget(
                categoryNews: category,
                leading: ReorderableDragStartListener(
                  index: index,
                  child: Icon(
                    Icons.drag_indicator,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }

  Widget _buildFavoritesTab() {
    return Consumer<FavoritesService>(
      builder: (context, favoritesService, child) {
        final favorites = favoritesService.favorites;

        if (favorites.isEmpty) {
          return const Center(
            child: Text('Keine Favoriten gespeichert'),
          );
        }

        return ResponsiveNewsGrid(
          newsList: favorites,
          showFavoriteButton: true,
        );
      },
    );
  }

  Widget _buildCategoriesTab() {
    // Build category list from known categories and saved order
    final settingsProvider = Provider.of<SettingsProvider>(context);
    final preferredOrder = settingsProvider.categoryOrder;
    final knownCodes = CategoryUtils.allKnownCodes.toList();

    // Combine saved order with any newly discovered categories
    final allCategoryCodes = <String>[];
    for (final code in preferredOrder) {
      allCategoryCodes.add(code);
    }
    for (final code in knownCodes) {
      if (!allCategoryCodes.contains(code)) {
        allCategoryCodes.add(code);
      }
    }

    // Ensure all known categories are included in saved order (async, fire-and-forget)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      settingsProvider.ensureCategoriesIncluded(allCategoryCodes);
    });

    // Create a sorted list based on preferred order, then append any missing
    final sortedCodes = <String>[];
    for (final code in preferredOrder) {
      if (allCategoryCodes.contains(code)) {
        sortedCodes.add(code);
      }
    }
    for (final code in allCategoryCodes) {
      if (!sortedCodes.contains(code)) {
        sortedCodes.add(code);
      }
    }

    return ReorderableListView.builder(
      buildDefaultDragHandles: false,
      itemCount: sortedCodes.length,
      onReorder: (oldIndex, newIndex) async {
        if (newIndex > oldIndex) newIndex -= 1;
        final updated = List<String>.from(sortedCodes);
        final moved = updated.removeAt(oldIndex);
        updated.insert(newIndex, moved);
        await settingsProvider.setCategoryOrder(updated);
      },
      itemBuilder: (context, index) {
        final code = sortedCodes[index];
        final name = CategoryUtils.getDisplayName(code);
        return ListTile(
          key: ValueKey(code),
          leading: ReorderableDragStartListener(
            index: index,
            child: Icon(
              Icons.drag_indicator,
              color: Theme.of(context).primaryColor,
            ),
          ),
          title: Text(name),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CategoryViewScreen(
                  categoryCode: code,
                  categoryName: name,
                ),
              ),
            );
          },
        );
      },
    );
  }
}
