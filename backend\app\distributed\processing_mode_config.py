"""
Configuration management for switching between monolithic and distributed processing modes.

This module provides utilities for configuring and switching between the legacy
monolithic processing approach and the new distributed worker approach.
"""

import os
import logging
from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


class ProcessingMode(Enum):
    """Processing mode enumeration."""
    MONOLITHIC = "monolithic"
    DISTRIBUTED = "distributed"


@dataclass
class ProcessingModeConfig:
    """Configuration for processing mode switching."""
    
    mode: ProcessingMode
    database_url: str
    worker_id_prefix: str = "default"
    batch_size: int = 10
    max_workers_per_stage: int = 1
    enable_monitoring: bool = True
    
    # Distributed mode specific settings
    heartbeat_interval: int = 30
    claim_timeout: int = 300
    max_retries: int = 3
    
    # Legacy mode specific settings
    legacy_thread_timeout: int = 3600
    
    @classmethod
    def from_environment(cls) -> 'ProcessingModeConfig':
        """
        Create configuration from environment variables.
        
        Environment variables:
        - PROCESSING_MODE: "monolithic" or "distributed" (default: "monolithic")
        - DATABASE_URL: Database connection URL
        - WORKER_ID_PREFIX: Prefix for worker IDs (default: "default")
        - BATCH_SIZE: Batch size for processing (default: 10)
        - MAX_WORKERS_PER_STAGE: Maximum workers per stage (default: 1)
        - ENABLE_MONITORING: Enable monitoring (default: True)
        - HEARTBEAT_INTERVAL: Heartbeat interval in seconds (default: 30)
        - CLAIM_TIMEOUT: Claim timeout in seconds (default: 300)
        - MAX_RETRIES: Maximum retries for failed items (default: 3)
        - LEGACY_THREAD_TIMEOUT: Timeout for legacy threads (default: 3600)
        
        Returns:
            ProcessingModeConfig instance
        """
        mode_str = os.getenv('PROCESSING_MODE', 'monolithic').lower()
        try:
            mode = ProcessingMode(mode_str)
        except ValueError:
            logger.warning(f"Invalid processing mode '{mode_str}', defaulting to monolithic")
            mode = ProcessingMode.MONOLITHIC
        
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        return cls(
            mode=mode,
            database_url=database_url,
            worker_id_prefix=os.getenv('WORKER_ID_PREFIX', 'default'),
            batch_size=int(os.getenv('BATCH_SIZE', '10')),
            max_workers_per_stage=int(os.getenv('MAX_WORKERS_PER_STAGE', '1')),
            enable_monitoring=os.getenv('ENABLE_MONITORING', 'true').lower() == 'true',
            heartbeat_interval=int(os.getenv('HEARTBEAT_INTERVAL', '30')),
            claim_timeout=int(os.getenv('CLAIM_TIMEOUT', '300')),
            max_retries=int(os.getenv('MAX_RETRIES', '3')),
            legacy_thread_timeout=int(os.getenv('LEGACY_THREAD_TIMEOUT', '3600'))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'mode': self.mode.value,
            'database_url': self.database_url,
            'worker_id_prefix': self.worker_id_prefix,
            'batch_size': self.batch_size,
            'max_workers_per_stage': self.max_workers_per_stage,
            'enable_monitoring': self.enable_monitoring,
            'heartbeat_interval': self.heartbeat_interval,
            'claim_timeout': self.claim_timeout,
            'max_retries': self.max_retries,
            'legacy_thread_timeout': self.legacy_thread_timeout
        }


class ProcessingModeManager:
    """
    Manager for switching between processing modes and creating appropriate processors.
    """
    
    def __init__(self, config: ProcessingModeConfig):
        """
        Initialize the processing mode manager.
        
        Args:
            config: Processing mode configuration
        """
        self.config = config
        self._processor = None
        
        logger.info(f"Initialized processing mode manager with mode: {config.mode.value}")
    
    def get_processor(self):
        """
        Get the appropriate processor based on the configured mode.
        
        Returns:
            Either a LegacyThreadWrapper (for distributed mode) or the original
            monolithic processor (for monolithic mode)
        """
        if self._processor is None:
            if self.config.mode == ProcessingMode.DISTRIBUTED:
                from backend.app.distributed.legacy_thread_wrapper import LegacyThreadWrapper
                self._processor = LegacyThreadWrapper(
                    database_url=self.config.database_url,
                    worker_id_prefix=self.config.worker_id_prefix
                )
                logger.info("Created distributed processor (LegacyThreadWrapper)")
            else:
                # For monolithic mode, we would return the original NewsAggregator
                # This is a placeholder - in real implementation, this would import
                # and return the original monolithic processor
                logger.info("Using monolithic processor mode")
                raise NotImplementedError(
                    "Monolithic mode processor not implemented in this wrapper. "
                    "This should return the original NewsAggregator instance."
                )
        
        return self._processor
    
    def switch_mode(self, new_mode: ProcessingMode):
        """
        Switch to a different processing mode.
        
        Args:
            new_mode: New processing mode to switch to
        """
        if new_mode == self.config.mode:
            logger.info(f"Already in {new_mode.value} mode")
            return
        
        logger.info(f"Switching from {self.config.mode.value} to {new_mode.value} mode")
        
        # Clean up existing processor
        if self._processor is not None:
            if hasattr(self._processor, 'stop_all_workers'):
                self._processor.stop_all_workers()
            self._processor = None
        
        # Update configuration
        self.config.mode = new_mode
        
        logger.info(f"Successfully switched to {new_mode.value} mode")
    
    def validate_configuration(self) -> bool:
        """
        Validate the current configuration.
        
        Returns:
            True if configuration is valid
        """
        try:
            # Check database URL
            if not self.config.database_url:
                logger.error("Database URL is required")
                return False
            
            # Check batch size
            if self.config.batch_size <= 0:
                logger.error("Batch size must be positive")
                return False
            
            # Check worker limits
            if self.config.max_workers_per_stage <= 0:
                logger.error("Max workers per stage must be positive")
                return False
            
            # Check timeouts
            if self.config.heartbeat_interval <= 0:
                logger.error("Heartbeat interval must be positive")
                return False
            
            if self.config.claim_timeout <= 0:
                logger.error("Claim timeout must be positive")
                return False
            
            logger.info("Configuration validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current status of the processing mode manager.
        
        Returns:
            Dictionary with status information
        """
        status = {
            'mode': self.config.mode.value,
            'configuration': self.config.to_dict(),
            'processor_initialized': self._processor is not None
        }
        
        # Add processor-specific status if available
        if self._processor is not None and hasattr(self._processor, 'get_processing_status'):
            status['processor_status'] = self._processor.get_processing_status()
        
        return status
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        if self._processor is not None and hasattr(self._processor, 'stop_all_workers'):
            self._processor.stop_all_workers()