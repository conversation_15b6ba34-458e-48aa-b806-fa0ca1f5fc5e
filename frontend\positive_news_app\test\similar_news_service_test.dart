import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/services/similar_news_service.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

@GenerateMocks([ApiService])
import 'service_test.mocks.dart';

void main() {
  group('SimilarNewsService Detailed Tests', () {
    late SimilarNewsService service;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      service = SimilarNewsService(mockApiService);
    });

    group('Basic Functionality', () {
      test('initial state is empty', () {
        expect(service.getSimilarFlag('any_id'), null);
        expect(service.isLoaded('any_id'), false);
        expect(service.isLoading('any_id'), false);
      });

      test('loadSimilarFlags updates state correctly', () async {
        final entryIds = ['id1', 'id2', 'id3'];
        final mockResponse = {
          'id1': true,
          'id2': false,
          'id3': true,
        };

        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => mockResponse);

        await service.loadSimilarFlags(entryIds);

        expect(service.getSimilarFlag('id1'), true);
        expect(service.getSimilarFlag('id2'), false);
        expect(service.getSimilarFlag('id3'), true);
        expect(service.isLoaded('id1'), true);
        expect(service.isLoaded('id2'), true);
        expect(service.isLoaded('id3'), true);
        expect(service.isLoading('id1'), false);
      });

      test('loadSimilarFlag works for single entry', () async {
        when(mockApiService.checkSimilarNews(['single_id']))
            .thenAnswer((_) async => {'single_id': true});

        final result = await service.loadSimilarFlag('single_id');

        expect(result, true);
        expect(service.getSimilarFlag('single_id'), true);
        expect(service.isLoaded('single_id'), true);
      });
    });

    group('Caching and Deduplication', () {
      test('avoids duplicate API calls for same entries', () async {
        final entryIds = ['id1', 'id2'];
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => {'id1': true, 'id2': false});

        // First call
        await service.loadSimilarFlags(entryIds);
        
        // Second call should not trigger API
        await service.loadSimilarFlags(entryIds);

        verify(mockApiService.checkSimilarNews(entryIds)).called(1);
      });

      test('only loads unloaded entries in mixed batch', () async {
        // Pre-load some entries
        when(mockApiService.checkSimilarNews(['id1']))
            .thenAnswer((_) async => {'id1': true});
        await service.loadSimilarFlags(['id1']);

        // Now load a batch with mixed loaded/unloaded
        when(mockApiService.checkSimilarNews(['id2', 'id3']))
            .thenAnswer((_) async => {'id2': false, 'id3': true});

        await service.loadSimilarFlags(['id1', 'id2', 'id3']);

        // Should only call API for unloaded entries
        verify(mockApiService.checkSimilarNews(['id1'])).called(1);
        verify(mockApiService.checkSimilarNews(['id2', 'id3'])).called(1);
        verifyNever(mockApiService.checkSimilarNews(['id1', 'id2', 'id3']));
      });

      test('handles empty entry list gracefully', () async {
        await service.loadSimilarFlags([]);
        
        verifyNever(mockApiService.checkSimilarNews(any));
      });
    });

    group('Loading State Management', () {
      test('tracks loading state correctly', () async {
        final entryIds = ['id1'];
        
        // Create a completer to control when the API call completes
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return {'id1': true};
        });

        // Start loading
        final future = service.loadSimilarFlags(entryIds);
        
        // Should be loading
        expect(service.isLoading('id1'), true);
        expect(service.isLoaded('id1'), false);

        // Wait for completion
        await future;

        // Should be loaded, not loading
        expect(service.isLoading('id1'), false);
        expect(service.isLoaded('id1'), true);
      });

      test('prevents concurrent loads of same entries', () async {
        final entryIds = ['id1'];
        
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 50));
          return {'id1': true};
        });

        // Start two concurrent loads
        final future1 = service.loadSimilarFlags(entryIds);
        final future2 = service.loadSimilarFlags(entryIds);

        await Future.wait([future1, future2]);

        // Should only call API once
        verify(mockApiService.checkSimilarNews(entryIds)).called(1);
      });
    });

    group('News Item Updates', () {
      test('updateNewsWithSimilarFlags creates new instances with updated flags', () async {
        // Setup cached flags
        when(mockApiService.checkSimilarNews(['id1', 'id2']))
            .thenAnswer((_) async => {'id1': true, 'id2': false});
        await service.loadSimilarFlags(['id1', 'id2']);

        final originalNews = [
          EntryWithSource(
            entryId: 'id1',
            title: 'News 1',
            link: 'https://example.com/1',
            source: 'source1',
            published: DateTime.now(),
            hasSimilar: null,
          ),
          EntryWithSource(
            entryId: 'id2',
            title: 'News 2',
            link: 'https://example.com/2',
            source: 'source2',
            published: DateTime.now(),
            hasSimilar: null,
          ),
        ];

        final updatedNews = service.updateNewsWithSimilarFlags(originalNews);

        // Should create new instances with updated flags
        expect(updatedNews[0].hasSimilar, true);
        expect(updatedNews[1].hasSimilar, false);
        
        // Original instances should be unchanged
        expect(originalNews[0].hasSimilar, null);
        expect(originalNews[1].hasSimilar, null);
        
        // Should be different instances
        expect(identical(originalNews[0], updatedNews[0]), false);
        expect(identical(originalNews[1], updatedNews[1]), false);
      });

      test('updateNewsWithSimilarFlags preserves items without cached flags', () async {
        final originalNews = [
          EntryWithSource(
            entryId: 'uncached_id',
            title: 'Uncached News',
            link: 'https://example.com/uncached',
            source: 'source',
            published: DateTime.now(),
            hasSimilar: null,
          ),
        ];

        final updatedNews = service.updateNewsWithSimilarFlags(originalNews);

        // Should return same instances for uncached items
        expect(identical(originalNews[0], updatedNews[0]), true);
        expect(updatedNews[0].hasSimilar, null);
      });

      test('preloadSimilarFlags only loads null hasSimilar items', () async {
        final newsItems = [
          EntryWithSource(
            entryId: 'null_id',
            title: 'Null News',
            link: 'https://example.com/null',
            source: 'source',
            published: DateTime.now(),
            hasSimilar: null, // Should be loaded
          ),
          EntryWithSource(
            entryId: 'true_id',
            title: 'True News',
            link: 'https://example.com/true',
            source: 'source',
            published: DateTime.now(),
            hasSimilar: true, // Should be skipped
          ),
          EntryWithSource(
            entryId: 'false_id',
            title: 'False News',
            link: 'https://example.com/false',
            source: 'source',
            published: DateTime.now(),
            hasSimilar: false, // Should be skipped
          ),
        ];

        when(mockApiService.checkSimilarNews(['null_id']))
            .thenAnswer((_) async => {'null_id': true});

        await service.preloadSimilarFlags(newsItems);

        verify(mockApiService.checkSimilarNews(['null_id'])).called(1);
        expect(service.isLoaded('null_id'), true);
        expect(service.isLoaded('true_id'), false);
        expect(service.isLoaded('false_id'), false);
      });
    });

    group('Error Handling', () {
      test('handles API errors gracefully', () async {
        final entryIds = ['error_id'];
        
        when(mockApiService.checkSimilarNews(entryIds))
            .thenThrow(Exception('Network error'));

        // Should not throw
        await service.loadSimilarFlags(entryIds);

        // Should mark as loaded to prevent infinite retries
        expect(service.isLoaded('error_id'), true);
        expect(service.getSimilarFlag('error_id'), null);
        expect(service.isLoading('error_id'), false);
      });

      test('continues working after error', () async {
        // First call fails
        when(mockApiService.checkSimilarNews(['error_id']))
            .thenThrow(Exception('Network error'));
        await service.loadSimilarFlags(['error_id']);

        // Second call with different ID should work
        when(mockApiService.checkSimilarNews(['success_id']))
            .thenAnswer((_) async => {'success_id': true});
        await service.loadSimilarFlags(['success_id']);

        expect(service.isLoaded('error_id'), true);
        expect(service.getSimilarFlag('error_id'), null);
        expect(service.isLoaded('success_id'), true);
        expect(service.getSimilarFlag('success_id'), true);
      });
    });

    group('Clear Functionality', () {
      test('clear removes all cached data', () async {
        // Load some data
        when(mockApiService.checkSimilarNews(['id1', 'id2']))
            .thenAnswer((_) async => {'id1': true, 'id2': false});
        await service.loadSimilarFlags(['id1', 'id2']);

        expect(service.isLoaded('id1'), true);
        expect(service.getSimilarFlag('id1'), true);

        // Clear
        service.clear();

        expect(service.isLoaded('id1'), false);
        expect(service.getSimilarFlag('id1'), null);
        expect(service.isLoading('id1'), false);
        expect(service.isLoaded('id2'), false);
        expect(service.getSimilarFlag('id2'), null);
      });

      test('can load data again after clear', () async {
        // Load, clear, load again
        when(mockApiService.checkSimilarNews(['id1']))
            .thenAnswer((_) async => {'id1': true});
        
        await service.loadSimilarFlags(['id1']);
        service.clear();
        await service.loadSimilarFlags(['id1']);

        expect(service.getSimilarFlag('id1'), true);
        verify(mockApiService.checkSimilarNews(['id1'])).called(2);
      });
    });

    group('Edge Cases', () {
      test('handles partial API responses', () async {
        final entryIds = ['id1', 'id2', 'id3'];
        
        // API returns only partial data
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => {'id1': true}); // Missing id2 and id3

        await service.loadSimilarFlags(entryIds);

        expect(service.getSimilarFlag('id1'), true);
        expect(service.getSimilarFlag('id2'), null);
        expect(service.getSimilarFlag('id3'), null);
        
        // All should be marked as loaded to prevent retries
        expect(service.isLoaded('id1'), true);
        expect(service.isLoaded('id2'), true);
        expect(service.isLoaded('id3'), true);
      });

      test('handles empty API responses', () async {
        final entryIds = ['id1'];
        
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => {});

        await service.loadSimilarFlags(entryIds);

        expect(service.getSimilarFlag('id1'), null);
        expect(service.isLoaded('id1'), true);
      });
    });
  });
}