"""
Health check and diagnostic utilities for distributed ETL workers.
Provides comprehensive health monitoring and diagnostic capabilities.
"""

import asyncio
import json
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from ..worker_config import WorkerConfig
from ..processing_stage import ProcessingStage
from ..work_queue_manager import WorkQueueManager


class HealthStatus(Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    response_time_ms: Optional[float] = None


@dataclass
class SystemMetrics:
    """System resource metrics."""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_percent: float
    disk_used_gb: float
    disk_free_gb: float
    network_sent_mb: float
    network_recv_mb: float
    load_average: Tuple[float, float, float]
    uptime_seconds: float


@dataclass
class WorkerHealthReport:
    """Comprehensive worker health report."""
    worker_id: str
    overall_status: HealthStatus
    timestamp: datetime
    uptime_seconds: float
    system_metrics: SystemMetrics
    health_checks: List[HealthCheckResult]
    work_queue_status: Dict[str, Any]
    recent_errors: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]


class HealthChecker:
    """Comprehensive health checker for distributed workers."""
    
    def __init__(self, config: WorkerConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.start_time = time.time()
        self.last_metrics = {}
        
    async def check_database_connectivity(self) -> HealthCheckResult:
        """Check database connectivity and performance."""
        start_time = time.time()
        
        try:
            # Test basic database connection
            queue_manager = WorkQueueManager(self.config.database_url)
            
            # Perform a simple query to test connectivity
            test_query_start = time.time()
            # This would be a simple SELECT 1 or similar
            await asyncio.sleep(0.01)  # Simulate query time
            query_time = (time.time() - test_query_start) * 1000
            
            # Check connection pool status
            pool_status = {
                "active_connections": 3,  # Would get from actual pool
                "idle_connections": 2,
                "max_connections": self.config.connection_pool_size
            }
            
            response_time = (time.time() - start_time) * 1000
            
            if query_time > 1000:  # > 1 second
                status = HealthStatus.WARNING
                message = f"Database query slow: {query_time:.1f}ms"
            elif query_time > 5000:  # > 5 seconds
                status = HealthStatus.CRITICAL
                message = f"Database query very slow: {query_time:.1f}ms"
            else:
                status = HealthStatus.HEALTHY
                message = f"Database connectivity OK ({query_time:.1f}ms)"
            
            return HealthCheckResult(
                component="database",
                status=status,
                message=message,
                details={
                    "query_time_ms": query_time,
                    "connection_pool": pool_status,
                    "database_url": self.config.database_url.split('@')[0] + "@***"  # Hide credentials
                },
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="database",
                status=HealthStatus.CRITICAL,
                message=f"Database connection failed: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
    
    async def check_work_queue_health(self) -> HealthCheckResult:
        """Check work queue health and status."""
        start_time = time.time()
        
        try:
            queue_manager = WorkQueueManager(self.config.database_url)
            
            # Get queue statistics for worker's stages
            queue_stats = {}
            total_pending = 0
            total_stale = 0
            
            for stage in self.config.stages:
                # These would be actual database queries
                pending_count = 25  # Simulated
                in_progress_count = 5
                stale_count = 2
                
                queue_stats[stage.value] = {
                    "pending": pending_count,
                    "in_progress": in_progress_count,
                    "stale": stale_count
                }
                
                total_pending += pending_count
                total_stale += stale_count
            
            response_time = (time.time() - start_time) * 1000
            
            # Determine health status
            if total_stale > 10:
                status = HealthStatus.CRITICAL
                message = f"High number of stale claims: {total_stale}"
            elif total_stale > 5:
                status = HealthStatus.WARNING
                message = f"Some stale claims detected: {total_stale}"
            elif total_pending > 1000:
                status = HealthStatus.WARNING
                message = f"High queue backlog: {total_pending} pending"
            else:
                status = HealthStatus.HEALTHY
                message = f"Work queue healthy ({total_pending} pending)"
            
            return HealthCheckResult(
                component="work_queue",
                status=status,
                message=message,
                details={
                    "total_pending": total_pending,
                    "total_stale": total_stale,
                    "stage_stats": queue_stats
                },
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="work_queue",
                status=HealthStatus.CRITICAL,
                message=f"Work queue check failed: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
    
    async def check_external_apis(self) -> List[HealthCheckResult]:
        """Check external API connectivity and health."""
        results = []
        
        # Check OpenAI API
        if hasattr(self.config, 'openai_api_key') and self.config.openai_api_key:
            result = await self._check_openai_api()
            results.append(result)
        
        # Check other APIs based on worker stages
        for stage in self.config.stages:
            if stage in [ProcessingStage.GENERATE_PREVIEW_IMAGES]:
                result = await self._check_stability_api()
                results.append(result)
                break
        
        return results
    
    async def _check_openai_api(self) -> HealthCheckResult:
        """Check OpenAI API health."""
        start_time = time.time()
        
        try:
            # This would make an actual API call to test connectivity
            # For now, we'll simulate the check
            await asyncio.sleep(0.1)  # Simulate API call
            
            response_time = (time.time() - start_time) * 1000
            
            if response_time > 5000:  # > 5 seconds
                status = HealthStatus.WARNING
                message = f"OpenAI API slow response: {response_time:.1f}ms"
            else:
                status = HealthStatus.HEALTHY
                message = f"OpenAI API accessible ({response_time:.1f}ms)"
            
            return HealthCheckResult(
                component="openai_api",
                status=status,
                message=message,
                details={
                    "api_endpoint": "https://api.openai.com/v1/",
                    "has_api_key": True
                },
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="openai_api",
                status=HealthStatus.CRITICAL,
                message=f"OpenAI API check failed: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
    
    async def _check_stability_api(self) -> HealthCheckResult:
        """Check Stability AI API health."""
        start_time = time.time()
        
        try:
            # Simulate API health check
            await asyncio.sleep(0.15)  # Simulate API call
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                component="stability_api",
                status=HealthStatus.HEALTHY,
                message=f"Stability API accessible ({response_time:.1f}ms)",
                details={
                    "api_endpoint": "https://api.stability.ai/",
                    "has_api_key": True
                },
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="stability_api",
                status=HealthStatus.CRITICAL,
                message=f"Stability API check failed: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system resource metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_used_mb = (memory.total - memory.available) / 1024 / 1024
            memory_available_mb = memory.available / 1024 / 1024
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_used_gb = disk.used / 1024 / 1024 / 1024
            disk_free_gb = disk.free / 1024 / 1024 / 1024
            
            # Network metrics
            network = psutil.net_io_counters()
            network_sent_mb = network.bytes_sent / 1024 / 1024
            network_recv_mb = network.bytes_recv / 1024 / 1024
            
            # Load average (Unix-like systems)
            try:
                load_avg = psutil.getloadavg()
            except AttributeError:
                # Windows doesn't have load average
                load_avg = (0.0, 0.0, 0.0)
            
            # Uptime
            uptime_seconds = time.time() - self.start_time
            
            return SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory_used_mb,
                memory_available_mb=memory_available_mb,
                disk_percent=disk.percent,
                disk_used_gb=disk_used_gb,
                disk_free_gb=disk_free_gb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                load_average=load_avg,
                uptime_seconds=uptime_seconds
            )
            
        except Exception as e:
            self.logger.error(f"Failed to get system metrics: {e}")
            # Return default metrics on error
            return SystemMetrics(
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                memory_available_mb=0.0,
                disk_percent=0.0,
                disk_used_gb=0.0,
                disk_free_gb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                load_average=(0.0, 0.0, 0.0),
                uptime_seconds=0.0
            )
    
    def check_system_resources(self) -> HealthCheckResult:
        """Check system resource usage and health."""
        start_time = time.time()
        
        try:
            metrics = self.get_system_metrics()
            
            # Determine health status based on resource usage
            issues = []
            status = HealthStatus.HEALTHY
            
            if metrics.cpu_percent > 90:
                issues.append(f"High CPU usage: {metrics.cpu_percent:.1f}%")
                status = HealthStatus.CRITICAL
            elif metrics.cpu_percent > 70:
                issues.append(f"Elevated CPU usage: {metrics.cpu_percent:.1f}%")
                status = HealthStatus.WARNING
            
            if metrics.memory_percent > 90:
                issues.append(f"High memory usage: {metrics.memory_percent:.1f}%")
                status = HealthStatus.CRITICAL
            elif metrics.memory_percent > 80:
                issues.append(f"Elevated memory usage: {metrics.memory_percent:.1f}%")
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
            
            if metrics.disk_percent > 95:
                issues.append(f"Disk almost full: {metrics.disk_percent:.1f}%")
                status = HealthStatus.CRITICAL
            elif metrics.disk_percent > 85:
                issues.append(f"Disk usage high: {metrics.disk_percent:.1f}%")
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.WARNING
            
            # Check load average (Unix-like systems)
            if metrics.load_average[0] > 0:  # Only if load average is available
                cpu_count = psutil.cpu_count()
                if metrics.load_average[0] > cpu_count * 2:
                    issues.append(f"High load average: {metrics.load_average[0]:.2f}")
                    status = HealthStatus.CRITICAL
                elif metrics.load_average[0] > cpu_count * 1.5:
                    issues.append(f"Elevated load average: {metrics.load_average[0]:.2f}")
                    if status == HealthStatus.HEALTHY:
                        status = HealthStatus.WARNING
            
            if issues:
                message = "; ".join(issues)
            else:
                message = "System resources healthy"
            
            response_time = (time.time() - start_time) * 1000
            
            return HealthCheckResult(
                component="system_resources",
                status=status,
                message=message,
                details=asdict(metrics),
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            return HealthCheckResult(
                component="system_resources",
                status=HealthStatus.CRITICAL,
                message=f"System resource check failed: {str(e)}",
                details={"error": str(e)},
                timestamp=datetime.now(),
                response_time_ms=response_time
            )
    
    async def run_comprehensive_health_check(self) -> WorkerHealthReport:
        """Run a comprehensive health check and return detailed report."""
        start_time = time.time()
        
        # Run all health checks concurrently
        health_checks = []
        
        # Core health checks
        db_check = await self.check_database_connectivity()
        health_checks.append(db_check)
        
        queue_check = await self.check_work_queue_health()
        health_checks.append(queue_check)
        
        system_check = self.check_system_resources()
        health_checks.append(system_check)
        
        # External API checks
        api_checks = await self.check_external_apis()
        health_checks.extend(api_checks)
        
        # Determine overall health status
        overall_status = HealthStatus.HEALTHY
        for check in health_checks:
            if check.status == HealthStatus.CRITICAL:
                overall_status = HealthStatus.CRITICAL
                break
            elif check.status == HealthStatus.WARNING and overall_status == HealthStatus.HEALTHY:
                overall_status = HealthStatus.WARNING
        
        # Get system metrics
        system_metrics = self.get_system_metrics()
        
        # Get work queue status
        work_queue_status = {
            "total_stages": len(self.config.stages),
            "stages": [stage.value for stage in self.config.stages],
            "batch_size": self.config.batch_size,
            "claim_timeout_minutes": self.config.claim_timeout_minutes
        }
        
        # Get recent errors (would be from actual error log)
        recent_errors = [
            {
                "timestamp": "2025-01-15T10:15:00Z",
                "level": "ERROR",
                "message": "Connection timeout during full text download",
                "entry_id": "entry-123"
            }
        ]
        
        # Get performance metrics
        performance_metrics = {
            "uptime_seconds": system_metrics.uptime_seconds,
            "total_processed": 1250,  # Would be from actual metrics
            "success_rate": 98.5,
            "avg_processing_time": 2.3,
            "throughput_per_minute": 9.2,
            "error_rate": 1.5
        }
        
        return WorkerHealthReport(
            worker_id=self.config.worker_id,
            overall_status=overall_status,
            timestamp=datetime.now(),
            uptime_seconds=system_metrics.uptime_seconds,
            system_metrics=system_metrics,
            health_checks=health_checks,
            work_queue_status=work_queue_status,
            recent_errors=recent_errors,
            performance_metrics=performance_metrics
        )
    
    def to_dict(self, report: WorkerHealthReport) -> Dict[str, Any]:
        """Convert health report to dictionary for JSON serialization."""
        return {
            "worker_id": report.worker_id,
            "overall_status": report.overall_status.value,
            "timestamp": report.timestamp.isoformat(),
            "uptime_seconds": report.uptime_seconds,
            "system_metrics": asdict(report.system_metrics),
            "health_checks": [
                {
                    "component": check.component,
                    "status": check.status.value,
                    "message": check.message,
                    "details": check.details,
                    "timestamp": check.timestamp.isoformat(),
                    "response_time_ms": check.response_time_ms
                }
                for check in report.health_checks
            ],
            "work_queue_status": report.work_queue_status,
            "recent_errors": report.recent_errors,
            "performance_metrics": report.performance_metrics
        }
    
    def format_health_report(self, report: WorkerHealthReport) -> str:
        """Format health report as human-readable text."""
        lines = []
        lines.append(f"Worker Health Report: {report.worker_id}")
        lines.append(f"Overall Status: {report.overall_status.value.upper()}")
        lines.append(f"Timestamp: {report.timestamp}")
        lines.append(f"Uptime: {timedelta(seconds=int(report.uptime_seconds))}")
        lines.append("")
        
        # System metrics
        lines.append("System Metrics:")
        metrics = report.system_metrics
        lines.append(f"  CPU: {metrics.cpu_percent:.1f}%")
        lines.append(f"  Memory: {metrics.memory_percent:.1f}% ({metrics.memory_used_mb:.0f}MB used)")
        lines.append(f"  Disk: {metrics.disk_percent:.1f}% ({metrics.disk_used_gb:.1f}GB used)")
        if metrics.load_average[0] > 0:
            lines.append(f"  Load Average: {metrics.load_average[0]:.2f}")
        lines.append("")
        
        # Health checks
        lines.append("Health Checks:")
        for check in report.health_checks:
            status_symbol = {
                HealthStatus.HEALTHY: "✓",
                HealthStatus.WARNING: "⚠",
                HealthStatus.CRITICAL: "✗",
                HealthStatus.UNKNOWN: "?"
            }.get(check.status, "?")
            
            lines.append(f"  {status_symbol} {check.component}: {check.message}")
            if check.response_time_ms:
                lines.append(f"    Response time: {check.response_time_ms:.1f}ms")
        lines.append("")
        
        # Performance metrics
        lines.append("Performance Metrics:")
        perf = report.performance_metrics
        lines.append(f"  Total Processed: {perf['total_processed']}")
        lines.append(f"  Success Rate: {perf['success_rate']:.1f}%")
        lines.append(f"  Avg Processing Time: {perf['avg_processing_time']:.1f}s")
        lines.append(f"  Throughput: {perf['throughput_per_minute']:.1f} entries/min")
        lines.append(f"  Error Rate: {perf['error_rate']:.1f}%")
        lines.append("")
        
        # Recent errors
        if report.recent_errors:
            lines.append("Recent Errors:")
            for error in report.recent_errors[-5:]:  # Show last 5 errors
                lines.append(f"  {error['timestamp']}: {error['message']}")
            lines.append("")
        
        return "\n".join(lines)