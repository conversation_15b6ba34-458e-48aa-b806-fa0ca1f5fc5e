"""
Integration tests for optimized work queue queries.

Tests the optimized database operations with real database connections
to verify performance and correctness under concurrent access scenarios.
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone, timedelta
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from backend.app.models.models import Base, Entry
from backend.app.distributed.optimized_queries import OptimizedWorkQueries
from backend.app.distributed.processing_stage import (
    ProcessingStage, ProcessingStatus, ProcessingStatusValue
)


class TestOptimizedQueries:
    """Integration tests for optimized work queue queries."""
    
    @pytest.fixture(scope="function")
    def db_session(self):
        """Create an in-memory SQLite database for testing."""
        # Create in-memory SQLite database
        engine = create_engine(
            "sqlite:///:memory:",
            poolclass=StaticPool,
            connect_args={"check_same_thread": False},
            echo=False
        )
        
        # Create tables
        Base.metadata.create_all(engine)
        
        # Create session
        SessionLocal = sessionmaker(bind=engine)
        session = SessionLocal()
        
        yield session
        
        session.close()
    
    @pytest.fixture
    def optimized_queries(self, db_session):
        """Create OptimizedWorkQueries instance."""
        return OptimizedWorkQueries(db_session)
    
    def create_test_entry(self, 
                         db_session,
                         entry_id: str,
                         processing_status: ProcessingStatus = None,
                         claimed_by: str = None,
                         claimed_at: datetime = None,
                         source: str = "test-source") -> Entry:
        """Create a test entry in the database."""
        entry = Entry(
            entry_id=entry_id,
            title=f"Test Entry {entry_id}",
            link=f"https://example.com/{entry_id}",
            source=source,
            description=f"Test description for {entry_id}",
            published=datetime.now(timezone.utc),
            processing_status=processing_status.to_json() if processing_status else None,
            claimed_by=claimed_by,
            claimed_at=claimed_at,
            retry_count=0
        )
        
        db_session.add(entry)
        db_session.commit()
        return entry
    
    def test_find_claimable_entries_optimized_basic(self, db_session, optimized_queries):
        """Test basic functionality of optimized claimable entry finding."""
        # Create test entries
        self.create_test_entry(db_session, "entry1")  # Available
        self.create_test_entry(db_session, "entry2")  # Available
        
        # Create entry with completed status
        completed_status = ProcessingStatus()
        completed_status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "other-worker")
        self.create_test_entry(db_session, "entry3", completed_status)  # Not available
        
        # Find claimable entries
        claimable = optimized_queries.find_claimable_entries_optimized(
            ProcessingStage.DOWNLOAD_FULL_TEXT, 5, 3
        )
        
        # Should return only available entries
        assert len(claimable) == 2
        entry_ids = [entry.entry_id for entry in claimable]
        assert "entry1" in entry_ids
        assert "entry2" in entry_ids
        assert "entry3" not in entry_ids
    
    def test_find_claimable_entries_respects_batch_size(self, db_session, optimized_queries):
        """Test that batch size is respected."""
        # Create more entries than batch size
        for i in range(10):
            self.create_test_entry(db_session, f"entry{i}")
        
        # Find claimable entries with small batch size
        claimable = optimized_queries.find_claimable_entries_optimized(
            ProcessingStage.DOWNLOAD_FULL_TEXT, 3, 3
        )
        
        # Should return only batch size number of entries
        assert len(claimable) == 3
    
    def test_find_claimable_entries_excludes_sources(self, db_session, optimized_queries):
        """Test source exclusion functionality."""
        # Create entries from different sources
        self.create_test_entry(db_session, "entry1", source="source1")
        self.create_test_entry(db_session, "entry2", source="source2")
        self.create_test_entry(db_session, "entry3", source="excluded-source")
        
        # Find claimable entries excluding specific source
        claimable = optimized_queries.find_claimable_entries_optimized(
            ProcessingStage.DOWNLOAD_FULL_TEXT, 5, 3, exclude_sources=["excluded-source"]
        )
        
        # Should exclude entries from excluded source
        entry_ids = [entry.entry_id for entry in claimable]
        assert "entry1" in entry_ids
        assert "entry2" in entry_ids
        assert "entry3" not in entry_ids
    
    def test_find_claimable_entries_includes_failed_with_retries(self, db_session, optimized_queries):
        """Test that failed entries with remaining retries are included."""
        # Create entry with failed status but retries remaining
        failed_status = ProcessingStatus()
        failed_status.mark_stage_failed(
            ProcessingStage.DOWNLOAD_FULL_TEXT, "worker1", "Test error"
        )
        self.create_test_entry(db_session, "failed_entry", failed_status)
        
        # Find claimable entries
        claimable = optimized_queries.find_claimable_entries_optimized(
            ProcessingStage.DOWNLOAD_FULL_TEXT, 5, 3
        )
        
        # Should include failed entry with retries remaining
        entry_ids = [entry.entry_id for entry in claimable]
        assert "failed_entry" in entry_ids
    
    def test_find_claimable_entries_excludes_failed_max_retries(self, db_session, optimized_queries):
        """Test that failed entries with max retries are excluded."""
        # Create entry with failed status and max retries
        failed_status = ProcessingStatus()
        for _ in range(3):  # Fail 3 times (max retries)
            failed_status.mark_stage_failed(
                ProcessingStage.DOWNLOAD_FULL_TEXT, "worker1", "Test error"
            )
        
        self.create_test_entry(db_session, "max_failed_entry", failed_status)
        
        # Find claimable entries with max_retries=3
        claimable = optimized_queries.find_claimable_entries_optimized(
            ProcessingStage.DOWNLOAD_FULL_TEXT, 5, 3
        )
        
        # Should exclude entry with max retries
        entry_ids = [entry.entry_id for entry in claimable]
        assert "max_failed_entry" not in entry_ids
    
    def test_atomic_claim_batch_success(self, db_session, optimized_queries):
        """Test successful atomic batch claiming."""
        # Create test entries
        self.create_test_entry(db_session, "entry1")
        self.create_test_entry(db_session, "entry2")
        self.create_test_entry(db_session, "entry3")
        
        entry_ids = ["entry1", "entry2", "entry3"]
        worker_id = "test-worker"
        
        # Claim batch atomically
        claimed_ids = optimized_queries.atomic_claim_batch(
            entry_ids, worker_id, ProcessingStage.DOWNLOAD_FULL_TEXT
        )
        
        # Verify all entries were claimed
        assert set(claimed_ids) == set(entry_ids)
        
        # Verify database state
        for entry_id in entry_ids:
            entry = db_session.query(Entry).filter(Entry.entry_id == entry_id).first()
            assert entry.claimed_by == worker_id
            assert entry.claimed_at is not None
            
            # Verify processing status
            processing_status = ProcessingStatus.from_json(entry.processing_status)
            assert processing_status.is_stage_in_progress(ProcessingStage.DOWNLOAD_FULL_TEXT)
    
    def test_atomic_claim_batch_already_claimed(self, db_session, optimized_queries):
        """Test claiming entries that are already claimed."""
        # Create entry already claimed by another worker
        self.create_test_entry(
            db_session, "entry1", 
            claimed_by="other-worker",
            claimed_at=datetime.now(timezone.utc)
        )
        
        # Try to claim the already claimed entry
        claimed_ids = optimized_queries.atomic_claim_batch(
            ["entry1"], "test-worker", ProcessingStage.DOWNLOAD_FULL_TEXT
        )
        
        # Should return empty list (no entries claimed)
        assert claimed_ids == []
        
        # Verify entry is still claimed by original worker
        entry = db_session.query(Entry).filter(Entry.entry_id == "entry1").first()
        assert entry.claimed_by == "other-worker"
    
    def test_atomic_claim_batch_partial_success(self, db_session, optimized_queries):
        """Test claiming when some entries are already claimed."""
        # Create mix of available and claimed entries
        self.create_test_entry(db_session, "available1")
        self.create_test_entry(
            db_session, "claimed1",
            claimed_by="other-worker",
            claimed_at=datetime.now(timezone.utc)
        )
        self.create_test_entry(db_session, "available2")
        
        # Try to claim all entries
        claimed_ids = optimized_queries.atomic_claim_batch(
            ["available1", "claimed1", "available2"],
            "test-worker",
            ProcessingStage.DOWNLOAD_FULL_TEXT
        )
        
        # Should only claim available entries
        assert set(claimed_ids) == {"available1", "available2"}
    
    def test_bulk_update_completion_success(self, db_session, optimized_queries):
        """Test successful bulk completion update."""
        worker_id = "test-worker"
        
        # Create claimed entries
        for i in range(3):
            self.create_test_entry(
                db_session, f"entry{i}",
                claimed_by=worker_id,
                claimed_at=datetime.now(timezone.utc)
            )
        
        entry_ids = ["entry0", "entry1", "entry2"]
        
        # Mark entries as completed
        updated_count = optimized_queries.bulk_update_completion(
            entry_ids, ProcessingStage.DOWNLOAD_FULL_TEXT, worker_id
        )
        
        # Verify update count
        assert updated_count == 3
        
        # Verify database state
        for entry_id in entry_ids:
            entry = db_session.query(Entry).filter(Entry.entry_id == entry_id).first()
            assert entry.claimed_by is None
            assert entry.claimed_at is None
            assert entry.last_error is None
            
            # Verify processing status
            processing_status = ProcessingStatus.from_json(entry.processing_status)
            assert processing_status.is_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT)
    
    def test_bulk_update_completion_wrong_worker(self, db_session, optimized_queries):
        """Test bulk update with wrong worker ID."""
        # Create entry claimed by different worker
        self.create_test_entry(
            db_session, "entry1",
            claimed_by="other-worker",
            claimed_at=datetime.now(timezone.utc)
        )
        
        # Try to mark as completed with wrong worker ID
        updated_count = optimized_queries.bulk_update_completion(
            ["entry1"], ProcessingStage.DOWNLOAD_FULL_TEXT, "wrong-worker"
        )
        
        # Should not update any entries
        assert updated_count == 0
        
        # Verify entry is unchanged
        entry = db_session.query(Entry).filter(Entry.entry_id == "entry1").first()
        assert entry.claimed_by == "other-worker"
    
    def test_efficient_stale_cleanup_success(self, db_session, optimized_queries):
        """Test efficient stale claim cleanup."""
        timeout_minutes = 30
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=timeout_minutes)
        
        # Create stale entries
        self.create_test_entry(
            db_session, "stale1",
            claimed_by="stale-worker",
            claimed_at=cutoff_time - timedelta(minutes=10)
        )
        self.create_test_entry(
            db_session, "stale2",
            claimed_by="stale-worker",
            claimed_at=cutoff_time - timedelta(minutes=5)
        )
        
        # Create fresh entry (not stale)
        self.create_test_entry(
            db_session, "fresh1",
            claimed_by="active-worker",
            claimed_at=datetime.now(timezone.utc)
        )
        
        # Run stale cleanup
        cleaned_count = optimized_queries.efficient_stale_cleanup(timeout_minutes)
        
        # Verify cleanup count
        assert cleaned_count == 2
        
        # Verify stale entries were released
        stale1 = db_session.query(Entry).filter(Entry.entry_id == "stale1").first()
        stale2 = db_session.query(Entry).filter(Entry.entry_id == "stale2").first()
        assert stale1.claimed_by is None
        assert stale1.claimed_at is None
        assert stale2.claimed_by is None
        assert stale2.claimed_at is None
        
        # Verify fresh entry was not affected
        fresh1 = db_session.query(Entry).filter(Entry.entry_id == "fresh1").first()
        assert fresh1.claimed_by == "active-worker"
        assert fresh1.claimed_at is not None
    
    def test_efficient_stale_cleanup_no_stale_entries(self, db_session, optimized_queries):
        """Test stale cleanup when no stale entries exist."""
        # Create only fresh entries
        self.create_test_entry(
            db_session, "fresh1",
            claimed_by="active-worker",
            claimed_at=datetime.now(timezone.utc)
        )
        
        # Run stale cleanup
        cleaned_count = optimized_queries.efficient_stale_cleanup(30)
        
        # Should clean up nothing
        assert cleaned_count == 0
    
    def test_get_queue_metrics_optimized(self, db_session, optimized_queries):
        """Test optimized queue metrics collection."""
        # Create entries with different statuses
        self.create_test_entry(db_session, "pending1")  # Pending
        
        completed_status = ProcessingStatus()
        completed_status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker1")
        self.create_test_entry(db_session, "completed1", completed_status)
        
        failed_status = ProcessingStatus()
        failed_status.mark_stage_failed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker2", "error")
        self.create_test_entry(db_session, "failed1", failed_status)
        
        # Get metrics
        metrics = optimized_queries.get_queue_metrics_optimized([ProcessingStage.DOWNLOAD_FULL_TEXT])
        
        # Verify metrics structure
        assert ProcessingStage.DOWNLOAD_FULL_TEXT.value in metrics
        stage_metrics = metrics[ProcessingStage.DOWNLOAD_FULL_TEXT.value]
        
        # Verify metric keys exist
        assert 'pending' in stage_metrics
        assert 'completed' in stage_metrics
        assert 'failed' in stage_metrics
        assert 'in_progress' in stage_metrics
    
    def test_find_entries_by_worker_current_only(self, db_session, optimized_queries):
        """Test finding entries currently claimed by a worker."""
        worker_id = "test-worker"
        
        # Create entries claimed by the worker
        self.create_test_entry(
            db_session, "current1",
            claimed_by=worker_id,
            claimed_at=datetime.now(timezone.utc)
        )
        self.create_test_entry(
            db_session, "current2",
            claimed_by=worker_id,
            claimed_at=datetime.now(timezone.utc)
        )
        
        # Create entry claimed by different worker
        self.create_test_entry(
            db_session, "other1",
            claimed_by="other-worker",
            claimed_at=datetime.now(timezone.utc)
        )
        
        # Find entries by worker (current only)
        entries = optimized_queries.find_entries_by_worker(worker_id, include_completed=False)
        
        # Should return only current entries
        entry_ids = [entry.entry_id for entry in entries]
        assert set(entry_ids) == {"current1", "current2"}
    
    def test_find_entries_by_worker_include_completed(self, db_session, optimized_queries):
        """Test finding entries including completed ones."""
        worker_id = "test-worker"
        
        # Create current entry
        self.create_test_entry(
            db_session, "current1",
            claimed_by=worker_id,
            claimed_at=datetime.now(timezone.utc)
        )
        
        # Create completed entry (processing status contains worker_id)
        completed_status = ProcessingStatus()
        completed_status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, worker_id)
        self.create_test_entry(db_session, "completed1", completed_status)
        
        # Find entries by worker (include completed)
        entries = optimized_queries.find_entries_by_worker(worker_id, include_completed=True)
        
        # Should return both current and completed entries
        entry_ids = [entry.entry_id for entry in entries]
        assert "current1" in entry_ids
        assert "completed1" in entry_ids


if __name__ == "__main__":
    pytest.main([__file__])