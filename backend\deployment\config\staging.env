# Staging Environment Configuration for Distributed ETL Workers

# Worker Configuration
WORKER_LOG_LEVEL=INFO
WORKER_METRICS_ENABLED=true
WORKER_BATCH_SIZE=15
WORKER_CLAIM_TIMEOUT_MINUTES=20
WORKER_MAX_RETRIES=3
WORKER_HEARTBEAT_INTERVAL_SECONDS=45
WORKER_PROCESSING_DELAY_SECONDS=0.2

# Database Configuration
WORKER_CONNECTION_POOL_SIZE=5
WORKER_CONNECTION_TIMEOUT_SECONDS=25

# Performance Tuning (Staging - moderate performance)
WORKER_BATCH_CLAIM_RETRY_DELAY_SECONDS=1.5
WORKER_MAX_BATCH_CLAIM_RETRIES=4
WORKER_SHUTDOWN_TIMEOUT_SECONDS=180

# Health Check Configuration
WORKER_HEALTH_CHECK_PORT=8080

# Stage-specific Staging Settings
FEED_WORKER_BATCH_SIZE=30
FEED_WORKER_CLAIM_TIMEOUT_MINUTES=12
FEED_WORKER_MAX_RETRIES=4

FULLTEXT_WORKER_BATCH_SIZE=8
FULLTEXT_WORKER_CLAIM_TIMEOUT_MINUTES=15
FULLTEXT_WORKER_MAX_RETRIES=3

ANALYSIS_WORKER_BATCH_SIZE=4
ANALYSIS_WORKER_CLAIM_TIMEOUT_MINUTES=45
ANALYSIS_WORKER_MAX_RETRIES=3
ANALYSIS_WORKER_PROCESSING_DELAY_SECONDS=0.3

ML_WORKER_BATCH_SIZE=6
ML_WORKER_CLAIM_TIMEOUT_MINUTES=35
ML_WORKER_MAX_RETRIES=3

GENERATION_WORKER_BATCH_SIZE=2
GENERATION_WORKER_CLAIM_TIMEOUT_MINUTES=70
GENERATION_WORKER_MAX_RETRIES=2

UTILITY_WORKER_BATCH_SIZE=12
UTILITY_WORKER_CLAIM_TIMEOUT_MINUTES=25
UTILITY_WORKER_MAX_RETRIES=3

# Cleanup Service Configuration
CLEANUP_INTERVAL_MINUTES=3
STALE_TIMEOUT_MINUTES=45

# Staging-specific API Keys (use staging/test keys)
# OPENAI_API_KEY=sk-staging-...
# IONOS_API_KEY=staging-...
# HUGGINGFACE_TOKEN=hf_staging...
# STABILITY_API_KEY=sk-staging-...