"""
Embedding worker for distributed ETL processing.

This module provides the EmbeddingWorker class that handles computing embeddings
for news articles using SentenceTransformer models in batch processing mode.
"""

import logging
import numpy as np
from typing import List, Dict, Optional, Any
from datetime import datetime, timezone

from sqlalchemy.orm import Session

from backend.app.distributed.distributed_worker import Distri<PERSON><PERSON>orker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry

logger = logging.getLogger(__name__)


class EmbeddingWorker(DistributedWorker):
    """
    Worker specialized in computing embeddings for news articles.
    
    Adapts the existing compute_embeddings logic for batch processing with
    batch-optimized model loading, proper resource management, and error handling.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the embedding worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.COMPUTE_EMBEDDINGS not in config.stages:
            raise ValueError("EmbeddingWorker requires COMPUTE_EMBEDDINGS stage")
        
        # Initialize embedding model (lazy loading)
        self._embedding_model = None
        
        # Configuration for embedding computation
        self.model_name = config.get_stage_config(
            ProcessingStage.COMPUTE_EMBEDDINGS, 'model_name', 'BAAI/bge-m3'
        )
        self.batch_optimize_model = config.get_stage_config(
            ProcessingStage.COMPUTE_EMBEDDINGS, 'batch_optimize_model', True
        )
        self.preload_model = config.get_stage_config(
            ProcessingStage.COMPUTE_EMBEDDINGS, 'preload_model', False
        )
        self.max_text_length = config.get_stage_config(
            ProcessingStage.COMPUTE_EMBEDDINGS, 'max_text_length', 8192
        )
        
        logger.info(
            f"Initialized EmbeddingWorker {self.worker_id} "
            f"(model: {self.model_name}, batch_optimize: {self.batch_optimize_model}, "
            f"preload: {self.preload_model})"
        )
        
        # Preload model if configured
        if self.preload_model:
            self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize the SentenceTransformer embedding model."""
        try:
            logger.info(f"Initializing embedding model: {self.model_name}")
            
            from sentence_transformers import SentenceTransformer
            self._embedding_model = SentenceTransformer(self.model_name)
            
            logger.info(f"Embedding model {self.model_name} loaded successfully")
            
        except ImportError as e:
            logger.error(f"Failed to import SentenceTransformer library: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize embedding model {self.model_name}: {e}")
            raise
    
    def _get_embedding_model(self):
        """Get embedding model, initializing if needed."""
        if self._embedding_model is None:
            from sentence_transformers import SentenceTransformer
            self._embedding_model = SentenceTransformer(self.model_name)
            logger.debug(f"Lazy-loaded embedding model: {self.model_name}")
        return self._embedding_model
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for embedding computation.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be COMPUTE_EMBEDDINGS)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.COMPUTE_EMBEDDINGS:
            logger.error(f"EmbeddingWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing batch of {len(entries)} entries for embedding computation")
        
        # Filter entries that have required text fields
        valid_entries = []
        results = {}
        
        for entry in entries:
            if not entry.full_text:
                logger.warning(
                    f"Entry {entry.entry_id} missing required full_text field"
                )
                results[entry.entry_id] = False
            else:
                valid_entries.append(entry)
        
        if not valid_entries:
            logger.warning("No valid entries to process in batch")
            return results
        
        # Process valid entries
        if self.batch_optimize_model:
            # Batch-optimized processing
            batch_results = self._process_batch_optimized(valid_entries)
        else:
            # Individual processing
            batch_results = self._process_batch_individual(valid_entries)
        
        results.update(batch_results)
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"Completed embedding computation batch: {success_count}/{len(entries)} successful"
        )
        
        return results
    
    def _process_batch_optimized(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries with batch-optimized model usage.
        
        Args:
            entries: List of valid entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        try:
            # Get model once for the entire batch
            embedding_model = self._get_embedding_model()
            
            # Prepare batch data
            texts = []
            entry_ids = []
            
            for entry in entries:
                # Truncate text if too long
                text = entry.full_text
                if len(text) > self.max_text_length:
                    text = text[:self.max_text_length]
                    logger.debug(f"Truncated text for entry {entry.entry_id} to {self.max_text_length} characters")
                
                texts.append(text)
                entry_ids.append(entry.entry_id)
            
            # Batch compute embeddings
            logger.debug(f"Computing embeddings for {len(texts)} texts in batch")
            embeddings = embedding_model.encode(
                texts, 
                convert_to_tensor=True,
                show_progress_bar=False
            ).cpu().numpy()
            
            # Update database for each entry
            for i, (entry_id, embedding) in enumerate(zip(entry_ids, embeddings)):
                try:
                    success = self._update_entry_embedding(entry_id, embedding)
                    results[entry_id] = success
                    
                    if success:
                        logger.debug(f"Successfully computed embedding for entry {entry_id}")
                    else:
                        logger.warning(f"Failed to update database for entry {entry_id}")
                
                except Exception as e:
                    logger.error(f"Error updating entry {entry_id}: {e}")
                    results[entry_id] = False
        
        except Exception as e:
            logger.error(f"Error in batch-optimized processing: {e}")
            # Fall back to individual processing
            return self._process_batch_individual(entries)
        
        return results
    
    def _process_batch_individual(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries individually (fallback method).
        
        Args:
            entries: List of valid entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        # Get model
        embedding_model = self._get_embedding_model()
        
        for entry in entries:
            try:
                # Truncate text if too long
                text = entry.full_text
                if len(text) > self.max_text_length:
                    text = text[:self.max_text_length]
                    logger.debug(f"Truncated text for entry {entry.entry_id} to {self.max_text_length} characters")
                
                # Compute embedding (individual)
                embedding = embedding_model.encode(
                    [text], 
                    convert_to_tensor=True,
                    show_progress_bar=False
                ).cpu().numpy()[0]
                
                # Update database
                success = self._update_entry_embedding(entry.entry_id, embedding)
                results[entry.entry_id] = success
                
                if success:
                    logger.debug(f"Successfully computed embedding for entry {entry.entry_id}")
                else:
                    logger.warning(f"Failed to update database for entry {entry.entry_id}")
            
            except Exception as e:
                logger.error(f"Error processing entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
        
        return results
    
    def _update_entry_embedding(self, entry_id: str, embedding: np.ndarray) -> bool:
        """
        Update the entry's embedding field in the database.
        
        Args:
            entry_id: ID of the entry to update
            embedding: Computed embedding as numpy array
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create a new database session for this update
            db_session = self.db_session_factory()
            
            try:
                # Convert embedding to bytes for database storage
                embedding_bytes = embedding.tobytes()
                
                # Update embedding field
                db_session.query(Entry).filter_by(entry_id=entry_id).update({
                    "embedding": embedding_bytes
                })
                db_session.commit()
                
                logger.debug(f"Updated embedding for entry {entry_id}")
                return True
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error updating entry {entry_id}: {e}")
                return False
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for entry {entry_id}: {e}")
            return False
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to embedding worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add embedding worker specific metrics
        health.update({
            'worker_type': 'EmbeddingWorker',
            'model_name': self.model_name,
            'batch_optimize_model': self.batch_optimize_model,
            'preload_model': self.preload_model,
            'max_text_length': self.max_text_length,
            'model_loaded': self._embedding_model is not None,
            'supported_stages': [ProcessingStage.COMPUTE_EMBEDDINGS.value]
        })
        
        return health
    
    def cleanup_resources(self) -> None:
        """Clean up model resources when worker shuts down."""
        logger.info("Cleaning up embedding model resources")
        
        # Clear model reference to free memory
        self._embedding_model = None
        
        logger.info("Embedding model resources cleaned up")