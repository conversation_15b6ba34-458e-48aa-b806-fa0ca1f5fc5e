"""
Integration test for Embedding<PERSON>orker with the distributed processing framework.

This test verifies that the EmbeddingWorker integrates properly with the
work queue management and distributed processing system.
"""

import pytest
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from backend.app.distributed.workers.embedding_worker import Embedding<PERSON>orker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.models.models import Entry


class TestEmbeddingWorkerIntegrationFramework:
    """Integration tests for EmbeddingWorker with the distributed framework."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        return mock_session
    
    @pytest.fixture
    def mock_db_session_factory(self, mock_db_session):
        """Create a mock database session factory."""
        def session_factory():
            return mock_db_session
        return session_factory
    
    @pytest.fixture
    def embedding_config(self):
        """Create configuration for embedding worker."""
        config = WorkerConfig(
            worker_id="test-embedding-worker",
            stages=[ProcessingStage.COMPUTE_EMBEDDINGS],
            batch_size=3
        )
        config.stage_configs = {
            ProcessingStage.COMPUTE_EMBEDDINGS.value: {
                'model_name': 'BAAI/bge-m3',
                'batch_optimize_model': True,
                'preload_model': False,
                'max_text_length': 1000
            }
        }
        return config
    
    def create_test_entry(self, entry_id: str, full_text: str) -> Entry:
        """Create a test entry."""
        entry = Entry()
        entry.entry_id = entry_id
        entry.title = f"Title for {entry_id}"
        entry.full_text = full_text
        return entry
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_embedding_worker_with_work_queue_manager(self, mock_sentence_transformer, 
                                                    embedding_config, mock_db_session_factory):
        """Test EmbeddingWorker integration with WorkQueueManager."""
        # Setup mock model
        mock_model = Mock()
        embeddings = np.random.rand(2, 768).astype(np.float32)
        mock_tensor = Mock()
        mock_tensor.cpu.return_value.numpy.return_value = embeddings
        mock_model.encode.return_value = mock_tensor
        mock_sentence_transformer.return_value = mock_model
        
        # Create test entries
        entries = [
            self.create_test_entry("entry1", "First test entry content"),
            self.create_test_entry("entry2", "Second test entry content")
        ]
        
        # Create worker
        worker = EmbeddingWorker(embedding_config, mock_db_session_factory)
        
        # Process batch
        results = worker.process_batch(entries, ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Verify results
        assert len(results) == 2
        assert results["entry1"] is True
        assert results["entry2"] is True
        
        # Verify model was called
        mock_model.encode.assert_called_once()
        call_args = mock_model.encode.call_args[0]
        assert len(call_args[0]) == 2  # Batch of 2 texts
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_embedding_worker_error_handling(self, mock_sentence_transformer,
                                           embedding_config, mock_db_session_factory):
        """Test EmbeddingWorker error handling during processing."""
        # Setup mock model that fails
        mock_model = Mock()
        mock_model.encode.side_effect = Exception("Model processing failed")
        mock_sentence_transformer.return_value = mock_model
        
        # Create test entry
        entry = self.create_test_entry("error_entry", "Test content")
        
        # Create worker
        worker = EmbeddingWorker(embedding_config, mock_db_session_factory)
        
        # Process batch - should handle error gracefully
        results = worker.process_batch([entry], ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Verify error handling
        assert len(results) == 1
        assert results["error_entry"] is False
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_embedding_worker_database_error_handling(self, mock_sentence_transformer,
                                                    embedding_config, mock_db_session_factory):
        """Test EmbeddingWorker handling of database errors."""
        # Setup mock model
        mock_model = Mock()
        embedding = np.random.rand(768).astype(np.float32)
        mock_tensor = Mock()
        mock_tensor.cpu.return_value.numpy.return_value = [embedding]
        mock_model.encode.return_value = mock_tensor
        mock_sentence_transformer.return_value = mock_model
        
        # Setup database session to fail on update
        mock_session = mock_db_session_factory()
        mock_session.query.side_effect = Exception("Database connection failed")
        
        # Create test entry
        entry = self.create_test_entry("db_error_entry", "Test content")
        
        # Create worker
        worker = EmbeddingWorker(embedding_config, mock_db_session_factory)
        
        # Process batch - should handle database error gracefully
        results = worker.process_batch([entry], ProcessingStage.COMPUTE_EMBEDDINGS)
        
        # Verify error handling
        assert len(results) == 1
        assert results["db_error_entry"] is False
        
        # Verify rollback was called
        mock_session.rollback.assert_called()
        mock_session.close.assert_called()
    
    def test_embedding_worker_health_status(self, embedding_config, mock_db_session_factory):
        """Test EmbeddingWorker health status reporting."""
        worker = EmbeddingWorker(embedding_config, mock_db_session_factory)
        
        health = worker.get_worker_specific_health()
        
        # Verify health status structure
        assert health['worker_type'] == 'EmbeddingWorker'
        assert health['model_name'] == 'BAAI/bge-m3'
        assert health['batch_optimize_model'] is True
        assert health['preload_model'] is False
        assert health['max_text_length'] == 1000
        assert health['model_loaded'] is False
        assert ProcessingStage.COMPUTE_EMBEDDINGS.value in health['supported_stages']
        
        # Verify base health status is included
        assert 'worker_id' in health
        assert 'state' in health
        assert 'stats' in health
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_embedding_worker_resource_cleanup(self, mock_sentence_transformer,
                                             embedding_config, mock_db_session_factory):
        """Test EmbeddingWorker resource cleanup."""
        mock_model = Mock()
        mock_sentence_transformer.return_value = mock_model
        
        worker = EmbeddingWorker(embedding_config, mock_db_session_factory)
        
        # Load model
        worker._get_embedding_model()
        assert worker._embedding_model is not None
        
        # Cleanup resources
        worker.cleanup_resources()
        assert worker._embedding_model is None
    
    def test_embedding_worker_configuration_validation(self, mock_db_session_factory):
        """Test EmbeddingWorker configuration validation."""
        # Test with wrong stage
        wrong_config = WorkerConfig(
            worker_id="wrong-worker",
            stages=[ProcessingStage.SENTIMENT_ANALYSIS],  # Wrong stage
            batch_size=5
        )
        
        with pytest.raises(ValueError, match="EmbeddingWorker requires COMPUTE_EMBEDDINGS stage"):
            EmbeddingWorker(wrong_config, mock_db_session_factory)
    
    @patch('sentence_transformers.SentenceTransformer')
    def test_embedding_worker_batch_size_handling(self, mock_sentence_transformer,
                                                 mock_db_session_factory):
        """Test EmbeddingWorker with different batch sizes."""
        # Setup mock model
        mock_model = Mock()
        
        def mock_encode(texts, **kwargs):
            num_texts = len(texts)
            embeddings = np.random.rand(num_texts, 768).astype(np.float32)
            mock_tensor = Mock()
            mock_tensor.cpu.return_value.numpy.return_value = embeddings
            return mock_tensor
        
        mock_model.encode.side_effect = mock_encode
        mock_sentence_transformer.return_value = mock_model
        
        # Test with different batch sizes
        for batch_size in [1, 5, 10]:
            config = WorkerConfig(
                worker_id=f"batch-{batch_size}-worker",
                stages=[ProcessingStage.COMPUTE_EMBEDDINGS],
                batch_size=batch_size
            )
            config.stage_configs = {
                ProcessingStage.COMPUTE_EMBEDDINGS.value: {
                    'batch_optimize_model': True
                }
            }
            
            worker = EmbeddingWorker(config, mock_db_session_factory)
            
            # Create entries matching batch size
            entries = [
                self.create_test_entry(f"entry_{i}", f"Content {i}")
                for i in range(batch_size)
            ]
            
            results = worker.process_batch(entries, ProcessingStage.COMPUTE_EMBEDDINGS)
            
            # Verify all entries processed successfully
            assert len(results) == batch_size
            assert all(success for success in results.values())