"""
Unit tests for IPTCClassificationWorker.

Tests the IPTC classification worker functionality including batch processing,
model management, error handling, and database operations.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from backend.app.distributed.workers.iptc_classification_worker import IPTCClassificationWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestIPTCClassificationWorker:
    """Test suite for IPTCClassificationWorker."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        config = WorkerConfig(
            worker_id="test-iptc-worker",
            stages=[ProcessingStage.IPTC_CLASSIFICATION],
            batch_size=5,
            heartbeat_interval=30,
            claim_timeout=300,
            stage_configs={
                ProcessingStage.IPTC_CLASSIFICATION: {
                    'model_name': 'test-model',
                    'batch_optimize_model': True,
                    'preload_model': False,
                    'max_text_length': 512
                }
            }
        )
        return config
    
    @pytest.fixture
    def sample_entries(self):
        """Create sample Entry objects for testing."""
        entries = []
        for i in range(3):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.description = f"Test Description {i}"
            entry.full_text = f"This is the full text content for test entry {i}. It contains news content."
            entries.append(entry)
        return entries
    
    def test_worker_initialization(self, worker_config, mock_db_session_factory):
        """Test worker initialization with correct configuration."""
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            assert worker.model_name == 'test-model'
            assert worker.batch_optimize_model is True
            assert worker.preload_model is False
            assert worker.max_text_length == 512
            assert worker._classifier is None  # Not preloaded
    
    def test_worker_initialization_with_preload(self, worker_config, mock_db_session_factory):
        """Test worker initialization with model preloading."""
        worker_config.stage_configs[ProcessingStage.IPTC_CLASSIFICATION]['preload_model'] = True
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            with patch.object(IPTCClassificationWorker, '_initialize_model') as mock_init:
                worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
                mock_init.assert_called_once()
    
    def test_worker_initialization_invalid_stage(self, mock_db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.FEED_DOWNLOAD],  # Wrong stage
            batch_size=5
        )
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            with pytest.raises(ValueError, match="IPTCClassificationWorker requires IPTC_CLASSIFICATION stage"):
                IPTCClassificationWorker(invalid_config, mock_db_session_factory)
    
    @patch('backend.app.distributed.workers.iptc_classification_worker.pipeline')
    def test_initialize_model_success(self, mock_pipeline, worker_config, mock_db_session_factory):
        """Test successful model initialization."""
        mock_classifier = Mock()
        mock_classifier.model.config.max_position_embeddings = 1024
        mock_pipeline.return_value = mock_classifier
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            worker._initialize_model()
            
            mock_pipeline.assert_called_once_with("text-classification", model='test-model')
            assert worker._classifier == mock_classifier
            assert worker.max_text_length == 1024
    
    @patch('backend.app.distributed.workers.iptc_classification_worker.pipeline')
    def test_initialize_model_no_max_length(self, mock_pipeline, worker_config, mock_db_session_factory):
        """Test model initialization when max_position_embeddings is not available."""
        mock_classifier = Mock()
        del mock_classifier.model.config.max_position_embeddings  # Simulate missing attribute
        mock_pipeline.return_value = mock_classifier
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            worker.max_text_length = None  # Reset to None
            worker._initialize_model()
            
            assert worker.max_text_length == 512  # Default fallback
    
    @patch('backend.app.distributed.workers.iptc_classification_worker.pipeline')
    def test_initialize_model_import_error(self, mock_pipeline, worker_config, mock_db_session_factory):
        """Test model initialization handles import errors."""
        mock_pipeline.side_effect = ImportError("transformers not installed")
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            with pytest.raises(ImportError):
                worker._initialize_model()
    
    def test_process_batch_wrong_stage(self, worker_config, mock_db_session_factory, sample_entries):
        """Test process_batch rejects wrong processing stage."""
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(sample_entries, ProcessingStage.FEED_DOWNLOAD)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(sample_entries)
    
    def test_process_batch_no_full_text(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries without full_text."""
        # Create entries without full_text
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.description = f"Test Description {i}"
            entry.full_text = None  # Missing full_text
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.IPTC_CLASSIFICATION)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(entries)
    
    @patch('backend.app.distributed.workers.iptc_classification_worker.pipeline')
    def test_process_batch_optimized_success(self, mock_pipeline, worker_config, mock_db_session_factory, sample_entries):
        """Test successful batch-optimized processing."""
        # Setup mock classifier
        mock_classifier = Mock()
        mock_classifier.return_value = [
            [{'label': 'POLITICS', 'score': 0.95}],
            [{'label': 'SPORTS', 'score': 0.88}],
            [{'label': 'TECHNOLOGY', 'score': 0.92}]
        ]
        mock_pipeline.return_value = mock_classifier
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            # Mock the database update method
            with patch.object(worker, '_update_entry_classification', return_value=True) as mock_update:
                results = worker.process_batch(sample_entries, ProcessingStage.IPTC_CLASSIFICATION)
                
                # Verify all entries were processed successfully
                assert all(success for success in results.values())
                assert len(results) == len(sample_entries)
                
                # Verify classifier was called with batch
                mock_classifier.assert_called_once()
                call_args = mock_classifier.call_args[0][0]
                assert len(call_args) == len(sample_entries)
                
                # Verify database updates were called
                assert mock_update.call_count == len(sample_entries)
    
    @patch('backend.app.distributed.workers.iptc_classification_worker.pipeline')
    def test_process_batch_individual_success(self, mock_pipeline, worker_config, mock_db_session_factory, sample_entries):
        """Test successful individual processing."""
        # Disable batch optimization
        worker_config.stage_configs[ProcessingStage.IPTC_CLASSIFICATION]['batch_optimize_model'] = False
        
        # Setup mock classifier
        mock_classifier = Mock()
        mock_classifier.side_effect = [
            {'label': 'POLITICS', 'score': 0.95},
            {'label': 'SPORTS', 'score': 0.88},
            {'label': 'TECHNOLOGY', 'score': 0.92}
        ]
        mock_pipeline.return_value = mock_classifier
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            # Mock the database update method
            with patch.object(worker, '_update_entry_classification', return_value=True) as mock_update:
                results = worker.process_batch(sample_entries, ProcessingStage.IPTC_CLASSIFICATION)
                
                # Verify all entries were processed successfully
                assert all(success for success in results.values())
                assert len(results) == len(sample_entries)
                
                # Verify classifier was called for each entry
                assert mock_classifier.call_count == len(sample_entries)
                
                # Verify database updates were called
                assert mock_update.call_count == len(sample_entries)
    
    @patch('backend.app.distributed.workers.iptc_classification_worker.pipeline')
    def test_process_batch_text_truncation(self, mock_pipeline, worker_config, mock_db_session_factory):
        """Test text truncation for long content."""
        # Create entry with very long text
        entry = Mock(spec=Entry)
        entry.entry_id = "test-entry-long"
        entry.title = "Short Title"
        entry.description = "Short Description"
        entry.full_text = "A" * 1000  # Very long text
        
        # Setup mock classifier
        mock_classifier = Mock()
        mock_classifier.return_value = [{'label': 'NEWS', 'score': 0.90}]
        mock_pipeline.return_value = mock_classifier
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_update_entry_classification', return_value=True):
                results = worker.process_batch([entry], ProcessingStage.IPTC_CLASSIFICATION)
                
                # Verify processing succeeded
                assert results[entry.entry_id] is True
                
                # Verify text was truncated
                mock_classifier.assert_called_once()
                call_args = mock_classifier.call_args[0][0]
                assert len(call_args[0]) <= worker.max_text_length
    
    def test_update_entry_classification_success(self, worker_config, mock_db_session_factory):
        """Test successful database update for entry classification."""
        mock_session = mock_db_session_factory()
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_classification("test-entry", "POLITICS", 0.95)
            
            assert result is True
            mock_session.query.assert_called_once_with(Entry)
            mock_session.commit.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_classification_database_error(self, worker_config, mock_db_session_factory):
        """Test database error handling in entry update."""
        mock_session = mock_db_session_factory()
        mock_session.commit.side_effect = Exception("Database error")
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_classification("test-entry", "POLITICS", 0.95)
            
            assert result is False
            mock_session.rollback.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_classification_session_error(self, worker_config):
        """Test session creation error in entry update."""
        def failing_session_factory():
            raise Exception("Session creation failed")
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, failing_session_factory)
            
            result = worker._update_entry_classification("test-entry", "POLITICS", 0.95)
            
            assert result is False
    
    def test_get_worker_specific_health(self, worker_config, mock_db_session_factory):
        """Test worker-specific health information."""
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.get_health_status') as mock_health:
                mock_health.return_value = {'status': 'healthy'}
                
                worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
                health = worker.get_worker_specific_health()
                
                assert health['worker_type'] == 'IPTCClassificationWorker'
                assert health['model_name'] == 'test-model'
                assert health['batch_optimize_model'] is True
                assert health['preload_model'] is False
                assert health['max_text_length'] == 512
                assert health['model_loaded'] is False
                assert ProcessingStage.IPTC_CLASSIFICATION.value in health['supported_stages']
    
    def test_cleanup_resources(self, worker_config, mock_db_session_factory):
        """Test resource cleanup."""
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            worker._classifier = Mock()  # Simulate loaded model
            
            worker.cleanup_resources()
            
            assert worker._classifier is None
    
    @patch('backend.app.distributed.workers.iptc_classification_worker.pipeline')
    def test_fallback_to_individual_processing(self, mock_pipeline, worker_config, mock_db_session_factory, sample_entries):
        """Test fallback from batch to individual processing on error."""
        # Setup mock classifier that fails on batch but succeeds individually
        mock_classifier = Mock()
        mock_classifier.side_effect = [
            Exception("Batch processing failed"),  # First call (batch) fails
            {'label': 'POLITICS', 'score': 0.95},   # Individual calls succeed
            {'label': 'SPORTS', 'score': 0.88},
            {'label': 'TECHNOLOGY', 'score': 0.92}
        ]
        mock_pipeline.return_value = mock_classifier
        
        with patch('backend.app.distributed.workers.iptc_classification_worker.DistributedWorker.__init__'):
            worker = IPTCClassificationWorker(worker_config, mock_db_session_factory)
            
            with patch.object(worker, '_update_entry_classification', return_value=True):
                results = worker.process_batch(sample_entries, ProcessingStage.IPTC_CLASSIFICATION)
                
                # Should still succeed via individual processing
                assert all(success for success in results.values())
                assert len(results) == len(sample_entries)