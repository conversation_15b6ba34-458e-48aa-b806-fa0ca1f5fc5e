"""
Logging configuration for distributed ETL workers.

Provides color-coded logging similar to the monolithic news_aggregator,
with different colors for different worker types and stages.
"""

import logging
import sys
import os
from logging.handlers import TimedRotatingFileHandler
from typing import Optional

try:
    import colorlog
    COLORLOG_AVAILABLE = True
except ImportError:
    COLORLOG_AVAILABLE = False


class DistributedWorkerColoredFormatter(colorlog.ColoredFormatter if COLORLOG_AVAILABLE else logging.Formatter):
    """Custom colored formatter for distributed workers with stage-specific colors."""
    
    def format(self, record):
        if not COLORLOG_AVAILABLE:
            return super().format(record)
            
        # Color mapping for different worker types and stages
        worker_color_map = {
            # Worker types
            'FeedDownloadWorker': 'white,bg_light_black',
            'FullTextDownloadWorker': 'white,bg_green', 
            'EmbeddingWorker': 'white,bg_green',
            'SentimentAnalysisWorker': 'white,bg_light_green',
            'LLMAnalysisWorker': 'white,bg_light_purple',
            'IPTCClassificationWorker': 'white,bg_light_yellow',
            'DuplicateCheckWorker': 'white,bg_light_cyan',
            'DescriptionGenerationWorker': 'white,bg_light_white',
            'ImagePromptWorker': 'white,bg_blue',
            'PreviewImageWorker': 'white,bg_light_blue',
            'RSSFeedGenerationWorker': 'white,bg_light_white',
            
            # Processing stages
            'download_feeds': 'white,bg_light_black',
            'download_full_text': 'white,bg_green',
            'translate_text': 'white,bg_yellow',
            'compute_embeddings': 'white,bg_green',
            'sentiment_analysis': 'white,bg_light_green',
            'llm_analysis': 'white,bg_light_purple',
            'iptc_classification': 'white,bg_light_yellow',
            'duplicate_check': 'white,bg_light_cyan',
            'generate_descriptions': 'white,bg_light_white',
            'generate_image_prompts': 'white,bg_blue',
            'generate_preview_images': 'white,bg_light_blue',
            'generate_rss_feed': 'white,bg_light_white',
            
            # Manager and CLI
            'WorkerManager': 'white,bg_red',
            'WorkerCLI': 'white,bg_purple',
            'HealthCheckServer': 'white,bg_cyan',
        }
        
        levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        self.secondary_log_colors = {'threadName': {}}
        
        # Check thread name for worker type
        thread_name = getattr(record, 'threadName', '')

        # Extract stage information from message if present
        stage_info = ""
        message = getattr(record, 'msg', '')
        if isinstance(message, str):
            # Look for [stage_name] pattern in message
            import re
            stage_match = re.match(r'^\[([^\]]+)\]\s*(.*)', message)
            if stage_match:
                stage_name = stage_match.group(1)
                stage_info = f"[{stage_name}]"
                # Update the message to remove the stage prefix
                record.msg = stage_match.group(2)
                # Use stage color if available
                if stage_name in worker_color_map:
                    color = worker_color_map[stage_name]
                else:
                    color = None
            else:
                color = None
        else:
            color = None

        # If no stage-specific color, try to match worker type in thread name or logger name
        if not color:
            for key, worker_color in worker_color_map.items():
                if key in thread_name or key in record.name:
                    color = worker_color
                    break

        # If no specific color found, use default based on logger name
        if not color:
            if 'worker' in record.name.lower():
                color = 'white,bg_light_black'  # Default worker color
            elif 'manager' in record.name.lower():
                color = 'white,bg_red'
            elif 'cli' in record.name.lower():
                color = 'white,bg_purple'
            else:
                color = 'white'  # Default

        # Add stage info to thread name for display
        if stage_info:
            display_thread_name = f"{stage_info}{thread_name}"
        else:
            display_thread_name = thread_name

        # Store original thread name and set display name
        original_thread_name = record.threadName
        record.threadName = display_thread_name

        if color:
            self.secondary_log_colors = {
                'threadName': {level: color for level in levels}
            }

        # Format the record
        formatted = super().format(record)

        # Restore original thread name
        record.threadName = original_thread_name

        return formatted


def setup_distributed_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    enable_colors: bool = True,
    worker_id: Optional[str] = None
) -> logging.Logger:
    """
    Setup logging configuration for distributed workers.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path. If None, uses default location
        enable_colors: Whether to enable colored console output
        worker_id: Optional worker ID to include in log file name
        
    Returns:
        Configured logger instance
    """
    # Determine log level
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Setup handlers
    handlers = []
    
    # Console handler with colors
    if enable_colors and COLORLOG_AVAILABLE:
        formatter = DistributedWorkerColoredFormatter(
            "%(log_color)s%(asctime)s - %(levelname)s - [%(threadName_log_color)s%(threadName)s%(reset)s%(log_color)s] - %(message)s",
            log_colors={
                'DEBUG': 'white',
                'INFO': 'cyan',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler = colorlog.StreamHandler(sys.stdout)
    else:
        formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s"
        )
        console_handler = logging.StreamHandler(sys.stdout)
    
    console_handler.setFormatter(formatter)
    console_handler.setLevel(numeric_level)
    handlers.append(console_handler)
    
    # File handler
    if log_file is None:
        log_dir = os.getenv('LOG_DIR', 'c:/temp')
        if worker_id:
            log_file = f"{log_dir}/distributed_worker_{worker_id}.log"
        else:
            log_file = f"{log_dir}/distributed_workers.log"
    
    try:
        file_handler = TimedRotatingFileHandler(
            log_file, 
            when='midnight', 
            interval=1, 
            encoding="utf-8"
        )
        file_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - [%(threadName)s] - %(name)s - %(message)s")
        )
        file_handler.suffix = "%Y-%m-%d"
        file_handler.setLevel(numeric_level)
        handlers.append(file_handler)
    except (OSError, PermissionError) as e:
        # If file logging fails, continue with console only
        print(f"Warning: Could not setup file logging: {e}")
    
    # Configure root logger
    logging.basicConfig(
        level=numeric_level,
        handlers=handlers,
        force=True  # Override any existing configuration
    )
    
    # Reduce noise from external libraries
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    
    # Fix SQLAlchemy logging format string errors by completely disabling problematic loggers
    # This prevents the "not all arguments converted during string formatting" error
    _configure_sqlalchemy_logging()
    
    return logging.getLogger(__name__)


def get_worker_logger(worker_name: str, worker_id: Optional[str] = None) -> logging.Logger:
    """
    Get a logger for a specific worker.
    
    Args:
        worker_name: Name of the worker class or component
        worker_id: Optional worker ID
        
    Returns:
        Logger instance with appropriate naming
    """
    if worker_id:
        logger_name = f"{worker_name}.{worker_id}"
    else:
        logger_name = worker_name
        
    return logging.getLogger(logger_name)


def log_worker_summary(logger: logging.Logger, worker_id: str, stage: str, 
                      processed: int, failed: int, duration: float):
    """
    Log a summary of worker processing in a standardized format.
    
    Args:
        logger: Logger instance
        worker_id: Worker identifier
        stage: Processing stage name
        processed: Number of entries processed successfully
        failed: Number of entries that failed
        duration: Processing duration in seconds
    """
    if failed > 0:
        logger.warning(
            f"[{stage}] Processed {processed} entries, {failed} failed "
            f"in {duration:.1f}s (worker: {worker_id})"
        )
    else:
        logger.info(
            f"[{stage}] Processed {processed} entries in {duration:.1f}s "
            f"(worker: {worker_id})"
        )


def log_stage_start(logger: logging.Logger, worker_id: str, stage: str, batch_size: int):
    """Log the start of a processing stage."""
    logger.info(f"[{stage}] Starting batch processing (size: {batch_size}, worker: {worker_id})")


def log_stage_complete(logger: logging.Logger, worker_id: str, stage: str,
                      entries_processed: int, duration: float):
    """Log the completion of a processing stage."""
    rate = entries_processed / duration if duration > 0 else 0
    logger.info(
        f"[{stage}] Completed: {entries_processed} entries in {duration:.1f}s "
        f"({rate:.1f} entries/sec, worker: {worker_id})"
    )


def _configure_sqlalchemy_logging():
    """Configure SQLAlchemy logging to prevent format string errors."""
    sqlalchemy_loggers = [
        'sqlalchemy.engine',
        'sqlalchemy.engine.Engine',
        'sqlalchemy.pool',
        'sqlalchemy.dialects',
        'sqlalchemy.orm',
        'sqlalchemy.pool.impl.QueuePool',
        'sqlalchemy.pool.impl.NullPool',
        'sqlalchemy.engine.base',
        'sqlalchemy.engine.base.Engine'
    ]

    for logger_name in sqlalchemy_loggers:
        sql_logger = logging.getLogger(logger_name)
        # Always set to ERROR level to prevent format string issues
        sql_logger.setLevel(logging.ERROR)
        # Disable propagation to prevent format errors from bubbling up
        sql_logger.propagate = False


def fix_sqlalchemy_logging():
    """Public function to fix SQLAlchemy logging issues. Call this early in worker startup."""
    _configure_sqlalchemy_logging()
