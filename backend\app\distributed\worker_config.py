"""
Worker configuration management for distributed ETL processing.
"""

import os
import json
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any, Union
from pathlib import Path

from .processing_stage import ProcessingStage


@dataclass
class WorkerConfig:
    """Configuration for distributed ETL workers."""
    
    # Worker identification
    worker_id: str
    
    # Processing configuration
    stages: List[ProcessingStage]
    batch_size: int = 25
    claim_timeout_minutes: int = 30
    heartbeat_interval_seconds: int = 60
    max_retries: int = 3
    
    # Database configuration
    database_url: Optional[str] = None
    connection_pool_size: int = 5
    connection_timeout_seconds: int = 30
    
    # Performance tuning
    processing_delay_seconds: float = 0.1
    no_work_delay_seconds: float = 120.0  # Delay when no work is found for any stage
    batch_claim_retry_delay_seconds: float = 1.0
    max_batch_claim_retries: int = 5
    
    # Monitoring and logging
    log_level: str = "INFO"
    metrics_enabled: bool = True
    health_check_port: Optional[int] = None
    
    # Graceful shutdown
    shutdown_timeout_seconds: int = 300
    
    # Stage-specific configurations
    stage_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate configuration after initialization."""
        self._validate_config()
    
    def _validate_config(self) -> None:
        """Validate all configuration parameters."""
        errors = []
        
        # Validate worker_id
        if not self.worker_id or not isinstance(self.worker_id, str):
            errors.append("worker_id must be a non-empty string")
        elif len(self.worker_id) > 255:
            errors.append("worker_id must be 255 characters or less")
        
        # Validate stages
        if not self.stages:
            errors.append("stages list cannot be empty")
        elif not isinstance(self.stages, list):
            errors.append("stages must be a list")
        else:
            for stage in self.stages:
                if not isinstance(stage, ProcessingStage):
                    errors.append(f"Invalid stage type: {type(stage)}. Must be ProcessingStage enum")
        
        # Validate batch_size
        if not isinstance(self.batch_size, int) or self.batch_size <= 0:
            errors.append("batch_size must be a positive integer")
        elif self.batch_size > 1000:
            errors.append("batch_size should not exceed 1000 for performance reasons")
        
        # Validate claim_timeout_minutes
        if not isinstance(self.claim_timeout_minutes, int) or self.claim_timeout_minutes <= 0:
            errors.append("claim_timeout_minutes must be a positive integer")
        elif self.claim_timeout_minutes > 1440:  # 24 hours
            errors.append("claim_timeout_minutes should not exceed 1440 (24 hours)")
        
        # Validate heartbeat_interval_seconds
        if not isinstance(self.heartbeat_interval_seconds, int) or self.heartbeat_interval_seconds <= 0:
            errors.append("heartbeat_interval_seconds must be a positive integer")
        elif self.heartbeat_interval_seconds > 3600:  # 1 hour
            errors.append("heartbeat_interval_seconds should not exceed 3600 (1 hour)")
        
        # Validate max_retries
        if not isinstance(self.max_retries, int) or self.max_retries < 0:
            errors.append("max_retries must be a non-negative integer")
        elif self.max_retries > 10:
            errors.append("max_retries should not exceed 10 for practical reasons")
        
        # Validate connection_pool_size
        if not isinstance(self.connection_pool_size, int) or self.connection_pool_size <= 0:
            errors.append("connection_pool_size must be a positive integer")
        elif self.connection_pool_size > 100:
            errors.append("connection_pool_size should not exceed 100")
        
        # Validate connection_timeout_seconds
        if not isinstance(self.connection_timeout_seconds, int) or self.connection_timeout_seconds <= 0:
            errors.append("connection_timeout_seconds must be a positive integer")
        
        # Validate processing_delay_seconds
        if not isinstance(self.processing_delay_seconds, (int, float)) or self.processing_delay_seconds < 0:
            errors.append("processing_delay_seconds must be a non-negative number")

        # Validate no_work_delay_seconds
        if not isinstance(self.no_work_delay_seconds, (int, float)) or self.no_work_delay_seconds < 0:
            errors.append("no_work_delay_seconds must be a non-negative number")
        
        # Validate batch_claim_retry_delay_seconds
        if not isinstance(self.batch_claim_retry_delay_seconds, (int, float)) or self.batch_claim_retry_delay_seconds < 0:
            errors.append("batch_claim_retry_delay_seconds must be a non-negative number")
        
        # Validate max_batch_claim_retries
        if not isinstance(self.max_batch_claim_retries, int) or self.max_batch_claim_retries < 0:
            errors.append("max_batch_claim_retries must be a non-negative integer")
        
        # Validate log_level
        valid_log_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if self.log_level not in valid_log_levels:
            errors.append(f"log_level must be one of: {', '.join(valid_log_levels)}")
        
        # Validate health_check_port
        if self.health_check_port is not None:
            if not isinstance(self.health_check_port, int) or (self.health_check_port != 0 and not (1 <= self.health_check_port <= 65535)):
                errors.append("health_check_port must be an integer between 1 and 65535, or 0 for any available port")
        
        # Validate shutdown_timeout_seconds
        if not isinstance(self.shutdown_timeout_seconds, int) or self.shutdown_timeout_seconds <= 0:
            errors.append("shutdown_timeout_seconds must be a positive integer")
        
        # Validate stage_configs
        if not isinstance(self.stage_configs, dict):
            errors.append("stage_configs must be a dictionary")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def get_stage_config(self, stage: ProcessingStage, key: str, default: Any = None) -> Any:
        """Get stage-specific configuration value."""
        return self.stage_configs.get(stage.value, {}).get(key, default)
    
    def set_stage_config(self, stage: ProcessingStage, key: str, value: Any) -> None:
        """Set stage-specific configuration value."""
        if stage.value not in self.stage_configs:
            self.stage_configs[stage.value] = {}
        self.stage_configs[stage.value][key] = value
    
    def has_stage(self, stage: ProcessingStage) -> bool:
        """Check if worker is configured to handle a specific stage."""
        return stage in self.stages
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "worker_id": self.worker_id,
            "stages": [stage.value for stage in self.stages],
            "batch_size": self.batch_size,
            "claim_timeout_minutes": self.claim_timeout_minutes,
            "heartbeat_interval_seconds": self.heartbeat_interval_seconds,
            "max_retries": self.max_retries,
            "database_url": self.database_url,
            "connection_pool_size": self.connection_pool_size,
            "connection_timeout_seconds": self.connection_timeout_seconds,
            "processing_delay_seconds": self.processing_delay_seconds,
            "no_work_delay_seconds": self.no_work_delay_seconds,
            "batch_claim_retry_delay_seconds": self.batch_claim_retry_delay_seconds,
            "max_batch_claim_retries": self.max_batch_claim_retries,
            "log_level": self.log_level,
            "metrics_enabled": self.metrics_enabled,
            "health_check_port": self.health_check_port,
            "shutdown_timeout_seconds": self.shutdown_timeout_seconds,
            "stage_configs": self.stage_configs
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkerConfig':
        """Create configuration from dictionary."""
        # Convert stage strings back to enums
        stages = []
        for stage_str in data.get("stages", []):
            try:
                stages.append(ProcessingStage(stage_str))
            except ValueError:
                raise ValueError(f"Unknown processing stage: {stage_str}")
        
        # Create config with converted stages
        config_data = data.copy()
        config_data["stages"] = stages
        
        return cls(**config_data)
    
    def to_json(self) -> str:
        """Serialize configuration to JSON string."""
        return json.dumps(self.to_dict(), indent=2)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'WorkerConfig':
        """Deserialize configuration from JSON string."""
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON configuration: {e}")


class WorkerConfigLoader:
    """Utility class for loading worker configuration from various sources."""
    
    @staticmethod
    def from_environment(worker_id: Optional[str] = None, 
                        stages: Optional[List[str]] = None) -> WorkerConfig:
        """Load configuration from environment variables."""
        
        # Get worker_id from parameter or environment
        if worker_id is None:
            worker_id = os.getenv("WORKER_ID")

        # Always append timestamp for uniqueness, even if worker_id was provided
        import socket
        import uuid
        from datetime import datetime, timezone

        if not worker_id:
            # Generate completely new worker ID
            hostname = socket.gethostname()
            unique_id = str(uuid.uuid4())[:8]
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Remove last 3 digits to get milliseconds
            worker_id = f"{hostname}-{unique_id}-{timestamp}"
        else:
            # Append timestamp to provided worker_id for uniqueness
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Remove last 3 digits to get milliseconds
            worker_id = f"{worker_id}-{timestamp}"
        
        # Get stages from parameter or environment
        if stages is None:
            stages_env = os.getenv("WORKER_STAGES", "")
            if stages_env:
                stages = [s.strip() for s in stages_env.split(",") if s.strip()]
            else:
                # Default to all stages if none specified
                stages = [stage.value for stage in ProcessingStage]
        
        # Convert stage strings to enums
        stage_enums = []
        for stage_str in stages:
            try:
                stage_enums.append(ProcessingStage(stage_str))
            except ValueError:
                raise ValueError(f"Unknown processing stage in environment: {stage_str}")
        
        # Build configuration from environment variables
        config = WorkerConfig(
            worker_id=worker_id,
            stages=stage_enums,
            batch_size=int(os.getenv("WORKER_BATCH_SIZE", "25")),
            claim_timeout_minutes=int(os.getenv("WORKER_CLAIM_TIMEOUT_MINUTES", "30")),
            heartbeat_interval_seconds=int(os.getenv("WORKER_HEARTBEAT_INTERVAL_SECONDS", "60")),
            max_retries=int(os.getenv("WORKER_MAX_RETRIES", "3")),
            database_url=os.getenv("DATABASE_URL"),
            connection_pool_size=int(os.getenv("WORKER_CONNECTION_POOL_SIZE", "5")),
            connection_timeout_seconds=int(os.getenv("WORKER_CONNECTION_TIMEOUT_SECONDS", "30")),
            processing_delay_seconds=float(os.getenv("WORKER_PROCESSING_DELAY_SECONDS", "0.1")),
            no_work_delay_seconds=float(os.getenv("WORKER_NO_WORK_DELAY_SECONDS", "2.0")),
            batch_claim_retry_delay_seconds=float(os.getenv("WORKER_BATCH_CLAIM_RETRY_DELAY_SECONDS", "1.0")),
            max_batch_claim_retries=int(os.getenv("WORKER_MAX_BATCH_CLAIM_RETRIES", "5")),
            log_level=os.getenv("WORKER_LOG_LEVEL", "INFO"),
            metrics_enabled=os.getenv("WORKER_METRICS_ENABLED", "true").lower() == "true",
            health_check_port=int(os.getenv("WORKER_HEALTH_CHECK_PORT")) if os.getenv("WORKER_HEALTH_CHECK_PORT") else None,
            shutdown_timeout_seconds=int(os.getenv("WORKER_SHUTDOWN_TIMEOUT_SECONDS", "300"))
        )
        
        return config
    
    @staticmethod
    def from_file(file_path: Union[str, Path]) -> WorkerConfig:
        """Load configuration from JSON file."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_content = f.read()
            return WorkerConfig.from_json(json_content)
        except Exception as e:
            raise ValueError(f"Failed to load configuration from {file_path}: {e}")
    
    @staticmethod
    def from_dict_with_env_override(base_config: Dict[str, Any]) -> WorkerConfig:
        """Load configuration from dictionary with environment variable overrides."""
        config = base_config.copy()
        
        # Override with environment variables if they exist
        env_overrides = {
            "WORKER_ID": "worker_id",
            "WORKER_BATCH_SIZE": ("batch_size", int),
            "WORKER_CLAIM_TIMEOUT_MINUTES": ("claim_timeout_minutes", int),
            "WORKER_HEARTBEAT_INTERVAL_SECONDS": ("heartbeat_interval_seconds", int),
            "WORKER_MAX_RETRIES": ("max_retries", int),
            "DATABASE_URL": "database_url",
            "WORKER_CONNECTION_POOL_SIZE": ("connection_pool_size", int),
            "WORKER_CONNECTION_TIMEOUT_SECONDS": ("connection_timeout_seconds", int),
            "WORKER_PROCESSING_DELAY_SECONDS": ("processing_delay_seconds", float),
            "WORKER_NO_WORK_DELAY_SECONDS": ("no_work_delay_seconds", float),
            "WORKER_BATCH_CLAIM_RETRY_DELAY_SECONDS": ("batch_claim_retry_delay_seconds", float),
            "WORKER_MAX_BATCH_CLAIM_RETRIES": ("max_batch_claim_retries", int),
            "WORKER_LOG_LEVEL": "log_level",
            "WORKER_METRICS_ENABLED": ("metrics_enabled", lambda x: x.lower() == "true"),
            "WORKER_HEALTH_CHECK_PORT": ("health_check_port", lambda x: int(x) if x else None),
            "WORKER_SHUTDOWN_TIMEOUT_SECONDS": ("shutdown_timeout_seconds", int)
        }
        
        for env_var, config_key in env_overrides.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                if isinstance(config_key, tuple):
                    key, converter = config_key
                    try:
                        config[key] = converter(env_value)
                    except (ValueError, TypeError) as e:
                        raise ValueError(f"Invalid value for {env_var}: {env_value} ({e})")
                else:
                    config[config_key] = env_value
        
        # Handle stages separately
        stages_env = os.getenv("WORKER_STAGES")
        if stages_env:
            stages = [s.strip() for s in stages_env.split(",") if s.strip()]
            config["stages"] = stages
        
        return WorkerConfig.from_dict(config)


# Predefined configuration templates

def create_download_worker_config(worker_id: str, **kwargs) -> WorkerConfig:
    """Create configuration for a worker specialized in downloading full text."""
    defaults = {
        "stages": [ProcessingStage.DOWNLOAD_FULL_TEXT],
        "batch_size": 10,  # Smaller batches for network operations
        "claim_timeout_minutes": 15,  # Shorter timeout for downloads
        "max_retries": 5,  # More retries for network failures
    }
    defaults.update(kwargs)
    return WorkerConfig(worker_id=worker_id, **defaults)


def create_analysis_worker_config(worker_id: str, **kwargs) -> WorkerConfig:
    """Create configuration for a worker specialized in analysis tasks."""
    defaults = {
        "stages": [
            ProcessingStage.SENTIMENT_ANALYSIS,
            ProcessingStage.LLM_ANALYSIS,
            ProcessingStage.IPTC_CLASSIFICATION
        ],
        "batch_size": 5,  # Smaller batches for CPU-intensive operations
        "claim_timeout_minutes": 60,  # Longer timeout for analysis
        "processing_delay_seconds": 0.5,  # Longer delay between batches
    }
    defaults.update(kwargs)
    return WorkerConfig(worker_id=worker_id, **defaults)


def create_general_worker_config(worker_id: str, **kwargs) -> WorkerConfig:
    """Create configuration for a general-purpose worker."""
    defaults = {
        "stages": list(ProcessingStage),  # All stages
        "batch_size": 25,
        "claim_timeout_minutes": 30,
    }
    defaults.update(kwargs)
    return WorkerConfig(worker_id=worker_id, **defaults)