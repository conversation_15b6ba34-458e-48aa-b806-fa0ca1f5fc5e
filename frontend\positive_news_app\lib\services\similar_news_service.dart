import 'package:flutter/foundation.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/models/news_model.dart';

/// Service to manage similar news flags with efficient batch loading
class SimilarNewsService extends ChangeNotifier {
  final ApiService _apiService;
  final Map<String, bool> _similarFlags = {};
  final Set<String> _loadingEntries = {};
  final Set<String> _loadedEntries = {};

  SimilarNewsService(this._apiService);

  /// Get the similar flag for an entry ID
  bool? getSimilarFlag(String entryId) {
    return _similarFlags[entryId];
  }

  /// Check if an entry is currently being loaded
  bool isLoading(String entryId) {
    return _loadingEntries.contains(entryId);
  }

  /// Check if an entry has been loaded
  bool isLoaded(String entryId) {
    return _loadedEntries.contains(entryId);
  }

  /// Load similar flags for a batch of entries
  Future<void> loadSimilarFlags(List<String> entryIds) async {
    // Filter out already loaded or loading entries
    final toLoad = entryIds
        .where((id) => !_loadedEntries.contains(id) && !_loadingEntries.contains(id))
        .toList();

    if (toLoad.isEmpty) return;

    // Mark as loading
    _loadingEntries.addAll(toLoad);
    notifyListeners();

    try {
      final flags = await _apiService.checkSimilarNews(toLoad);
      
      // Update the flags
      _similarFlags.addAll(flags);
      _loadedEntries.addAll(toLoad);
      
      debugPrint('SimilarNewsService: Loaded ${flags.length} similar flags');
    } catch (e) {
      debugPrint('SimilarNewsService: Error loading similar flags: $e');
      // Mark as loaded even on error to prevent infinite retries
      _loadedEntries.addAll(toLoad);
    } finally {
      // Remove from loading set
      _loadingEntries.removeAll(toLoad);
      notifyListeners();
    }
  }

  /// Load similar flag for a single entry (uses batch loading internally)
  Future<bool> loadSimilarFlag(String entryId) async {
    if (_loadedEntries.contains(entryId)) {
      return _similarFlags[entryId] ?? false;
    }

    await loadSimilarFlags([entryId]);
    return _similarFlags[entryId] ?? false;
  }

  /// Update news items with loaded similar flags
  List<EntryWithSource> updateNewsWithSimilarFlags(List<EntryWithSource> newsItems) {
    return newsItems.map((news) {
      final similarFlag = _similarFlags[news.entryId];
      if (similarFlag != null && news.hasSimilar != similarFlag) {
        // Create a new instance with updated similar flag
        return EntryWithSource(
          entryId: news.entryId,
          title: news.title,
          link: news.link,
          source: news.source,
          sourceName: news.sourceName,
          description: news.description,
          published: news.published,
          fullText: news.fullText,
          iptcNewscode: news.iptcNewscode,
          categoryName: news.categoryName,
          llmPositive: news.llmPositive,
          llmReasonList: news.llmReasonList,
          dupEntryId: news.dupEntryId,
          descriptionAutoGenerated: news.descriptionAutoGenerated,
          previewImg: news.previewImg,
          hasSimilar: similarFlag,
          hasImage: news.hasImage,
        );
      }
      return news;
    }).toList();
  }

  /// Clear all cached data
  void clear() {
    _similarFlags.clear();
    _loadingEntries.clear();
    _loadedEntries.clear();
    notifyListeners();
  }

  /// Preload similar flags for news items that don't have them set
  Future<void> preloadSimilarFlags(List<EntryWithSource> newsItems) async {
    final entryIds = newsItems
        .where((news) => news.hasSimilar == null)
        .map((news) => news.entryId)
        .toList();

    if (entryIds.isNotEmpty) {
      await loadSimilarFlags(entryIds);
    }
  }
}