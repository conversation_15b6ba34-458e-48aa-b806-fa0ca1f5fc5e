#!/bin/bash

# Distributed ETL Workers Deployment Script
# This script helps deploy the distributed ETL system using Docker or Kubernetes

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOYMENT_DIR="$(dirname "$SCRIPT_DIR")"
PROJECT_ROOT="$(dirname "$(dirname "$DEPLOYMENT_DIR")")"

# Default values
ENVIRONMENT="development"
DEPLOYMENT_TYPE="docker"
NAMESPACE="breaking-bright-distributed"
IMAGE_TAG="latest"
BUILD_IMAGES="false"
VALIDATE_ONLY="false"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Distributed ETL Workers Deployment Script

Usage: $0 [OPTIONS]

OPTIONS:
    -e, --environment ENV       Environment to deploy (development|staging|production) [default: development]
    -t, --type TYPE            Deployment type (docker|kubernetes) [default: docker]
    -n, --namespace NAMESPACE  Kubernetes namespace [default: breaking-bright-distributed]
    -i, --image-tag TAG        Docker image tag [default: latest]
    -b, --build                Build Docker images before deployment
    -v, --validate-only        Only validate configuration, don't deploy
    -h, --help                 Show this help message

EXAMPLES:
    # Deploy to development using Docker Compose
    $0 -e development -t docker

    # Deploy to production using Kubernetes
    $0 -e production -t kubernetes -b

    # Validate staging configuration only
    $0 -e staging -t kubernetes -v

    # Deploy with custom image tag
    $0 -e production -t kubernetes -i v1.2.3 -b
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -t|--type)
            DEPLOYMENT_TYPE="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -i|--image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_IMAGES="true"
            shift
            ;;
        -v|--validate-only)
            VALIDATE_ONLY="true"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate arguments
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    log_error "Invalid environment: $ENVIRONMENT. Must be development, staging, or production."
    exit 1
fi

if [[ ! "$DEPLOYMENT_TYPE" =~ ^(docker|kubernetes)$ ]]; then
    log_error "Invalid deployment type: $DEPLOYMENT_TYPE. Must be docker or kubernetes."
    exit 1
fi

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker is not installed or not in PATH"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            log_error "Docker Compose is not installed or not in PATH"
            exit 1
        fi
    elif [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        if ! command -v kubectl &> /dev/null; then
            log_error "kubectl is not installed or not in PATH"
            exit 1
        fi
        
        # Check if kubectl can connect to cluster
        if ! kubectl cluster-info &> /dev/null; then
            log_error "Cannot connect to Kubernetes cluster"
            exit 1
        fi
    fi
    
    log_success "Prerequisites check passed"
}

# Validate configuration files
validate_configuration() {
    log_info "Validating configuration files..."
    
    # Check if environment config exists
    ENV_CONFIG="$DEPLOYMENT_DIR/config/$ENVIRONMENT.env"
    if [[ ! -f "$ENV_CONFIG" ]]; then
        log_error "Environment configuration not found: $ENV_CONFIG"
        exit 1
    fi
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        # Validate Docker Compose file
        COMPOSE_FILE="$DEPLOYMENT_DIR/docker/docker-compose.distributed.yml"
        if [[ ! -f "$COMPOSE_FILE" ]]; then
            log_error "Docker Compose file not found: $COMPOSE_FILE"
            exit 1
        fi
        
        # Validate Docker Compose syntax
        if ! docker-compose -f "$COMPOSE_FILE" config &> /dev/null; then
            log_error "Invalid Docker Compose configuration"
            exit 1
        fi
    elif [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        # Validate Kubernetes manifests
        K8S_DIR="$DEPLOYMENT_DIR/k8s"
        if [[ ! -d "$K8S_DIR" ]]; then
            log_error "Kubernetes manifests directory not found: $K8S_DIR"
            exit 1
        fi
        
        # Check if all required manifests exist
        REQUIRED_MANIFESTS=(
            "namespace.yaml"
            "configmap.yaml"
            "secrets.yaml"
            "worker-manager-deployment.yaml"
            "feed-workers-deployment.yaml"
            "fulltext-workers-deployment.yaml"
            "analysis-workers-deployment.yaml"
            "ml-workers-deployment.yaml"
            "generation-workers-deployment.yaml"
            "utility-workers-deployment.yaml"
            "cleanup-service-deployment.yaml"
        )
        
        for manifest in "${REQUIRED_MANIFESTS[@]}"; do
            if [[ ! -f "$K8S_DIR/$manifest" ]]; then
                log_error "Required Kubernetes manifest not found: $manifest"
                exit 1
            fi
        done
        
        # Validate YAML syntax
        for manifest in "$K8S_DIR"/*.yaml; do
            if ! kubectl apply --dry-run=client -f "$manifest" &> /dev/null; then
                log_error "Invalid Kubernetes manifest: $(basename "$manifest")"
                exit 1
            fi
        done
    fi
    
    log_success "Configuration validation passed"
}

# Build Docker images
build_images() {
    if [[ "$BUILD_IMAGES" == "true" ]]; then
        log_info "Building Docker images..."
        
        cd "$PROJECT_ROOT"
        
        # Build distributed worker image
        log_info "Building distributed worker image..."
        docker build \
            -f backend/deployment/docker/Dockerfile.distributed-worker \
            -t "robertschulze/breaking-bright-worker:$IMAGE_TAG" \
            .
        
        # Build backend image (if needed)
        log_info "Building backend image..."
        docker build \
            -f backend/Dockerfile \
            -t "robertschulze/breaking-bright-backend:$IMAGE_TAG" \
            .
        
        log_success "Docker images built successfully"
    fi
}

# Deploy using Docker Compose
deploy_docker() {
    log_info "Deploying using Docker Compose..."
    
    cd "$DEPLOYMENT_DIR/docker"
    
    # Load environment configuration
    export $(grep -v '^#' "../config/$ENVIRONMENT.env" | xargs)
    
    # Set image tag
    export IMAGE_TAG="$IMAGE_TAG"
    
    # Deploy services
    docker-compose -f docker-compose.distributed.yml down
    docker-compose -f docker-compose.distributed.yml up -d
    
    log_success "Docker Compose deployment completed"
    
    # Show status
    log_info "Service status:"
    docker-compose -f docker-compose.distributed.yml ps
}

# Deploy using Kubernetes
deploy_kubernetes() {
    log_info "Deploying using Kubernetes..."
    
    cd "$DEPLOYMENT_DIR/k8s"
    
    # Create namespace
    log_info "Creating namespace: $NAMESPACE"
    kubectl apply -f namespace.yaml
    
    # Apply ConfigMap with environment-specific values
    log_info "Applying ConfigMap..."
    
    # Create temporary ConfigMap with environment-specific values
    TEMP_CONFIGMAP=$(mktemp)
    cp configmap.yaml "$TEMP_CONFIGMAP"
    
    # Replace values from environment config
    while IFS='=' read -r key value; do
        if [[ ! "$key" =~ ^#.*$ ]] && [[ -n "$key" ]] && [[ -n "$value" ]]; then
            # Remove quotes from value if present
            value=$(echo "$value" | sed 's/^"//;s/"$//')
            # Update ConfigMap
            sed -i "s|$key:.*|$key: \"$value\"|g" "$TEMP_CONFIGMAP"
        fi
    done < "../config/$ENVIRONMENT.env"
    
    kubectl apply -f "$TEMP_CONFIGMAP"
    rm "$TEMP_CONFIGMAP"
    
    # Apply secrets (user needs to update with actual values)
    log_warning "Please update secrets.yaml with actual secret values before applying"
    kubectl apply -f secrets.yaml
    
    # Apply all deployments
    log_info "Applying deployments..."
    for deployment in *-deployment.yaml; do
        log_info "Applying $deployment..."
        kubectl apply -f "$deployment"
    done
    
    log_success "Kubernetes deployment completed"
    
    # Show status
    log_info "Deployment status:"
    kubectl get pods -n "$NAMESPACE"
}

# Check deployment health
check_health() {
    log_info "Checking deployment health..."
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        cd "$DEPLOYMENT_DIR/docker"
        
        # Check if all services are running
        FAILED_SERVICES=$(docker-compose -f docker-compose.distributed.yml ps --services --filter "status=exited")
        if [[ -n "$FAILED_SERVICES" ]]; then
            log_error "Some services failed to start: $FAILED_SERVICES"
            return 1
        fi
        
        # Check health endpoints (if accessible)
        log_info "Checking health endpoints..."
        for port in 8080 8081 8082 8083 8084 8085 8086 8087 8088 8089; do
            if curl -f "http://localhost:$port/health" &> /dev/null; then
                log_success "Health check passed for port $port"
            else
                log_warning "Health check failed for port $port (service may still be starting)"
            fi
        done
        
    elif [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        # Wait for deployments to be ready
        log_info "Waiting for deployments to be ready..."
        kubectl wait --for=condition=available --timeout=300s deployment --all -n "$NAMESPACE"
        
        # Check pod status
        FAILED_PODS=$(kubectl get pods -n "$NAMESPACE" --field-selector=status.phase!=Running --no-headers | wc -l)
        if [[ "$FAILED_PODS" -gt 0 ]]; then
            log_error "$FAILED_PODS pods are not running"
            kubectl get pods -n "$NAMESPACE"
            return 1
        fi
    fi
    
    log_success "Health check passed"
}

# Main deployment function
main() {
    log_info "Starting deployment..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Deployment Type: $DEPLOYMENT_TYPE"
    log_info "Image Tag: $IMAGE_TAG"
    
    check_prerequisites
    validate_configuration
    
    if [[ "$VALIDATE_ONLY" == "true" ]]; then
        log_success "Validation completed successfully"
        exit 0
    fi
    
    build_images
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        deploy_docker
    elif [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        deploy_kubernetes
    fi
    
    # Wait a bit for services to start
    log_info "Waiting for services to start..."
    sleep 30
    
    check_health
    
    log_success "Deployment completed successfully!"
    
    # Show next steps
    cat << EOF

Next Steps:
1. Monitor the deployment logs
2. Check worker health endpoints
3. Verify that work is being processed
4. Set up monitoring and alerting

EOF
    
    if [[ "$DEPLOYMENT_TYPE" == "docker" ]]; then
        cat << EOF
Docker Commands:
- View logs: docker-compose -f $DEPLOYMENT_DIR/docker/docker-compose.distributed.yml logs -f
- Stop services: docker-compose -f $DEPLOYMENT_DIR/docker/docker-compose.distributed.yml down
- Restart services: docker-compose -f $DEPLOYMENT_DIR/docker/docker-compose.distributed.yml restart

EOF
    elif [[ "$DEPLOYMENT_TYPE" == "kubernetes" ]]; then
        cat << EOF
Kubernetes Commands:
- View logs: kubectl logs -f deployment/worker-manager -n $NAMESPACE
- Check status: kubectl get pods -n $NAMESPACE
- Delete deployment: kubectl delete namespace $NAMESPACE

EOF
    fi
}

# Run main function
main