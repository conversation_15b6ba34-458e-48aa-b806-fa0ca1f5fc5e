import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/services/similar_news_service.dart';
import 'package:breakingbright/services/api_service.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

@GenerateMocks([ApiService])
import 'service_test.mocks.dart';

void main() {
  group('Lazy Loading Core Tests', () {
    late MockApiService mockApiService;
    late SimilarNewsService similarNewsService;

    setUp(() {
      mockApiService = MockApiService();
      similarNewsService = SimilarNewsService(mockApiService);
    });

    group('API Service Tests', () {
      test('checkSimilarNews returns correct batch results', () async {
        final entryIds = ['id1', 'id2', 'id3'];
        final expectedResult = {
          'id1': true,
          'id2': false,
          'id3': true,
        };

        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => expectedResult);

        final result = await mockApiService.checkSimilarNews(entryIds);

        expect(result, expectedResult);
        verify(mockApiService.checkSimilarNews(entryIds)).called(1);
      });

      test('hasSimilarNews uses checkSimilarNews internally', () async {
        // Create a real ApiService to test the actual implementation
        final realApiService = ApiService();
        
        // We can't easily test the internal implementation without mocking HTTP calls
        // So let's test the logic by checking that hasSimilarNews returns a boolean
        // This test would need actual HTTP mocking for full coverage
        expect(realApiService.hasSimilarNews('test_id'), isA<Future<bool>>());
      });

      test('checkSimilarNews handles empty list', () async {
        // Create a real ApiService to test the actual implementation
        final realApiService = ApiService();
        
        final result = await realApiService.checkSimilarNews([]);
        expect(result, isEmpty);
      });
    });

    group('SimilarNewsService Core Tests', () {
      test('loads and caches similar flags correctly', () async {
        final entryIds = ['id1', 'id2'];
        final expectedFlags = {'id1': true, 'id2': false};

        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => expectedFlags);

        await similarNewsService.loadSimilarFlags(entryIds);

        expect(similarNewsService.getSimilarFlag('id1'), true);
        expect(similarNewsService.getSimilarFlag('id2'), false);
        expect(similarNewsService.isLoaded('id1'), true);
        expect(similarNewsService.isLoaded('id2'), true);
      });

      test('avoids duplicate API calls', () async {
        final entryIds = ['id1'];
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => {'id1': true});

        await similarNewsService.loadSimilarFlags(entryIds);
        await similarNewsService.loadSimilarFlags(entryIds); // Second call

        verify(mockApiService.checkSimilarNews(entryIds)).called(1);
      });

      test('handles API errors gracefully', () async {
        final entryIds = ['error_id'];
        when(mockApiService.checkSimilarNews(entryIds))
            .thenThrow(Exception('Network error'));

        await similarNewsService.loadSimilarFlags(entryIds);

        expect(similarNewsService.isLoaded('error_id'), true);
        expect(similarNewsService.getSimilarFlag('error_id'), null);
      });

      test('clear removes all cached data', () async {
        final entryIds = ['id1'];
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => {'id1': true});

        await similarNewsService.loadSimilarFlags(entryIds);
        expect(similarNewsService.isLoaded('id1'), true);

        similarNewsService.clear();

        expect(similarNewsService.getSimilarFlag('id1'), null);
        expect(similarNewsService.isLoaded('id1'), false);
      });
    });

    group('News Model Tests', () {
      test('EntryWithSource handles null hasSimilar correctly', () {
        final jsonData = {
          'entry_id': 'test_id',
          'title': 'Test Title',
          'link': 'https://example.com',
          'source': 'test_source',
          'published': '2024-01-01T12:00:00Z',
          'has_similar': null,
        };

        final entry = EntryWithSource.fromJson(jsonData);
        expect(entry.hasSimilar, null);
      });

      test('EntryWithSource handles boolean hasSimilar correctly', () {
        final jsonTrue = {
          'entry_id': 'test_id_1',
          'title': 'Test Title 1',
          'link': 'https://example.com/1',
          'source': 'test_source',
          'published': '2024-01-01T12:00:00Z',
          'has_similar': true,
        };

        final jsonFalse = {
          'entry_id': 'test_id_2',
          'title': 'Test Title 2',
          'link': 'https://example.com/2',
          'source': 'test_source',
          'published': '2024-01-01T12:00:00Z',
          'has_similar': false,
        };

        final entryTrue = EntryWithSource.fromJson(jsonTrue);
        final entryFalse = EntryWithSource.fromJson(jsonFalse);

        expect(entryTrue.hasSimilar, true);
        expect(entryFalse.hasSimilar, false);
      });
    });

    group('Integration Tests', () {
      test('complete lazy loading workflow', () async {
        // Step 1: Create news items with null hasSimilar
        final newsItems = [
          EntryWithSource(
            entryId: 'id1',
            title: 'News 1',
            link: 'https://example.com/1',
            source: 'source1',
            published: DateTime.now(),
            hasSimilar: null,
          ),
          EntryWithSource(
            entryId: 'id2',
            title: 'News 2',
            link: 'https://example.com/2',
            source: 'source2',
            published: DateTime.now(),
            hasSimilar: null,
          ),
        ];

        // Step 2: Mock API response
        when(mockApiService.checkSimilarNews(['id1', 'id2']))
            .thenAnswer((_) async => {'id1': true, 'id2': false});

        // Step 3: Preload similar flags
        await similarNewsService.preloadSimilarFlags(newsItems);

        // Step 4: Update news items with cached flags
        final updatedNews = similarNewsService.updateNewsWithSimilarFlags(newsItems);

        // Step 5: Verify results
        expect(updatedNews[0].hasSimilar, true);
        expect(updatedNews[1].hasSimilar, false);
        
        // Original items should be unchanged
        expect(newsItems[0].hasSimilar, null);
        expect(newsItems[1].hasSimilar, null);
      });

      test('mixed loading scenario', () async {
        // Some items already have hasSimilar set, others don't
        final newsItems = [
          EntryWithSource(
            entryId: 'id1',
            title: 'News 1',
            link: 'https://example.com/1',
            source: 'source1',
            published: DateTime.now(),
            hasSimilar: null, // Should be loaded
          ),
          EntryWithSource(
            entryId: 'id2',
            title: 'News 2',
            link: 'https://example.com/2',
            source: 'source2',
            published: DateTime.now(),
            hasSimilar: true, // Should be skipped
          ),
        ];

        when(mockApiService.checkSimilarNews(['id1']))
            .thenAnswer((_) async => {'id1': false});

        await similarNewsService.preloadSimilarFlags(newsItems);

        // Only id1 should have been loaded
        expect(similarNewsService.isLoaded('id1'), true);
        expect(similarNewsService.isLoaded('id2'), false);
        expect(similarNewsService.getSimilarFlag('id1'), false);
        expect(similarNewsService.getSimilarFlag('id2'), null);
      });
    });

    group('Performance Tests', () {
      test('batch loading is more efficient than individual calls', () async {
        final entryIds = ['id1', 'id2', 'id3'];
        
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async => {
              'id1': true,
              'id2': false,
              'id3': true,
            });

        // Batch load
        await similarNewsService.loadSimilarFlags(entryIds);

        // Should only call API once for all entries
        verify(mockApiService.checkSimilarNews(entryIds)).called(1);
        
        // All entries should be loaded
        for (final id in entryIds) {
          expect(similarNewsService.isLoaded(id), true);
        }
      });

      test('concurrent loads are handled correctly', () async {
        final entryIds = ['id1'];
        
        when(mockApiService.checkSimilarNews(entryIds))
            .thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 10));
          return {'id1': true};
        });

        // Start multiple concurrent loads
        final futures = [
          similarNewsService.loadSimilarFlags(entryIds),
          similarNewsService.loadSimilarFlags(entryIds),
          similarNewsService.loadSimilarFlags(entryIds),
        ];

        await Future.wait(futures);

        // Should only call API once despite multiple concurrent requests
        verify(mockApiService.checkSimilarNews(entryIds)).called(1);
        expect(similarNewsService.getSimilarFlag('id1'), true);
      });
    });
  });
}