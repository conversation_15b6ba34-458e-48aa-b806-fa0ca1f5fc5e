# Distributed ETL Workers Deployment

This directory contains deployment configurations and tools for the distributed ETL processing system.

## Overview

The distributed ETL system replaces the monolithic thread-based processing with a scalable, distributed architecture where work is divided into batches and processed by specialized workers.

## Directory Structure

```
deployment/
├── docker/                     # Docker deployment configurations
│   ├── Dockerfile.distributed-worker
│   └── docker-compose.distributed.yml
├── k8s/                       # Kubernetes deployment manifests
│   ├── namespace.yaml
│   ├── configmap.yaml
│   ├── secrets.yaml
│   ├── worker-manager-deployment.yaml
│   ├── feed-workers-deployment.yaml
│   ├── fulltext-workers-deployment.yaml
│   ├── analysis-workers-deployment.yaml
│   ├── ml-workers-deployment.yaml
│   ├── generation-workers-deployment.yaml
│   ├── utility-workers-deployment.yaml
│   └── cleanup-service-deployment.yaml
├── config/                    # Environment-specific configurations
│   ├── development.env
│   ├── staging.env
│   └── production.env
├── scripts/                   # Deployment scripts
│   └── deploy.sh
├── tests/                     # Deployment validation tests
│   └── test_deployment_validation.py
└── README.md                  # This file
```

## Worker Types

The system includes several specialized worker types:

### 1. Worker Manager
- **Purpose**: Coordinates and monitors distributed workers
- **Replicas**: 1
- **Resources**: 512Mi RAM, 250m CPU
- **Health Check**: Port 8080

### 2. Feed Workers
- **Purpose**: Download and parse RSS feeds
- **Replicas**: 2
- **Batch Size**: 50 entries
- **Timeout**: 15 minutes
- **Resources**: 256Mi RAM, 100m CPU

### 3. Full Text Workers
- **Purpose**: Extract full article content
- **Replicas**: 2
- **Batch Size**: 10 entries
- **Timeout**: 20 minutes
- **Resources**: 256Mi RAM, 100m CPU

### 4. Analysis Workers
- **Purpose**: Sentiment analysis and IPTC classification
- **Replicas**: 2
- **Batch Size**: 5 entries
- **Timeout**: 60 minutes
- **Resources**: 1Gi RAM, 500m CPU

### 5. ML Workers
- **Purpose**: Translation and embedding computation
- **Replicas**: 1
- **Batch Size**: 8 entries
- **Timeout**: 45 minutes
- **Resources**: 2Gi RAM, 1000m CPU

### 6. Generation Workers
- **Purpose**: Description and image generation
- **Replicas**: 1
- **Batch Size**: 3 entries
- **Timeout**: 90 minutes
- **Resources**: 1Gi RAM, 500m CPU

### 7. Utility Workers
- **Purpose**: Duplicate checking and RSS generation
- **Replicas**: 1
- **Batch Size**: 15 entries
- **Timeout**: 30 minutes
- **Resources**: 512Mi RAM, 250m CPU

### 8. Cleanup Service
- **Purpose**: Clean up stale work claims
- **Replicas**: 1
- **Interval**: 5 minutes
- **Resources**: 128Mi RAM, 50m CPU

## Deployment Methods

### Docker Compose Deployment

**Prerequisites:**
- Docker and Docker Compose installed
- Environment variables configured

**Quick Start:**
```bash
# Deploy to development environment
./scripts/deploy.sh -e development -t docker -b

# View logs
docker-compose -f docker/docker-compose.distributed.yml logs -f

# Stop services
docker-compose -f docker/docker-compose.distributed.yml down
```

**Manual Deployment:**
```bash
cd docker/
export $(grep -v '^#' ../config/development.env | xargs)
docker-compose -f docker-compose.distributed.yml up -d
```

### Kubernetes Deployment

**Prerequisites:**
- kubectl configured with cluster access
- Kubernetes cluster with sufficient resources
- Docker images built and pushed to registry

**Quick Start:**
```bash
# Deploy to production environment
./scripts/deploy.sh -e production -t kubernetes -b

# Check status
kubectl get pods -n breaking-bright-distributed

# View logs
kubectl logs -f deployment/worker-manager -n breaking-bright-distributed
```

**Manual Deployment:**
```bash
cd k8s/
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secrets.yaml
kubectl apply -f *-deployment.yaml
```

## Configuration

### Environment Variables

Each environment (development, staging, production) has its own configuration file in the `config/` directory.

**Key Configuration Variables:**

| Variable | Description | Default |
|----------|-------------|---------|
| `WORKER_LOG_LEVEL` | Logging level | INFO |
| `WORKER_BATCH_SIZE` | Default batch size | 25 |
| `WORKER_CLAIM_TIMEOUT_MINUTES` | Work claim timeout | 30 |
| `WORKER_MAX_RETRIES` | Maximum retry attempts | 3 |
| `WORKER_HEARTBEAT_INTERVAL_SECONDS` | Heartbeat frequency | 60 |
| `DATABASE_URL` | Database connection string | Required |
| `OPENAI_API_KEY` | OpenAI API key | Required |
| `IONOS_API_KEY` | IONOS API key | Required |

### Stage-Specific Configuration

Each worker type can have specialized configuration:

```bash
# Feed workers
FEED_WORKER_BATCH_SIZE=50
FEED_WORKER_CLAIM_TIMEOUT_MINUTES=15
FEED_WORKER_MAX_RETRIES=5

# Analysis workers
ANALYSIS_WORKER_BATCH_SIZE=5
ANALYSIS_WORKER_CLAIM_TIMEOUT_MINUTES=60
ANALYSIS_WORKER_PROCESSING_DELAY_SECONDS=0.5
```

### Secrets Management

**Docker Compose:**
- Use `.env` file in project root
- Environment variables are loaded automatically

**Kubernetes:**
- Update `secrets.yaml` with base64-encoded values
- Use external secret management tools in production

```bash
# Encode secrets for Kubernetes
echo -n "your-secret-value" | base64
```

## Scaling

### Docker Compose Scaling

```bash
# Scale specific services
docker-compose -f docker-compose.distributed.yml up -d --scale feed-worker-1=3
```

### Kubernetes Scaling

```bash
# Scale deployments
kubectl scale deployment feed-workers --replicas=5 -n breaking-bright-distributed

# Auto-scaling (requires metrics server)
kubectl autoscale deployment feed-workers --cpu-percent=70 --min=2 --max=10 -n breaking-bright-distributed
```

## Monitoring

### Health Checks

All workers expose health check endpoints:
- `/health` - Liveness probe
- `/ready` - Readiness probe
- `/metrics` - Prometheus metrics (if enabled)

### Docker Compose Monitoring

```bash
# Check service status
docker-compose -f docker-compose.distributed.yml ps

# View logs
docker-compose -f docker-compose.distributed.yml logs -f [service-name]

# Check health endpoints
curl http://localhost:8080/health
```

### Kubernetes Monitoring

```bash
# Check pod status
kubectl get pods -n breaking-bright-distributed

# View logs
kubectl logs -f deployment/worker-manager -n breaking-bright-distributed

# Check events
kubectl get events -n breaking-bright-distributed --sort-by='.lastTimestamp'
```

## Troubleshooting

### Common Issues

**1. Workers not claiming work**
- Check database connectivity
- Verify processing_status column exists
- Check worker configuration

**2. High resource usage**
- Reduce batch sizes
- Increase processing delays
- Scale down replicas

**3. Stale work claims**
- Check cleanup service logs
- Verify timeout configurations
- Manually release claims if needed

### Debugging Commands

**Docker:**
```bash
# Enter worker container
docker exec -it <container-name> /bin/bash

# Check worker logs
docker logs <container-name>

# Restart specific service
docker-compose restart <service-name>
```

**Kubernetes:**
```bash
# Describe pod issues
kubectl describe pod <pod-name> -n breaking-bright-distributed

# Get pod logs
kubectl logs <pod-name> -n breaking-bright-distributed

# Execute commands in pod
kubectl exec -it <pod-name> -n breaking-bright-distributed -- /bin/bash
```

### Performance Tuning

**Batch Size Optimization:**
- Small batches (5-10): Lower latency, higher overhead
- Medium batches (25-50): Balanced performance
- Large batches (100+): Higher throughput, increased failure impact

**Resource Allocation:**
- CPU-intensive stages: Increase CPU limits
- Memory-intensive stages: Increase memory limits
- I/O-intensive stages: Optimize batch sizes

**Database Optimization:**
- Ensure proper indexes exist
- Monitor connection pool usage
- Tune query timeouts

## Security Considerations

### Container Security

- Workers run as non-root user
- Minimal base image (python:3.13-slim)
- No unnecessary packages installed
- Health checks configured

### Network Security

- Internal communication only
- No external ports exposed (except health checks)
- Network policies can be applied in Kubernetes

### Secret Management

- Never commit secrets to version control
- Use external secret management in production
- Rotate secrets regularly
- Limit secret access to necessary services

## Migration from Monolithic System

### Gradual Migration

1. **Phase 1**: Deploy distributed system alongside monolithic
2. **Phase 2**: Migrate one processing stage at a time
3. **Phase 3**: Monitor and validate results
4. **Phase 4**: Complete migration and remove monolithic code

### Rollback Plan

1. Stop distributed workers
2. Re-enable monolithic threads
3. Clean up any incomplete work
4. Monitor for data consistency

## Testing

### Validation Tests

Run deployment validation tests:

```bash
cd tests/
python -m pytest test_deployment_validation.py -v
```

### Integration Testing

```bash
# Test Docker deployment
./scripts/deploy.sh -e development -t docker -v

# Test Kubernetes deployment
./scripts/deploy.sh -e staging -t kubernetes -v
```

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review worker logs
3. Validate configuration files
4. Run deployment validation tests
5. Check system resources and connectivity

## Contributing

When adding new worker types or modifying configurations:

1. Update deployment manifests
2. Add environment-specific configurations
3. Update validation tests
4. Update this documentation
5. Test in development environment first