apiVersion: v1
kind: ConfigMap
metadata:
  name: distributed-worker-config
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: distributed-etl
data:
  # Global worker configuration
  WORKER_LOG_LEVEL: "INFO"
  WORKER_METRICS_ENABLED: "true"
  WORKER_CONNECTION_POOL_SIZE: "5"
  WORKER_CONNECTION_TIMEOUT_SECONDS: "30"
  WORKER_PROCESSING_DELAY_SECONDS: "0.1"
  WORKER_BATCH_CLAIM_RETRY_DELAY_SECONDS: "1.0"
  WORKER_MAX_BATCH_CLAIM_RETRIES: "5"
  WORKER_SHUTDOWN_TIMEOUT_SECONDS: "300"
  
  # Stage-specific configurations
  FEED_WORKER_BATCH_SIZE: "50"
  FEED_WORKER_CLAIM_TIMEOUT_MINUTES: "15"
  FEED_WORKER_MAX_RETRIES: "5"
  
  FULLTEXT_WORKER_BATCH_SIZE: "10"
  FULLTEXT_WORKER_CLAIM_TIMEOUT_MINUTES: "20"
  FULLTEXT_WORKER_MAX_RETRIES: "3"
  
  ANALYSIS_WORKER_BATCH_SIZE: "5"
  ANALYSIS_WORKER_CLAIM_TIMEOUT_MINUTES: "60"
  ANALYSIS_WORKER_MAX_RETRIES: "3"
  ANALYSIS_WORKER_PROCESSING_DELAY_SECONDS: "0.5"
  
  ML_WORKER_BATCH_SIZE: "8"
  ML_WORKER_CLAIM_TIMEOUT_MINUTES: "45"
  ML_WORKER_MAX_RETRIES: "3"
  
  GENERATION_WORKER_BATCH_SIZE: "3"
  GENERATION_WORKER_CLAIM_TIMEOUT_MINUTES: "90"
  GENERATION_WORKER_MAX_RETRIES: "2"
  
  UTILITY_WORKER_BATCH_SIZE: "15"
  UTILITY_WORKER_CLAIM_TIMEOUT_MINUTES: "30"
  UTILITY_WORKER_MAX_RETRIES: "3"
  
  # Cleanup service configuration
  CLEANUP_INTERVAL_MINUTES: "5"
  STALE_TIMEOUT_MINUTES: "60"
  
  # Health check configuration
  WORKER_HEARTBEAT_INTERVAL_SECONDS: "60"