"""Add description_auto_generated column

Revision ID: 0859eceb52a2
Revises: 55dddefdc455
Create Date: 2025-04-21 11:43:33.790248

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0859eceb52a2'
down_revision: Union[str, None] = '55dddefdc455'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('entries', sa.Column('description_auto_generated', sa.<PERSON>(), nullable=True))


def downgrade() -> None:
    op.drop_column('entries', 'description_auto_generated')
