import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:video_player/video_player.dart';

class OnboardingScreen extends StatefulWidget {
  final VoidCallback onComplete;

  const OnboardingScreen({super.key, required this.onComplete});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  final PageController _videoPageController = PageController(); // Separate controller for videos
  List<VideoPlayerController>? _videoControllers;
  int _currentPage = 0;
  bool _cookiesAccepted = false;
  bool _isInitializing = true;

  final List<String> _videoAssets = [
    'assets/videos/4790096_Boy_Bucket_1280x720.mp4',
    'assets/videos/6036881_Office_People_1280x720.mp4',
    'assets/videos/253998_small.mp4',
  ];

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Willkommen bei Breaking Bright',
      description: 'Jeden Tag wird unsere Welt ein bisschen besser. Wir zeigen es Dir!',
      image: 'assets/icons/logo.svg',
    ),
    OnboardingPage(
      title: 'Fokus auf positive Nachrichten',
      description: 'Unser Alltag ist voller Erfolge, nicht Katastrophen. Breaking Bright legt den richtigen Fokus.',
      image: 'assets/icons/sun_thumbsUp2.svg',
    ),
    OnboardingPage(
      title: 'Maßgeschneidert für Dich',
      description: 'Gemeinschaft, Natur, Wissenschaft, Livestyle... Nutze die Optionen um für Dich relevante Kategorien und Quellen zu definieren.',
      image: 'assets/icons/sun_rocket.svg',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayers();
  }

  Future<void> _initializeVideoPlayers() async {
    debugPrint('Starting video initialization');
    try {
      final initializedControllers = <VideoPlayerController>[];
      
      for (final asset in _videoAssets) {
        debugPrint('Attempting to initialize: $asset');
        final controller = VideoPlayerController.asset(asset);
        
        try {
          await controller.initialize();
          debugPrint('Successfully initialized: $asset');
          await controller.setLooping(true);
          await controller.setVolume(0.0); // Mute videos
          
          if (_videoAssets.indexOf(asset) == _currentPage) {
            debugPrint('Playing first video: $asset');
            await controller.play();
          }
          
          initializedControllers.add(controller);
        } catch (e) {
          debugPrint('Failed to initialize video $asset: $e');
          controller.dispose();
        }
      }

      if (mounted) {
        setState(() {
          _videoControllers = initializedControllers;
          _isInitializing = false;
        });
      }
    } catch (e) {
      debugPrint('Error in _initializeVideoPlayers: $e');
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _videoControllers?.forEach((controller) {
      controller.dispose();
    });
    _pageController.dispose();
    _videoPageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int page) {
    if (_videoControllers == null || _videoControllers!.isEmpty) return;
    
    setState(() {
      // Pause the current video
      _videoControllers![_currentPage].pause();
      
      // Update current page
      _currentPage = page;
      
      // Animate video page
      _videoPageController.animateToPage(
        page,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      ).then((_) {
        // Play the new video after animation completes
        if (_videoControllers![page].value.isInitialized) {
          _videoControllers![page].play();
        }
      });
    });
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else if (_cookiesAccepted) {
      widget.onComplete();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Background video
            if (_isInitializing)
              const Center(child: CircularProgressIndicator())
            else if (_videoControllers != null && _videoControllers!.isNotEmpty)
              Positioned.fill(
                child: PageView.builder(
                  controller: _videoPageController,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: _videoControllers!.length,
                  itemBuilder: (context, index) {
                    final controller = _videoControllers![index];
                    if (!controller.value.isInitialized) {
                      return Container(color: Colors.black);
                    }
                    return SizedBox.expand(
                      child: FittedBox(
                        fit: BoxFit.cover,
                        child: SizedBox(
                          width: controller.value.size.width,
                          height: controller.value.size.height,
                          child: VideoPlayer(controller),
                        ),
                      ),
                    );
                  },
                ),
              )
            else
              Container(color: Colors.black),

            // Overlay with gradient
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withAlpha(153),  // Reduced from 204
                      Colors.black.withAlpha(102),  // Reduced from 153
                      Colors.black.withAlpha(51),   // Reduced from 102
                      Colors.black.withAlpha(102),  // Reduced from 153
                      Colors.black.withAlpha(153),  // Reduced from 204
                    ],
                  ),
                ),
              ),
            ),

            // Content with text shadow
            Column(
              children: [
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: _pages.length,
                    onPageChanged: _onPageChanged,
                    itemBuilder: (context, index) {
                      return _buildPage(_pages[index]);
                    },
                  ),
                ),
                if (_currentPage == _pages.length - 1)
                  _buildCookieConsent(),
                _buildPageIndicator(),
                _buildNavigationButtons(),
                const SizedBox(height: 20),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0),
        child: IntrinsicHeight(
          child: Container(
            padding: const EdgeInsets.all(24.0),
            decoration: BoxDecoration(
              color: Colors.black.withAlpha(110),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Add glow effect to icons
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withAlpha(128),
                        blurRadius: 3,
                        spreadRadius: 15,
                      ),
                    ],
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: page.image is IconData
                        ? Icon(
                            page.image as IconData,
                            size: 120,
                            color: Theme.of(context).colorScheme.primary,
                          )
                        : SvgPicture.asset(
                            page.image as String,
                            height: 120,
                            width: 120,
                          ),
                  ),
                ),
                const SizedBox(height: 40),
                Text(
                  page.title,
                  style: Theme.of(context).textTheme.displaySmall?.copyWith(
                        color: Colors.white,
                      ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Text(
                  page.description,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white,
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCookieConsent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      child: Container(
        padding: const EdgeInsets.all(24.0),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(110),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              'Cookie-Zustimmung',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Wir verwenden Cookies, um Ihre Erfahrung zu verbessern und Ihre Einstellungen zu speichern.',
              style: TextStyle(
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _cookiesAccepted = false;
                    });
                    widget.onComplete();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: const Text('Ablehnen'),
                ),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _cookiesAccepted = true;
                    });
                    widget.onComplete();
                  },
                  child: const Text('Akzeptieren'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _pages.length,
          (index) => _buildIndicator(index == _currentPage),
        ),
      ),
    );
  }

  Widget _buildIndicator(bool isActive) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      height: 8.0,
      width: isActive ? 24.0 : 8.0,
      decoration: BoxDecoration(
        color: isActive
            ? Theme.of(context).colorScheme.primary
            : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(4.0),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _currentPage > 0
              ? TextButton(
                  onPressed: _previousPage,
                  child: const Text('Zurück'),
                )
              : const SizedBox(width: 80),
          ElevatedButton(
            onPressed: _currentPage == _pages.length - 1 && !_cookiesAccepted
                ? null  // Disable button if on last page and cookies not accepted
                : _nextPage,
            child: Text(
              _currentPage < _pages.length - 1 ? 'Weiter' : 'Fertig',
            ),
          ),
        ],
      ),
    );
  }
}

class OnboardingPage {
  final String title;
  final String description;
  final dynamic image;  // Can be either IconData or String (for SVG path)

  OnboardingPage({
    required this.title,
    required this.description,
    required this.image,
  });
}
