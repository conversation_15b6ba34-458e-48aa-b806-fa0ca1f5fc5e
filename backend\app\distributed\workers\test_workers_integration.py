"""
Integration test for all distributed workers.

Tests that all workers can be imported and instantiated correctly.
"""

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from backend.app.distributed.workers import (
    FullTextDownloadWorker,
    SentimentAnalysisWorker,
    LLMAnalysisWorker
)
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Base


class TestWorkersIntegration:
    """Integration tests for all distributed workers."""
    
    @pytest.fixture
    def db_session_factory(self):
        """Create an in-memory SQLite database for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        SessionLocal = sessionmaker(bind=engine)
        
        def get_session():
            return SessionLocal()
        
        return get_session
    
    def test_all_workers_can_be_imported(self):
        """Test that all workers can be imported successfully."""
        # This test passes if the imports at the top of the file succeed
        assert FullTextDownloadWorker is not None
        assert SentimentAnalysisWorker is not None
        assert LLMAnalysisWorker is not None
    
    def test_full_text_download_worker_instantiation(self, db_session_factory):
        """Test FullTextDownloadWorker can be instantiated."""
        config = WorkerConfig(
            worker_id="test-download-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            batch_size=5
        )
        
        worker = FullTextDownloadWorker(config, db_session_factory)
        
        assert worker.worker_id == "test-download-worker"
        assert ProcessingStage.DOWNLOAD_FULL_TEXT in worker.config.stages
        assert worker.download_timeout == 30  # default value
        assert worker.max_concurrent_downloads == 5  # default value
    
    def test_sentiment_analysis_worker_instantiation(self, db_session_factory):
        """Test SentimentAnalysisWorker can be instantiated."""
        config = WorkerConfig(
            worker_id="test-sentiment-worker",
            stages=[ProcessingStage.SENTIMENT_ANALYSIS],
            batch_size=5
        )
        
        worker = SentimentAnalysisWorker(config, db_session_factory)
        
        assert worker.worker_id == "test-sentiment-worker"
        assert ProcessingStage.SENTIMENT_ANALYSIS in worker.config.stages
        assert worker.batch_optimize_models == True  # default value
        assert worker.preload_models == False  # default value
    
    def test_llm_analysis_worker_instantiation(self, db_session_factory):
        """Test LLMAnalysisWorker can be instantiated."""
        config = WorkerConfig(
            worker_id="test-llm-worker",
            stages=[ProcessingStage.LLM_ANALYSIS],
            batch_size=5
        )
        
        worker = LLMAnalysisWorker(config, db_session_factory)
        
        assert worker.worker_id == "test-llm-worker"
        assert ProcessingStage.LLM_ANALYSIS in worker.config.stages
        assert worker.llm_calls_per_entry == 5  # default value
        assert worker.batch_optimize_calls == True  # default value
    
    def test_multi_stage_worker_configuration(self, db_session_factory):
        """Test that workers can be configured for multiple stages."""
        # Test a worker that handles multiple stages
        config = WorkerConfig(
            worker_id="multi-stage-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS],
            batch_size=10
        )
        
        # Should be able to create workers for different stages
        download_worker = FullTextDownloadWorker(config, db_session_factory)
        sentiment_worker = SentimentAnalysisWorker(config, db_session_factory)
        
        assert download_worker.worker_id == "multi-stage-worker"
        assert sentiment_worker.worker_id == "multi-stage-worker"
        
        # Both should have the same config but handle different stages
        assert ProcessingStage.DOWNLOAD_FULL_TEXT in download_worker.config.stages
        assert ProcessingStage.SENTIMENT_ANALYSIS in sentiment_worker.config.stages
    
    def test_worker_health_status_consistency(self, db_session_factory):
        """Test that all workers provide consistent health status format."""
        configs = [
            (FullTextDownloadWorker, ProcessingStage.DOWNLOAD_FULL_TEXT),
            (SentimentAnalysisWorker, ProcessingStage.SENTIMENT_ANALYSIS),
            (LLMAnalysisWorker, ProcessingStage.LLM_ANALYSIS)
        ]
        
        for worker_class, stage in configs:
            config = WorkerConfig(
                worker_id=f"test-{stage.value}-worker",
                stages=[stage],
                batch_size=5
            )
            
            worker = worker_class(config, db_session_factory)
            health = worker.get_worker_specific_health()
            
            # All workers should provide these common health fields
            assert 'worker_type' in health
            assert 'supported_stages' in health
            assert stage.value in health['supported_stages']
            assert 'worker_id' in health
            assert health['worker_id'] == f"test-{stage.value}-worker"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])