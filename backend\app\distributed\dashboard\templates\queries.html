{% extends "base.html" %}

{% block title %}SQL Queries - Distributed ETL{% endblock %}

{% block extra_head %}
<style>
.query-editor {
    min-height: 200px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.query-help {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.saved-queries {
    max-height: 200px;
    overflow-y: auto;
}

.table-responsive {
    height: 100%;
    overflow: auto;
}

.results-container {
    height: calc(100vh - 500px);
    min-height: 400px;
}

#results-table {
    font-size: 12px;
}

.status-message {
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.status-success { background-color: #d4edda; color: #155724; }
.status-error { background-color: #f8d7da; color: #721c24; }
.status-info { background-color: #d1ecf1; color: #0c5460; }

.clickable-cell {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
}

.clickable-cell:hover {
    color: #0056b3;
}

.binary-data {
    font-family: monospace;
    font-size: 11px;
    color: #6c757d;
}

.preview-img {
    max-width: 50px;
    max-height: 50px;
    cursor: pointer;
}

.preview-img:hover {
    transform: scale(1.1);
}
</style>
{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2><i class="fas fa-database"></i> SQL Query Interface</h2>
            <div>
                <button class="btn btn-success" onclick="executeQuery()">
                    <i class="fas fa-play"></i> Execute Query
                </button>
                <button class="btn btn-primary" onclick="saveQuery()">
                    <i class="fas fa-save"></i> Save Query
                </button>
                <button class="btn btn-secondary" onclick="clearQuery()">
                    <i class="fas fa-trash"></i> Clear
                </button>
            </div>
        </div>
    </div>
</div>

<!-- SQL Query Section -->
<div class="row mb-3">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-code"></i> SQL Query</h5>
            </div>
            <div class="card-body">
                <textarea id="sql-query" class="form-control query-editor"
                        placeholder="Enter your SQL query here..."></textarea>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-bookmark"></i> Saved Queries</h6>
            </div>
            <div class="card-body saved-queries">
                <div class="list-group" id="saved-queries-list">
                    <div class="text-muted text-center">Loading...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI Query Assistant Section -->
<div class="row mb-3">
    <div class="col-12">
        <div class="query-help">
            <h6><i class="fas fa-robot"></i> AI Query Assistant</h6>
            <p class="small mb-2">Describe what you want to query in natural language:</p>
            <div class="input-group">
                <textarea id="query-description" class="form-control" rows="2"
                        placeholder="Example: Show me the top 10 most positive news articles from the last week"></textarea>
                <button id="get-help-btn" class="btn btn-outline-secondary" onclick="generateQuery()">
                    <i class="fas fa-magic"></i> Generate SQL
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Results Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-table"></i> Query Results</h5>
                <div>
                    <button class="btn btn-sm btn-outline-success" onclick="exportResults('csv')" disabled id="export-csv-btn">
                        <i class="fas fa-file-csv"></i> Export CSV
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportResults('xlsx')" disabled id="export-xlsx-btn">
                        <i class="fas fa-file-excel"></i> Export XLSX
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="results-status"></div>
                <div class="results-container">
                    <div class="table-responsive">
                        <table id="results-table" class="table table-striped table-bordered table-sm mb-0">
                            <thead id="results-header"></thead>
                            <tbody id="results-body"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Query Modal -->
<div class="modal fade" id="saveQueryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Save Query</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="query-name" class="form-label">Query Name</label>
                    <input type="text" class="form-control" id="query-name" placeholder="Enter a name for this query">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmSaveQuery()">Save</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
let currentResults = null;
let sortColumn = null;
let sortDirection = 'asc';

// Load saved queries from monolithic system
let savedQueries = {};

async function executeQuery() {
    const query = document.getElementById('sql-query').value.trim();
    if (!query) {
        setStatus('Please enter a SQL query', 'error');
        return;
    }
    
    setStatus('Executing query...', 'info');
    clearResults();
    
    try {
        const response = await fetch('/api/execute_query', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ query })
        });

        const result = await response.json();

        if (!response.ok) {
            // Handle HTTP error responses
            setStatus(`Error: ${result.detail || result.error || 'Query execution failed'}`, 'error');
        } else if (result.error) {
            // Handle application-level errors
            setStatus(`Error: ${result.error}`, 'error');
        } else {
            setStatus(`Query executed successfully. ${result.rows.length} rows returned.`, 'success');
            displayResults(result);
            // Export buttons are enabled in displayResults function
        }
    } catch (error) {
        setStatus(`Error: ${error.message}`, 'error');
    }
}

async function generateQuery() {
    const description = document.getElementById('query-description').value.trim();
    if (!description) {
        setStatus('Please enter a description for the query', 'error');
        return;
    }
    
    setStatus('Generating SQL query...', 'info');
    
    try {
        const response = await fetch('/api/generate_query', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ description })
        });
        
        const result = await response.json();
        
        if (result.error) {
            setStatus(`Error generating query: ${result.error}`, 'error');
        } else {
            // Remove trailing semicolon if present
            let query = result.query.trim();
            if (query.endsWith(';')) {
                query = query.slice(0, -1);
            }
            document.getElementById('sql-query').value = query;
            setStatus('SQL query generated successfully', 'success');
        }
    } catch (error) {
        setStatus(`Error: ${error.message}`, 'error');
    }
}

function saveQuery() {
    const query = document.getElementById('sql-query').value.trim();
    if (!query) {
        setStatus('Please enter a SQL query to save', 'error');
        return;
    }
    
    new bootstrap.Modal(document.getElementById('saveQueryModal')).show();
}

async function confirmSaveQuery() {
    const name = document.getElementById('query-name').value.trim();
    const query = document.getElementById('sql-query').value.trim();
    
    if (!name) {
        alert('Please enter a name for the query');
        return;
    }
    
    try {
        const response = await fetch('/api/save_query', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ name, query })
        });
        
        const result = await response.json();
        
        if (result.error) {
            setStatus(`Error saving query: ${result.error}`, 'error');
        } else {
            setStatus('Query saved successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('saveQueryModal')).hide();
            document.getElementById('query-name').value = '';
            loadSavedQueries();
        }
    } catch (error) {
        setStatus(`Error: ${error.message}`, 'error');
    }
}

function clearQuery() {
    document.getElementById('sql-query').value = '';
    document.getElementById('query-description').value = '';
    clearResults();
    setStatus('', '');
}

function displayResults(result) {
    currentResults = result;
    const header = document.getElementById('results-header');
    const body = document.getElementById('results-body');

    if (result.rows.length === 0) {
        header.innerHTML = '';
        body.innerHTML = '<tr><td class="text-center text-muted">No results found</td></tr>';
        return;
    }

    // Create sortable header
    const columns = result.columns;
    header.innerHTML = '<tr>' + columns.map((col, index) =>
        `<th style="cursor: pointer;" onclick="sortTable(${index})">${col} <i class="fas fa-sort"></i></th>`
    ).join('') + '</tr>';

    // Create body with special handling for binary data and clickable fields
    body.innerHTML = result.rows.map(row =>
        '<tr>' + row.map((cell, colIndex) => {
            const columnName = columns[colIndex].toLowerCase();
            return `<td>${formatCell(cell, columnName)}</td>`;
        }).join('') + '</tr>'
    ).join('');

    // Enable export buttons
    document.getElementById('export-csv-btn').disabled = false;
    document.getElementById('export-xlsx-btn').disabled = false;
}

function formatCell(cell, columnName) {
    if (cell === null) {
        return '<em>NULL</em>';
    }

    // Handle binary data
    if (columnName === 'embedding' && typeof cell === 'string' && cell.length > 100) {
        // Show first few hex bytes for embedding
        const hexBytes = cell.substring(0, 20) + '...';
        return `<span class="binary-data" title="Binary embedding data">${hexBytes}</span>`;
    }

    if (columnName === 'preview_img' && cell) {
        // Show as clickable thumbnail
        return `<img src="data:image/jpeg;base64,${cell}" class="preview-img" onclick="showImageModal('${cell}')" title="Click to view full size">`;
    }

    // Handle clickable entry_id
    if (columnName === 'entry_id') {
        return `<span class="clickable-cell" onclick="openEntryDetails('${cell}')">${cell}</span>`;
    }

    // Handle clickable dup_entry_id
    if (columnName === 'dup_entry_id' && cell) {
        return `<span class="clickable-cell" onclick="openDuplicateDetails('${cell}')">${cell}</span>`;
    }

    return cell;
}

function sortTable(columnIndex) {
    if (!currentResults || !currentResults.rows.length) return;

    const isCurrentColumn = sortColumn === columnIndex;
    sortDirection = isCurrentColumn && sortDirection === 'asc' ? 'desc' : 'asc';
    sortColumn = columnIndex;

    // Sort the data
    currentResults.rows.sort((a, b) => {
        let aVal = a[columnIndex];
        let bVal = b[columnIndex];

        // Handle null values
        if (aVal === null && bVal === null) return 0;
        if (aVal === null) return sortDirection === 'asc' ? -1 : 1;
        if (bVal === null) return sortDirection === 'asc' ? 1 : -1;

        // Try to parse as numbers
        const aNum = parseFloat(aVal);
        const bNum = parseFloat(bVal);
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
        }

        // String comparison
        const aStr = String(aVal).toLowerCase();
        const bStr = String(bVal).toLowerCase();
        if (sortDirection === 'asc') {
            return aStr.localeCompare(bStr);
        } else {
            return bStr.localeCompare(aStr);
        }
    });

    // Re-display results
    displayResults(currentResults);

    // Update sort indicator
    const headers = document.querySelectorAll('#results-header th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (index === columnIndex) {
            icon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        } else {
            icon.className = 'fas fa-sort';
        }
    });
}

function clearResults() {
    document.getElementById('results-header').innerHTML = '';
    document.getElementById('results-body').innerHTML = '';
    document.getElementById('export-csv-btn').disabled = true;
    document.getElementById('export-xlsx-btn').disabled = true;
    currentResults = null;
    sortColumn = null;
    sortDirection = 'asc';
}

function setStatus(message, type) {
    const statusDiv = document.getElementById('results-status');
    if (!message) {
        statusDiv.innerHTML = '';
        return;
    }
    
    statusDiv.innerHTML = `<div class="status-message status-${type}">${message}</div>`;
}

function exportResults(format = 'csv') {
    if (!currentResults || !currentResults.rows.length) {
        setStatus('No results to export', 'error');
        return;
    }

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

    if (format === 'csv') {
        // Create CSV content
        const csv = [
            currentResults.columns.join(','),
            ...currentResults.rows.map(row =>
                row.map(cell => `"${cell !== null ? String(cell).replace(/"/g, '""') : ''}"`).join(',')
            )
        ].join('\n');

        // Download CSV
        const blob = new Blob([csv], { type: 'text/csv' });
        downloadFile(blob, `query_results_${timestamp}.csv`);

    } else if (format === 'xlsx') {
        // Create XLSX content
        const ws = XLSX.utils.aoa_to_sheet([
            currentResults.columns,
            ...currentResults.rows
        ]);

        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Query Results');

        // Download XLSX
        XLSX.writeFile(wb, `query_results_${timestamp}.xlsx`);
    }
}

function downloadFile(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

async function loadSavedQueries() {
    try {
        const response = await fetch('/api/saved_queries');
        const queries = await response.json();
        savedQueries = queries;

        const container = document.getElementById('saved-queries-list');
        if (Object.keys(queries).length === 0) {
            container.innerHTML = '<div class="text-muted text-center">No saved queries</div>';
            return;
        }

        container.innerHTML = Object.entries(queries).map(([name, query]) => `
            <a href="#" class="list-group-item list-group-item-action" onclick="loadQuery('${name}')">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${name}</h6>
                    <small><i class="fas fa-code"></i></small>
                </div>
                <p class="mb-1 small text-muted">${query.substring(0, 60)}...</p>
            </a>
        `).join('');

    } catch (error) {
        console.error('Error loading saved queries:', error);
        document.getElementById('saved-queries-list').innerHTML =
            '<div class="text-muted text-center">Error loading queries</div>';
    }
}

function loadQuery(name) {
    if (savedQueries[name]) {
        document.getElementById('sql-query').value = savedQueries[name];
        setStatus(`Loaded query: ${name}`, 'info');
    }
}

function openEntryDetails(entryId) {
    const url = `/entry_details/${entryId}`;
    window.open(url, '_blank');
}

function openDuplicateDetails(dupEntryId) {
    const url = `/duplicate_details/${dupEntryId}`;
    window.open(url, '_blank');
}

function showImageModal(base64Image) {
    // Create modal for full-size image
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Preview Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="data:image/jpeg;base64,${base64Image}" class="img-fluid" style="max-width: 100%;">
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // Remove modal from DOM when hidden
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

// Keyboard shortcuts
document.getElementById('sql-query').addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        executeQuery();
    }
});

document.getElementById('query-description').addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'Enter') {
        e.preventDefault();
        generateQuery();
    }
});

// Load initial data
document.addEventListener('DOMContentLoaded', function() {
    loadSavedQueries();
});
</script>
{% endblock %}
