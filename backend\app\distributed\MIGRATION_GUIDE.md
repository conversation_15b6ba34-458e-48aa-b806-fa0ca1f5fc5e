# ETL Processing Migration Guide

This guide provides comprehensive instructions for migrating from monolithic to distributed ETL processing.

## Overview

The migration system provides utilities for transitioning from the current monolithic thread-based ETL processing to a distributed worker-based system. The migration includes:

- **Database schema migration** - Adding columns and indexes for distributed processing
- **Data migration** - Converting legacy processing state to new format
- **Validation** - Ensuring data consistency between old and new systems
- **Rollback** - Ability to revert changes if migration fails

## Prerequisites

Before starting migration, ensure:

1. **Database Schema is Up-to-Date**
   ```bash
   alembic upgrade head
   ```

2. **Stop All Active Workers**
   - Stop any running monolithic ETL threads
   - Ensure no distributed workers are active
   - Verify no entries are currently being processed

3. **Backup Your Data** (Recommended)
   ```bash
   # Create database backup
   pg_dump your_database > backup_before_migration.sql
   ```

## Migration Process

### Step 1: Check Current Status

```bash
python -m backend.app.distributed.migration_cli status
```

This shows:
- Current processing mode (monolithic/distributed/hybrid)
- Number of entries with processing status
- Migration progress percentage
- Active workers and claimed entries

### Step 2: Validate Current Data

```bash
python -m backend.app.distributed.migration_cli validate --sample-size 1000
```

This validates:
- Processing status consistency
- Work queue integrity
- Data consistency between legacy and new formats

### Step 3: Perform Migration

```bash
python -m backend.app.distributed.migration_cli migrate distributed --yes
```

Options:
- `--batch-size 1000` - Number of entries to process per batch
- `--validation-sample-size 100` - Number of entries to validate
- `--timeout 30` - Migration timeout in minutes
- `--no-backup` - Skip creating backup before migration
- `--no-validation` - Skip data validation after migration
- `--no-rollback` - Disable automatic rollback on failure
- `--yes` - Skip confirmation prompts

### Step 4: Verify Migration

```bash
python -m backend.app.distributed.migration_cli status
python -m backend.app.distributed.migration_cli validate
```

## Migration Modes

### Monolithic Mode
- Original thread-based processing
- No processing status tracking
- All processing state in legacy database fields

### Distributed Mode
- Worker-based processing with work queue
- Processing status tracked in JSON format
- Atomic work claiming and release

### Hybrid Mode
- Both systems can coexist
- Some entries have processing status, others don't
- Useful for gradual migration

## Data Migration Details

### Processing Status Generation

The migration converts legacy database fields to processing status JSON:

```json
{
  "download_full_text": {
    "status": "completed",
    "completed_at": "2025-01-15T10:30:00Z",
    "worker_id": "legacy_migration"
  },
  "translate_text": {
    "status": "pending"
  }
}
```

### Field Mappings

| Processing Stage | Legacy Fields | Status Logic |
|-----------------|---------------|--------------|
| `download_feeds` | `title` | Completed if title exists |
| `download_full_text` | `full_text` | Completed if full_text exists and not empty |
| `translate_text` | `english_text` | Completed if english_text exists and not empty |
| `compute_embeddings` | `embedding` | Completed if embedding exists |
| `sentiment_analysis` | `vader_pos`, `vader_neu`, `vader_neg` | Completed if VADER scores exist |
| `llm_analysis` | `llm_positive`, `llm_neutral`, `llm_negative` | Completed if LLM scores exist |
| `iptc_classification` | `iptc_newscode` | Completed if IPTC code exists and not empty |
| `duplicate_check` | `dup_entry_id` | Completed if duplicate ID exists |
| `generate_descriptions` | `description_auto_generated` | Completed if flag is True |
| `generate_image_prompts` | `image_prompt` | Completed if prompt exists and not empty |
| `generate_preview_images` | `preview_img` | Completed if image data exists |

## Validation

### Processing Status Consistency
- Validates JSON structure of processing status
- Cross-validates with legacy database fields
- Identifies inconsistencies between status and actual data

### Work Queue Integrity
- Checks for stale claims (entries claimed by inactive workers)
- Identifies orphaned claims (entries claimed by non-existent workers)
- Validates claim timestamps and worker references

### Data Comparison
- Compares processing results between monolithic and distributed modes
- Validates that both systems produce identical results
- Identifies differences in sentiment scores, classifications, etc.

## Rollback

### Automatic Rollback
Migration automatically rolls back if:
- Critical validation errors are found
- Migration fails due to database errors
- Timeout is exceeded

### Manual Rollback
```bash
python -m backend.app.distributed.migration_cli rollback --yes
```

Rollback process:
1. Clears `processing_status` field for all entries
2. Clears `claimed_by` and `claimed_at` fields
3. Resets `retry_count` to 0
4. Clears `last_error` field

## Monitoring and Maintenance

### Clean Up Stale Claims
```bash
python -m backend.app.distributed.migration_cli cleanup --timeout 120
```

### Export Status Report
```bash
python -m backend.app.distributed.migration_cli export-status --include-validation --output migration_report.json
```

### Monitor Migration Progress
```python
from backend.app.distributed.migration_utils import MigrationManager

with migration_session(session_factory) as session:
    manager = MigrationManager(session)
    status = manager.get_migration_status()
    print(f"Migration progress: {status['migration_percentage']:.1f}%")
```

## Troubleshooting

### Common Issues

#### Migration Fails with "Active Workers Found"
**Problem**: Migration detects active workers
**Solution**: Stop all workers before migration
```bash
# Check for active workers
SELECT * FROM workers WHERE status = 'running';

# Stop workers manually or wait for them to finish
```

#### Validation Errors After Migration
**Problem**: Inconsistencies between processing status and legacy fields
**Solution**: Review validation errors and fix data manually
```bash
python -m backend.app.distributed.migration_cli validate --sample-size 1000 > validation_report.txt
```

#### Migration Timeout
**Problem**: Migration takes too long
**Solution**: Increase timeout or reduce batch size
```bash
python -m backend.app.distributed.migration_cli migrate distributed --timeout 60 --batch-size 500
```

#### Stale Claims After Migration
**Problem**: Entries stuck in claimed state
**Solution**: Clean up stale claims
```bash
python -m backend.app.distributed.migration_cli cleanup --timeout 60
```

### Recovery Procedures

#### Partial Migration Failure
1. Check migration status
2. Identify failed entries
3. Fix underlying issues
4. Resume migration or rollback

#### Data Corruption
1. Stop all processing
2. Restore from backup
3. Investigate root cause
4. Retry migration with fixes

#### Performance Issues
1. Monitor database performance during migration
2. Adjust batch sizes based on system capacity
3. Consider migrating during low-traffic periods

## Best Practices

### Before Migration
- [ ] Create full database backup
- [ ] Stop all ETL processing
- [ ] Verify database schema is current
- [ ] Test migration on staging environment
- [ ] Plan maintenance window

### During Migration
- [ ] Monitor migration progress
- [ ] Watch for validation errors
- [ ] Keep backup of original data
- [ ] Document any issues encountered

### After Migration
- [ ] Validate migrated data thoroughly
- [ ] Test distributed workers
- [ ] Monitor system performance
- [ ] Update operational procedures
- [ ] Train team on new system

## Configuration

### Migration Configuration Options

```python
config = MigrationConfig(
    target_mode=MigrationMode.DISTRIBUTED,
    batch_size=1000,                    # Entries per batch
    validation_sample_size=100,         # Entries to validate
    timeout_minutes=30,                 # Migration timeout
    backup_before_migration=True,       # Create backup
    validate_data_integrity=True,       # Validate after migration
    rollback_on_failure=True,          # Auto-rollback on failure
    parallel_validation=False,          # Parallel validation (experimental)
    stages_to_migrate=None,            # All stages by default
    preserve_processing_history=True    # Keep processing timestamps
)
```

### Environment Variables

```bash
# Database connection
DATABASE_URL=postgresql://user:pass@host:port/db

# Migration settings
MIGRATION_BATCH_SIZE=1000
MIGRATION_TIMEOUT_MINUTES=30
MIGRATION_BACKUP_ENABLED=true
MIGRATION_VALIDATION_ENABLED=true
```

## API Reference

### MigrationManager

```python
from backend.app.distributed.migration_utils import MigrationManager

manager = MigrationManager(db_session)

# Get migration status
status = manager.get_migration_status()

# Perform migration
result = manager.migrate_to_distributed(config)

# Check if migration is needed
needs_migration = status['migration_percentage'] < 100
```

### MigrationValidator

```python
from backend.app.distributed.migration_utils import MigrationValidator

validator = MigrationValidator(db_session)

# Validate processing status
result = validator.validate_processing_status_consistency(entry_ids)

# Validate work queue
queue_result = validator.validate_work_queue_integrity()

# Compare processing results
comparison = validator.compare_processing_results(
    entry_ids, monolithic_results, distributed_results
)
```

## Support

For issues or questions:

1. Check this documentation
2. Review validation error messages
3. Check system logs
4. Create issue with detailed error information

## Changelog

### Version 1.0.0
- Initial migration utilities implementation
- Support for monolithic to distributed migration
- Comprehensive validation and rollback mechanisms
- CLI tools for migration management