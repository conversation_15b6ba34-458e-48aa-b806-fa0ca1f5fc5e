"""
Unit tests for processing stage and status management.
"""

import pytest
import json
from datetime import datetime
from backend.app.distributed.processing_stage import (
    ProcessingStage, ProcessingStatusValue, StageStatus, ProcessingStatus,
    create_empty_processing_status, get_next_pending_stage, can_process_stage,
    get_processable_stages
)


class TestProcessingStage:
    """Test ProcessingStage enum."""
    
    def test_all_stages_defined(self):
        """Test that all expected stages are defined."""
        expected_stages = {
            "download_feeds", "download_full_text", "translate_text", "compute_embeddings",
            "sentiment_analysis", "llm_analysis", "iptc_classification",
            "duplicate_check", "generate_descriptions", "generate_image_prompts",
            "generate_preview_images", "generate_rss_feed"
        }

        actual_stages = {stage.value for stage in ProcessingStage}
        assert actual_stages == expected_stages
    
    def test_stage_values(self):
        """Test specific stage values."""
        assert ProcessingStage.DOWNLOAD_FULL_TEXT.value == "download_full_text"
        assert ProcessingStage.LLM_ANALYSIS.value == "llm_analysis"
        assert ProcessingStage.GENERATE_PREVIEW_IMAGES.value == "generate_preview_images"


class TestStageStatus:
    """Test StageStatus dataclass."""
    
    def test_stage_status_creation(self):
        """Test creating a stage status."""
        status = StageStatus(ProcessingStatusValue.PENDING)
        assert status.status == ProcessingStatusValue.PENDING
        assert status.completed_at is None
        assert status.worker_id is None
        assert status.error is None
        assert status.retry_count == 0
    
    def test_stage_status_with_all_fields(self):
        """Test creating a stage status with all fields."""
        completed_at = datetime.utcnow()
        status = StageStatus(
            status=ProcessingStatusValue.COMPLETED,
            completed_at=completed_at,
            worker_id="worker-001",
            error=None,
            retry_count=2
        )
        
        assert status.status == ProcessingStatusValue.COMPLETED
        assert status.completed_at == completed_at
        assert status.worker_id == "worker-001"
        assert status.error is None
        assert status.retry_count == 2
    
    def test_to_dict(self):
        """Test converting stage status to dictionary."""
        completed_at = datetime(2025, 1, 15, 10, 30, 0)
        status = StageStatus(
            status=ProcessingStatusValue.COMPLETED,
            completed_at=completed_at,
            worker_id="worker-001",
            retry_count=1
        )
        
        result = status.to_dict()
        expected = {
            "status": "completed",
            "completed_at": "2025-01-15T10:30:00",
            "worker_id": "worker-001",
            "retry_count": 1
        }
        
        assert result == expected
    
    def test_to_dict_minimal(self):
        """Test converting minimal stage status to dictionary."""
        status = StageStatus(ProcessingStatusValue.PENDING)
        result = status.to_dict()
        expected = {
            "status": "pending",
            "retry_count": 0
        }
        
        assert result == expected
    
    def test_from_dict(self):
        """Test creating stage status from dictionary."""
        data = {
            "status": "completed",
            "completed_at": "2025-01-15T10:30:00",
            "worker_id": "worker-001",
            "error": "Some error",
            "retry_count": 2
        }
        
        status = StageStatus.from_dict(data)
        
        assert status.status == ProcessingStatusValue.COMPLETED
        assert status.completed_at == datetime(2025, 1, 15, 10, 30, 0)
        assert status.worker_id == "worker-001"
        assert status.error == "Some error"
        assert status.retry_count == 2
    
    def test_from_dict_minimal(self):
        """Test creating stage status from minimal dictionary."""
        data = {"status": "pending"}
        status = StageStatus.from_dict(data)
        
        assert status.status == ProcessingStatusValue.PENDING
        assert status.completed_at is None
        assert status.worker_id is None
        assert status.error is None
        assert status.retry_count == 0


class TestProcessingStatus:
    """Test ProcessingStatus class."""
    
    def test_empty_processing_status(self):
        """Test creating empty processing status."""
        status = ProcessingStatus()
        
        # All stages should default to pending
        for stage in ProcessingStage:
            assert status.is_stage_pending(stage)
            assert not status.is_stage_completed(stage)
            assert not status.is_stage_in_progress(stage)
            assert not status.is_stage_failed(stage)
    
    def test_set_stage_status(self):
        """Test setting stage status."""
        status = ProcessingStatus()
        
        status.set_stage_status(
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            ProcessingStatusValue.IN_PROGRESS,
            worker_id="worker-001"
        )
        
        assert status.is_stage_in_progress(ProcessingStage.DOWNLOAD_FULL_TEXT)
        stage_status = status.get_stage_status(ProcessingStage.DOWNLOAD_FULL_TEXT)
        assert stage_status.worker_id == "worker-001"
    
    def test_mark_stage_in_progress(self):
        """Test marking stage as in progress."""
        status = ProcessingStatus()
        status.mark_stage_in_progress(ProcessingStage.LLM_ANALYSIS, "worker-002")
        
        assert status.is_stage_in_progress(ProcessingStage.LLM_ANALYSIS)
        stage_status = status.get_stage_status(ProcessingStage.LLM_ANALYSIS)
        assert stage_status.worker_id == "worker-002"
    
    def test_mark_stage_completed(self):
        """Test marking stage as completed."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.SENTIMENT_ANALYSIS, "worker-003")
        
        assert status.is_stage_completed(ProcessingStage.SENTIMENT_ANALYSIS)
        stage_status = status.get_stage_status(ProcessingStage.SENTIMENT_ANALYSIS)
        assert stage_status.worker_id == "worker-003"
        assert stage_status.completed_at is not None
    
    def test_mark_stage_failed(self):
        """Test marking stage as failed."""
        status = ProcessingStatus()
        error_msg = "Network timeout"
        status.mark_stage_failed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001", error_msg)
        
        assert status.is_stage_failed(ProcessingStage.DOWNLOAD_FULL_TEXT)
        stage_status = status.get_stage_status(ProcessingStage.DOWNLOAD_FULL_TEXT)
        assert stage_status.worker_id == "worker-001"
        assert stage_status.error == error_msg
        assert stage_status.retry_count == 1
    
    def test_retry_count_increment(self):
        """Test that retry count increments on failure."""
        status = ProcessingStatus()
        
        # First failure
        status.mark_stage_failed(ProcessingStage.LLM_ANALYSIS, "worker-001", "Error 1")
        assert status.get_retry_count(ProcessingStage.LLM_ANALYSIS) == 1
        
        # Second failure
        status.mark_stage_failed(ProcessingStage.LLM_ANALYSIS, "worker-002", "Error 2")
        assert status.get_retry_count(ProcessingStage.LLM_ANALYSIS) == 2
    
    def test_get_pending_stages(self):
        """Test getting pending stages."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
        status.mark_stage_in_progress(ProcessingStage.SENTIMENT_ANALYSIS, "worker-002")
        
        pending_stages = status.get_pending_stages()
        
        # Should have all stages except the completed and in-progress ones
        assert ProcessingStage.DOWNLOAD_FULL_TEXT not in pending_stages
        assert ProcessingStage.SENTIMENT_ANALYSIS not in pending_stages
        assert ProcessingStage.LLM_ANALYSIS in pending_stages
        assert len(pending_stages) == len(ProcessingStage) - 2
    
    def test_get_completed_stages(self):
        """Test getting completed stages."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
        status.mark_stage_completed(ProcessingStage.SENTIMENT_ANALYSIS, "worker-002")
        
        completed_stages = status.get_completed_stages()
        
        assert ProcessingStage.DOWNLOAD_FULL_TEXT in completed_stages
        assert ProcessingStage.SENTIMENT_ANALYSIS in completed_stages
        assert len(completed_stages) == 2
    
    def test_get_failed_stages(self):
        """Test getting failed stages."""
        status = ProcessingStatus()
        status.mark_stage_failed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001", "Error 1")
        status.mark_stage_failed(ProcessingStage.LLM_ANALYSIS, "worker-002", "Error 2")
        
        failed_stages = status.get_failed_stages()
        
        assert ProcessingStage.DOWNLOAD_FULL_TEXT in failed_stages
        assert ProcessingStage.LLM_ANALYSIS in failed_stages
        assert len(failed_stages) == 2
    
    def test_to_json(self):
        """Test serializing to JSON."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
        status.mark_stage_failed(ProcessingStage.LLM_ANALYSIS, "worker-002", "Error message")
        
        json_str = status.to_json()
        data = json.loads(json_str)
        
        assert "download_full_text" in data
        assert data["download_full_text"]["status"] == "completed"
        assert data["download_full_text"]["worker_id"] == "worker-001"
        
        assert "llm_analysis" in data
        assert data["llm_analysis"]["status"] == "failed"
        assert data["llm_analysis"]["worker_id"] == "worker-002"
        assert data["llm_analysis"]["error"] == "Error message"
        assert data["llm_analysis"]["retry_count"] == 1
    
    def test_from_json(self):
        """Test deserializing from JSON."""
        json_data = {
            "download_full_text": {
                "status": "completed",
                "completed_at": "2025-01-15T10:30:00",
                "worker_id": "worker-001",
                "retry_count": 0
            },
            "llm_analysis": {
                "status": "failed",
                "worker_id": "worker-002",
                "error": "Network error",
                "retry_count": 2
            }
        }
        json_str = json.dumps(json_data)
        
        status = ProcessingStatus.from_json(json_str)
        
        assert status.is_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT)
        assert status.is_stage_failed(ProcessingStage.LLM_ANALYSIS)
        assert status.get_retry_count(ProcessingStage.LLM_ANALYSIS) == 2
        
        # Other stages should be pending
        assert status.is_stage_pending(ProcessingStage.SENTIMENT_ANALYSIS)
    
    def test_from_json_empty(self):
        """Test deserializing from empty/None JSON."""
        status1 = ProcessingStatus.from_json(None)
        status2 = ProcessingStatus.from_json("")
        status3 = ProcessingStatus.from_json("{}")
        
        for status in [status1, status2, status3]:
            for stage in ProcessingStage:
                assert status.is_stage_pending(stage)
    
    def test_from_json_invalid(self):
        """Test deserializing from invalid JSON."""
        status = ProcessingStatus.from_json("invalid json")
        
        # Should return empty status on invalid JSON
        for stage in ProcessingStage:
            assert status.is_stage_pending(stage)
    
    def test_from_json_unknown_stages(self):
        """Test deserializing JSON with unknown stages (forward compatibility)."""
        json_data = {
            "download_full_text": {
                "status": "completed",
                "worker_id": "worker-001",
                "retry_count": 0
            },
            "unknown_future_stage": {
                "status": "pending",
                "retry_count": 0
            }
        }
        json_str = json.dumps(json_data)
        
        status = ProcessingStatus.from_json(json_str)
        
        # Known stage should be loaded
        assert status.is_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Unknown stage should be ignored, other stages should be pending
        assert status.is_stage_pending(ProcessingStage.LLM_ANALYSIS)


class TestUtilityFunctions:
    """Test utility functions."""
    
    def test_create_empty_processing_status(self):
        """Test creating empty processing status."""
        status = create_empty_processing_status()
        
        for stage in ProcessingStage:
            assert status.is_stage_pending(stage)
    
    def test_get_next_pending_stage(self):
        """Test getting next pending stage."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
        
        available_stages = [
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            ProcessingStage.SENTIMENT_ANALYSIS,
            ProcessingStage.LLM_ANALYSIS
        ]
        
        next_stage = get_next_pending_stage(status, available_stages)
        assert next_stage == ProcessingStage.SENTIMENT_ANALYSIS
    
    def test_get_next_pending_stage_none_available(self):
        """Test getting next pending stage when none available."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
        status.mark_stage_completed(ProcessingStage.SENTIMENT_ANALYSIS, "worker-002")
        
        available_stages = [
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            ProcessingStage.SENTIMENT_ANALYSIS
        ]
        
        next_stage = get_next_pending_stage(status, available_stages)
        assert next_stage is None
    
    def test_can_process_stage_pending(self):
        """Test can process stage when pending."""
        status = ProcessingStatus()
        
        assert can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT)
    
    def test_can_process_stage_completed(self):
        """Test can process stage when completed."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
        
        assert not can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT)
    
    def test_can_process_stage_failed_with_retries(self):
        """Test can process stage when failed but retries remaining."""
        status = ProcessingStatus()
        status.mark_stage_failed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001", "Error")
        
        assert can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT, max_retries=3)
        assert status.get_retry_count(ProcessingStage.DOWNLOAD_FULL_TEXT) == 1
    
    def test_can_process_stage_failed_no_retries(self):
        """Test can process stage when failed and no retries remaining."""
        status = ProcessingStatus()
        
        # Fail 3 times to exceed max retries
        for i in range(3):
            status.mark_stage_failed(ProcessingStage.DOWNLOAD_FULL_TEXT, f"worker-{i}", f"Error {i}")
        
        assert not can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT, max_retries=3)
        assert status.get_retry_count(ProcessingStage.DOWNLOAD_FULL_TEXT) == 3
    
    def test_get_processable_stages(self):
        """Test getting all processable stages."""
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
        status.mark_stage_failed(ProcessingStage.SENTIMENT_ANALYSIS, "worker-002", "Error")
        
        available_stages = [
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            ProcessingStage.SENTIMENT_ANALYSIS,
            ProcessingStage.LLM_ANALYSIS,
            ProcessingStage.IPTC_CLASSIFICATION
        ]
        
        processable = get_processable_stages(status, available_stages, max_retries=3)
        
        # Should include failed stage (has retries) and pending stages, but not completed
        assert ProcessingStage.DOWNLOAD_FULL_TEXT not in processable  # completed
        assert ProcessingStage.SENTIMENT_ANALYSIS in processable      # failed but retries left
        assert ProcessingStage.LLM_ANALYSIS in processable           # pending
        assert ProcessingStage.IPTC_CLASSIFICATION in processable    # pending
        
        assert len(processable) == 3


if __name__ == "__main__":
    pytest.main([__file__])