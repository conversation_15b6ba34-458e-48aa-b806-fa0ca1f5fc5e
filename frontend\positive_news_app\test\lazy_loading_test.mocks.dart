// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in breakingbright/test/lazy_loading_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:breakingbright/models/news_model.dart' as _i2;
import 'package:breakingbright/services/api_service.dart' as _i3;
import 'package:flutter/material.dart' as _i6;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeNewsResponse_0 extends _i1.SmartFake implements _i2.NewsResponse {
  _FakeNewsResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePaginatedNewsResponse_1 extends _i1.SmartFake
    implements _i2.PaginatedNewsResponse {
  _FakePaginatedNewsResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSimilarNewsResponse_2 extends _i1.SmartFake
    implements _i2.SimilarNewsResponse {
  _FakeSimilarNewsResponse_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ApiService].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiService extends _i1.Mock implements _i3.ApiService {
  MockApiService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get baseUrl => (super.noSuchMethod(
        Invocation.getter(#baseUrl),
        returnValue: _i4.dummyValue<String>(
          this,
          Invocation.getter(#baseUrl),
        ),
      ) as String);

  @override
  _i5.Future<_i2.NewsResponse> getNewsByCategories({
    double? minPositive = 0.7,
    int? limitPerCategory = 3,
    _i6.BuildContext? context,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNewsByCategories,
          [],
          {
            #minPositive: minPositive,
            #limitPerCategory: limitPerCategory,
            #context: context,
          },
        ),
        returnValue: _i5.Future<_i2.NewsResponse>.value(_FakeNewsResponse_0(
          this,
          Invocation.method(
            #getNewsByCategories,
            [],
            {
              #minPositive: minPositive,
              #limitPerCategory: limitPerCategory,
              #context: context,
            },
          ),
        )),
      ) as _i5.Future<_i2.NewsResponse>);

  @override
  _i5.Future<_i2.PaginatedNewsResponse> getNews({
    int? skip = 0,
    int? limit = 10,
    double? minPositive = 0.7,
    String? category,
    _i6.BuildContext? context,
    int? lookbackHours,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNews,
          [],
          {
            #skip: skip,
            #limit: limit,
            #minPositive: minPositive,
            #category: category,
            #context: context,
            #lookbackHours: lookbackHours,
          },
        ),
        returnValue: _i5.Future<_i2.PaginatedNewsResponse>.value(
            _FakePaginatedNewsResponse_1(
          this,
          Invocation.method(
            #getNews,
            [],
            {
              #skip: skip,
              #limit: limit,
              #minPositive: minPositive,
              #category: category,
              #context: context,
              #lookbackHours: lookbackHours,
            },
          ),
        )),
      ) as _i5.Future<_i2.PaginatedNewsResponse>);

  @override
  _i5.Future<_i2.SimilarNewsResponse> getSimilarNews(String? entryId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSimilarNews,
          [entryId],
        ),
        returnValue: _i5.Future<_i2.SimilarNewsResponse>.value(
            _FakeSimilarNewsResponse_2(
          this,
          Invocation.method(
            #getSimilarNews,
            [entryId],
          ),
        )),
      ) as _i5.Future<_i2.SimilarNewsResponse>);

  @override
  _i5.Future<Map<String, bool>> checkSimilarNews(List<String>? entryIds) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkSimilarNews,
          [entryIds],
        ),
        returnValue: _i5.Future<Map<String, bool>>.value(<String, bool>{}),
      ) as _i5.Future<Map<String, bool>>);

  @override
  _i5.Future<bool> hasSimilarNews(String? entryId) => (super.noSuchMethod(
        Invocation.method(
          #hasSimilarNews,
          [entryId],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<_i2.PaginatedNewsResponse> searchNews(String? query) =>
      (super.noSuchMethod(
        Invocation.method(
          #searchNews,
          [query],
        ),
        returnValue: _i5.Future<_i2.PaginatedNewsResponse>.value(
            _FakePaginatedNewsResponse_1(
          this,
          Invocation.method(
            #searchNews,
            [query],
          ),
        )),
      ) as _i5.Future<_i2.PaginatedNewsResponse>);

  @override
  _i5.Future<bool> testConnection() => (super.noSuchMethod(
        Invocation.method(
          #testConnection,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<_i2.NewsResponse> getHomeScreenData({
    double? minPositive = 0.7,
    int? limitPerCategory = 3,
    _i6.BuildContext? context,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getHomeScreenData,
          [],
          {
            #minPositive: minPositive,
            #limitPerCategory: limitPerCategory,
            #context: context,
          },
        ),
        returnValue: _i5.Future<_i2.NewsResponse>.value(_FakeNewsResponse_0(
          this,
          Invocation.method(
            #getHomeScreenData,
            [],
            {
              #minPositive: minPositive,
              #limitPerCategory: limitPerCategory,
              #context: context,
            },
          ),
        )),
      ) as _i5.Future<_i2.NewsResponse>);

  @override
  String getImageUrl(
    String? entryId, {
    bool? hasImage = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getImageUrl,
          [entryId],
          {#hasImage: hasImage},
        ),
        returnValue: _i4.dummyValue<String>(
          this,
          Invocation.method(
            #getImageUrl,
            [entryId],
            {#hasImage: hasImage},
          ),
        ),
      ) as String);
}
