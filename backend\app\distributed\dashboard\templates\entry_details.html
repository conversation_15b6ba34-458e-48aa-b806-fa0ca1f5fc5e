{% extends "base.html" %}

{% block title %}Entry Details - {{ entry.entry_id }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2><i class="fas fa-newspaper"></i> Entry Details</h2>
            <button class="btn btn-secondary" onclick="window.close()">
                <i class="fas fa-times"></i> Close
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Basic Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr><td><strong>Entry ID:</strong></td><td><code>{{ entry.entry_id }}</code></td></tr>
                    <tr><td><strong>Title:</strong></td><td>{{ entry.title or 'N/A' }}</td></tr>
                    <tr><td><strong>Source:</strong></td><td>{{ entry.source or 'N/A' }}</td></tr>
                    <tr><td><strong>Link:</strong></td><td>
                        {% if entry.link %}
                            <a href="{{ entry.link }}" target="_blank">{{ entry.link[:50] }}...</a>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                    <tr><td><strong>Published:</strong></td><td>{{ entry.published or 'N/A' }}</td></tr>
                    <tr><td><strong>Description:</strong></td><td>{{ entry.description or 'N/A' }}</td></tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Sentiment Analysis -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line"></i> Sentiment Analysis</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr><td><strong>LLM Positive:</strong></td><td>
                        {% if entry.llm_positive %}
                            <span class="badge bg-success">{{ "%.2f"|format(entry.llm_positive) }}</span>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                    <tr><td><strong>LLM Neutral:</strong></td><td>
                        {% if entry.llm_neutral %}
                            <span class="badge bg-secondary">{{ "%.2f"|format(entry.llm_neutral) }}</span>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                    <tr><td><strong>LLM Negative:</strong></td><td>
                        {% if entry.llm_negative %}
                            <span class="badge bg-danger">{{ "%.2f"|format(entry.llm_negative) }}</span>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                    <tr><td><strong>Is Advertisement:</strong></td><td>
                        {% if entry.llm_is_ad is not none %}
                            <span class="badge {{ 'bg-warning' if entry.llm_is_ad > 0.5 else 'bg-success' }}">
                                {{ "%.2f"|format(entry.llm_is_ad) }}
                            </span>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                    <tr><td><strong>LLM Reason:</strong></td><td>{{ entry.llm_reason_list or 'N/A' }}</td></tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Preview Image -->
    {% if entry.preview_img %}
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-image"></i> Preview Image</h5>
            </div>
            <div class="card-body text-center">
                <img src="data:image/jpeg;base64,{{ entry.preview_img }}" 
                     class="img-fluid" 
                     style="max-width: 100%; cursor: pointer;"
                     onclick="showImageModal('{{ entry.preview_img }}')"
                     title="Click to view full size">
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Duplicate Information -->
    <div class="col-md-{{ '8' if entry.preview_img else '12' }}">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-copy"></i> Duplicate Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr><td><strong>Duplicate Entry ID:</strong></td><td>
                        {% if entry.dup_entry_id %}
                            <span class="clickable-cell" onclick="openDuplicateDetails('{{ entry.dup_entry_id }}')">
                                {{ entry.dup_entry_id }}
                            </span>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                    <tr><td><strong>Duplicate Confidence:</strong></td><td>
                        {% if entry.dup_entry_conf %}
                            <span class="badge bg-info">{{ "%.2f"|format(entry.dup_entry_conf) }}</span>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                    <tr><td><strong>Embedding:</strong></td><td>
                        {% if entry.embedding %}
                            <code class="binary-data">{{ entry.embedding }}</code>
                        {% else %}
                            N/A
                        {% endif %}
                    </td></tr>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Full Text -->
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-file-text"></i> Full Text</h5>
            </div>
            <div class="card-body">
                <div style="max-height: 300px; overflow-y: auto;">
                    <p>{{ entry.full_text or 'N/A' }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Technical Details -->
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs"></i> Technical Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr><td><strong>IPTC News Code:</strong></td><td>{{ entry.iptc_newscode or 'N/A' }}</td></tr>
                            <tr><td><strong>IPTC Score:</strong></td><td>{{ entry.iptc_score or 'N/A' }}</td></tr>
                            <tr><td><strong>LLM Provider:</strong></td><td>{{ entry.llm_provider or 'N/A' }}</td></tr>
                            <tr><td><strong>LLM Model:</strong></td><td>{{ entry.llm_model or 'N/A' }}</td></tr>
                            <tr><td><strong>LLM Iterations:</strong></td><td>{{ entry.llm_iterations or 'N/A' }}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm">
                            <tr><td><strong>VADER Positive:</strong></td><td>{{ entry.vader_pos or 'N/A' }}</td></tr>
                            <tr><td><strong>VADER Neutral:</strong></td><td>{{ entry.vader_neu or 'N/A' }}</td></tr>
                            <tr><td><strong>VADER Negative:</strong></td><td>{{ entry.vader_neg or 'N/A' }}</td></tr>
                            <tr><td><strong>VADER Compound:</strong></td><td>{{ entry.vader_compound or 'N/A' }}</td></tr>
                            <tr><td><strong>Image Prompt:</strong></td><td>{{ entry.image_prompt or 'N/A' }}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
function openDuplicateDetails(dupEntryId) {
    window.open(`/duplicate_details/${dupEntryId}`, '_blank');
}

function showImageModal(base64Image) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Preview Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="data:image/jpeg;base64,${base64Image}" class="img-fluid" style="max-width: 100%;">
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
    
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}
</script>

<style>
.clickable-cell {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
}

.clickable-cell:hover {
    color: #0056b3;
}

.binary-data {
    font-family: monospace;
    font-size: 11px;
    color: #6c757d;
}
</style>
{% endblock %}
