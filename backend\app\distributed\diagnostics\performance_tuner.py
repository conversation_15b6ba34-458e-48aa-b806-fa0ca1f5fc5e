"""
Performance tuning and optimization tools for distributed ETL workers.
Provides automated performance analysis and tuning recommendations.
"""

import asyncio
import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from ..worker_config import WorkerConfig
from ..processing_stage import ProcessingStage
from .health_checker import SystemMetrics


class OptimizationLevel(Enum):
    """Optimization recommendation levels."""
    LOW_IMPACT = "low_impact"
    MEDIUM_IMPACT = "medium_impact"
    HIGH_IMPACT = "high_impact"
    CRITICAL = "critical"


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    name: str
    value: float
    unit: str
    timestamp: datetime
    stage: Optional[ProcessingStage] = None


@dataclass
class OptimizationRecommendation:
    """Performance optimization recommendation."""
    category: str
    level: OptimizationLevel
    title: str
    description: str
    current_value: Any
    recommended_value: Any
    expected_improvement: str
    implementation_steps: List[str]
    risks: List[str]


@dataclass
class PerformanceAnalysis:
    """Comprehensive performance analysis result."""
    worker_id: str
    analysis_timestamp: datetime
    analysis_period_hours: float
    overall_score: float  # 0-100
    bottlenecks: List[str]
    recommendations: List[OptimizationRecommendation]
    metrics_summary: Dict[str, Any]
    resource_utilization: Dict[str, float]


class PerformanceTuner:
    """Performance analysis and tuning tool for distributed workers."""
    
    def __init__(self, config: WorkerConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.metrics_history: List[PerformanceMetric] = []
        
    def add_metric(self, name: str, value: float, unit: str, stage: Optional[ProcessingStage] = None):
        """Add a performance metric to the history."""
        metric = PerformanceMetric(
            name=name,
            value=value,
            unit=unit,
            timestamp=datetime.now(),
            stage=stage
        )
        self.metrics_history.append(metric)
        
        # Keep only last 24 hours of metrics
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.metrics_history = [m for m in self.metrics_history if m.timestamp > cutoff_time]
    
    def analyze_batch_size_performance(self) -> List[OptimizationRecommendation]:
        """Analyze batch size performance and provide recommendations."""
        recommendations = []
        
        # Get processing time metrics by batch size
        processing_times = [m for m in self.metrics_history if m.name == "batch_processing_time"]
        
        if len(processing_times) < 10:
            return recommendations  # Not enough data
        
        # Calculate average processing time per entry
        avg_time_per_entry = statistics.mean([m.value for m in processing_times]) / self.config.batch_size
        
        # Analyze throughput
        throughput_metrics = [m for m in self.metrics_history if m.name == "throughput_per_minute"]
        if throughput_metrics:
            current_throughput = statistics.mean([m.value for m in throughput_metrics[-10:]])
            
            # Batch size recommendations based on performance patterns
            if avg_time_per_entry > 10:  # > 10 seconds per entry
                if self.config.batch_size > 10:
                    recommendations.append(OptimizationRecommendation(
                        category="batch_size",
                        level=OptimizationLevel.MEDIUM_IMPACT,
                        title="Reduce batch size for slow processing",
                        description="Current batch size is too large for the processing time per entry, causing long claim timeouts and potential work loss.",
                        current_value=self.config.batch_size,
                        recommended_value=max(5, self.config.batch_size // 2),
                        expected_improvement="20-30% reduction in timeout risk, better fault tolerance",
                        implementation_steps=[
                            f"Update WORKER_BATCH_SIZE to {max(5, self.config.batch_size // 2)}",
                            "Monitor processing times for 1 hour",
                            "Adjust further if needed"
                        ],
                        risks=["Slightly higher database overhead per entry"]
                    ))
            
            elif avg_time_per_entry < 2:  # < 2 seconds per entry
                if self.config.batch_size < 50:
                    recommendations.append(OptimizationRecommendation(
                        category="batch_size",
                        level=OptimizationLevel.MEDIUM_IMPACT,
                        title="Increase batch size for fast processing",
                        description="Processing is fast enough to handle larger batches, which would improve database efficiency.",
                        current_value=self.config.batch_size,
                        recommended_value=min(100, self.config.batch_size * 2),
                        expected_improvement="15-25% improvement in throughput, reduced database overhead",
                        implementation_steps=[
                            f"Update WORKER_BATCH_SIZE to {min(100, self.config.batch_size * 2)}",
                            "Monitor memory usage and processing times",
                            "Ensure claim timeout is sufficient for larger batches"
                        ],
                        risks=["Higher memory usage", "Longer recovery time on worker failure"]
                    ))
        
        return recommendations
    
    def analyze_timeout_configuration(self) -> List[OptimizationRecommendation]:
        """Analyze timeout configuration and provide recommendations."""
        recommendations = []
        
        # Get timeout-related metrics
        timeout_events = [m for m in self.metrics_history if m.name == "claim_timeout"]
        processing_times = [m for m in self.metrics_history if m.name == "batch_processing_time"]
        
        if processing_times:
            max_processing_time = max([m.value for m in processing_times])
            avg_processing_time = statistics.mean([m.value for m in processing_times])
            
            # Calculate recommended timeout (processing time + buffer)
            recommended_timeout_minutes = max(15, int((max_processing_time * 1.5) / 60))
            
            if self.config.claim_timeout_minutes < recommended_timeout_minutes:
                recommendations.append(OptimizationRecommendation(
                    category="timeout",
                    level=OptimizationLevel.HIGH_IMPACT,
                    title="Increase claim timeout to prevent work loss",
                    description=f"Current timeout ({self.config.claim_timeout_minutes}m) is too short for maximum processing time ({max_processing_time:.1f}s).",
                    current_value=f"{self.config.claim_timeout_minutes} minutes",
                    recommended_value=f"{recommended_timeout_minutes} minutes",
                    expected_improvement="Eliminate timeout-related work loss, improve reliability",
                    implementation_steps=[
                        f"Update WORKER_CLAIM_TIMEOUT_MINUTES to {recommended_timeout_minutes}",
                        "Monitor for timeout events over next 24 hours",
                        "Adjust if timeouts still occur"
                    ],
                    risks=["Slower recovery from worker failures"]
                ))
            
            elif self.config.claim_timeout_minutes > recommended_timeout_minutes * 2:
                recommendations.append(OptimizationRecommendation(
                    category="timeout",
                    level=OptimizationLevel.LOW_IMPACT,
                    title="Reduce claim timeout for faster recovery",
                    description=f"Current timeout ({self.config.claim_timeout_minutes}m) is much longer than needed, slowing failure recovery.",
                    current_value=f"{self.config.claim_timeout_minutes} minutes",
                    recommended_value=f"{recommended_timeout_minutes} minutes",
                    expected_improvement="Faster recovery from worker failures",
                    implementation_steps=[
                        f"Update WORKER_CLAIM_TIMEOUT_MINUTES to {recommended_timeout_minutes}",
                        "Monitor processing times to ensure no timeouts",
                        "Gradually reduce if stable"
                    ],
                    risks=["Potential timeout if processing becomes slower"]
                ))
        
        return recommendations
    
    def analyze_resource_utilization(self, system_metrics: SystemMetrics) -> List[OptimizationRecommendation]:
        """Analyze system resource utilization and provide recommendations."""
        recommendations = []
        
        # CPU utilization analysis
        if system_metrics.cpu_percent > 85:
            recommendations.append(OptimizationRecommendation(
                category="resources",
                level=OptimizationLevel.HIGH_IMPACT,
                title="High CPU usage detected",
                description="CPU usage is consistently high, which may cause processing delays and timeouts.",
                current_value=f"{system_metrics.cpu_percent:.1f}%",
                recommended_value="< 70%",
                expected_improvement="Reduced processing delays, better stability",
                implementation_steps=[
                    "Reduce batch size to decrease CPU load per cycle",
                    "Increase processing delay between batches",
                    "Consider scaling horizontally with more workers",
                    "Profile CPU-intensive operations for optimization"
                ],
                risks=["Reduced throughput per worker"]
            ))
        
        # Memory utilization analysis
        if system_metrics.memory_percent > 80:
            recommendations.append(OptimizationRecommendation(
                category="resources",
                level=OptimizationLevel.HIGH_IMPACT,
                title="High memory usage detected",
                description="Memory usage is high, which may cause swapping and performance degradation.",
                current_value=f"{system_metrics.memory_percent:.1f}%",
                recommended_value="< 70%",
                expected_improvement="Reduced swapping, better performance stability",
                implementation_steps=[
                    "Reduce batch size to lower memory usage per batch",
                    "Implement memory cleanup after each batch",
                    "Profile memory usage patterns",
                    "Consider increasing available memory"
                ],
                risks=["Reduced throughput per worker"]
            ))
        
        # Disk utilization analysis
        if system_metrics.disk_percent > 85:
            recommendations.append(OptimizationRecommendation(
                category="resources",
                level=OptimizationLevel.CRITICAL,
                title="Disk space critically low",
                description="Disk usage is very high, which may cause application failures.",
                current_value=f"{system_metrics.disk_percent:.1f}%",
                recommended_value="< 80%",
                expected_improvement="Prevent disk-related failures",
                implementation_steps=[
                    "Clean up temporary files and logs",
                    "Implement log rotation",
                    "Move large files to external storage",
                    "Monitor disk usage continuously"
                ],
                risks=["Application may fail if disk becomes full"]
            ))
        
        return recommendations
    
    def analyze_stage_performance(self) -> List[OptimizationRecommendation]:
        """Analyze performance by processing stage."""
        recommendations = []
        
        # Group metrics by stage
        stage_metrics = {}
        for metric in self.metrics_history:
            if metric.stage:
                if metric.stage not in stage_metrics:
                    stage_metrics[metric.stage] = []
                stage_metrics[metric.stage].append(metric)
        
        # Analyze each stage
        for stage, metrics in stage_metrics.items():
            processing_times = [m for m in metrics if m.name == "processing_time"]
            error_rates = [m for m in metrics if m.name == "error_rate"]
            
            if processing_times:
                avg_time = statistics.mean([m.value for m in processing_times])
                
                # Stage-specific recommendations
                if stage == ProcessingStage.DOWNLOAD_FULL_TEXT:
                    if avg_time > 30:  # > 30 seconds per entry
                        recommendations.append(OptimizationRecommendation(
                            category="stage_optimization",
                            level=OptimizationLevel.MEDIUM_IMPACT,
                            title=f"Optimize {stage.value} performance",
                            description="Full text download is taking too long, likely due to network timeouts or slow websites.",
                            current_value=f"{avg_time:.1f}s per entry",
                            recommended_value="< 20s per entry",
                            expected_improvement="Faster processing, reduced timeouts",
                            implementation_steps=[
                                "Reduce HTTP timeout for full text downloads",
                                "Implement parallel downloads within batch",
                                "Add retry logic with exponential backoff",
                                "Skip slow websites after timeout"
                            ],
                            risks=["May miss content from slow websites"]
                        ))
                
                elif stage in [ProcessingStage.SENTIMENT_ANALYSIS, ProcessingStage.IPTC_CLASSIFICATION]:
                    if avg_time > 60:  # > 60 seconds per entry
                        recommendations.append(OptimizationRecommendation(
                            category="stage_optimization",
                            level=OptimizationLevel.MEDIUM_IMPACT,
                            title=f"Optimize {stage.value} model loading",
                            description="ML model processing is slow, likely due to repeated model loading.",
                            current_value=f"{avg_time:.1f}s per entry",
                            recommended_value="< 30s per entry",
                            expected_improvement="2x faster processing through model caching",
                            implementation_steps=[
                                "Implement model caching to avoid reloading",
                                "Process entries in batches through the same model",
                                "Use model quantization for faster inference",
                                "Consider GPU acceleration if available"
                            ],
                            risks=["Higher memory usage for cached models"]
                        ))
            
            if error_rates:
                avg_error_rate = statistics.mean([m.value for m in error_rates])
                if avg_error_rate > 5:  # > 5% error rate
                    recommendations.append(OptimizationRecommendation(
                        category="reliability",
                        level=OptimizationLevel.HIGH_IMPACT,
                        title=f"High error rate in {stage.value}",
                        description=f"Error rate is {avg_error_rate:.1f}%, indicating reliability issues.",
                        current_value=f"{avg_error_rate:.1f}%",
                        recommended_value="< 2%",
                        expected_improvement="Better reliability, fewer failed entries",
                        implementation_steps=[
                            "Analyze error logs to identify common failure patterns",
                            "Implement better error handling and retries",
                            "Add input validation to prevent processing errors",
                            "Monitor external dependencies for availability"
                        ],
                        risks=["May require significant code changes"]
                    ))
        
        return recommendations
    
    def analyze_database_performance(self) -> List[OptimizationRecommendation]:
        """Analyze database performance and provide recommendations."""
        recommendations = []
        
        # Get database-related metrics
        db_query_times = [m for m in self.metrics_history if m.name == "db_query_time"]
        connection_errors = [m for m in self.metrics_history if m.name == "db_connection_error"]
        
        if db_query_times:
            avg_query_time = statistics.mean([m.value for m in db_query_times])
            max_query_time = max([m.value for m in db_query_times])
            
            if avg_query_time > 1000:  # > 1 second
                recommendations.append(OptimizationRecommendation(
                    category="database",
                    level=OptimizationLevel.HIGH_IMPACT,
                    title="Slow database queries detected",
                    description=f"Average query time is {avg_query_time:.0f}ms, which is impacting performance.",
                    current_value=f"{avg_query_time:.0f}ms",
                    recommended_value="< 500ms",
                    expected_improvement="Faster work claiming and status updates",
                    implementation_steps=[
                        "Analyze slow query logs",
                        "Add missing indexes on processing_status and claimed_by columns",
                        "Optimize batch claiming queries",
                        "Consider database connection pooling tuning"
                    ],
                    risks=["Database schema changes may require downtime"]
                ))
        
        if connection_errors:
            error_count = len(connection_errors)
            if error_count > 5:  # More than 5 connection errors
                recommendations.append(OptimizationRecommendation(
                    category="database",
                    level=OptimizationLevel.CRITICAL,
                    title="Database connection issues",
                    description=f"Detected {error_count} database connection errors, indicating connectivity problems.",
                    current_value=f"{error_count} errors",
                    recommended_value="0 errors",
                    expected_improvement="Improved reliability and reduced processing failures",
                    implementation_steps=[
                        "Check database server health and connectivity",
                        "Increase connection pool size",
                        "Implement connection retry logic",
                        "Monitor database server resources"
                    ],
                    risks=["May require database server maintenance"]
                ))
        
        return recommendations
    
    async def run_performance_analysis(self, system_metrics: SystemMetrics, 
                                     analysis_period_hours: float = 1.0) -> PerformanceAnalysis:
        """Run comprehensive performance analysis."""
        
        # Collect all recommendations
        all_recommendations = []
        all_recommendations.extend(self.analyze_batch_size_performance())
        all_recommendations.extend(self.analyze_timeout_configuration())
        all_recommendations.extend(self.analyze_resource_utilization(system_metrics))
        all_recommendations.extend(self.analyze_stage_performance())
        all_recommendations.extend(self.analyze_database_performance())
        
        # Calculate overall performance score (0-100)
        score = 100.0
        for rec in all_recommendations:
            if rec.level == OptimizationLevel.CRITICAL:
                score -= 25
            elif rec.level == OptimizationLevel.HIGH_IMPACT:
                score -= 15
            elif rec.level == OptimizationLevel.MEDIUM_IMPACT:
                score -= 10
            elif rec.level == OptimizationLevel.LOW_IMPACT:
                score -= 5
        
        score = max(0, score)
        
        # Identify bottlenecks
        bottlenecks = []
        if system_metrics.cpu_percent > 80:
            bottlenecks.append("High CPU usage")
        if system_metrics.memory_percent > 80:
            bottlenecks.append("High memory usage")
        
        # Get recent metrics for summary
        recent_metrics = [m for m in self.metrics_history 
                         if m.timestamp > datetime.now() - timedelta(hours=analysis_period_hours)]
        
        metrics_summary = {}
        if recent_metrics:
            processing_times = [m.value for m in recent_metrics if m.name == "processing_time"]
            if processing_times:
                metrics_summary["avg_processing_time"] = statistics.mean(processing_times)
                metrics_summary["max_processing_time"] = max(processing_times)
            
            throughput_metrics = [m.value for m in recent_metrics if m.name == "throughput_per_minute"]
            if throughput_metrics:
                metrics_summary["avg_throughput"] = statistics.mean(throughput_metrics)
            
            error_rates = [m.value for m in recent_metrics if m.name == "error_rate"]
            if error_rates:
                metrics_summary["avg_error_rate"] = statistics.mean(error_rates)
        
        # Resource utilization summary
        resource_utilization = {
            "cpu_percent": system_metrics.cpu_percent,
            "memory_percent": system_metrics.memory_percent,
            "disk_percent": system_metrics.disk_percent
        }
        
        return PerformanceAnalysis(
            worker_id=self.config.worker_id,
            analysis_timestamp=datetime.now(),
            analysis_period_hours=analysis_period_hours,
            overall_score=score,
            bottlenecks=bottlenecks,
            recommendations=all_recommendations,
            metrics_summary=metrics_summary,
            resource_utilization=resource_utilization
        )
    
    def format_analysis_report(self, analysis: PerformanceAnalysis) -> str:
        """Format performance analysis as human-readable report."""
        lines = []
        lines.append(f"Performance Analysis Report: {analysis.worker_id}")
        lines.append(f"Analysis Time: {analysis.analysis_timestamp}")
        lines.append(f"Analysis Period: {analysis.analysis_period_hours:.1f} hours")
        lines.append(f"Overall Performance Score: {analysis.overall_score:.1f}/100")
        lines.append("")
        
        # Performance score interpretation
        if analysis.overall_score >= 90:
            lines.append("✓ Performance: EXCELLENT - System is running optimally")
        elif analysis.overall_score >= 75:
            lines.append("⚠ Performance: GOOD - Minor optimizations recommended")
        elif analysis.overall_score >= 50:
            lines.append("⚠ Performance: FAIR - Several optimizations needed")
        else:
            lines.append("✗ Performance: POOR - Critical optimizations required")
        lines.append("")
        
        # Bottlenecks
        if analysis.bottlenecks:
            lines.append("Identified Bottlenecks:")
            for bottleneck in analysis.bottlenecks:
                lines.append(f"  • {bottleneck}")
            lines.append("")
        
        # Resource utilization
        lines.append("Resource Utilization:")
        for resource, usage in analysis.resource_utilization.items():
            status = "✓" if usage < 70 else "⚠" if usage < 85 else "✗"
            lines.append(f"  {status} {resource.replace('_', ' ').title()}: {usage:.1f}%")
        lines.append("")
        
        # Metrics summary
        if analysis.metrics_summary:
            lines.append("Performance Metrics Summary:")
            for metric, value in analysis.metrics_summary.items():
                if isinstance(value, float):
                    lines.append(f"  {metric.replace('_', ' ').title()}: {value:.2f}")
                else:
                    lines.append(f"  {metric.replace('_', ' ').title()}: {value}")
            lines.append("")
        
        # Recommendations by priority
        if analysis.recommendations:
            lines.append("Optimization Recommendations:")
            lines.append("")
            
            # Group by level
            by_level = {}
            for rec in analysis.recommendations:
                if rec.level not in by_level:
                    by_level[rec.level] = []
                by_level[rec.level].append(rec)
            
            # Show in priority order
            for level in [OptimizationLevel.CRITICAL, OptimizationLevel.HIGH_IMPACT, 
                         OptimizationLevel.MEDIUM_IMPACT, OptimizationLevel.LOW_IMPACT]:
                if level in by_level:
                    level_symbol = {
                        OptimizationLevel.CRITICAL: "🔴",
                        OptimizationLevel.HIGH_IMPACT: "🟠",
                        OptimizationLevel.MEDIUM_IMPACT: "🟡",
                        OptimizationLevel.LOW_IMPACT: "🟢"
                    }[level]
                    
                    lines.append(f"{level_symbol} {level.value.replace('_', ' ').title()} Priority:")
                    
                    for rec in by_level[level]:
                        lines.append(f"  • {rec.title}")
                        lines.append(f"    {rec.description}")
                        lines.append(f"    Current: {rec.current_value} → Recommended: {rec.recommended_value}")
                        lines.append(f"    Expected improvement: {rec.expected_improvement}")
                        
                        if rec.implementation_steps:
                            lines.append("    Implementation steps:")
                            for step in rec.implementation_steps:
                                lines.append(f"      - {step}")
                        
                        if rec.risks:
                            lines.append("    Risks:")
                            for risk in rec.risks:
                                lines.append(f"      - {risk}")
                        lines.append("")
                    
                    lines.append("")
        else:
            lines.append("No optimization recommendations at this time.")
        
        return "\n".join(lines)
    
    def to_dict(self, analysis: PerformanceAnalysis) -> Dict[str, Any]:
        """Convert performance analysis to dictionary for JSON serialization."""
        return {
            "worker_id": analysis.worker_id,
            "analysis_timestamp": analysis.analysis_timestamp.isoformat(),
            "analysis_period_hours": analysis.analysis_period_hours,
            "overall_score": analysis.overall_score,
            "bottlenecks": analysis.bottlenecks,
            "recommendations": [
                {
                    "category": rec.category,
                    "level": rec.level.value,
                    "title": rec.title,
                    "description": rec.description,
                    "current_value": rec.current_value,
                    "recommended_value": rec.recommended_value,
                    "expected_improvement": rec.expected_improvement,
                    "implementation_steps": rec.implementation_steps,
                    "risks": rec.risks
                }
                for rec in analysis.recommendations
            ],
            "metrics_summary": analysis.metrics_summary,
            "resource_utilization": analysis.resource_utilization
        }