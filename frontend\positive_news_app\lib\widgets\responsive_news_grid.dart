import 'package:flutter/material.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:breakingbright/utils/responsive_layout_builder.dart';
import 'package:breakingbright/widgets/news_card.dart';

class ResponsiveNewsGrid extends StatelessWidget {
  // Shared constants for layout calculations
  static const double _imageHeight = NewsCard.imageHeight;
  static const double _bottomBarHeight = NewsCard.bottomBarHeight;
  static const double _cardPadding = 16.0;
  static const double _gridSpacing = 16.0;
  static const double _estimatedTextHeight = 155.0;
  static const double _minCardWidth = 300.0; // Minimum width for news cards

  final List<EntryWithSource> newsList;
  final bool showFavoriteButton;
  final bool showSimilarMessage;
  final ScrollController? scrollController;
  final bool shrinkWrap; // Add this parameter
  final ScrollPhysics? physics; // Add this parameter

  const ResponsiveNewsGrid({
    super.key,
    required this.newsList,
    this.showFavoriteButton = true,
    this.showSimilarMessage = true,
    this.scrollController,
    this.shrinkWrap = false, // Default to false
    this.physics, // Default to null
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveLayoutBuilder(
      builder: (context, constraints) {
        var columnCount = ResponsiveLayoutBuilder.getColumnCount(constraints);
        final padding = ResponsiveLayoutBuilder.getPadding(constraints);

        // Calculate the width available for each card
        final availableWidth = constraints.maxWidth - padding.horizontal;
        
        // Calculate maximum columns that can fit with minimum card width
        final maxColumns = (availableWidth + _gridSpacing) ~/ (_minCardWidth + _gridSpacing);
        
        // Use the smaller of the responsive column count and max columns that fit
        columnCount = maxColumns.clamp(1, columnCount);

        // Recalculate card width with adjusted column count
        final cardWidth = (availableWidth - (_gridSpacing * (columnCount - 1))) / columnCount;

        // Calculate the expected height of a card
        final estimatedCardHeight = _imageHeight + // Image
                                  (_cardPadding * 2) + // Top and bottom padding
                                  _estimatedTextHeight + // Text content
                                  _bottomBarHeight; // Bottom bar
        
        return GridView.builder(
          controller: scrollController,
          padding: padding,
          shrinkWrap: shrinkWrap, // Use the parameter
          physics: physics, // Use the parameter
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columnCount,
            childAspectRatio: cardWidth / estimatedCardHeight,
            crossAxisSpacing: _gridSpacing,
            mainAxisSpacing: _gridSpacing,
          ),
          itemCount: newsList.length,
          itemBuilder: (context, index) {
            return NewsCard(
              news: newsList[index],
              showFavoriteButton: showFavoriteButton,
              showSimilarMessage: showSimilarMessage,
              width: cardWidth,
            );
          },
        );
      },
    );
  }
}


