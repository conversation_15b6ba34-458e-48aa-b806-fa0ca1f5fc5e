"""
Health Check Server for distributed ETL workers.

This module provides HTTP endpoints for monitoring worker health and status.
"""

import json
import logging
import threading
from datetime import datetime, timezone
from http.server import HTTPServer, BaseHTTPRequestHandler
from typing import Optional, Dict, Any, Callable
from urllib.parse import urlparse, parse_qs

logger = logging.getLogger(__name__)


class HealthCheckHandler(BaseHTTPRequestHandler):
    """HTTP request handler for health check endpoints."""
    
    def __init__(self, *args, health_provider: Callable[[], Dict[str, Any]] = None, **kwargs):
        """Initialize handler with health provider function."""
        self.health_provider = health_provider
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests for health endpoints."""
        try:
            parsed_url = urlparse(self.path)
            path = parsed_url.path
            
            if path == '/health':
                self._handle_health_check()
            elif path == '/health/detailed':
                self._handle_detailed_health()
            elif path == '/health/ready':
                self._handle_readiness_check()
            elif path == '/health/live':
                self._handle_liveness_check()
            elif path == '/metrics':
                self._handle_metrics()
            else:
                self._send_error(404, "Not Found")
                
        except Exception as e:
            logger.error(f"Error handling health check request: {e}")
            self._send_error(500, "Internal Server Error")
    
    def _handle_health_check(self):
        """Handle basic health check endpoint."""
        if not self.health_provider:
            self._send_error(503, "Health provider not available")
            return
        
        try:
            health_data = self.health_provider()
            
            # Determine overall health status
            is_healthy = health_data.get('healthy', False)
            status_code = 200 if is_healthy else 503
            
            response = {
                'status': 'healthy' if is_healthy else 'unhealthy',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'worker_id': health_data.get('worker_id'),
                'state': health_data.get('state'),
                'heartbeat_stale': health_data.get('heartbeat_stale', False)
            }
            
            self._send_json_response(response, status_code)
            
        except Exception as e:
            logger.error(f"Error getting health data: {e}")
            self._send_error(503, "Health check failed")
    
    def _handle_detailed_health(self):
        """Handle detailed health check endpoint."""
        if not self.health_provider:
            self._send_error(503, "Health provider not available")
            return
        
        try:
            health_data = self.health_provider()
            
            # Add timestamp to response
            health_data['timestamp'] = datetime.now(timezone.utc).isoformat()
            
            # Determine status code based on health
            is_healthy = health_data.get('healthy', False)
            status_code = 200 if is_healthy else 503
            
            self._send_json_response(health_data, status_code)
            
        except Exception as e:
            logger.error(f"Error getting detailed health data: {e}")
            self._send_error(503, "Detailed health check failed")
    
    def _handle_readiness_check(self):
        """Handle Kubernetes-style readiness check."""
        if not self.health_provider:
            self._send_error(503, "Health provider not available")
            return
        
        try:
            health_data = self.health_provider()
            
            # Worker is ready if it's running and not stale
            is_ready = (
                health_data.get('state') == 'running' and
                not health_data.get('heartbeat_stale', True)
            )
            
            status_code = 200 if is_ready else 503
            response = {
                'status': 'ready' if is_ready else 'not_ready',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'worker_id': health_data.get('worker_id'),
                'state': health_data.get('state')
            }
            
            self._send_json_response(response, status_code)
            
        except Exception as e:
            logger.error(f"Error checking readiness: {e}")
            self._send_error(503, "Readiness check failed")
    
    def _handle_liveness_check(self):
        """Handle Kubernetes-style liveness check."""
        if not self.health_provider:
            self._send_error(503, "Health provider not available")
            return
        
        try:
            health_data = self.health_provider()
            
            # Worker is alive if it's not in error state
            is_alive = health_data.get('state') != 'error'
            
            status_code = 200 if is_alive else 503
            response = {
                'status': 'alive' if is_alive else 'dead',
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'worker_id': health_data.get('worker_id'),
                'state': health_data.get('state'),
                'last_error': health_data.get('last_error')
            }
            
            self._send_json_response(response, status_code)
            
        except Exception as e:
            logger.error(f"Error checking liveness: {e}")
            self._send_error(503, "Liveness check failed")
    
    def _handle_metrics(self):
        """Handle metrics endpoint."""
        if not self.health_provider:
            self._send_error(503, "Health provider not available")
            return
        
        try:
            health_data = self.health_provider()
            stats = health_data.get('stats', {})
            
            # Format metrics in Prometheus-style format
            metrics_lines = [
                f"# HELP worker_batches_processed_total Total number of batches processed",
                f"# TYPE worker_batches_processed_total counter",
                f'worker_batches_processed_total{{worker_id="{health_data.get("worker_id")}"}} {stats.get("batches_processed", 0)}',
                "",
                f"# HELP worker_entries_processed_total Total number of entries processed",
                f"# TYPE worker_entries_processed_total counter", 
                f'worker_entries_processed_total{{worker_id="{health_data.get("worker_id")}"}} {stats.get("entries_processed", 0)}',
                "",
                f"# HELP worker_entries_failed_total Total number of entries that failed processing",
                f"# TYPE worker_entries_failed_total counter",
                f'worker_entries_failed_total{{worker_id="{health_data.get("worker_id")}"}} {stats.get("entries_failed", 0)}',
                "",
                f"# HELP worker_processing_time_seconds_total Total processing time in seconds",
                f"# TYPE worker_processing_time_seconds_total counter",
                f'worker_processing_time_seconds_total{{worker_id="{health_data.get("worker_id")}"}} {stats.get("total_processing_time", 0)}',
                "",
                f"# HELP worker_current_batch_size Current batch size being processed",
                f"# TYPE worker_current_batch_size gauge",
                f'worker_current_batch_size{{worker_id="{health_data.get("worker_id")}"}} {health_data.get("current_batch_size", 0)}',
                "",
                f"# HELP worker_healthy Worker health status (1 = healthy, 0 = unhealthy)",
                f"# TYPE worker_healthy gauge",
                f'worker_healthy{{worker_id="{health_data.get("worker_id")}"}} {1 if health_data.get("healthy") else 0}',
                ""
            ]
            
            metrics_text = "\n".join(metrics_lines)
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.send_header('Content-Length', str(len(metrics_text.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(metrics_text.encode('utf-8'))
            
        except Exception as e:
            logger.error(f"Error generating metrics: {e}")
            self._send_error(503, "Metrics generation failed")
    
    def _send_json_response(self, data: Dict[str, Any], status_code: int = 200):
        """Send JSON response."""
        json_data = json.dumps(data, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(json_data.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))
    
    def _send_error(self, status_code: int, message: str):
        """Send error response."""
        error_data = {
            'error': message,
            'status_code': status_code,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        self._send_json_response(error_data, status_code)
    
    def log_message(self, format, *args):
        """Override to use our logger instead of stderr."""
        logger.debug(f"Health check request: {format % args}")


class HealthCheckServer:
    """
    HTTP server for worker health check endpoints.
    
    Provides REST endpoints for monitoring worker health, readiness, and metrics.
    """
    
    def __init__(self, port: int, health_provider: Callable[[], Dict[str, Any]]):
        """
        Initialize health check server.
        
        Args:
            port: Port to listen on
            health_provider: Function that returns current health status
        """
        self.port = port
        self.health_provider = health_provider
        self.server: Optional[HTTPServer] = None
        self.server_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
    
    def start(self) -> None:
        """Start the health check server."""
        try:
            # Create handler class with health provider
            def handler_factory(*args, **kwargs):
                return HealthCheckHandler(*args, health_provider=self.health_provider, **kwargs)
            
            # Create and start server
            self.server = HTTPServer(('0.0.0.0', self.port), handler_factory)
            
            # Start server in separate thread
            self.server_thread = threading.Thread(
                target=self._server_loop,
                name=f"health-server-{self.port}",
                daemon=True
            )
            self.server_thread.start()
            
            logger.info(f"Health check server started on port {self.port}")
            
        except Exception as e:
            logger.error(f"Failed to start health check server: {e}")
            raise
    
    def stop(self, timeout: int = 5) -> None:
        """Stop the health check server."""
        if not self.server:
            return
        
        logger.info(f"Stopping health check server on port {self.port}")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Shutdown server
        self.server.shutdown()
        
        # Wait for server thread to finish
        if self.server_thread and self.server_thread.is_alive():
            self.server_thread.join(timeout=timeout)
        
        self.server.server_close()
        logger.info("Health check server stopped")
    
    def _server_loop(self) -> None:
        """Main server loop."""
        try:
            self.server.serve_forever()
        except Exception as e:
            if not self._shutdown_event.is_set():
                logger.error(f"Health check server error: {e}")
    
    @property
    def is_running(self) -> bool:
        """Check if server is running."""
        return (
            self.server is not None and 
            self.server_thread is not None and 
            self.server_thread.is_alive()
        )


class WorkerHealthEndpoints:
    """
    Convenience class for managing worker health check endpoints.
    
    Integrates with DistributedWorker to provide health monitoring.
    """
    
    def __init__(self, worker, port: Optional[int] = None):
        """
        Initialize health endpoints for a worker.
        
        Args:
            worker: DistributedWorker instance
            port: Port for health check server (optional)
        """
        self.worker = worker
        self.port = port or getattr(worker.config, 'health_check_port', None)
        self.server: Optional[HealthCheckServer] = None
    
    def start(self) -> None:
        """Start health check endpoints if port is configured."""
        if not self.port:
            logger.debug("No health check port configured, skipping health endpoints")
            return
        
        try:
            self.server = HealthCheckServer(
                port=self.port,
                health_provider=self.worker.get_health_status
            )
            self.server.start()
            
            logger.info(f"Worker health endpoints available at http://localhost:{self.port}/health")
            
        except Exception as e:
            logger.error(f"Failed to start health endpoints: {e}")
            # Don't fail worker startup if health endpoints fail
            self.server = None
    
    def stop(self) -> None:
        """Stop health check endpoints."""
        if self.server:
            self.server.stop()
            self.server = None
    
    @property
    def is_running(self) -> bool:
        """Check if health endpoints are running."""
        return self.server is not None and self.server.is_running
    
    def get_endpoint_urls(self) -> Dict[str, str]:
        """Get URLs for all health endpoints."""
        if not self.port:
            return {}
        
        base_url = f"http://localhost:{self.port}"
        return {
            'health': f"{base_url}/health",
            'detailed_health': f"{base_url}/health/detailed",
            'readiness': f"{base_url}/health/ready",
            'liveness': f"{base_url}/health/live",
            'metrics': f"{base_url}/metrics"
        }