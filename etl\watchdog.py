#!/usr/bin/env python3
"""
ETL Manager Script
Monitor ETL health, restart when needed, or run one-time checks
Usage:
  python watchdog.py                    # Run continuous monitoring (default)
  python watchdog.py --check-once       # Run single health check
  python watchdog.py --restart          # Restart ETL process
  python watchdog.py --stop             # Stop ETL process
"""

import os
import sys
import time
import logging
import subprocess
import psutil
from datetime import datetime, timedelta

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)

class ETLManager:
    def __init__(self, check_interval=300, max_failures=3, restart_cooldown=600):
        """
        Initialize watchdog
        
        Args:
            check_interval: Seconds between health checks (default: 5 minutes)
            max_failures: Max consecutive failures before restart (default: 3)
            restart_cooldown: Seconds to wait after restart before monitoring (default: 10 minutes)
        """
        self.check_interval = check_interval
        self.max_failures = max_failures
        self.restart_cooldown = restart_cooldown
        
        self.consecutive_failures = 0
        self.last_restart = None
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('c:/temp/etl_watchdog.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def check_log_activity(self, log_path='c:/temp/news_aggregator.log', max_silence_minutes=30):
        """Check if the log file shows recent activity"""
        if not os.path.exists(log_path):
            return False, "Log file not found"
        
        try:
            # Get last modification time
            last_modified = datetime.fromtimestamp(os.path.getmtime(log_path))
            silence_duration = datetime.now() - last_modified
            
            if silence_duration > timedelta(minutes=max_silence_minutes):
                return False, f"Log silent for {silence_duration}"
            
            # Check last few lines for errors
            with open(log_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-50:] if len(lines) > 50 else lines
                
                error_count = sum(1 for line in recent_lines if 'ERROR' in line or 'CRITICAL' in line)
                if error_count > 10:
                    return False, f"High error rate: {error_count} errors in recent logs"
            
            return True, f"Active, last update: {last_modified}"
        except Exception as e:
            return False, f"Error reading log: {e}"

    def check_process_health(self):
        """Check if news_aggregator process is running and healthy"""
        etl_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'cpu_percent']):
            try:
                if proc.info['cmdline'] and any('news_aggregator.py' in arg for arg in proc.info['cmdline']):
                    etl_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if not etl_processes:
            return False, "No ETL process found"
        
        # Check resource usage
        for proc in etl_processes:
            try:
                memory_mb = proc.memory_info().rss / 1024 / 1024
                
                if memory_mb > 8192:  # 8GB
                    return False, f"High memory usage: {memory_mb:.1f}MB"
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return True, f"Process healthy, {len(etl_processes)} instance(s) running"

    def check_database_connectivity(self):
        """Test database connection"""
        try:
            from backend.app.core.config import settings
            from sqlalchemy import create_engine, text
            
            engine = create_engine(settings.DATABASE_URL, pool_pre_ping=True)
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1 FROM dual"))
                result.fetchone()
            
            return True, "Database connection OK"
        except Exception as e:
            return False, f"Database connection failed: {e}"

    def find_etl_processes(self):
        """Find all running ETL processes"""
        etl_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('news_aggregator.py' in arg for arg in proc.info['cmdline']):
                    etl_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return etl_processes

    def stop_etl_processes(self):
        """Gracefully stop ETL processes"""
        processes = self.find_etl_processes()
        
        if not processes:
            print("No ETL processes found to stop")
            return True
        
        print(f"Found {len(processes)} ETL process(es) to stop")
        
        # First try graceful termination
        for proc in processes:
            try:
                print(f"Sending SIGTERM to PID {proc.pid}")
                proc.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"Could not terminate PID {proc.pid}: {e}")
        
        # Wait for graceful shutdown
        time.sleep(10)
        
        # Check if processes are still running
        remaining = self.find_etl_processes()
        if remaining:
            print(f"Force killing {len(remaining)} remaining process(es)")
            for proc in remaining:
                try:
                    proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    print(f"Could not kill PID {proc.pid}: {e}")
            
            time.sleep(5)
        
        # Final check
        final_check = self.find_etl_processes()
        if final_check:
            print(f"Warning: {len(final_check)} process(es) still running")
            return False
        
        print("All ETL processes stopped successfully")
        return True

    def start_etl_process(self):
        """Start the ETL process"""
        # First check if ETL is already running
        existing_processes = self.find_etl_processes()
        if existing_processes:
            print(f"ETL already running ({len(existing_processes)} process(es)). Not starting new instance.")
            return True
        
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.abspath(os.path.join(script_dir, '..'))
        etl_script = os.path.join(script_dir, 'news_aggregator.py')
        
        if not os.path.exists(etl_script):
            print(f"ETL script not found: {etl_script}")
            return False
        
        try:
            # Start the process in the background
            print(f"Starting ETL process: {etl_script}")
            
            # Use subprocess.Popen to start in background, run from project root
            process = subprocess.Popen(
                [sys.executable, etl_script],
                cwd=project_root,  # Run from project root so .env is found
                # stdout=subprocess.PIPE,
                # stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            # Give it a moment to start
            time.sleep(5)
            
            # Check if it's still running
            if process.poll() is None:
                print(f"ETL process started successfully (PID: {process.pid})")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"ETL process failed to start:")
                print(f"STDOUT: {stdout.decode()}")
                print(f"STDERR: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"Error starting ETL process: {e}")
            return False
    
    def check_health(self):
        """Perform comprehensive health check"""
        issues = []
        
        # Check log activity
        log_ok, log_msg = self.check_log_activity(max_silence_minutes=45)  # More lenient for watchdog
        if not log_ok:
            issues.append(f"Log: {log_msg}")
        
        # Check process health
        proc_ok, proc_msg = self.check_process_health()
        if not proc_ok:
            issues.append(f"Process: {proc_msg}")
        
        # Check database connectivity
        db_ok, db_msg = self.check_database_connectivity()
        if not db_ok:
            issues.append(f"Database: {db_msg}")
        
        return len(issues) == 0, issues
    
    def should_restart(self):
        """Determine if restart is needed based on failure count and cooldown"""
        if self.consecutive_failures < self.max_failures:
            return False
        
        if self.last_restart:
            time_since_restart = datetime.now() - self.last_restart
            if time_since_restart.total_seconds() < self.restart_cooldown:
                return False
        
        return True
    
    def restart_etl(self):
        """Restart the ETL process"""
        self.logger.warning(f"Restarting ETL after {self.consecutive_failures} consecutive failures")
        
        try:
            # Stop existing processes
            if self.stop_etl_processes():
                self.logger.info("Successfully stopped ETL processes")
            else:
                self.logger.error("Failed to stop all ETL processes")
            
            # Wait before restart
            time.sleep(30)
            
            # Start new process
            if self.start_etl_process():
                self.logger.info("Successfully started ETL process")
                self.last_restart = datetime.now()
                self.consecutive_failures = 0
                return True
            else:
                self.logger.error("Failed to start ETL process")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during restart: {e}")
            return False
    
    def run(self):
        """Main watchdog loop"""
        self.logger.info("ETL Watchdog started")
        
        # Initial check - start ETL if not running
        proc_ok, proc_msg = self.check_process_health()
        if not proc_ok and "No ETL process found" in proc_msg:
            self.logger.info("ETL not running on startup, starting it now...")
            if self.start_etl_process():
                self.logger.info("ETL started successfully on startup")
                self.last_restart = datetime.now()
                # Wait for ETL to initialize before starting monitoring
                self.logger.info(f"Waiting {self.restart_cooldown}s for ETL to initialize...")
                time.sleep(self.restart_cooldown)
            else:
                self.logger.error("Failed to start ETL on startup")
        else:
            self.logger.info(f"ETL process status: {proc_msg}")
        
        while True:
            try:
                # Skip monitoring if we recently restarted
                if self.last_restart:
                    time_since_restart = datetime.now() - self.last_restart
                    if time_since_restart.total_seconds() < self.restart_cooldown:
                        self.logger.info(f"In restart cooldown, waiting {self.restart_cooldown - time_since_restart.total_seconds():.0f}s")
                        time.sleep(self.check_interval)
                        continue
                
                # Perform health check
                is_healthy, issues = self.check_health()
                
                if is_healthy:
                    if self.consecutive_failures > 0:
                        self.logger.info("ETL health restored")
                    self.consecutive_failures = 0
                else:
                    self.consecutive_failures += 1
                    self.logger.warning(f"Health check failed ({self.consecutive_failures}/{self.max_failures}): {'; '.join(issues)}")
                    
                    if self.should_restart():
                        if not self.restart_etl():
                            self.logger.error("Restart failed, will retry next cycle")
                
                # Wait before next check
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                self.logger.info("Watchdog stopped by user")
                break
            except Exception as e:
                self.logger.error(f"Unexpected error in watchdog: {e}")
                time.sleep(60)  # Wait a minute before retrying

    def run_health_check(self):
        """Run a single health check and display results"""
        print("ETL Health Check")
        print("=" * 50)
        
        # Check log activity
        log_ok, log_msg = self.check_log_activity()
        status = "✓" if log_ok else "✗"
        print(f"{status} Log Activity: {log_msg}")
        
        # Check process health
        proc_ok, proc_msg = self.check_process_health()
        status = "✓" if proc_ok else "✗"
        print(f"{status} Process Health: {proc_msg}")
        
        # Check database connectivity
        db_ok, db_msg = self.check_database_connectivity()
        status = "✓" if db_ok else "✗"
        print(f"{status} Database: {db_msg}")
        
        # Overall status
        overall_ok = log_ok and proc_ok and db_ok
        if not overall_ok:
            print("\n⚠️  ETL system may need attention!")
            return 1
        else:
            print("\n✅ ETL system appears healthy")
            return 0

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ETL Manager - Monitor, restart, or check ETL health')
    parser.add_argument('--check-once', action='store_true',
                       help='Run single health check and exit')
    parser.add_argument('--restart', action='store_true',
                       help='Restart ETL process and exit')
    parser.add_argument('--stop', action='store_true',
                       help='Stop ETL process and exit')
    parser.add_argument('--check-interval', type=int, default=300, 
                       help='Seconds between health checks for monitoring (default: 300)')
    parser.add_argument('--max-failures', type=int, default=3,
                       help='Max consecutive failures before restart (default: 3)')
    parser.add_argument('--restart-cooldown', type=int, default=600,
                       help='Seconds to wait after restart (default: 600)')
    
    args = parser.parse_args()
    
    manager = ETLManager(
        check_interval=args.check_interval,
        max_failures=args.max_failures,
        restart_cooldown=args.restart_cooldown
    )
    
    # Handle one-time operations
    if args.check_once:
        return manager.run_health_check()
    
    if args.stop:
        print("Stopping ETL processes...")
        success = manager.stop_etl_processes()
        return 0 if success else 1
    
    if args.restart:
        print("Restarting ETL process...")
        success = manager.restart_etl()
        return 0 if success else 1
    
    # Default: run continuous monitoring
    print("Starting ETL continuous monitoring...")
    print("Use Ctrl+C to stop, or run with --help for other options")
    try:
        manager.run()
    except KeyboardInterrupt:
        print("\nMonitoring stopped")
        return 0

if __name__ == "__main__":
    sys.exit(main())