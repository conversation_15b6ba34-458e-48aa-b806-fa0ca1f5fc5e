"""
Tests for singleton stage functionality in the distributed processing system.

This module tests the singleton stage management features including:
- Stage claiming and conflict detection
- Proper cleanup on worker failure
- Integration with the work queue system
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from backend.app.models.models import Worker, Entry
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.worker_health_manager import WorkerHealthManager
from backend.app.distributed.worker_config import WorkerConfig


@pytest.fixture
def mock_db_session():
    """Create a mock database session."""
    session = Mock(spec=Session)
    session.begin.return_value.__enter__ = Mock(return_value=session)
    session.begin.return_value.__exit__ = Mock(return_value=None)
    return session


@pytest.fixture
def work_queue_manager(mock_db_session):
    """Create a WorkQueueManager instance with mock database."""
    return WorkQueueManager(mock_db_session)


@pytest.fixture
def worker_health_manager(mock_db_session):
    """Create a WorkerHealthManager instance with mock database."""
    return WorkerHealthManager(mock_db_session)


@pytest.fixture
def mock_worker():
    """Create a mock worker for testing."""
    worker = Mock(spec=Worker)
    worker.worker_id = "test-worker-1"
    worker.status = "running"
    worker.singleton_stages = None
    return worker


class TestSingletonStageManagement:
    """Test singleton stage management functionality."""
    
    def test_is_singleton_stage_claimed_no_workers(self, work_queue_manager, mock_db_session):
        """Test checking singleton stage claim when no workers are processing it."""
        # Mock query to return no workers
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        result = work_queue_manager.is_singleton_stage_claimed(ProcessingStage.DOWNLOAD_FEEDS)
        
        assert result is False
        mock_db_session.query.assert_called_once_with(Worker)
    
    def test_is_singleton_stage_claimed_with_worker(self, work_queue_manager, mock_db_session, mock_worker):
        """Test checking singleton stage claim when a worker is processing it."""
        # Mock query to return a worker processing the stage
        mock_worker.singleton_stages = "download_feeds"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_worker
        
        result = work_queue_manager.is_singleton_stage_claimed(ProcessingStage.DOWNLOAD_FEEDS)
        
        assert result is True
    
    def test_claim_singleton_stage_success(self, work_queue_manager, mock_db_session, mock_worker):
        """Test successfully claiming a singleton stage."""
        # Mock queries
        mock_db_session.query.return_value.filter.return_value.first.side_effect = [
            None,  # No existing worker processing the stage
            mock_worker  # Return the worker for claiming
        ]
        
        result = work_queue_manager.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker-1")
        
        assert result is True
        assert mock_worker.singleton_stages == "download_feeds"
    
    def test_claim_singleton_stage_already_claimed(self, work_queue_manager, mock_db_session):
        """Test claiming a singleton stage that's already claimed by another worker."""
        # Mock existing worker processing the stage
        existing_worker = Mock(spec=Worker)
        existing_worker.worker_id = "other-worker"
        mock_db_session.query.return_value.filter.return_value.first.return_value = existing_worker
        
        result = work_queue_manager.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker-1")
        
        assert result is False
    
    def test_claim_singleton_stage_worker_not_found(self, work_queue_manager, mock_db_session):
        """Test claiming a singleton stage when the worker doesn't exist."""
        # Mock queries
        mock_db_session.query.return_value.filter.return_value.first.side_effect = [
            None,  # No existing worker processing the stage
            None   # Worker not found
        ]
        
        result = work_queue_manager.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "nonexistent-worker")
        
        assert result is False
    
    def test_claim_singleton_stage_append_to_existing(self, work_queue_manager, mock_db_session, mock_worker):
        """Test claiming a singleton stage when worker already has other singleton stages."""
        # Mock worker with existing singleton stages
        mock_worker.singleton_stages = "other_stage"
        mock_db_session.query.return_value.filter.return_value.first.side_effect = [
            None,  # No existing worker processing the stage
            mock_worker  # Return the worker for claiming
        ]
        
        result = work_queue_manager.claim_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker-1")
        
        assert result is True
        assert "other_stage" in mock_worker.singleton_stages
        assert "download_feeds" in mock_worker.singleton_stages
    
    def test_release_singleton_stage_success(self, work_queue_manager, mock_db_session, mock_worker):
        """Test successfully releasing a singleton stage."""
        # Mock worker with the stage
        mock_worker.singleton_stages = "download_feeds,other_stage"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_worker
        
        work_queue_manager.release_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker-1")
        
        assert mock_worker.singleton_stages == "other_stage"
    
    def test_release_singleton_stage_only_stage(self, work_queue_manager, mock_db_session, mock_worker):
        """Test releasing the only singleton stage."""
        # Mock worker with only the stage to release
        mock_worker.singleton_stages = "download_feeds"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_worker
        
        work_queue_manager.release_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker-1")
        
        assert mock_worker.singleton_stages is None
    
    def test_release_singleton_stage_worker_not_found(self, work_queue_manager, mock_db_session):
        """Test releasing a singleton stage when worker doesn't exist."""
        # Mock query to return no worker
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # Should not raise an exception
        work_queue_manager.release_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "nonexistent-worker")
    
    def test_release_singleton_stage_not_claimed(self, work_queue_manager, mock_db_session, mock_worker):
        """Test releasing a singleton stage that wasn't claimed by the worker."""
        # Mock worker without the stage
        mock_worker.singleton_stages = "other_stage"
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_worker
        
        work_queue_manager.release_singleton_stage(ProcessingStage.DOWNLOAD_FEEDS, "test-worker-1")
        
        # Should remain unchanged
        assert mock_worker.singleton_stages == "other_stage"


class TestWorkerHealthManagerSingletonStages:
    """Test WorkerHealthManager singleton stage handling."""
    
    def test_register_worker_clears_singleton_stages(self, worker_health_manager, mock_db_session):
        """Test that worker registration clears singleton stages."""
        # Mock existing worker
        existing_worker = Mock(spec=Worker)
        existing_worker.singleton_stages = "download_feeds"
        mock_db_session.query.return_value.filter.return_value.first.return_value = existing_worker
        
        config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.DOWNLOAD_FEEDS]
        )
        
        worker_health_manager.register_worker("test-worker", "FeedDownloadWorker", config)
        
        assert existing_worker.singleton_stages is None
    
    def test_unregister_worker_clears_singleton_stages(self, worker_health_manager, mock_db_session):
        """Test that worker unregistration clears singleton stages."""
        # Mock worker with singleton stages
        worker = Mock(spec=Worker)
        worker.singleton_stages = "download_feeds"
        mock_db_session.query.return_value.filter.return_value.first.return_value = worker
        
        worker_health_manager.unregister_worker("test-worker")
        
        assert worker.singleton_stages is None
    
    def test_recover_failed_workers_clears_singleton_stages(self, worker_health_manager, mock_db_session):
        """Test that failed worker recovery clears singleton stages."""
        # Mock stale worker detection
        with patch.object(worker_health_manager, 'detect_stale_workers') as mock_detect:
            mock_detect.return_value = [{'worker_id': 'stale-worker'}]

            # Mock the update query
            mock_query = Mock()
            mock_db_session.query.return_value = mock_query
            mock_query.filter.return_value = mock_query
            mock_query.update.return_value = 1

            result = worker_health_manager.recover_failed_workers()

            # Verify that update was called (should be called twice - once for workers, once for entries)
            assert mock_query.update.call_count == 2

            # Check the first update call (workers update)
            first_update_call_args = mock_query.update.call_args_list[0][0][0]

            # Check that singleton_stages is being set to None in the workers update
            singleton_stages_updated = False
            for key, value in first_update_call_args.items():
                if (hasattr(key, 'name') and key.name == 'singleton_stages') or key == 'singleton_stages':
                    if value is None:
                        singleton_stages_updated = True
                        break

            assert singleton_stages_updated, f"singleton_stages not properly cleared in workers update call: {first_update_call_args}"


if __name__ == "__main__":
    pytest.main([__file__])
