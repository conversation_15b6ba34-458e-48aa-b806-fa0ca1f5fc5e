import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PerformanceOptimizer {
  // Optimiert Bilder und reduziert Speicherverbrauch
  static void optimizeImageCache() {
    // Begrenzt den Speicher für den Image-Cache
    PaintingBinding.instance.imageCache.maximumSize = 100;
    PaintingBinding.instance.imageCache.maximumSizeBytes = 50 << 20; // 50 MB
  }

  // Optimiert die App für verschiedene Plattformen
  static void optimizeForPlatform(BuildContext context) {
    // Setzt die bevorzugte Orientierung
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Optimiert die Statusleiste
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Theme.of(context).brightness == Brightness.dark
            ? Brightness.light
            : Brightness.dark,
        systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
        systemNavigationBarIconBrightness:
            Theme.of(context).brightness == Brightness.dark
                ? Brightness.light
                : Brightness.dark,
      ),
    );
  }

  // Optimiert die Leistung für Web-Plattformen
  static void optimizeForWeb() {
    // Hier könnten Web-spezifische Optimierungen hinzugefügt werden
  }

  // Optimiert die Leistung für mobile Plattformen
  static void optimizeForMobile() {
    // Hier könnten mobile-spezifische Optimierungen hinzugefügt werden
  }
}
