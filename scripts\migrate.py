#!/usr/bin/env python3
"""
Convenience script for running migration CLI commands.

This script provides a simple entry point for migration operations
without needing to use the full module path.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Change to project root directory
os.chdir(project_root)

if __name__ == '__main__':
    from backend.app.distributed.migration_cli import main
    sys.exit(main())