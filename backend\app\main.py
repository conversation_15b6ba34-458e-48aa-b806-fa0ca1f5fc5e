from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware

from backend.app.core.config import settings
from backend.app.routers import news


def create_application() -> FastAPI:
    """
    Erstellt und konfiguriert die FastAPI-Anwendung.
    """
    application = FastAPI(
        title=settings.APP_NAME,
        description=settings.APP_DESCRIPTION,
        version=settings.APP_VERSION,
    )

    # Debug print database URL
    print(f"Database URL: {settings.DATABASE_URL}")

    # CORS-Middleware hinzufügen
    application.add_middleware(
        CORSMiddleware,
        allow_origins=settings.API_CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # GZip-Middleware hinzufügen
    application.add_middleware(
        GZipMiddleware, 
        minimum_size=1000,  # Komprimiere nur Antworten größer als 1000 Bytes
        compresslevel=6     # Ausgewogenes Verhältnis zwischen Geschwindigkeit und Kompression
    )

    # API-Router einbinden
    application.include_router(news.router, prefix=settings.API_PREFIX)

    @application.get("/")
    async def root():
        return {"message": "Willkommen bei der Positive News API"}

    return application


app = create_application()
