from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from backend.app.core.config import settings

# Create SQLAlchemy engine using the DATABASE_URL from settings
# Disable SQL query logging by default (can be enabled with LOG_LEVEL=DEBUG)
import logging
import os

# Completely disable SQLAlchemy echo to prevent logging format errors
echo_sql = False  # Always disabled to prevent format string errors

# Configure SQLAlchemy logging before creating engine
sqlalchemy_logger = logging.getLogger('sqlalchemy.engine')
sqlalchemy_logger.setLevel(logging.ERROR)
sqlalchemy_logger.propagate = False

engine = create_engine(
    settings.DATABASE_URL,
    echo=echo_sql,  # SQL queries always disabled
    echo_pool=False,  # Pool logging disabled
    pool_size=10,  # Connection pool size
    max_overflow=20,  # Additional connections beyond pool_size
    pool_pre_ping=True,  # Validate connections before use
    pool_recycle=3600  # Recycle connections every hour
)
        
# Create sessionmaker
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()


def get_db():
    """
    Dependency for FastAPI to provide a database connection.
    Ensures the connection is closed after use.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
