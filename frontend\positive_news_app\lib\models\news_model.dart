import 'package:intl/intl.dart';

class CategoryNews {
  final String category;
  final String categoryCode;
  final List<EntryWithSource> news; // Updated to use EntryWithSource

  CategoryNews({
    required this.category,
    required this.categoryCode,
    required this.news,
  });

  factory CategoryNews.fromJson(Map<String, dynamic> json) {
    return CategoryNews(
      category: json['category'],
      categoryCode: json['category_code'],
      news: (json['news'] as List)
          .map((newsJson) => EntryWithSource.fromJson(newsJson))
          .toList(),
    );
  }
}

class NewsResponse {
  final List<CategoryNews> categories;

  NewsResponse({
    required this.categories,
  });

  factory NewsResponse.fromJson(Map<String, dynamic> json) {
    return NewsResponse(
      categories: (json['categories'] as List)
          .map((categoryJson) => CategoryNews.fromJson(categoryJson))
          .toList(),
    );
  }
}

class SimilarNewsResponse {
  final EntryWithSource original; // Updated to use EntryWithSource
  final List<EntryWithSource> similarSources; // Updated to use EntryWithSource

  SimilarNewsResponse({
    required this.original,
    required this.similarSources,
  });

  factory SimilarNewsResponse.fromJson(Map<String, dynamic> json) {
    return SimilarNewsResponse(
      original: EntryWithSource.fromJson(json['original']),
      similarSources: (json['similar_sources'] as List)
          .map((newsJson) => EntryWithSource.fromJson(newsJson))
          .toList(),
    );
  }
}

class PaginatedNewsResponse {
  final List<EntryWithSource> items; // Updated to use EntryWithSource
  final int total;
  final bool hasMore;

  PaginatedNewsResponse({
    required this.items,
    required this.total,
    required this.hasMore,
  });

  factory PaginatedNewsResponse.fromJson(Map<String, dynamic> json) {
    return PaginatedNewsResponse(
      items: (json['items'] as List)
          .map((newsJson) => EntryWithSource.fromJson(newsJson))
          .toList(),
      total: json['total'],
      hasMore: json['has_more'],
    );
  }
}

class EntryWithSource {
  final String entryId;
  final String title;
  final String link;
  final String source;
  final String? sourceName;
  final String? description;
  final DateTime published;
  final String? fullText;
  final String? iptcNewscode;
  final String? categoryName;
  final double? llmPositive;
  final String? llmReasonList;
  final String? dupEntryId;
  final bool? descriptionAutoGenerated;
  final String? previewImg;
  final bool? hasSimilar;  // Now nullable for lazy loading
  final bool? hasImage;

  EntryWithSource({
    required this.entryId,
    required this.title,
    required this.link,
    required this.source,
    this.hasSimilar,  // Now optional for lazy loading
    this.sourceName,
    this.description,
    required this.published,
    this.fullText,
    this.iptcNewscode,
    this.categoryName,
    this.llmPositive,
    this.llmReasonList,
    this.dupEntryId,
    this.descriptionAutoGenerated,
    this.previewImg,
    this.hasImage,
  });

  factory EntryWithSource.fromJson(Map<String, dynamic> json) {
    return EntryWithSource(
      entryId: json['entry_id'],
      title: json['title'],
      link: json['link'],
      source: json['source'],
      sourceName: json['source_name'],
      description: json['description'],
      published: DateTime.parse(json['published']),
      fullText: json['full_text'],
      iptcNewscode: json['iptc_newscode'],
      categoryName: json['category_name'],
      llmPositive: json['llm_positive']?.toDouble(),
      llmReasonList: json['llm_reason_list'],
      dupEntryId: json['dup_entry_id'],
      descriptionAutoGenerated: json['description_auto_generated'],
      previewImg: json['preview_img'],
      hasSimilar: json['has_similar'] as bool?, // Can be null for lazy loading
      hasImage: json['has_image'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'entry_id': entryId,
      'title': title,
      'link': link,
      'source': source,
      'source_name': sourceName,
      'description': description,
      'published': published.toIso8601String(),
      'full_text': fullText,
      'iptc_newscode': iptcNewscode,
      'category_name': categoryName,
      'llm_positive': llmPositive,
      'llm_reason_list': llmReasonList,
      'dup_entry_id': dupEntryId,
      'description_auto_generated': descriptionAutoGenerated,
      'preview_img': previewImg,
      'has_similar': hasSimilar,
      'has_image': hasImage,
    };
  }

  // Gibt die Uhrzeit der Nachricht zurück, die auf dem Bildschirm angezeigt wird  
  String get formattedDate {
    return DateFormat('dd.MM.yyyy HH:mm').format(published);
  }
  
  // Removed imageUrl getter

  String? get formattedDescription {
    if (description == null) return null;
    return descriptionAutoGenerated == true ? "⭐ $description" : description;
  }
}
