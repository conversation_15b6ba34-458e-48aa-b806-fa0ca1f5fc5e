#!/usr/bin/env python3
"""
Performance testing script for distributed ETL dashboard.

This script tests the performance of various dashboard endpoints and operations
to validate the optimizations made to the distributed ETL system.
"""

import time
import statistics
import requests
import json
from typing import List, Dict, Any
import argparse
import sys
from datetime import datetime


class PerformanceTest:
    """Performance testing utility for distributed ETL dashboard."""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8081"):
        self.base_url = base_url
        self.results = {}
    
    def time_request(self, url: str, method: str = "GET", **kwargs) -> float:
        """Time a single HTTP request and return duration in milliseconds."""
        start_time = time.perf_counter()
        try:
            if method.upper() == "GET":
                response = requests.get(url, **kwargs)
            elif method.upper() == "POST":
                response = requests.post(url, **kwargs)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response.raise_for_status()
            end_time = time.perf_counter()
            return (end_time - start_time) * 1000  # Convert to milliseconds
        except Exception as e:
            print(f"Request failed: {e}")
            return -1
    
    def run_multiple_tests(self, url: str, iterations: int = 10, **kwargs) -> Dict[str, float]:
        """Run multiple tests and return statistics."""
        times = []
        for i in range(iterations):
            duration = self.time_request(url, **kwargs)
            if duration > 0:
                times.append(duration)
            time.sleep(0.1)  # Small delay between requests
        
        if not times:
            return {"error": "All requests failed"}
        
        return {
            "min": min(times),
            "max": max(times),
            "mean": statistics.mean(times),
            "median": statistics.median(times),
            "stdev": statistics.stdev(times) if len(times) > 1 else 0,
            "count": len(times)
        }
    
    def test_stages_progress(self, iterations: int = 10) -> Dict[str, Any]:
        """Test the /api/stages/progress endpoint."""
        print("Testing /api/stages/progress endpoint...")
        url = f"{self.base_url}/api/stages/progress"
        return self.run_multiple_tests(url, iterations)
    
    def test_workers_status(self, iterations: int = 10) -> Dict[str, Any]:
        """Test the /api/workers/status endpoint."""
        print("Testing /api/workers/status endpoint...")
        url = f"{self.base_url}/api/workers/status"
        return self.run_multiple_tests(url, iterations)
    
    def test_dashboard_page(self, iterations: int = 5) -> Dict[str, Any]:
        """Test the main dashboard page load time."""
        print("Testing dashboard page load...")
        url = f"{self.base_url}/"
        return self.run_multiple_tests(url, iterations)
    
    def test_api_health(self, iterations: int = 5) -> Dict[str, Any]:
        """Test the health check endpoint."""
        print("Testing health check...")
        url = f"{self.base_url}/health"
        return self.run_multiple_tests(url, iterations)
    
    def run_all_tests(self, iterations: int = 10) -> Dict[str, Any]:
        """Run all performance tests."""
        print(f"Running performance tests with {iterations} iterations each...")
        print(f"Target: {self.base_url}")
        print("-" * 60)
        
        results = {}
        
        # Test health check first
        results["health"] = self.test_api_health(5)
        
        # Test main API endpoints
        results["stages_progress"] = self.test_stages_progress(iterations)
        results["workers_status"] = self.test_workers_status(iterations)
        
        # Test dashboard page
        results["dashboard_page"] = self.test_dashboard_page(5)
        
        self.results = results
        return results
    
    def print_results(self):
        """Print formatted test results."""
        if not self.results:
            print("No results to display. Run tests first.")
            return
        
        print("\n" + "=" * 60)
        print("PERFORMANCE TEST RESULTS")
        print("=" * 60)
        
        for endpoint, stats in self.results.items():
            print(f"\n{endpoint.upper().replace('_', ' ')}:")
            print("-" * 40)
            
            if "error" in stats:
                print(f"  ERROR: {stats['error']}")
                continue
            
            print(f"  Requests: {stats['count']}")
            print(f"  Mean:     {stats['mean']:.2f} ms")
            print(f"  Median:   {stats['median']:.2f} ms")
            print(f"  Min:      {stats['min']:.2f} ms")
            print(f"  Max:      {stats['max']:.2f} ms")
            print(f"  Std Dev:  {stats['stdev']:.2f} ms")
            
            # Performance assessment
            mean_time = stats['mean']
            if mean_time < 100:
                assessment = "EXCELLENT"
            elif mean_time < 250:
                assessment = "GOOD"
            elif mean_time < 500:
                assessment = "ACCEPTABLE"
            else:
                assessment = "NEEDS IMPROVEMENT"
            
            print(f"  Assessment: {assessment}")
    
    def save_results(self, filename: str):
        """Save results to JSON file."""
        if not self.results:
            print("No results to save.")
            return
        
        output = {
            "timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "results": self.results
        }
        
        with open(filename, 'w') as f:
            json.dump(output, f, indent=2)
        
        print(f"Results saved to {filename}")


def main():
    parser = argparse.ArgumentParser(description="Performance test for distributed ETL dashboard")
    parser.add_argument("--url", default="http://127.0.0.1:8081", 
                       help="Base URL for the dashboard (default: http://127.0.0.1:8081)")
    parser.add_argument("--iterations", type=int, default=10,
                       help="Number of iterations per test (default: 10)")
    parser.add_argument("--output", help="Save results to JSON file")
    
    args = parser.parse_args()
    
    # Create and run tests
    tester = PerformanceTest(args.url)
    
    try:
        tester.run_all_tests(args.iterations)
        tester.print_results()
        
        if args.output:
            tester.save_results(args.output)
            
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
