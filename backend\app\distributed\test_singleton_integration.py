"""
Integration test for singleton stage functionality.

This test verifies that the singleton stage system works end-to-end with
the distributed worker system.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from backend.app.distributed.processing_stage import ProcessingStage, stage_requires_singleton, stage_requires_entry_retrieval
from backend.app.distributed.distributed_worker import Distri<PERSON>Worker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.models.models import Entry


class MockFeedDownloadWorker(DistributedWorker):
    """Mock implementation of a feed download worker for testing."""
    
    def process_batch(self, entries, stage):
        """Mock process_batch that simulates feed download."""
        if stage == ProcessingStage.DOWNLOAD_FEEDS:
            # Simulate successful feed download
            return {"feed_download": True}
        return {}


def test_stage_configuration():
    """Test that download_feeds is properly configured as a singleton stage."""
    assert stage_requires_singleton(ProcessingStage.DOWNLOAD_FEEDS)
    assert not stage_requires_entry_retrieval(ProcessingStage.DOWNLOAD_FEEDS)
    
    # Test that other stages are not singleton
    assert not stage_requires_singleton(ProcessingStage.DOWNLOAD_FULL_TEXT)
    assert stage_requires_entry_retrieval(ProcessingStage.DOWNLOAD_FULL_TEXT)


@patch('backend.app.distributed.distributed_worker.WorkQueueManager')
def test_singleton_stage_processing(mock_work_queue_class):
    """Test that singleton stages are processed correctly by the worker."""
    # Setup mock work queue manager
    mock_work_queue = Mock(spec=WorkQueueManager)
    mock_work_queue_class.return_value = mock_work_queue
    
    # Mock cooldown check (not in cooldown)
    mock_work_queue.is_stage_in_cooldown.return_value = False

    # Mock singleton stage claiming
    mock_work_queue.is_singleton_stage_claimed.return_value = False
    mock_work_queue.claim_singleton_stage.return_value = True
    
    # Create worker config for download_feeds
    config = WorkerConfig(
        worker_id="test-feed-worker",
        stages=[ProcessingStage.DOWNLOAD_FEEDS],
        batch_size=1
    )
    
    # Create mock database session factory
    mock_db_session_factory = Mock()
    mock_session = Mock()
    mock_db_session_factory.return_value = mock_session
    
    # Create worker
    worker = MockFeedDownloadWorker(config, mock_db_session_factory)
    
    # Test processing a singleton stage
    result = worker._process_singleton_stage(mock_work_queue, ProcessingStage.DOWNLOAD_FEEDS)
    
    # Verify the singleton stage was claimed and processed
    mock_work_queue.is_singleton_stage_claimed.assert_called_once_with(ProcessingStage.DOWNLOAD_FEEDS)
    mock_work_queue.claim_singleton_stage.assert_called_once_with(ProcessingStage.DOWNLOAD_FEEDS, "test-feed-worker")
    mock_work_queue.release_singleton_stage.assert_called_once_with(ProcessingStage.DOWNLOAD_FEEDS, "test-feed-worker")
    
    assert result is True


@patch('backend.app.distributed.distributed_worker.WorkQueueManager')
def test_singleton_stage_already_claimed(mock_work_queue_class):
    """Test that worker skips processing when singleton stage is already claimed."""
    # Setup mock work queue manager
    mock_work_queue = Mock(spec=WorkQueueManager)
    mock_work_queue_class.return_value = mock_work_queue
    
    # Mock cooldown check (not in cooldown)
    mock_work_queue.is_stage_in_cooldown.return_value = False

    # Mock singleton stage already claimed
    mock_work_queue.is_singleton_stage_claimed.return_value = True
    
    # Create worker config
    config = WorkerConfig(
        worker_id="test-feed-worker",
        stages=[ProcessingStage.DOWNLOAD_FEEDS],
        batch_size=1
    )
    
    # Create mock database session factory
    mock_db_session_factory = Mock()
    mock_session = Mock()
    mock_db_session_factory.return_value = mock_session
    
    # Create worker
    worker = MockFeedDownloadWorker(config, mock_db_session_factory)
    
    # Test processing when singleton stage is already claimed
    result = worker._process_singleton_stage(mock_work_queue, ProcessingStage.DOWNLOAD_FEEDS)
    
    # Verify the worker detected the stage was already claimed and skipped
    mock_work_queue.is_singleton_stage_claimed.assert_called_once_with(ProcessingStage.DOWNLOAD_FEEDS)
    mock_work_queue.claim_singleton_stage.assert_not_called()
    mock_work_queue.release_singleton_stage.assert_not_called()
    
    assert result is False


@patch('backend.app.distributed.distributed_worker.WorkQueueManager')
def test_singleton_stage_claim_failure(mock_work_queue_class):
    """Test that worker handles singleton stage claim failure gracefully."""
    # Setup mock work queue manager
    mock_work_queue = Mock(spec=WorkQueueManager)
    mock_work_queue_class.return_value = mock_work_queue
    
    # Mock cooldown check (not in cooldown)
    mock_work_queue.is_stage_in_cooldown.return_value = False

    # Mock singleton stage claiming failure
    mock_work_queue.is_singleton_stage_claimed.return_value = False
    mock_work_queue.claim_singleton_stage.return_value = False
    
    # Create worker config
    config = WorkerConfig(
        worker_id="test-feed-worker",
        stages=[ProcessingStage.DOWNLOAD_FEEDS],
        batch_size=1
    )
    
    # Create mock database session factory
    mock_db_session_factory = Mock()
    mock_session = Mock()
    mock_db_session_factory.return_value = mock_session
    
    # Create worker
    worker = MockFeedDownloadWorker(config, mock_db_session_factory)
    
    # Test processing when singleton stage claim fails
    result = worker._process_singleton_stage(mock_work_queue, ProcessingStage.DOWNLOAD_FEEDS)
    
    # Verify the worker attempted to claim but failed
    mock_work_queue.is_singleton_stage_claimed.assert_called_once_with(ProcessingStage.DOWNLOAD_FEEDS)
    mock_work_queue.claim_singleton_stage.assert_called_once_with(ProcessingStage.DOWNLOAD_FEEDS, "test-feed-worker")
    mock_work_queue.release_singleton_stage.assert_not_called()
    
    assert result is False


@patch('backend.app.distributed.distributed_worker.WorkQueueManager')
def test_regular_stage_processing_unchanged(mock_work_queue_class):
    """Test that regular (non-singleton) stages still work as before."""
    # Setup mock work queue manager
    mock_work_queue = Mock(spec=WorkQueueManager)
    mock_work_queue_class.return_value = mock_work_queue
    
    # Mock regular stage processing
    mock_work_queue.claim_batch.return_value = ["entry1", "entry2"]
    
    # Create worker config for a regular stage
    config = WorkerConfig(
        worker_id="test-regular-worker",
        stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
        batch_size=2
    )
    
    # Create mock database session factory
    mock_db_session_factory = Mock()
    mock_session = Mock()
    mock_db_session_factory.return_value = mock_session
    
    # Create worker
    worker = MockFeedDownloadWorker(config, mock_db_session_factory)
    
    # Mock the batch processing methods
    with patch.object(worker, '_process_batch_with_error_handling') as mock_process:
        mock_process.return_value = (["entry1", "entry2"], [])
        
        with patch.object(worker, '_release_current_batch'):
            # Test processing a regular stage
            result = worker._process_stage_batch(ProcessingStage.DOWNLOAD_FULL_TEXT)
    
    # Verify regular batch claiming was used
    mock_work_queue.claim_batch.assert_called_once()
    mock_work_queue.is_singleton_stage_claimed.assert_not_called()
    mock_work_queue.claim_singleton_stage.assert_not_called()
    
    assert result is True


if __name__ == "__main__":
    pytest.main([__file__])
