# Distributed ETL Processing Module

This module provides the core data structures and utilities for distributed ETL processing in the news aggregation system.

## Overview

The distributed processing system transforms the monolithic ETL approach into a scalable, distributed architecture where work is divided into smaller batches that can be processed independently by multiple workers.

## Components

### 1. Processing Stage Management (`processing_stage.py`)

**ProcessingStage Enum**: Defines all ETL processing stages:
- `DOWNLOAD_FULL_TEXT` - Download full article text
- `TRANSLATE_TEXT` - Translate text to target language  
- `COMPUTE_EMBEDDINGS` - Generate text embeddings
- `SENTIMENT_ANALYSIS` - Analyze sentiment
- `LLM_ANALYSIS` - Large language model analysis
- `IPTC_CLASSIFICATION` - IPTC news categorization
- `DUPLICATE_CHECK` - Check for duplicate entries
- `GENERATE_DESCRIPTIONS` - Generate article descriptions
- `GENERATE_IMAGE_PROMPTS` - Generate image prompts
- `GENERATE_PREVIEW_IMAGES` - Generate preview images

**ProcessingStatus Class**: Manages processing status for all stages of an entry:
- JSON serialization for database storage
- Stage-specific status tracking (pending, in_progress, completed, failed)
- Retry count management
- Worker ID tracking
- Completion timestamps

**Utility Functions**:
- `create_empty_processing_status()` - Create new empty status
- `get_next_pending_stage()` - Find next stage to process
- `can_process_stage()` - Check if stage can be processed (considering retries)
- `get_processable_stages()` - Get all processable stages

### 2. Worker Configuration (`worker_config.py`)

**WorkerConfig Class**: Comprehensive worker configuration with validation:
- Worker identification and stage assignment
- Batch processing parameters (size, timeouts, retries)
- Database connection settings
- Performance tuning options
- Monitoring and health check configuration
- Stage-specific configuration support

**WorkerConfigLoader Class**: Load configuration from multiple sources:
- Environment variables
- JSON configuration files
- Dictionary with environment overrides
- Automatic worker ID generation

**Configuration Templates**:
- `create_download_worker_config()` - Optimized for network operations
- `create_analysis_worker_config()` - Optimized for CPU-intensive tasks
- `create_general_worker_config()` - General-purpose worker

## Key Features

### Robust Validation
- Comprehensive parameter validation with clear error messages
- Type checking and range validation
- Stage enum validation for type safety

### Flexible Configuration
- Environment variable overrides
- Stage-specific configuration support
- Multiple configuration loading methods
- Predefined templates for common use cases

### JSON Serialization
- Database-compatible JSON serialization
- Forward compatibility with unknown stages
- Error handling for invalid JSON

### Comprehensive Testing
- 66 unit and integration tests
- 100% test coverage of core functionality
- Mock-based testing for external dependencies
- Integration tests verifying component interaction

## Usage Examples

### Basic Worker Configuration
```python
from backend.app.distributed import WorkerConfig, ProcessingStage

# Create a download worker
config = WorkerConfig(
    worker_id="download-worker-001",
    stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
    batch_size=10,
    max_retries=5
)
```

### Processing Status Management
```python
from backend.app.distributed import ProcessingStatus, ProcessingStage

# Create and manage processing status
status = ProcessingStatus()
status.mark_stage_in_progress(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")
status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "worker-001")

# Serialize for database storage
json_data = status.to_json()
```

### Configuration from Environment
```python
from backend.app.distributed import WorkerConfigLoader

# Load from environment variables
config = WorkerConfigLoader.from_environment(
    worker_id="env-worker",
    stages=["download_full_text", "sentiment_analysis"]
)
```

## Database Integration

The module integrates with the existing Entry model which already includes the necessary columns:
- `processing_status` (CLOB) - JSON serialized ProcessingStatus
- `claimed_by` (VARCHAR) - Worker ID that claimed the entry
- `claimed_at` (TIMESTAMP) - When entry was claimed
- `retry_count` (INTEGER) - Number of processing retries
- `last_error` (CLOB) - Last error message

## Testing

Run all tests:
```bash
python -m pytest backend/app/distributed/ -v
```

The test suite includes:
- 30 tests for processing stage management
- 31 tests for worker configuration
- 5 integration tests
- All tests pass with comprehensive coverage

## Requirements Satisfied

This implementation satisfies the following requirements from the specification:

**Requirement 2.1**: Workers automatically discover and claim work chunks from the database
**Requirement 4.1**: System supports different processing stages independently  
**Requirement 4.2**: Workers can specialize in specific ETL operations
**Requirements 3.1, 3.2, 3.3**: Configurable batch sizes and processing parameters per worker

The data structures provide the foundation for the distributed work queue management and worker coordination that will be implemented in subsequent tasks.