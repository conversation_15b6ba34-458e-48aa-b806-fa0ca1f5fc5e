"""
Simple unit tests for NewsAggregator functionality
"""

import unittest
from unittest.mock import Mock, patch, mock_open
import hashlib
import os
import sys

# Add paths for imports
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
etl_path = os.path.dirname(__file__)
sys.path.append(etl_path)


class TestNewsAggregatorBasics(unittest.TestCase):
    """Basic tests for NewsAggregator functionality"""

    def test_entry_id_generation(self):
        """Test entry ID generation logic"""
        # Test the ID generation logic directly
        title = "Test Article"
        link = "https://example.com/test"
        id_parts = [title, link]
        expected_id = hashlib.sha256('-'.join(id_parts).encode()).hexdigest()
        
        # This should be a 64-character hex string
        self.assertEqual(len(expected_id), 64)
        self.assertTrue(all(c in '0123456789abcdef' for c in expected_id))

    def test_html_cleaning(self):
        """Test HTML content cleaning logic"""
        from bs4 import BeautifulSoup
        import re
        
        def clean_html_content(html_content):
            """Replicate the HTML cleaning logic"""
            if not html_content:
                return ""
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Replace tags with newlines
            for tag in soup.find_all(['br', 'p', 'div']):
                tag.replace_with('\n' + tag.get_text() + '\n')
            
            text = soup.get_text()
            text = re.sub(r'\n\s*\n', '\n\n', text)
            text = re.sub(r' +', ' ', text)
            text = text.strip()
            
            return text

        # Test with HTML content
        html_content = "<p>This is a test</p><br><div>Another paragraph</div>"
        cleaned = clean_html_content(html_content)
        
        self.assertNotIn('<', cleaned)
        self.assertNotIn('>', cleaned)
        self.assertIn('This is a test', cleaned)
        self.assertIn('Another paragraph', cleaned)

        # Test with empty content
        self.assertEqual(clean_html_content(""), "")
        self.assertEqual(clean_html_content(None), "")

    @patch('builtins.open', new_callable=mock_open, read_data="""
rss_sources:
  - url: "https://example.com/rss"
    entry_id_from: "title,link"
  - url: "https://test.com/feed"
    entry_id_from: "link"
""")
    @patch('os.path.isabs')
    @patch('os.path.join')
    def test_yaml_loading(self, mock_join, mock_isabs, mock_file):
        """Test YAML configuration loading"""
        import yaml
        
        mock_isabs.return_value = False
        mock_join.return_value = "etl/test_sources.yaml"
        
        # Simulate loading RSS sources
        with open("test_file.yaml", 'r') as file:
            config = yaml.safe_load(file)
            sources = config['rss_sources']
        
        self.assertEqual(len(sources), 2)
        self.assertEqual(sources[0]['url'], "https://example.com/rss")
        self.assertEqual(sources[1]['url'], "https://test.com/feed")

    def test_dataframe_operations(self):
        """Test pandas DataFrame operations used in the aggregator"""
        import pandas as pd
        
        # Test DataFrame creation and duplicate removal
        entries = [
            {'entry_id': 'test1', 'title': 'Article 1', 'link': 'https://example.com/1'},
            {'entry_id': 'test2', 'title': 'Article 2', 'link': 'https://example.com/2'},
            {'entry_id': 'test1', 'title': 'Article 1', 'link': 'https://example.com/1'},  # Duplicate
        ]
        
        df = pd.DataFrame(entries).drop_duplicates(subset=['entry_id'])
        
        self.assertEqual(len(df), 2)  # Should remove duplicate
        self.assertIn('test1', df['entry_id'].values)
        self.assertIn('test2', df['entry_id'].values)

    def test_datetime_operations(self):
        """Test datetime operations used in the aggregator"""
        from datetime import datetime, timezone, timedelta
        
        # Test timezone-aware datetime creation
        now = datetime.now(timezone.utc)
        one_day_ago = now - timedelta(days=1)
        
        self.assertIsNotNone(now.tzinfo)
        self.assertIsNotNone(one_day_ago.tzinfo)
        self.assertTrue(now > one_day_ago)

    def test_text_processing(self):
        """Test text processing operations"""
        # Test text truncation (used in LLM processing)
        long_text = "This is a very long text " * 100
        max_length = 500
        truncated = long_text[:max_length]
        
        self.assertLessEqual(len(truncated), max_length)
        
        # Test text encoding/decoding
        text = "Test text with special characters: äöü"
        encoded = text.encode('utf-8')
        decoded = encoded.decode('utf-8')
        
        self.assertEqual(text, decoded)


class TestMockingPatterns(unittest.TestCase):
    """Test common mocking patterns used in the main test suite"""

    def test_mock_feedparser(self):
        """Test mocking feedparser responses"""
        mock_feed = Mock()
        mock_feed.bozo = False
        
        mock_entry = Mock()
        mock_entry.title = "Test Article"
        mock_entry.link = "https://example.com/article"
        mock_entry.description = "<p>Test description</p>"
        mock_entry.published_parsed = (2024, 1, 1, 12, 0, 0, 0, 1, -1)
        
        mock_feed.entries = [mock_entry]
        
        # Verify mock structure
        self.assertFalse(mock_feed.bozo)
        self.assertEqual(len(mock_feed.entries), 1)
        self.assertEqual(mock_feed.entries[0].title, "Test Article")

    def test_mock_database_session(self):
        """Test mocking database session operations"""
        mock_session = Mock()
        mock_query = Mock()
        mock_session.query.return_value = mock_query
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = []
        
        # Simulate query chain
        result = mock_session.query().filter().order_by().all()
        
        self.assertEqual(result, [])
        mock_session.query.assert_called_once()

    def test_mock_context_manager(self):
        """Test mocking context managers"""
        mock_context = Mock()
        mock_context.__enter__ = Mock(return_value=mock_context)
        mock_context.__exit__ = Mock(return_value=None)
        
        with mock_context as ctx:
            ctx.some_method()
        
        mock_context.__enter__.assert_called_once()
        mock_context.__exit__.assert_called_once()
        mock_context.some_method.assert_called_once()


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)