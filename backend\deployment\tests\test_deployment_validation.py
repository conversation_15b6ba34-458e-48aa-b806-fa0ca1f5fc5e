"""
Deployment validation tests for distributed ETL workers.
Tests Docker and Kubernetes configurations for correctness.
"""

import os
import json
import yaml
import pytest
import tempfile
import subprocess
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import patch, MagicMock

# Import the worker configuration for validation
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))
from app.distributed.worker_config import WorkerConfig, WorkerConfigLoader
from app.distributed.processing_stage import ProcessingStage


class TestDockerConfiguration:
    """Test Docker configuration files."""
    
    def test_dockerfile_exists(self):
        """Test that the distributed worker Dockerfile exists."""
        dockerfile_path = Path(__file__).parent.parent / "docker" / "Dockerfile.distributed-worker"
        assert dockerfile_path.exists(), "Distributed worker Dockerfile not found"
    
    def test_dockerfile_syntax(self):
        """Test that the Dockerfile has valid syntax."""
        dockerfile_path = Path(__file__).parent.parent / "docker" / "Dockerfile.distributed-worker"
        
        with open(dockerfile_path, 'r') as f:
            content = f.read()
        
        # Check for required instructions
        assert "FROM python:3.13-slim" in content
        assert "COPY backend/requirements.txt" in content
        assert "RUN pip install" in content
        assert "COPY backend/app backend/app" in content
        assert "HEALTHCHECK" in content
        assert "CMD" in content
    
    def test_docker_compose_syntax(self):
        """Test that docker-compose file has valid YAML syntax."""
        compose_path = Path(__file__).parent.parent / "docker" / "docker-compose.distributed.yml"
        
        with open(compose_path, 'r') as f:
            compose_config = yaml.safe_load(f)
        
        # Validate structure
        assert "version" in compose_config
        assert "services" in compose_config
        assert "networks" in compose_config
        
        # Check required services
        services = compose_config["services"]
        required_services = [
            "backend", "worker-manager", "feed-worker-1", "fulltext-worker-1",
            "analysis-worker-1", "ml-worker-1", "generation-worker-1",
            "utility-worker-1", "cleanup-service", "web"
        ]
        
        for service in required_services:
            assert service in services, f"Required service {service} not found"
    
    def test_worker_environment_variables(self):
        """Test that worker services have required environment variables."""
        compose_path = Path(__file__).parent.parent / "docker" / "docker-compose.distributed.yml"
        
        with open(compose_path, 'r') as f:
            compose_config = yaml.safe_load(f)
        
        worker_services = [
            "feed-worker-1", "fulltext-worker-1", "analysis-worker-1",
            "ml-worker-1", "generation-worker-1", "utility-worker-1"
        ]
        
        for service_name in worker_services:
            service = compose_config["services"][service_name]
            env_vars = service.get("environment", {})
            
            # Check required environment variables
            required_vars = ["WORKER_ID", "WORKER_STAGES", "WORKER_BATCH_SIZE"]
            for var in required_vars:
                assert var in env_vars, f"Required env var {var} not found in {service_name}"


class TestKubernetesConfiguration:
    """Test Kubernetes configuration files."""
    
    def test_kubernetes_manifests_exist(self):
        """Test that all required Kubernetes manifests exist."""
        k8s_path = Path(__file__).parent.parent / "k8s"
        
        required_files = [
            "namespace.yaml",
            "configmap.yaml",
            "secrets.yaml",
            "worker-manager-deployment.yaml",
            "feed-workers-deployment.yaml",
            "fulltext-workers-deployment.yaml",
            "analysis-workers-deployment.yaml",
            "ml-workers-deployment.yaml",
            "generation-workers-deployment.yaml",
            "utility-workers-deployment.yaml",
            "cleanup-service-deployment.yaml"
        ]
        
        for file_name in required_files:
            file_path = k8s_path / file_name
            assert file_path.exists(), f"Required Kubernetes manifest {file_name} not found"
    
    def test_kubernetes_yaml_syntax(self):
        """Test that all Kubernetes YAML files have valid syntax."""
        k8s_path = Path(__file__).parent.parent / "k8s"
        
        for yaml_file in k8s_path.glob("*.yaml"):
            with open(yaml_file, 'r') as f:
                try:
                    yaml.safe_load_all(f)
                except yaml.YAMLError as e:
                    pytest.fail(f"Invalid YAML syntax in {yaml_file}: {e}")
    
    def test_deployment_resource_limits(self):
        """Test that deployments have appropriate resource limits."""
        k8s_path = Path(__file__).parent.parent / "k8s"
        
        deployment_files = [
            "worker-manager-deployment.yaml",
            "feed-workers-deployment.yaml",
            "fulltext-workers-deployment.yaml",
            "analysis-workers-deployment.yaml",
            "ml-workers-deployment.yaml",
            "generation-workers-deployment.yaml",
            "utility-workers-deployment.yaml"
        ]
        
        for deployment_file in deployment_files:
            with open(k8s_path / deployment_file, 'r') as f:
                docs = list(yaml.safe_load_all(f))
            
            for doc in docs:
                if doc.get("kind") == "Deployment":
                    containers = doc["spec"]["template"]["spec"]["containers"]
                    for container in containers:
                        resources = container.get("resources", {})
                        
                        # Check that resources are defined
                        assert "requests" in resources, f"No resource requests in {deployment_file}"
                        assert "limits" in resources, f"No resource limits in {deployment_file}"
                        
                        # Check that memory and CPU are specified
                        requests = resources["requests"]
                        limits = resources["limits"]
                        
                        assert "memory" in requests and "cpu" in requests
                        assert "memory" in limits and "cpu" in limits
    
    def test_health_checks_configured(self):
        """Test that deployments have health checks configured."""
        k8s_path = Path(__file__).parent.parent / "k8s"
        
        deployment_files = [
            "worker-manager-deployment.yaml",
            "feed-workers-deployment.yaml",
            "fulltext-workers-deployment.yaml",
            "analysis-workers-deployment.yaml",
            "ml-workers-deployment.yaml",
            "generation-workers-deployment.yaml",
            "utility-workers-deployment.yaml"
        ]
        
        for deployment_file in deployment_files:
            with open(k8s_path / deployment_file, 'r') as f:
                docs = list(yaml.safe_load_all(f))
            
            for doc in docs:
                if doc.get("kind") == "Deployment":
                    containers = doc["spec"]["template"]["spec"]["containers"]
                    for container in containers:
                        # Check for health checks
                        assert "livenessProbe" in container, f"No liveness probe in {deployment_file}"
                        assert "readinessProbe" in container, f"No readiness probe in {deployment_file}"
                        
                        # Verify health check endpoints
                        liveness = container["livenessProbe"]
                        readiness = container["readinessProbe"]
                        
                        assert "httpGet" in liveness
                        assert "httpGet" in readiness
                        assert liveness["httpGet"]["path"] == "/health"
                        assert readiness["httpGet"]["path"] == "/ready"


class TestEnvironmentConfiguration:
    """Test environment-specific configuration files."""
    
    def test_environment_files_exist(self):
        """Test that environment configuration files exist."""
        config_path = Path(__file__).parent.parent / "config"
        
        required_files = ["development.env", "staging.env", "production.env"]
        
        for file_name in required_files:
            file_path = config_path / file_name
            assert file_path.exists(), f"Environment config {file_name} not found"
    
    def test_environment_variables_complete(self):
        """Test that environment files have all required variables."""
        config_path = Path(__file__).parent.parent / "config"
        
        required_vars = [
            "WORKER_LOG_LEVEL",
            "WORKER_METRICS_ENABLED",
            "WORKER_BATCH_SIZE",
            "WORKER_CLAIM_TIMEOUT_MINUTES",
            "WORKER_MAX_RETRIES",
            "WORKER_HEARTBEAT_INTERVAL_SECONDS",
            "WORKER_PROCESSING_DELAY_SECONDS",
            "WORKER_CONNECTION_POOL_SIZE",
            "WORKER_CONNECTION_TIMEOUT_SECONDS",
            "WORKER_HEALTH_CHECK_PORT"
        ]
        
        for env_file in ["development.env", "staging.env", "production.env"]:
            with open(config_path / env_file, 'r') as f:
                content = f.read()
            
            for var in required_vars:
                assert f"{var}=" in content, f"Required variable {var} not found in {env_file}"
    
    def test_environment_specific_values(self):
        """Test that environment files have appropriate values for their environment."""
        config_path = Path(__file__).parent.parent / "config"
        
        # Development should have smaller batch sizes and more debugging
        with open(config_path / "development.env", 'r') as f:
            dev_content = f.read()
        
        assert "WORKER_LOG_LEVEL=DEBUG" in dev_content
        assert "WORKER_BATCH_SIZE=5" in dev_content
        
        # Production should have optimized values
        with open(config_path / "production.env", 'r') as f:
            prod_content = f.read()
        
        assert "WORKER_LOG_LEVEL=INFO" in prod_content
        assert "WORKER_BATCH_SIZE=25" in prod_content


class TestWorkerConfigurationValidation:
    """Test worker configuration validation."""
    
    def test_worker_config_from_environment(self):
        """Test loading worker configuration from environment variables."""
        env_vars = {
            "WORKER_ID": "test-worker-001",
            "WORKER_STAGES": "download_feeds,download_full_text",
            "WORKER_BATCH_SIZE": "10",
            "WORKER_CLAIM_TIMEOUT_MINUTES": "15",
            "WORKER_MAX_RETRIES": "3",
            "DATABASE_URL": "sqlite:///test.db"
        }
        
        with patch.dict(os.environ, env_vars):
            config = WorkerConfigLoader.from_environment()
            
            assert config.worker_id == "test-worker-001"
            assert len(config.stages) == 2
            assert ProcessingStage.DOWNLOAD_FEEDS in config.stages
            assert ProcessingStage.DOWNLOAD_FULL_TEXT in config.stages
            assert config.batch_size == 10
            assert config.claim_timeout_minutes == 15
            assert config.max_retries == 3
            assert config.database_url == "sqlite:///test.db"
    
    def test_worker_config_validation(self):
        """Test worker configuration validation."""
        # Test valid configuration
        valid_config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.DOWNLOAD_FEEDS],
            batch_size=25,
            claim_timeout_minutes=30
        )
        
        # Should not raise any exception
        valid_config._validate_config()
        
        # Test invalid configurations
        with pytest.raises(ValueError, match="worker_id must be a non-empty string"):
            WorkerConfig(
                worker_id="",
                stages=[ProcessingStage.DOWNLOAD_FEEDS]
            )
        
        with pytest.raises(ValueError, match="stages list cannot be empty"):
            WorkerConfig(
                worker_id="test-worker",
                stages=[]
            )
        
        with pytest.raises(ValueError, match="batch_size must be a positive integer"):
            WorkerConfig(
                worker_id="test-worker",
                stages=[ProcessingStage.DOWNLOAD_FEEDS],
                batch_size=0
            )
    
    def test_stage_specific_configurations(self):
        """Test stage-specific configuration loading."""
        config_path = Path(__file__).parent.parent / "config"
        
        # Test that stage-specific configurations are present
        for env_file in ["development.env", "staging.env", "production.env"]:
            with open(config_path / env_file, 'r') as f:
                content = f.read()
            
            # Check for stage-specific batch sizes
            stage_configs = [
                "FEED_WORKER_BATCH_SIZE",
                "FULLTEXT_WORKER_BATCH_SIZE",
                "ANALYSIS_WORKER_BATCH_SIZE",
                "ML_WORKER_BATCH_SIZE",
                "GENERATION_WORKER_BATCH_SIZE",
                "UTILITY_WORKER_BATCH_SIZE"
            ]
            
            for stage_config in stage_configs:
                assert f"{stage_config}=" in content, f"Stage config {stage_config} not found in {env_file}"


class TestDeploymentIntegration:
    """Test deployment integration scenarios."""
    
    @pytest.mark.integration
    def test_docker_build_validation(self):
        """Test that Docker image can be built successfully."""
        dockerfile_path = Path(__file__).parent.parent / "docker" / "Dockerfile.distributed-worker"
        
        # This would require Docker to be available in the test environment
        # For now, we'll just validate the Dockerfile syntax
        with open(dockerfile_path, 'r') as f:
            content = f.read()
        
        # Basic validation that all required sections are present
        required_sections = ["FROM", "RUN", "COPY", "ENV", "CMD", "HEALTHCHECK"]
        for section in required_sections:
            assert section in content, f"Required Dockerfile section {section} not found"
    
    @pytest.mark.integration
    def test_kubernetes_deployment_validation(self):
        """Test that Kubernetes manifests can be applied."""
        k8s_path = Path(__file__).parent.parent / "k8s"
        
        # Validate that all manifests have required Kubernetes fields
        for yaml_file in k8s_path.glob("*.yaml"):
            with open(yaml_file, 'r') as f:
                docs = list(yaml.safe_load_all(f))
            
            for doc in docs:
                if doc:  # Skip empty documents
                    assert "apiVersion" in doc, f"Missing apiVersion in {yaml_file}"
                    assert "kind" in doc, f"Missing kind in {yaml_file}"
                    assert "metadata" in doc, f"Missing metadata in {yaml_file}"
                    assert "name" in doc["metadata"], f"Missing metadata.name in {yaml_file}"
    
    def test_configuration_consistency(self):
        """Test that configurations are consistent across deployment methods."""
        # Load Docker Compose configuration
        compose_path = Path(__file__).parent.parent / "docker" / "docker-compose.distributed.yml"
        with open(compose_path, 'r') as f:
            compose_config = yaml.safe_load(f)
        
        # Load Kubernetes ConfigMap
        k8s_path = Path(__file__).parent.parent / "k8s" / "configmap.yaml"
        with open(k8s_path, 'r') as f:
            k8s_config = yaml.safe_load(f)
        
        # Check that similar configurations exist in both
        # (This is a simplified check - in practice you'd want more detailed validation)
        compose_services = compose_config["services"]
        k8s_data = k8s_config["data"]
        
        # Verify that key configuration values are consistent
        assert "WORKER_LOG_LEVEL" in k8s_data
        assert k8s_data["WORKER_LOG_LEVEL"] == "INFO"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])