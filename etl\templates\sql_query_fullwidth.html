<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Query Tool - News Aggregator</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <!-- DataTables Buttons CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f7;
            color: #333;
        }
        h1, h2 {
            text-align: center;
            font-weight: 700;
            margin: 20px 0;
            color: #2c3e50;
        }
        .container-fluid {
            padding: 20px;
        }
        .query-container {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .results-container {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            overflow-x: auto;
            width: 100%;
        }
        .CodeMirror {
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            margin: 0 10px;
            color: #3498db;
            text-decoration: none;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
        .btn-toolbar {
            margin-top: 10px;
            margin-bottom: 10px;
        }
        .table-responsive {
            width: 100%;
            overflow-x: auto;
        }
        .table {
            margin-bottom: 0;
            width: 100%;
        }
        .pagination-container {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }
        .pagination {
            display: flex;
            list-style: none;
            padding: 0;
        }
        .page-item {
            margin: 0 5px;
        }
        .page-link {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            color: #007bff;
            background-color: #fff;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover {
            background-color: #e9ecef;
        }
        .page-item.active .page-link {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
        .results-info {
            margin-top: 10px;
            text-align: center;
        }
        .per-page-selector {
            margin-top: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }
        .pagination-container {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }
        .pagination {
            display: flex;
            list-style: none;
            padding: 0;
        }
        .page-item {
            margin: 0 5px;
        }
        .page-link {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            color: #007bff;
            background-color: #fff;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover {
            background-color: #e9ecef;
        }
        .page-item.active .page-link {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
        .results-info {
            margin-top: 10px;
            text-align: center;
        }
        .pagination-container {
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }
        .pagination {
            display: flex;
            list-style: none;
            padding: 0;
        }
        .page-item {
            margin: 0 5px;
        }
        .page-link {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            color: #007bff;
            background-color: #fff;
            text-decoration: none;
            border-radius: 4px;
        }
        .page-link:hover {
            background-color: #e9ecef;
        }
        .page-item.active .page-link {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .page-item.disabled .page-link {
            color: #6c757d;
            pointer-events: none;
            background-color: #fff;
            border-color: #dee2e6;
        }
        .results-info {
            margin-top: 10px;
            text-align: center;
        }
        .status {
            margin-top: 10px;
            font-style: italic;
        }
        .saved-queries {
            margin-top: 10px;
        }
        .query-help {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .query-help textarea {
            height: 100px;
            margin-bottom: 10px;
        }
        .row {
            margin-right: 0;
            margin-left: 0;
        }
        .col-md-6 {
            padding: 0 10px;
        }
        @media (max-width: 768px) {
            .col-md-6 {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="nav-links">
        <a href="/">Dashboard Home</a> | <a href="/sql_query">SQL Query Tool</a>
    </div>

    <h1>SQL Query Tool</h1>

    <div class="container-fluid">
        <div class="query-container">
            <h2>Query Editor</h2>
            <textarea id="sql-editor" class="form-control"></textarea>

            <div class="btn-toolbar">
                <button id="execute-btn" class="btn btn-primary me-2">Execute Query</button>
                <button id="save-btn" class="btn btn-success me-2">Save Query</button>
                <button id="load-btn" class="btn btn-info me-2">Load Query</button>
                <button id="clear-btn" class="btn btn-warning">Clear</button>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="saved-queries">
                        <select id="saved-query-select" class="form-select">
                            <option value="">-- Select a saved query --</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div id="status" class="status"></div>
                </div>
            </div>

            <div class="query-help">
                <h3>Get Help with SQL Query</h3>
                <p>Describe what you want to query in natural language, and the AI will help formulate a SQL query.</p>
                <textarea id="query-description" class="form-control" placeholder="Example: Show me the top 10 most positive news articles from the last week"></textarea>
                <button id="get-help-btn" class="btn btn-secondary">Generate SQL Query</button>
            </div>
        </div>

        <div class="results-container">
            <h2>Query Results</h2>
            <div class="table-responsive">
                <table id="results-table" class="table table-striped table-bordered">
                    <thead id="results-header"></thead>
                    <tbody id="results-body"></tbody>
                </table>
            </div>
            <div id="results-status"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/sql/sql.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <!-- DataTables Buttons JS and dependencies -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script>
        // Initialize CodeMirror for SQL editing
        const sqlEditor = CodeMirror.fromTextArea(document.getElementById('sql-editor'), {
            mode: 'text/x-sql',
            lineNumbers: true,
            indentWithTabs: true,
            smartIndent: true,
            lineWrapping: true,
            matchBrackets: true,
            autofocus: true
        });

        // Add event listener for Ctrl+Enter in the SQL editor
        sqlEditor.on('keydown', (cm, event) => {
            if (event.ctrlKey && event.key === 'Enter') {
                document.getElementById('execute-btn').click();
                event.preventDefault();
            }
        });

        // Add event listener for Ctrl+Enter in the query description textarea
        document.getElementById('query-description').addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.key === 'Enter') {
                document.getElementById('get-help-btn').click();
                event.preventDefault();
            }
        });

        // Execute query button
        document.getElementById('execute-btn').addEventListener('click', async () => {
            const query = sqlEditor.getValue();
            if (!query.trim()) {
                setStatus('Please enter a SQL query', 'danger');
                return;
            }

            setStatus('Executing query...', 'info');
            clearResults();  // Clear results before new query
            try {
                const response = await fetch('/execute_query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ query })
                });

                const result = await response.json();

                if (result.error) {
                    setStatus(`Error: ${result.error}`, 'danger');
                } else {
                    setStatus(`Query executed successfully. ${result.rows.length} rows returned.`, 'success');
                    displayResults(result);
                }
            } catch (error) {
                setStatus(`Error: ${error.message}`, 'danger');
            }
        });

        // Save query button
        document.getElementById('save-btn').addEventListener('click', async () => {
            const query = sqlEditor.getValue();
            if (!query.trim()) {
                setStatus('Nothing to save', 'warning');
                return;
            }

            const queryName = prompt('Enter a name for this query:');
            if (!queryName) return;

            try {
                const response = await fetch('/save_query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ name: queryName, query })
                });

                const result = await response.json();

                if (result.error) {
                    setStatus(`Error: ${result.error}`, 'danger');
                } else {
                    setStatus(`Query saved as "${queryName}"`, 'success');
                    loadSavedQueries();
                }
            } catch (error) {
                setStatus(`Error: ${error.message}`, 'danger');
            }
        });

        // Load query button
        document.getElementById('load-btn').addEventListener('click', () => {
            const select = document.getElementById('saved-query-select');
            if (select.value) {
                loadQuery(select.value);
            } else {
                setStatus('Please select a query to load', 'warning');
            }
        });

        // Clear button
        document.getElementById('clear-btn').addEventListener('click', () => {
            sqlEditor.setValue('');
            clearResults();
            setStatus('Editor cleared', 'info');
        });

        // Load saved query from dropdown
        document.getElementById('saved-query-select').addEventListener('change', (e) => {
            if (e.target.value) {
                loadQuery(e.target.value);
            }
        });

        // Get query help button
        document.getElementById('get-help-btn').addEventListener('click', async () => {
            const description = document.getElementById('query-description').value.trim();
            if (!description) {
                setStatus('Please enter a description of what you want to query', 'warning');
                return;
            }

            setStatus('Generating SQL query...', 'info');
            try {
                const response = await fetch('/generate_query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ description })
                });

                const result = await response.json();

                if (result.error) {
                    setStatus(`Error: ${result.error}`, 'danger');
                } else {
                    sqlEditor.setValue(result.query);
                    setStatus('SQL query generated successfully', 'success');
                }
            } catch (error) {
                setStatus(`Error: ${error.message}`, 'danger');
            }
        });

        // Load a saved query by name
        async function loadQuery(name) {
            try {
                const response = await fetch(`/load_query/${name}`);
                const result = await response.json();

                if (result.error) {
                    setStatus(`Error: ${result.error}`, 'danger');
                } else {
                    sqlEditor.setValue(result.query);
                    setStatus(`Query "${name}" loaded`, 'success');
                }
            } catch (error) {
                setStatus(`Error: ${error.message}`, 'danger');
            }
        }

        // Load the list of saved queries
        async function loadSavedQueries() {
            try {
                const response = await fetch('/list_queries');
                const result = await response.json();

                const select = document.getElementById('saved-query-select');
                // Clear all options except the first one
                while (select.options.length > 1) {
                    select.remove(1);
                }

                // Add the saved queries
                result.queries.forEach(query => {
                    const option = document.createElement('option');
                    option.value = query;
                    option.textContent = query;
                    select.appendChild(option);
                });
            } catch (error) {
                setStatus(`Error loading saved queries: ${error.message}`, 'danger');
            }
        }

        // Store the DataTable instance
        let dataTable = null;

        // Display query results using DataTables
        function displayResults(result) {
            // Always clear existing results first
            clearResults();
            
            if (result.columns.length === 0 || result.rows.length === 0) {
                document.getElementById('results-status').textContent = 'No results to display';
                return;
            }

            // Create table structure with just the headers
            const table = $('#results-table');
            const headerHtml = '<thead><tr>' + 
                result.columns.map(column => `<th>${column}</th>`).join('') + 
                '</tr></thead><tbody></tbody>';
            table.html(headerHtml);

            // Initialize DataTable
            dataTable = $('#results-table').DataTable({
                destroy: true,
                data: result.rows,
                columns: result.columns.map(column => ({
                    title: column,
                    data: null,
                    render: function(data, type, row, meta) {
                        const value = row[meta.col];
                        const columnName = result.columns[meta.col].toLowerCase();
                        
                        if (value === null) return 'NULL';
                        
                        if (columnName === 'entry_id') {
                            return `<a href="/entry_details?entry_id=${value}" target="_blank">${value}</a>`;
                        } else if (columnName === 'dup_entry_id') {
                            return `<a href="/entry_details?dup_entry_id=${value}" target="_blank">${value}</a>`;
                        }
                        return value;
                    }
                })),
                // Add these options to maintain server-side order
                ordering: false,  // Disable client-side ordering
                order: [],       // Clear any default ordering
                paging: true,
                info: true,
                searching: true,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                pageLength: 50,
                language: {
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    lengthMenu: "Show _MENU_ entries",
                    search: "Search:",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                },
                dom: 'Bfrtip',

            });
        }

        // Clear results table
        function clearResults() {
            if (dataTable) {
                dataTable.destroy();
                $('#results-table').empty();
            }
            dataTable = null;
            document.getElementById('results-status').textContent = '';
        }

        // Set status message
        function setStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status text-${type}`;
        }

        // Load saved queries when page loads
        window.onload = function() {
            loadSavedQueries();
            clearResults();
        };
    </script>
</body>
</html>











