# Development Environment Configuration for Distributed ETL Workers

# Worker Configuration
WORKER_LOG_LEVEL=DEBUG
WORKER_METRICS_ENABLED=true
WORKER_BATCH_SIZE=5
WORKER_CLAIM_TIMEOUT_MINUTES=10
WORKER_MAX_RETRIES=2
WORKER_HEARTBEAT_INTERVAL_SECONDS=30
WORKER_PROCESSING_DELAY_SECONDS=0.5

# Database Configuration
WORKER_CONNECTION_POOL_SIZE=3
WORKER_CONNECTION_TIMEOUT_SECONDS=15

# Performance Tuning (Development - slower for debugging)
WORKER_BATCH_CLAIM_RETRY_DELAY_SECONDS=2.0
WORKER_MAX_BATCH_CLAIM_RETRIES=3
WORKER_SHUTDOWN_TIMEOUT_SECONDS=60

# Health Check Configuration
WORKER_HEALTH_CHECK_PORT=8080

# Stage-specific Development Settings
FEED_WORKER_BATCH_SIZE=10
FEED_WORKER_CLAIM_TIMEOUT_MINUTES=5
FEED_WORKER_MAX_RETRIES=2

FULLTEXT_WORKER_BATCH_SIZE=3
FULLTEXT_WORKER_CLAIM_TIMEOUT_MINUTES=10
FULLTEXT_WORKER_MAX_RETRIES=2

ANALYSIS_WORKER_BATCH_SIZE=2
ANALYSIS_WORKER_CLAIM_TIMEOUT_MINUTES=30
ANALYSIS_WORKER_MAX_RETRIES=2
ANALYSIS_WORKER_PROCESSING_DELAY_SECONDS=1.0

ML_WORKER_BATCH_SIZE=2
ML_WORKER_CLAIM_TIMEOUT_MINUTES=20
ML_WORKER_MAX_RETRIES=2

GENERATION_WORKER_BATCH_SIZE=1
GENERATION_WORKER_CLAIM_TIMEOUT_MINUTES=45
GENERATION_WORKER_MAX_RETRIES=1

UTILITY_WORKER_BATCH_SIZE=5
UTILITY_WORKER_CLAIM_TIMEOUT_MINUTES=15
UTILITY_WORKER_MAX_RETRIES=2

# Cleanup Service Configuration
CLEANUP_INTERVAL_MINUTES=2
STALE_TIMEOUT_MINUTES=15

# Development-specific API Keys (use test/sandbox keys)
# OPENAI_API_KEY=sk-test-...
# IONOS_API_KEY=test-...
# HUGGINGFACE_TOKEN=hf_test...
# STABILITY_API_KEY=sk-test-...