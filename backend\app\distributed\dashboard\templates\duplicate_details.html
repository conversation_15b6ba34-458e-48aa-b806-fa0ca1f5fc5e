{% extends "base.html" %}

{% block title %}Duplicate Details - {{ dup_entry_id }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2><i class="fas fa-copy"></i> Duplicate Group Details</h2>
            <button class="btn btn-secondary" onclick="window.close()">
                <i class="fas fa-times"></i> Close
            </button>
        </div>
    </div>
</div>

<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Duplicate Entry ID:</strong> <code>{{ dup_entry_id }}</code><br>
            <strong>Total Entries:</strong> {{ entries|length }}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> All Entries with Same Duplicate ID</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th style="cursor: pointer;" onclick="sortTable(0)">Entry ID <i class="fas fa-sort"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(1)">Title <i class="fas fa-sort"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(2)">Source <i class="fas fa-sort"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(3)">Published <i class="fas fa-sort"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(4)">Similarity Score <i class="fas fa-sort"></i></th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="entries-table">
                            {% for entry in entries %}
                            <tr>
                                <td>
                                    <span class="clickable-cell" onclick="openEntryDetails('{{ entry.entry_id }}')">
                                        <code>{{ entry.entry_id }}</code>
                                    </span>
                                </td>
                                <td>
                                    <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" 
                                         title="{{ entry.title or 'N/A' }}">
                                        {{ entry.title or 'N/A' }}
                                    </div>
                                </td>
                                <td>{{ entry.source or 'N/A' }}</td>
                                <td>
                                    {% if entry.published %}
                                        {{ entry.published.strftime('%Y-%m-%d %H:%M') if entry.published.strftime else entry.published }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% if entry.dup_entry_conf %}
                                        <span class="badge bg-info">{{ "%.3f"|format(entry.dup_entry_conf) }}</span>
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="openEntryDetails('{{ entry.entry_id }}')">
                                        <i class="fas fa-eye"></i> View Details
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Duplicate Analysis</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-primary">{{ entries|length }}</h3>
                            <small class="text-muted">Total Duplicates</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-success">
                                {% set sources = entries|map(attribute='source')|unique|list %}
                                {{ sources|length }}
                            </h3>
                            <small class="text-muted">Unique Sources</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h3 class="text-info">
                                {% set avg_conf = entries|selectattr('dup_entry_conf')|map(attribute='dup_entry_conf')|list %}
                                {% if avg_conf %}
                                    {{ "%.3f"|format(avg_conf|sum / avg_conf|length) }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </h3>
                            <small class="text-muted">Avg. Similarity</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <h6>Sources Distribution:</h6>
                <div class="row">
                    {% set source_counts = {} %}
                    {% for entry in entries %}
                        {% if entry.source %}
                            {% set _ = source_counts.update({entry.source: source_counts.get(entry.source, 0) + 1}) %}
                        {% endif %}
                    {% endfor %}
                    
                    {% for source, count in source_counts.items() %}
                    <div class="col-md-3 mb-2">
                        <span class="badge bg-secondary">{{ source }}: {{ count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let sortColumn = null;
let sortDirection = 'asc';

function openEntryDetails(entryId) {
    window.open(`/entry_details/${entryId}`, '_blank');
}

function sortTable(columnIndex) {
    const table = document.getElementById('entries-table');
    const rows = Array.from(table.rows);
    
    // Determine sort direction
    if (sortColumn === columnIndex) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = columnIndex;
        sortDirection = 'asc';
    }
    
    // Sort rows
    rows.sort((a, b) => {
        let aVal = a.cells[columnIndex].textContent.trim();
        let bVal = b.cells[columnIndex].textContent.trim();
        
        // Handle N/A values
        if (aVal === 'N/A' && bVal === 'N/A') return 0;
        if (aVal === 'N/A') return sortDirection === 'asc' ? 1 : -1;
        if (bVal === 'N/A') return sortDirection === 'asc' ? -1 : 1;
        
        // Try to parse as numbers for similarity score
        if (columnIndex === 4) {
            const aNum = parseFloat(aVal);
            const bNum = parseFloat(bVal);
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
            }
        }
        
        // Try to parse as dates for published column
        if (columnIndex === 3) {
            const aDate = new Date(aVal);
            const bDate = new Date(bVal);
            if (!isNaN(aDate) && !isNaN(bDate)) {
                return sortDirection === 'asc' ? aDate - bDate : bDate - aDate;
            }
        }
        
        // String comparison
        if (sortDirection === 'asc') {
            return aVal.localeCompare(bVal);
        } else {
            return bVal.localeCompare(aVal);
        }
    });
    
    // Clear table and re-add sorted rows
    table.innerHTML = '';
    rows.forEach(row => table.appendChild(row));
    
    // Update sort indicators
    const headers = document.querySelectorAll('thead th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (icon) {
            if (index === columnIndex) {
                icon.className = sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
            } else {
                icon.className = 'fas fa-sort';
            }
        }
    });
}
</script>

<style>
.clickable-cell {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
}

.clickable-cell:hover {
    color: #0056b3;
}
</style>
{% endblock %}
