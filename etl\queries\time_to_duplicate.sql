-- Analyze how quickly news spreads across sources
-- Shows the time difference between the first and subsequent publications of the same story
WITH ranked_duplicates AS (
    SELECT 
        dup_entry_id,
        entry_id,
        title,
        source,
        published,
        ROW_NUMBER() OVER (PARTITION BY dup_entry_id ORDER BY published ASC) AS publish_order
    FROM 
        entries
    WHERE 
        entry_id <> dup_entry_id
),
first_publications AS (
    SELECT 
        dup_entry_id,
        entry_id AS first_entry_id,
        title AS first_title,
        source AS first_source,
        published AS first_published
    FROM 
        ranked_duplicates
    WHERE 
        publish_order = 1
)
SELECT 
    fp.dup_entry_id,
    fp.first_title,
    fp.first_source,
    s1.name AS first_source_name,
    fp.first_published,
    rd.entry_id AS duplicate_entry_id,
    rd.source AS duplicate_source,
    s2.name AS duplicate_source_name,
    rd.published AS duplicate_published,
    ROUND((rd.published - fp.first_published) * 24 * 60, 2) AS minutes_difference
FROM 
    first_publications fp
JOIN 
    ranked_duplicates rd ON fp.dup_entry_id = rd.dup_entry_id AND rd.publish_order > 1
LEFT JOIN
    sources s1 ON fp.first_source = s1.source
LEFT JOIN
    sources s2 ON rd.source = s2.source
ORDER BY 
    minutes_difference ASC
FETCH FIRST 100 ROWS ONLY
