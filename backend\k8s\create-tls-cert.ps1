# PowerShell script to create TLS certificate for Windows
# Requires OpenSSL to be installed (can be installed via chocolatey: choco install openssl)

Write-Host "Creating self-signed certificate for breakingbright.de..." -ForegroundColor Green

# Check if OpenSSL is available
try {
    C:\cygwin64\bin\openssl.exe version
} catch {
    Write-Host "OpenSSL not found. Please install OpenSSL:" -ForegroundColor Red
    Write-Host "Option 1: choco install openssl" -ForegroundColor Yellow
    Write-Host "Option 2: Download from https://slproweb.com/products/Win32OpenSSL.html" -ForegroundColor Yellow
    exit 1
}

# Create certificate (valid for ~100 years)
C:\cygwin64\bin\openssl.exe req -x509 -newkey rsa:4096 -keyout tls.key -out tls.crt -days 36500 -nodes `
  -subj "/C=DE/ST=Germany/L=Germany/O=BreakingBright/CN=breakingbright.de" `
  -addext "subjectAltName=DNS:breakingbright.de,DNS:*.breakingbright.de,DNS:api.breakingbright.de"

if ($LASTEXITCODE -eq 0) {
    Write-Host "Certificate created successfully!" -ForegroundColor Green
    
    # Show certificate validity
    $endDate = C:\cygwin64\bin\openssl.exe x509 -in tls.crt -noout -enddate
    Write-Host "Valid until: $endDate" -ForegroundColor Cyan
    
    # Create Kubernetes TLS secret YAML
    Write-Host "Creating Kubernetes TLS secret..." -ForegroundColor Green
    kubectl create secret tls api-tls-secret --cert=tls.crt --key=tls.key --dry-run=client -o yaml | Out-File -FilePath tls-secret.yaml -Encoding UTF8
    
    Write-Host "TLS secret YAML created: tls-secret.yaml" -ForegroundColor Green
    Write-Host "Apply with: kubectl apply -f tls-secret.yaml" -ForegroundColor Yellow
    
    # Clean up certificate files (they're now in the YAML)
    Remove-Item tls.key, tls.crt
    
    Write-Host "Setup complete!" -ForegroundColor Green
} else {
    Write-Host "Failed to create certificate" -ForegroundColor Red
    exit 1
}