"""add_composite_index_for_performance

Revision ID: 7fa7b5ad7bea
Revises: ed3e12a3790c
Create Date: 2025-05-05 23:49:52.874861

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7fa7b5ad7bea'
down_revision: Union[str, None] = 'ed3e12a3790c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_entries_dup_source_published', 'entries', ['dup_entry_id', 'source', sa.text('published DESC'), 'entry_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_entries_dup_source_published', table_name='entries')
    # ### end Alembic commands ###
