# Worker Stage Configuration Examples
# 
# This file demonstrates how to configure workers to run only specific stages
# based on hostname patterns or global settings.

# =============================================================================
# HOSTNAME-BASED CONFIGURATION
# =============================================================================

# Format: WORKER_STAGES_HOSTNAME_CONFIG="pattern1:action:stages;pattern2:action:stages"
# Actions: include (only run these stages) or exclude (run all except these stages)
# Patterns: Regular expressions matched against hostname

# Example 1: Dedicated feed download servers
# Servers with "feed" in hostname only download feeds
WORKER_STAGES_HOSTNAME_CONFIG=".*feed.*:include:download_feeds"

# Example 2: GPU servers for AI tasks only
# Servers with "gpu" in hostname only run AI-intensive stages
# WORKER_STAGES_HOSTNAME_CONFIG=".*gpu.*:include:compute_embeddings,sentiment_analysis,llm_analysis,iptc_classification"

# Example 3: Multiple hostname patterns
# Feed servers only download, GPU servers only do AI, others do everything else
# WORKER_STAGES_HOSTNAME_CONFIG=".*feed.*:include:download_feeds;.*gpu.*:include:compute_embeddings,sentiment_analysis,llm_analysis;.*web.*:exclude:download_feeds,compute_embeddings,sentiment_analysis,llm_analysis"

# Example 4: Development vs Production
# Dev servers exclude resource-intensive stages
# WORKER_STAGES_HOSTNAME_CONFIG=".*dev.*:exclude:compute_embeddings,llm_analysis,generate_preview_images"

# =============================================================================
# GLOBAL STAGE CONFIGURATION
# =============================================================================

# Global include: Only run these stages (overrides hostname config)
# WORKER_STAGES_INCLUDE="download_feeds,download_full_text"

# Global exclude: Run all stages except these
# WORKER_STAGES_EXCLUDE="generate_preview_images,generate_image_prompts"

# =============================================================================
# COMPLETE STAGE LIST (for reference)
# =============================================================================

# Available stages:
# - download_feeds              (Download RSS feeds)
# - download_full_text          (Download full article text)
# - compute_embeddings          (Generate text embeddings)
# - sentiment_analysis          (Analyze sentiment)
# - llm_analysis               (LLM-based analysis)
# - iptc_classification        (IPTC topic classification)
# - duplicate_check            (Check for duplicates)
# - generate_descriptions      (Generate article descriptions)
# - generate_image_prompts     (Generate image prompts)
# - generate_preview_images    (Generate preview images)

# =============================================================================
# EXAMPLE CONFIGURATIONS BY USE CASE
# =============================================================================

# Lightweight development setup (no AI/GPU intensive tasks)
# WORKER_STAGES_INCLUDE="download_feeds,download_full_text,duplicate_check"

# AI-only server (assumes feeds are downloaded elsewhere)
# WORKER_STAGES_INCLUDE="compute_embeddings,sentiment_analysis,llm_analysis,iptc_classification"

# Content processing server (no feed download, no image generation)
# WORKER_STAGES_EXCLUDE="download_feeds,generate_image_prompts,generate_preview_images"

# Image generation server only
# WORKER_STAGES_INCLUDE="generate_image_prompts,generate_preview_images"

# =============================================================================
# TESTING CONFIGURATIONS
# =============================================================================

# For testing: only run fast, non-resource intensive stages
# WORKER_STAGES_INCLUDE="download_feeds,duplicate_check,generate_descriptions"

# For load testing: exclude stages that make external API calls
# WORKER_STAGES_EXCLUDE="download_full_text,llm_analysis,generate_preview_images"
