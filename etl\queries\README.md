# SQL Query Tool for Oracle Database

This directory contains saved SQL queries for the Oracle database. These queries can be loaded, executed, and modified using the SQL Query Tool in the dashboard.

## Using the SQL Query Tool

1. Navigate to the SQL Query Tool page from the dashboard by clicking on "SQL Query Tool" in the navigation bar.
2. Write your SQL query in the editor or load an existing query from the dropdown.
3. Click "Execute Query" to run the query against the Oracle database.
4. Results will be displayed in a table below the query editor.
5. Save your queries for future use by clicking "Save Query" and providing a name.

## Example Queries

The following example queries are provided:

### example_query.sql
Shows the 10 most recent news entries with sentiment analysis.

### positive_news_count.sql
Counts positive news articles by source, ordered by count.

### table_structure.sql
Shows the structure of the ENTRIES table.

## Tips for Writing Queries

1. Use `SELECT` statements to retrieve data safely.
2. Be cautious with `UPDATE`, `DELETE`, or `INSERT` statements as they modify data.
3. For large result sets, consider adding a `FETCH FIRST n ROWS ONLY` clause to limit the number of rows returned.
4. Use column aliases with `AS` to make result columns more readable.
5. When joining tables, specify the join conditions explicitly.

## Database Schema

The main tables in the database are:

- `ENTRIES`: Contains news articles and their sentiment analysis
- `SOURCES`: Contains information about news sources
- `CATEGORIES`: Contains IPTC news categories

Refer to the `table_structure.sql` query to see the columns in the ENTRIES table.
