# Error Handling and Recovery Implementation

This document describes the comprehensive error handling and recovery system implemented for the distributed ETL processing framework.

## Overview

The error handling system provides intelligent retry strategies, error classification, dead letter queue management, and automatic recovery from various failure scenarios. It consists of two main components:

1. **Comprehensive Error Handling** (`error_handling.py`) - Intelligent retry logic with exponential backoff
2. **Stale Work Cleanup** (`stale_work_cleanup.py`) - Background cleanup and recovery system

## Components Implemented

### 1. Error Classification System

**File**: `backend/app/distributed/error_handling.py`

#### ErrorClassifier
- Automatically classifies errors into categories (Network, Rate Limit, Authentication, etc.)
- Determines appropriate retry strategies based on error type
- Supports custom classification rules
- Maps error patterns to handling strategies

#### Error Categories
- `NETWORK` - Connection and network-related errors
- `RATE_LIMIT` - API rate limiting errors  
- `RESOURCE` - Memory, disk space, and resource exhaustion
- `AUTHENTICATION` - Auth/permission errors (typically fatal)
- `DATA_FORMAT` - Invalid data format errors (typically fatal)
- `EXTERNAL_SERVICE` - Third-party service errors
- `DATABASE` - Database connectivity issues
- `PROCESSING` - General processing logic errors
- `TIMEOUT` - Operation timeout errors
- `UNKNOWN` - Unclassified errors

#### Error Severity Levels
- `TRANSIENT` - Temporary errors that should be retried aggressively
- `PERSISTENT` - Errors that may resolve with time and patience
- `FATAL` - Errors that won't resolve with retries
- `CONFIGURATION` - Configuration or setup errors

### 2. Retry Calculation System

#### RetryCalculator
Provides multiple backoff strategies:

- **Exponential Backoff**: `delay = base_delay * (multiplier ^ attempt)`
- **Linear Backoff**: `delay = base_delay + (increment * attempt)`
- **Fixed Delay**: Constant delay between retries
- **Jitter Support**: Adds randomness to prevent thundering herd

#### Default Retry Strategies by Error Type
- Network errors: 5 retries, exponential backoff (2s → 4s → 8s → 16s → 32s)
- Rate limits: 10 retries, exponential backoff starting at 60s
- Timeouts: 3 retries, exponential backoff starting at 5s
- Authentication: No retries (fatal)
- Data format: No retries (fatal)
- Processing: 2 retries, exponential backoff starting at 5s

### 3. Dead Letter Queue

#### DeadLetterQueue
- Manages entries that have failed repeatedly
- Stores comprehensive failure information
- Supports manual retry of dead letter entries
- Provides statistics and monitoring

#### Features
- Automatic dead letter classification
- Failure record preservation
- Manual intervention support
- Statistics and reporting

### 4. Comprehensive Error Handler

#### ComprehensiveErrorHandler
Main orchestrator that:
- Classifies errors using ErrorClassifier
- Calculates retry delays using RetryCalculator
- Manages failure records and retry attempts
- Integrates with dead letter queue
- Provides retry-ready entry detection

#### Key Methods
- `handle_processing_error()` - Main error handling entry point
- `get_retry_ready_entries()` - Find entries ready for retry
- `get_error_statistics()` - Comprehensive error metrics
- `cleanup_old_failure_records()` - Memory management

### 5. Stale Work Cleanup System

**File**: `backend/app/distributed/stale_work_cleanup.py`

#### StaleWorkCleanupSystem
Background system that automatically:
- Cleans up stale claims from inactive workers
- Recovers work from failed workers
- Detects and handles stuck processing
- Cleans up old retry information
- Identifies entries requiring manual intervention

#### Cleanup Operations
1. **Stale Claims**: Releases claims older than timeout threshold
2. **Worker Failures**: Recovers work from detected failed workers
3. **Stuck Processing**: Handles entries processing too long
4. **Old Retry Entries**: Cleans up outdated retry information
5. **Manual Intervention**: Identifies problematic entries

#### Configuration Options
- `cleanup_interval_seconds`: How often to run cleanup (default: 300s)
- `stale_claim_timeout_minutes`: When claims become stale (default: 30m)
- `stuck_processing_timeout_minutes`: When processing is stuck (default: 120m)
- `max_retry_age_hours`: When to clean retry info (default: 24h)

### 6. Manual Intervention Tools

#### ManualInterventionTools
Provides operators with tools to:
- View stuck entries with analysis
- Reset entry processing state
- Mark entries as failed manually
- Release stuck claims
- Get detailed entry information

#### Key Features
- Stuck entry detection and analysis
- Suggested recovery actions
- Safe manual operations
- Detailed entry diagnostics

## Integration Points

### With Work Queue Manager
- Error handler integrates with `WorkQueueManager.mark_failed()`
- Retry information stored in entry processing status
- Cleanup system uses work queue for claim management

### With Worker Health System
- Cleanup system integrates with `WorkerHealthManager`
- Detects failed workers for recovery
- Coordinates with existing health monitoring

### With Processing Status
- Stores retry information in processing status JSON
- Maintains compatibility with existing status structure
- Supports stage-specific error handling

## Usage Examples

### Basic Error Handling
```python
from backend.app.distributed.error_handling import ComprehensiveErrorHandler

# Initialize error handler
error_handler = ComprehensiveErrorHandler(db_session)

# Handle a processing error
should_retry, delay = error_handler.handle_processing_error(
    entry_id="entry-123",
    stage=ProcessingStage.DOWNLOAD_FULL_TEXT,
    error_message="Connection refused",
    worker_id="worker-001"
)

if should_retry:
    print(f"Retry in {delay} seconds")
else:
    print("Entry moved to dead letter queue")
```

### Cleanup System Setup
```python
from backend.app.distributed.stale_work_cleanup import create_default_cleanup_system

# Create cleanup system with default handlers
cleanup_system = create_default_cleanup_system(
    db_session_factory=get_db_session,
    cleanup_interval_seconds=300,
    stale_claim_timeout_minutes=30
)

# Start background cleanup
cleanup_system.start()

# Force immediate cleanup if needed
result = cleanup_system.force_cleanup()
```

### Manual Intervention
```python
from backend.app.distributed.stale_work_cleanup import ManualInterventionTools

# Initialize intervention tools
tools = ManualInterventionTools(db_session)

# Get stuck entries
stuck_entries = tools.get_stuck_entries(min_retry_count=5)

# Reset an entry
success = tools.reset_entry_processing(
    entry_id="stuck-entry-123",
    reset_retry_count=True
)

# Get detailed entry information
details = tools.get_entry_details("entry-123")
```

## Monitoring and Observability

### Error Statistics
- Total failure records by stage and error category
- Dead letter queue statistics
- Retry success/failure rates
- Error classification accuracy

### Cleanup Statistics  
- Cleanup cycles performed
- Entries cleaned/released/reset
- Stuck entries detected
- Manual interventions required

### Key Metrics to Monitor
- Dead letter queue growth rate
- Average retry attempts before success
- Cleanup effectiveness
- Worker failure detection accuracy
- Manual intervention frequency

## Configuration

### Environment Variables
Error handling behavior can be configured through:
- Worker configuration (max retries, timeouts)
- Cleanup system intervals and thresholds
- Error classification rules (customizable)

### Customization
- Add custom error classification rules
- Implement custom retry strategies
- Configure cleanup thresholds per environment
- Add custom notification handlers

## Testing

Comprehensive test suites provided:
- `test_error_handling.py` - 26 tests covering all error handling components
- `test_stale_work_cleanup.py` - 26 tests covering cleanup and recovery

### Test Coverage
- Error classification accuracy
- Retry calculation correctness
- Dead letter queue operations
- Cleanup system functionality
- Manual intervention tools
- Integration scenarios

## Requirements Satisfied

This implementation satisfies the following requirements from the specification:

### Requirement 1.3 (Worker Failures)
- ✅ Automatic detection and recovery from worker failures
- ✅ Graceful handling of worker crashes
- ✅ Work release on failure

### Requirement 5.3 (Monitoring and Recovery)
- ✅ Failed worker detection within configurable timeout
- ✅ Automatic work release from failed workers
- ✅ Comprehensive monitoring and statistics

### Additional Benefits
- Intelligent error classification reduces unnecessary retries
- Exponential backoff prevents system overload
- Dead letter queue prevents infinite retry loops
- Manual intervention tools enable operational control
- Background cleanup maintains system health
- Comprehensive monitoring enables proactive management

## Future Enhancements

Potential improvements for future versions:
- Machine learning-based error classification
- Adaptive retry strategies based on success rates
- Integration with external monitoring systems
- Advanced dead letter queue management UI
- Automated recovery suggestions
- Performance-based cleanup optimization