"""Add preview image metadata

Revision ID: 022b0510e688
Revises: f1aad1140fd3
Create Date: 2025-05-16 16:33:45.337006

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '022b0510e688'
down_revision: Union[str, None] = 'f1aad1140fd3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('entries', sa.Column('preview_model', sa.String(length=255), nullable=True))
    op.add_column('entries', sa.Column('preview_provider', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('entries', 'preview_provider')
    op.drop_column('entries', 'preview_model')
    # ### end Alembic commands ###
