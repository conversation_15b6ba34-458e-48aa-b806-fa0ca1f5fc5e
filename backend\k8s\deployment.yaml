apiVersion: apps/v1
kind: Deployment
metadata:
  name: breaking-bright-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: breaking-bright-backend
  template:
    metadata:
      labels:
        app: breaking-bright-backend
    spec:
      containers:
      - name: backend
        image: robertschulze/breaking-bright-backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: breaking-bright-secrets
              key: database-url
      imagePullSecrets:
      - name: docker-registry-secret
