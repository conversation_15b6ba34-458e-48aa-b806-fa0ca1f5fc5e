apiVersion: apps/v1
kind: Deployment
metadata:
  name: generation-workers
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: generation-worker
    app.kubernetes.io/part-of: distributed-etl
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: breaking-bright
      app.kubernetes.io/component: generation-worker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: breaking-bright
        app.kubernetes.io/component: generation-worker
        app.kubernetes.io/part-of: distributed-etl
    spec:
      containers:
      - name: generation-worker
        image: robertschulze/breaking-bright-worker:latest
        imagePullPolicy: Always
        command: ["python", "-m", "backend.app.distributed.distributed_worker"]
        ports:
        - containerPort: 8080
          name: health-check
          protocol: TCP
        env:
        - name: WORKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: WORKER_STAGES
          value: "generate_descriptions,generate_image_prompts,generate_preview_images"
        - name: WORKER_BATCH_SIZE
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: GENERATION_WORKER_BATCH_SIZE
        - name: WORKER_CLAIM_TIMEOUT_MINUTES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: GENERATION_WORKER_CLAIM_TIMEOUT_MINUTES
        - name: WORKER_MAX_RETRIES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: GENERATION_WORKER_MAX_RETRIES
        - name: WORKER_HEALTH_CHECK_PORT
          value: "8080"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: openai-api-key
        - name: IONOS_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: ionos-api-key
        - name: STABILITY_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: stability-api-key
        envFrom:
        - configMapRef:
            name: distributed-worker-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 120
          periodSeconds: 60
          timeoutSeconds: 20
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 20
          timeoutSeconds: 10
          failureThreshold: 3
        volumeMounts:
        - name: worker-logs
          mountPath: /var/log/worker
        - name: temp-images
          mountPath: /tmp/images
      volumes:
      - name: worker-logs
        emptyDir: {}
      - name: temp-images
        emptyDir:
          sizeLimit: 2Gi
      imagePullSecrets:
      - name: docker-registry-secret
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: generation-workers-service
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: generation-worker
spec:
  selector:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: generation-worker
  ports:
  - name: health-check
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP