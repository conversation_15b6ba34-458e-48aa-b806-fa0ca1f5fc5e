"""
Unit tests for the DistributedWorker class.

Tests worker lifecycle management, batch processing, error handling,
and graceful shutdown functionality.
"""

import pytest
import threading
import time
from unittest.mock import Mock, MagicMock, patch, call
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from backend.app.distributed.distributed_worker import (
    DistributedWorker, WorkerState, WorkerManager
)
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestDistributedWorker:
    """Test cases for DistributedWorker class."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Mock database session factory."""
        mock_session = Mock()
        mock_factory = Mock(return_value=mock_session)
        return mock_factory, mock_session
    
    @pytest.fixture
    def worker_config(self):
        """Create test worker configuration."""
        return WorkerConfig(
            worker_id="test-worker-001",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            batch_size=5,
            claim_timeout_minutes=10,
            heartbeat_interval_seconds=1,
            processing_delay_seconds=0.1,
            shutdown_timeout_seconds=5
        )
    
    @pytest.fixture
    def concrete_worker(self, worker_config, mock_db_session_factory):
        """Create a concrete implementation of DistributedWorker for testing."""
        
        class TestWorker(DistributedWorker):
            def __init__(self, config, db_session_factory):
                super().__init__(config, db_session_factory)
                self.process_batch_calls = []
                self.process_batch_results = {}
            
            def process_batch(self, entries, stage):
                self.process_batch_calls.append((entries, stage))
                return self.process_batch_results.get(stage, {})
        
        factory, _ = mock_db_session_factory
        return TestWorker(worker_config, factory)
    
    def test_worker_initialization(self, concrete_worker, worker_config):
        """Test worker initialization."""
        assert concrete_worker.worker_id == "test-worker-001"
        assert concrete_worker.config == worker_config
        assert concrete_worker.state == WorkerState.STOPPED
        assert not concrete_worker.is_running
        assert concrete_worker._current_batch == []
    
    def test_worker_stats_initial(self, concrete_worker):
        """Test initial worker statistics."""
        stats = concrete_worker.stats
        
        assert stats['worker_id'] == "test-worker-001"
        assert stats['state'] == WorkerState.STOPPED.value
        assert stats['batches_processed'] == 0
        assert stats['entries_processed'] == 0
        assert stats['entries_failed'] == 0
        assert stats['current_batch_size'] == 0
    
    def test_start_worker_success(self, concrete_worker):
        """Test successful worker startup."""
        # Mock the loops to run indefinitely until shutdown
        def mock_worker_loop():
            while not concrete_worker._shutdown_event.is_set():
                concrete_worker._shutdown_event.wait(0.01)
        
        def mock_heartbeat_loop():
            while not concrete_worker._shutdown_event.is_set():
                concrete_worker._shutdown_event.wait(0.01)
        
        with patch.object(concrete_worker, '_worker_loop', side_effect=mock_worker_loop), \
             patch.object(concrete_worker, '_heartbeat_loop', side_effect=mock_heartbeat_loop):
            
            concrete_worker.start()
            
            assert concrete_worker.state == WorkerState.RUNNING
            assert concrete_worker._worker_thread is not None
            assert concrete_worker._heartbeat_thread is not None
            assert concrete_worker._worker_thread.is_alive()
            assert concrete_worker._heartbeat_thread.is_alive()
            
            # Cleanup
            concrete_worker.stop()
    
    def test_start_worker_already_running(self, concrete_worker):
        """Test starting worker when already running raises error."""
        concrete_worker._state = WorkerState.RUNNING
        
        with pytest.raises(RuntimeError, match="Cannot start worker in state running"):
            concrete_worker.start()
    
    def test_stop_worker(self, concrete_worker):
        """Test worker shutdown."""
        with patch.object(concrete_worker, '_worker_loop'), \
             patch.object(concrete_worker, '_heartbeat_loop'):
            
            concrete_worker.start()
            assert concrete_worker.is_running
            
            concrete_worker.stop()
            assert concrete_worker.state == WorkerState.STOPPED
            assert not concrete_worker.is_running
    
    def test_stop_worker_not_running(self, concrete_worker):
        """Test stopping worker when not running."""
        assert concrete_worker.state == WorkerState.STOPPED
        
        # Should not raise error
        concrete_worker.stop()
        assert concrete_worker.state == WorkerState.STOPPED
    
    @patch('backend.app.distributed.distributed_worker.WorkQueueManager')
    def test_process_stage_batch_success(self, mock_queue_manager_class, 
                                       concrete_worker, mock_db_session_factory):
        """Test successful batch processing."""
        factory, mock_session = mock_db_session_factory
        mock_queue_manager = Mock()
        mock_queue_manager_class.return_value = mock_queue_manager
        
        # Setup mock data
        entry_ids = ["entry1", "entry2", "entry3"]
        mock_queue_manager.claim_batch.return_value = entry_ids
        
        mock_entries = [Mock(entry_id=eid) for eid in entry_ids]
        mock_session.query.return_value.filter.return_value.all.return_value = mock_entries
        
        # Setup worker to return success for all entries
        stage = ProcessingStage.DOWNLOAD_FULL_TEXT
        concrete_worker.process_batch_results[stage] = {
            "entry1": True, "entry2": True, "entry3": True
        }
        
        # Process batch
        result = concrete_worker._process_stage_batch(stage)
        
        assert result is True
        assert len(concrete_worker.process_batch_calls) == 1
        
        # Verify work queue interactions
        mock_queue_manager.claim_batch.assert_called_once_with(
            stage=stage,
            batch_size=5,
            worker_id="test-worker-001",
            max_retries=3
        )
        mock_queue_manager.mark_completed.assert_called_once_with(
            entry_ids, stage, "test-worker-001"
        )
        mock_queue_manager.mark_failed.assert_not_called()
    
    @patch('backend.app.distributed.distributed_worker.WorkQueueManager')
    def test_process_stage_batch_no_work(self, mock_queue_manager_class, 
                                       concrete_worker, mock_db_session_factory):
        """Test batch processing when no work is available."""
        factory, mock_session = mock_db_session_factory
        mock_queue_manager = Mock()
        mock_queue_manager_class.return_value = mock_queue_manager
        
        # No work available
        mock_queue_manager.claim_batch.return_value = []
        
        stage = ProcessingStage.DOWNLOAD_FULL_TEXT
        result = concrete_worker._process_stage_batch(stage)
        
        assert result is False
        assert len(concrete_worker.process_batch_calls) == 0
    
    @patch('backend.app.distributed.distributed_worker.WorkQueueManager')
    def test_process_stage_batch_partial_failure(self, mock_queue_manager_class,
                                                concrete_worker, mock_db_session_factory):
        """Test batch processing with partial failures."""
        factory, mock_session = mock_db_session_factory
        mock_queue_manager = Mock()
        mock_queue_manager_class.return_value = mock_queue_manager
        
        # Setup mock data
        entry_ids = ["entry1", "entry2", "entry3"]
        mock_queue_manager.claim_batch.return_value = entry_ids
        
        mock_entries = [Mock(entry_id=eid) for eid in entry_ids]
        mock_session.query.return_value.filter.return_value.all.return_value = mock_entries
        
        # Setup worker to return mixed results
        stage = ProcessingStage.DOWNLOAD_FULL_TEXT
        concrete_worker.process_batch_results[stage] = {
            "entry1": True, "entry2": False, "entry3": True
        }
        
        # Process batch
        result = concrete_worker._process_stage_batch(stage)
        
        assert result is True
        
        # Verify work queue interactions
        mock_queue_manager.mark_completed.assert_called_once_with(
            ["entry1", "entry3"], stage, "test-worker-001"
        )
        mock_queue_manager.mark_failed.assert_called_once_with(
            ["entry2"], stage, "test-worker-001", "Batch processing failed"
        )
    
    @patch('backend.app.distributed.distributed_worker.WorkQueueManager')
    def test_process_stage_batch_exception(self, mock_queue_manager_class,
                                         concrete_worker, mock_db_session_factory):
        """Test batch processing with exception handling."""
        factory, mock_session = mock_db_session_factory
        mock_queue_manager = Mock()
        mock_queue_manager_class.return_value = mock_queue_manager
        
        # Setup mock data
        entry_ids = ["entry1", "entry2"]
        mock_queue_manager.claim_batch.return_value = entry_ids
        
        # Simulate database error in the session creation itself
        factory.side_effect = Exception("Database connection error")
        
        stage = ProcessingStage.DOWNLOAD_FULL_TEXT
        result = concrete_worker._process_stage_batch(stage)
        
        assert result is False
    
    def test_heartbeat_functionality(self, concrete_worker):
        """Test heartbeat mechanism."""
        # Send heartbeat
        concrete_worker._send_heartbeat()
        
        assert concrete_worker._last_heartbeat is not None
        assert isinstance(concrete_worker._last_heartbeat, datetime)
    
    def test_health_status(self, concrete_worker):
        """Test health status reporting."""
        # Send heartbeat first
        concrete_worker._send_heartbeat()
        
        health = concrete_worker.get_health_status()
        
        assert health['worker_id'] == "test-worker-001"
        assert health['state'] == WorkerState.STOPPED.value
        assert health['healthy'] is False  # Not running
        assert health['last_heartbeat'] is not None
        assert health['current_batch_size'] == 0
        assert 'stats' in health
        assert 'config' in health
    
    def test_release_current_batch(self, concrete_worker, mock_db_session_factory):
        """Test releasing current batch during shutdown."""
        factory, mock_session = mock_db_session_factory
        
        # Set current batch
        concrete_worker._current_batch = ["entry1", "entry2"]
        
        with patch('backend.app.distributed.distributed_worker.WorkQueueManager') as mock_queue_class:
            mock_queue = Mock()
            mock_queue_class.return_value = mock_queue
            
            concrete_worker._release_current_batch()
            
            mock_queue.release_batch.assert_called_once_with(
                ["entry1", "entry2"], "test-worker-001"
            )
            assert concrete_worker._current_batch == []
    
    def test_update_stats(self, concrete_worker):
        """Test statistics updating."""
        # Set processing start time
        concrete_worker._processing_start_time = datetime.now(timezone.utc)
        
        # Wait a bit to get measurable duration
        time.sleep(0.01)
        
        concrete_worker._update_stats(total_entries=5, success_count=4, failed_count=1)
        
        stats = concrete_worker.stats
        assert stats['batches_processed'] == 1
        assert stats['entries_processed'] == 4
        assert stats['entries_failed'] == 1
        assert stats['last_batch_size'] == 5
        assert stats['last_batch_duration'] > 0


class TestWorkerManager:
    """Test cases for WorkerManager class."""
    
    @pytest.fixture
    def worker_manager(self):
        """Create test worker manager."""
        return WorkerManager()
    
    @pytest.fixture
    def mock_workers(self):
        """Create mock workers for testing."""
        workers = []
        for i in range(3):
            worker = Mock()
            worker.worker_id = f"worker-{i}"
            worker.is_running = False
            worker.state = WorkerState.STOPPED
            workers.append(worker)
        return workers
    
    def test_add_worker(self, worker_manager, mock_workers):
        """Test adding workers to manager."""
        worker = mock_workers[0]
        worker_manager.add_worker(worker)
        
        assert worker.worker_id in worker_manager.workers
        assert worker_manager.workers[worker.worker_id] == worker
    
    def test_start_all_workers(self, worker_manager, mock_workers):
        """Test starting all managed workers."""
        for worker in mock_workers:
            worker_manager.add_worker(worker)
        
        worker_manager.start_all()
        
        for worker in mock_workers:
            worker.start.assert_called_once()
    
    def test_stop_all_workers(self, worker_manager, mock_workers):
        """Test stopping all managed workers."""
        for worker in mock_workers:
            worker_manager.add_worker(worker)
        
        worker_manager.stop_all(timeout=10)
        
        for worker in mock_workers:
            worker.stop.assert_called_once_with(10)
    
    def test_get_worker_status(self, worker_manager, mock_workers):
        """Test getting status of all workers."""
        for worker in mock_workers:
            worker.get_health_status.return_value = {'status': 'test'}
            worker_manager.add_worker(worker)
        
        status = worker_manager.get_worker_status()
        
        assert len(status) == 3
        for worker_id in status:
            assert status[worker_id] == {'status': 'test'}
    
    def test_get_running_workers(self, worker_manager, mock_workers):
        """Test getting list of running workers."""
        # Set some workers as running
        mock_workers[0].is_running = True
        mock_workers[1].is_running = False
        mock_workers[2].is_running = True
        
        for worker in mock_workers:
            worker_manager.add_worker(worker)
        
        running = worker_manager.get_running_workers()
        
        assert len(running) == 2
        assert mock_workers[0] in running
        assert mock_workers[2] in running
    
    def test_cleanup_failed_workers(self, worker_manager, mock_workers):
        """Test cleaning up failed workers."""
        # Set some workers as failed
        mock_workers[0].state = WorkerState.RUNNING
        mock_workers[1].state = WorkerState.ERROR
        mock_workers[2].state = WorkerState.ERROR
        
        for worker in mock_workers:
            worker_manager.add_worker(worker)
        
        assert len(worker_manager.workers) == 3
        
        worker_manager.cleanup_failed_workers()
        
        assert len(worker_manager.workers) == 1
        assert "worker-0" in worker_manager.workers
        assert "worker-1" not in worker_manager.workers
        assert "worker-2" not in worker_manager.workers


class TestWorkerLifecycle:
    """Integration tests for worker lifecycle."""
    
    @pytest.fixture
    def integration_worker(self):
        """Create worker for integration testing."""
        
        class IntegrationTestWorker(DistributedWorker):
            def __init__(self):
                config = WorkerConfig(
                    worker_id="integration-test-worker",
                    stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                    batch_size=2,
                    heartbeat_interval_seconds=1,  # Must be integer >= 1
                    processing_delay_seconds=0.05,
                    shutdown_timeout_seconds=2
                )
                
                mock_session_factory = Mock(return_value=Mock())
                super().__init__(config, mock_session_factory)
                
                self.batches_processed = 0
                self.should_find_work = True
            
            def process_batch(self, entries, stage):
                self.batches_processed += 1
                time.sleep(0.01)  # Simulate processing time
                return {entry.entry_id: True for entry in entries}
        
        return IntegrationTestWorker()
    
    @patch('backend.app.distributed.distributed_worker.WorkQueueManager')
    def test_worker_lifecycle_integration(self, mock_queue_manager_class, integration_worker):
        """Test complete worker lifecycle with mocked dependencies."""
        mock_queue_manager = Mock()
        mock_queue_manager_class.return_value = mock_queue_manager
        
        # Setup work availability
        call_count = 0
        def claim_batch_side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2 and integration_worker.should_find_work:
                return [f"entry{call_count}"]
            return []
        
        mock_queue_manager.claim_batch.side_effect = claim_batch_side_effect
        
        # Mock database session
        mock_session = integration_worker.db_session_factory()
        mock_entry = Mock()
        mock_entry.entry_id = "entry1"
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_entry]
        
        try:
            # Start worker
            integration_worker.start()
            assert integration_worker.is_running
            
            # Let it process some batches
            time.sleep(0.3)
            
            # Stop finding work
            integration_worker.should_find_work = False
            
            # Wait a bit more then stop
            time.sleep(0.1)
            integration_worker.stop()
            
            # Verify worker processed some batches
            assert integration_worker.batches_processed > 0
            assert integration_worker.state == WorkerState.STOPPED
            
        finally:
            # Ensure cleanup
            if integration_worker.is_running:
                integration_worker.stop()


if __name__ == "__main__":
    pytest.main([__file__])