# Dockerfile for distributed ETL workers
# Build multi-architecture image using:
# docker buildx build --push --platform linux/amd64,linux/arm64 -t robertschulze/breaking-bright-worker:latest -f backend/deployment/docker/Dockerfile.distributed-worker .

FROM python:3.13-slim

# Install required tools
RUN apt-get update && apt-get install -y curl unzip telnet iputils-ping iproute2 traceroute dnsutils mc vim && rm -rf /var/lib/apt/lists/*

# Copy requirements and install dependencies
COPY backend/requirements.txt /backend/requirements.txt
RUN pip install --no-cache-dir -r /backend/requirements.txt

# Set the PYTHONPATH to include the backend directory
ENV PYTHONPATH=/

# Install Oracle Instant Client for multi-architecture support
ARG TARGETARCH

# Use the basiclite version of Oracle Instant Client for congruent versions
RUN if [ "$TARGETARCH" = "amd64" ]; then \
        curl -o instantclient.zip https://download.oracle.com/otn_software/linux/instantclient/2370000/instantclient-basiclite-linux.x64-*********.01.zip; \
    elif [ "$TARGETARCH" = "arm64" ]; then \
        curl -o instantclient.zip https://download.oracle.com/otn_software/linux/instantclient/2370000/instantclient-basiclite-linux.arm64-*********.01.zip; \
    fi && \
    unzip instantclient.zip -d /opt/oracle && \
    rm instantclient.zip

# Update environment variables for Oracle Instant Client version 23.7
ENV LD_LIBRARY_PATH=/opt/oracle/instantclient_23_7
ENV TNS_ADMIN=/opt/oracle/instantclient_23_7/network/admin

# Ensure the TNS_ADMIN directory exists and add a placeholder tnsnames.ora file
RUN mkdir -p /opt/oracle/instantclient_23_7/network/admin && \
    echo "" > /opt/oracle/instantclient_23_7/network/admin/tnsnames.ora

# Copy the application code
COPY backend/app backend/app

# Create non-root user for security
RUN groupadd -r worker && useradd -r -g worker worker
RUN chown -R worker:worker /backend
USER worker

# Health check for worker
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:${WORKER_HEALTH_CHECK_PORT:-8080}/health || exit 1

# Default command to run distributed worker
CMD ["python", "-m", "backend.app.distributed.distributed_worker"]