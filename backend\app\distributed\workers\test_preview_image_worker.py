"""
Unit tests for PreviewImageWorker.

Tests the preview image worker functionality including batch processing,
image generation API calls, rate limiting, and database operations.
"""

import pytest
import base64
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from backend.app.distributed.workers.preview_image_worker import Preview<PERSON>mageWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry


class TestPreviewImageWorker:
    """Test suite for PreviewImageWorker."""
    
    @pytest.fixture
    def mock_db_session_factory(self):
        """Create a mock database session factory."""
        mock_session = Mock()
        mock_session.query.return_value.filter_by.return_value.update.return_value = None
        mock_session.commit.return_value = None
        mock_session.rollback.return_value = None
        mock_session.close.return_value = None
        
        def session_factory():
            return mock_session
        
        return session_factory
    
    @pytest.fixture
    def worker_config(self):
        """Create a test worker configuration."""
        config = WorkerConfig(
            worker_id="test-preview-image-worker",
            stages=[ProcessingStage.GENERATE_PREVIEW_IMAGES],
            batch_size=5,
            heartbeat_interval=30,
            claim_timeout=300,
            stage_configs={
                ProcessingStage.GENERATE_PREVIEW_IMAGES: {
                    'sentiment_threshold': 0.5,
                    'ad_threshold': 0.5,
                    'batch_optimize_images': False,
                    'image_providers': ['stable_diffusion', 'together'],
                    'provider_configs': {
                        'stable_diffusion': {
                            'base_url': 'http://localhost:7860'
                        },
                        'together': {
                            'api_key': 'test-together-key'
                        }
                    }
                }
            }
        )
        return config
    
    @pytest.fixture
    def sample_entries(self):
        """Create sample Entry objects for testing."""
        entries = []
        for i in range(3):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.preview_img = None  # No existing image
            entry.full_text = f"This is the full text content for test entry {i}."
            entry.image_prompt = f"news, positive, uplifting, test, entry, {i}"
            entry.llm_positive = 0.7  # Above sentiment threshold
            entry.llm_is_ad = 0.2  # Below ad threshold
            entries.append(entry)
        return entries
    
    @pytest.fixture
    def sample_image_data(self):
        """Create sample image data."""
        # Create a simple base64 encoded image data
        return base64.b64encode(b"fake_image_data").decode()
    
    def test_worker_initialization(self, worker_config, mock_db_session_factory):
        """Test worker initialization with correct configuration."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            assert worker.sentiment_threshold == 0.5
            assert worker.ad_threshold == 0.5
            assert worker.batch_optimize_images is False
            assert worker._providers == ['stable_diffusion', 'together']
    
    def test_worker_initialization_invalid_stage(self, mock_db_session_factory):
        """Test worker initialization fails with invalid stage configuration."""
        invalid_config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.FEED_DOWNLOAD],  # Wrong stage
            batch_size=5
        )
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            with pytest.raises(ValueError, match="PreviewImageWorker requires GENERATE_PREVIEW_IMAGES stage"):
                PreviewImageWorker(invalid_config, mock_db_session_factory)
    
    def test_process_batch_wrong_stage(self, worker_config, mock_db_session_factory, sample_entries):
        """Test process_batch rejects wrong processing stage."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(sample_entries, ProcessingStage.FEED_DOWNLOAD)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(sample_entries)
    
    def test_process_batch_no_full_text(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries without full_text."""
        # Create entries without full_text
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.preview_img = None
            entry.full_text = None  # Missing full_text
            entry.image_prompt = f"test prompt {i}"
            entry.llm_positive = 0.7
            entry.llm_is_ad = 0.2
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_no_image_prompt(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries without image_prompt."""
        # Create entries without image_prompt
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.preview_img = None
            entry.full_text = f"Full text {i}"
            entry.image_prompt = None  # Missing image_prompt
            entry.llm_positive = 0.7
            entry.llm_is_ad = 0.2
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
            
            # Should return False for all entries
            assert all(not success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_existing_image(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries that already have preview images."""
        # Create entries with existing images
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.preview_img = b"existing_image_data"  # Already has image
            entry.full_text = f"Full text {i}"
            entry.image_prompt = f"test prompt {i}"
            entry.llm_positive = 0.7
            entry.llm_is_ad = 0.2
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
            
            # Should return True for all entries (already have images)
            assert all(success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_low_sentiment(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries with low sentiment scores."""
        # Create entries with low sentiment scores
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.preview_img = None
            entry.full_text = f"Full text {i}"
            entry.image_prompt = f"test prompt {i}"
            entry.llm_positive = 0.3  # Below threshold
            entry.llm_is_ad = 0.2
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
            
            # Should return True for all entries (doesn't meet criteria, but not an error)
            assert all(success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_high_ad_score(self, worker_config, mock_db_session_factory):
        """Test process_batch handles entries with high ad scores."""
        # Create entries with high ad scores
        entries = []
        for i in range(2):
            entry = Mock(spec=Entry)
            entry.entry_id = f"test-entry-{i}"
            entry.title = f"Test Title {i}"
            entry.preview_img = None
            entry.full_text = f"Full text {i}"
            entry.image_prompt = f"test prompt {i}"
            entry.llm_positive = 0.7
            entry.llm_is_ad = 0.8  # Above threshold
            entries.append(entry)
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            results = worker.process_batch(entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
            
            # Should return True for all entries (doesn't meet criteria, but not an error)
            assert all(success for success in results.values())
            assert len(results) == len(entries)
    
    def test_process_batch_successful_generation(self, worker_config, mock_db_session_factory, sample_entries):
        """Test successful preview image generation."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock the image generation and database update
            with patch.object(worker, '_generate_image_with_fallback', return_value=(b'image_data', 'test_model', 'stable_diffusion')) as mock_generate:
                with patch.object(worker, '_update_entry_preview_image', return_value=True) as mock_update:
                    results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
                    
                    # Verify all entries were processed successfully
                    assert all(success for success in results.values())
                    assert len(results) == len(sample_entries)
                    
                    # Verify image generation calls were made
                    assert mock_generate.call_count == len(sample_entries)
                    
                    # Verify database updates were called
                    assert mock_update.call_count == len(sample_entries)
    
    def test_process_batch_generation_failure(self, worker_config, mock_db_session_factory, sample_entries):
        """Test handling of image generation failures."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock image generation to return None (all providers failed)
            with patch.object(worker, '_generate_image_with_fallback', return_value=(None, None, None)):
                results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
                
                # Should return False for all entries
                assert all(not success for success in results.values())
                assert len(results) == len(sample_entries)
    
    def test_process_batch_rate_limiting(self, worker_config, mock_db_session_factory, sample_entries):
        """Test rate limiting functionality."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock rate limiting to return False
            with patch.object(worker, '_can_make_image_call', return_value=False):
                results = worker.process_batch(sample_entries, ProcessingStage.GENERATE_PREVIEW_IMAGES)
                
                # Should return False for all entries (rate limited)
                assert all(not success for success in results.values())
                assert len(results) == len(sample_entries)
    
    @patch('backend.app.distributed.workers.preview_image_worker.requests.post')
    def test_generate_stable_diffusion_success(self, mock_post, worker_config, mock_db_session_factory, sample_image_data):
        """Test successful Stable Diffusion image generation."""
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "images": [sample_image_data]
        }
        mock_post.return_value = mock_response
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            image_data, model = worker._generate_stable_diffusion("test prompt")
            
            assert image_data == base64.b64decode(sample_image_data)
            assert model == "stable_diffusion_local"
            mock_post.assert_called_once()
    
    @patch('backend.app.distributed.workers.preview_image_worker.requests.post')
    def test_generate_google_success(self, mock_post, worker_config, mock_db_session_factory, sample_image_data):
        """Test successful Google Gemini image generation."""
        # Configure Google provider
        worker_config.stage_configs[ProcessingStage.GENERATE_PREVIEW_IMAGES]['provider_configs']['google'] = {
            'api_key': 'test-google-key'
        }
        
        # Mock successful response
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "candidates": [{
                "content": {
                    "parts": [
                        {"text": "Generated image"},
                        {"inlineData": {"data": sample_image_data}}
                    ]
                }
            }]
        }
        mock_post.return_value = mock_response
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            image_data, model = worker._generate_google("test prompt")
            
            assert image_data == base64.b64decode(sample_image_data)
            assert model == "gemini-2.0-flash-preview-image-generation"
            mock_post.assert_called_once()
    
    def test_generate_together_success(self, worker_config, mock_db_session_factory, sample_image_data):
        """Test successful Together AI image generation."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock OpenAI client
            with patch('backend.app.distributed.workers.preview_image_worker.OpenAI') as mock_openai:
                mock_client = Mock()
                mock_response = Mock()
                mock_response.data = [Mock()]
                mock_response.data[0].b64_json = sample_image_data
                mock_client.images.generate.return_value = mock_response
                mock_openai.return_value = mock_client
                
                image_data, model = worker._generate_together("test prompt")
                
                assert image_data == base64.b64decode(sample_image_data)
                assert model == "flux1_schnell_free_together"
    
    def test_generate_ionos_success(self, worker_config, mock_db_session_factory, sample_image_data):
        """Test successful IONOS image generation."""
        # Configure IONOS provider
        worker_config.stage_configs[ProcessingStage.GENERATE_PREVIEW_IMAGES]['provider_configs']['ionos'] = {
            'api_key': 'test-ionos-key'
        }
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock OpenAI client
            with patch('backend.app.distributed.workers.preview_image_worker.OpenAI') as mock_openai:
                mock_client = Mock()
                mock_response = Mock()
                mock_response.data = [Mock()]
                mock_response.data[0].b64_json = sample_image_data
                mock_client.images.generate.return_value = mock_response
                mock_openai.return_value = mock_client
                
                image_data, model = worker._generate_ionos("test prompt")
                
                assert image_data == base64.b64decode(sample_image_data)
                assert model == "flux1_schnell_ionos"
    
    def test_generate_image_provider_unsupported(self, worker_config, mock_db_session_factory):
        """Test unsupported provider error."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            with pytest.raises(ValueError, match="Unsupported image provider: unsupported"):
                worker._generate_image_provider("unsupported", "test prompt")
    
    def test_generate_image_with_fallback(self, worker_config, mock_db_session_factory):
        """Test image generation with provider fallback."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock first provider to fail, second to succeed
            with patch.object(worker, '_generate_image_provider') as mock_provider:
                mock_provider.side_effect = [
                    Exception("First provider failed"),
                    (b'image_data', 'test_model')
                ]
                
                image_data, model, provider = worker._generate_image_with_fallback("test prompt")
                
                assert image_data == b'image_data'
                assert model == 'test_model'
                assert provider == 'together'  # Second provider in list
                assert mock_provider.call_count == 2
    
    def test_generate_image_with_fallback_all_fail(self, worker_config, mock_db_session_factory):
        """Test image generation when all providers fail."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock all providers to fail
            with patch.object(worker, '_generate_image_provider', side_effect=Exception("Provider failed")):
                image_data, model, provider = worker._generate_image_with_fallback("test prompt")
                
                assert image_data is None
                assert model is None
                assert provider is None
    
    def test_update_entry_preview_image_success(self, worker_config, mock_db_session_factory):
        """Test successful database update for entry preview image."""
        mock_session = mock_db_session_factory()
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_preview_image("test-entry", b"image_data", "test_model", "test_provider")
            
            assert result is True
            mock_session.query.assert_called_once_with(Entry)
            mock_session.commit.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_preview_image_database_error(self, worker_config, mock_db_session_factory):
        """Test database error handling in entry update."""
        mock_session = mock_db_session_factory()
        mock_session.commit.side_effect = Exception("Database error")
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            result = worker._update_entry_preview_image("test-entry", b"image_data", "test_model", "test_provider")
            
            assert result is False
            mock_session.rollback.assert_called_once()
            mock_session.close.assert_called_once()
    
    def test_update_entry_preview_image_session_error(self, worker_config):
        """Test session creation error in entry update."""
        def failing_session_factory():
            raise Exception("Session creation failed")
        
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, failing_session_factory)
            
            result = worker._update_entry_preview_image("test-entry", b"image_data", "test_model", "test_provider")
            
            assert result is False
    
    def test_can_make_image_call(self, worker_config, mock_db_session_factory):
        """Test rate limiting check."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Currently always returns True (simple implementation)
            assert worker._can_make_image_call() is True
    
    def test_get_worker_specific_health(self, worker_config, mock_db_session_factory):
        """Test worker-specific health information."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.get_health_status') as mock_health:
                mock_health.return_value = {'status': 'healthy'}
                
                worker = PreviewImageWorker(worker_config, mock_db_session_factory)
                health = worker.get_worker_specific_health()
                
                assert health['worker_type'] == 'PreviewImageWorker'
                assert health['sentiment_threshold'] == 0.5
                assert health['ad_threshold'] == 0.5
                assert health['batch_optimize_images'] is False
                assert health['image_providers'] == ['stable_diffusion', 'together']
                assert ProcessingStage.GENERATE_PREVIEW_IMAGES.value in health['supported_stages']
    
    def test_cleanup_resources(self, worker_config, mock_db_session_factory):
        """Test resource cleanup."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Should not raise any exceptions
            worker.cleanup_resources()
    
    def test_provider_missing_api_key(self, worker_config, mock_db_session_factory):
        """Test providers with missing API keys."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Test Together without API key
            worker._provider_configs['together'] = {}  # Remove API key
            with pytest.raises(ValueError, match="Together AI API key not provided"):
                worker._generate_together("test prompt")
            
            # Test IONOS without API key
            worker._provider_configs['ionos'] = {}  # Remove API key
            with pytest.raises(ValueError, match="IONOS API key not provided"):
                worker._generate_ionos("test prompt")
            
            # Test Google without API key
            worker._provider_configs['google'] = {}  # Remove API key
            with pytest.raises(ValueError, match="Google API key not provided"):
                worker._generate_google("test prompt")
    
    def test_openai_import_error(self, worker_config, mock_db_session_factory):
        """Test handling of missing OpenAI library."""
        with patch('backend.app.distributed.workers.preview_image_worker.DistributedWorker.__init__'):
            worker = PreviewImageWorker(worker_config, mock_db_session_factory)
            
            # Mock ImportError for OpenAI
            with patch('backend.app.distributed.workers.preview_image_worker.OpenAI', side_effect=ImportError("No module named 'openai'")):
                with pytest.raises(Exception, match="OpenAI library not available"):
                    worker._generate_together("test prompt")
                
                with pytest.raises(Exception, match="OpenAI library not available"):
                    worker._generate_ionos("test prompt")