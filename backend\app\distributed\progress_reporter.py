"""
Progress reporting utility for distributed workers.

This module provides functionality for workers to report their stage progress
to the workers table, which can then be efficiently read by the dashboard.
"""

import json
import logging
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import text

from backend.app.distributed.processing_stage import ProcessingStage, get_stage_selection_criteria
from backend.app.models.models import Worker, Entry

logger = logging.getLogger(__name__)


class ProgressReporter:
    """Handles progress reporting for distributed workers."""
    
    def __init__(self, worker_id: str, db_session_factory):
        """
        Initialize progress reporter.
        
        Args:
            worker_id: The worker ID
            db_session_factory: Factory function to create database sessions
        """
        self.worker_id = worker_id
        self.db_session_factory = db_session_factory
        self.logger = logging.getLogger(f"{__name__}.{worker_id}")
    
    def update_stage_progress(self, stage: ProcessingStage, progress_data: Dict[str, int]) -> None:
        """
        Update progress for a specific stage.
        
        Args:
            stage: The processing stage
            progress_data: Dictionary with keys: pending, in_progress, completed, failed, total
        """
        try:
            with self.db_session_factory() as session:
                # Get current progress data
                worker = session.query(Worker).filter(Worker.worker_id == self.worker_id).first()
                if not worker:
                    self.logger.warning(f"Worker {self.worker_id} not found in database")
                    return
                
                # Parse existing progress data
                current_progress = {}
                if worker.progress:
                    try:
                        current_progress = json.loads(worker.progress)
                    except json.JSONDecodeError:
                        self.logger.warning(f"Invalid progress JSON for worker {self.worker_id}")
                        current_progress = {}
                
                # Update stage progress
                current_progress[stage.value] = {
                    **progress_data,
                    "last_updated": datetime.now(timezone.utc).isoformat()
                }
                
                # Save back to database
                worker.progress = json.dumps(current_progress)
                session.commit()
                
                self.logger.debug(f"Updated progress for stage {stage.value}: {progress_data}")
                
        except Exception as e:
            self.logger.error(f"Error updating stage progress: {e}")
    
    def calculate_and_report_progress(self, stages: List[ProcessingStage]) -> None:
        """
        Calculate and report progress for all assigned stages.
        
        Args:
            stages: List of processing stages this worker handles
        """
        try:
            with self.db_session_factory() as session:
                progress_data = {}
                
                for stage in stages:
                    try:
                        stage_progress = self._calculate_stage_progress(session, stage)
                        if stage_progress:
                            progress_data[stage.value] = {
                                **stage_progress,
                                "last_updated": datetime.now(timezone.utc).isoformat()
                            }
                    except Exception as e:
                        self.logger.warning(f"Error calculating progress for stage {stage.value}: {e}")
                        continue
                
                # Update worker progress
                if progress_data:
                    worker = session.query(Worker).filter(Worker.worker_id == self.worker_id).first()
                    if worker:
                        worker.progress = json.dumps(progress_data)
                        session.commit()
                        self.logger.debug(f"Reported progress for {len(progress_data)} stages")
                
        except Exception as e:
            self.logger.error(f"Error calculating and reporting progress: {e}")
    
    def _calculate_stage_progress(self, session: Session, stage: ProcessingStage) -> Optional[Dict[str, int]]:
        """
        Calculate progress for a specific stage using proper stage selection criteria.

        Args:
            session: Database session
            stage: Processing stage to calculate progress for

        Returns:
            Dictionary with progress counts or None if stage creates entries
        """
        try:
            # Handle special case: download_feeds creates entries, doesn't process them
            if stage == ProcessingStage.DOWNLOAD_FEEDS:
                return {
                    "pending": 37,  # Number of RSS sources
                    "in_progress": 0,
                    "completed": 0,
                    "failed": 0,
                    "total": 37
                }

            # Apply proper stage selection criteria like monolithic ETL
            from backend.app.distributed.processing_stage import get_stage_selection_criteria, resolve_filter_values
            from backend.app.core.config import settings
            from datetime import timedelta


            criteria = get_stage_selection_criteria(stage)

            # Build query with same logic as monolithic ETL
            query = session.query(Entry)

            # Apply mandatory fields (must not be None) - these entries are eligible for processing
            if criteria.mandatory_fields:
                for field in criteria.mandatory_fields:
                    if hasattr(Entry, field):
                        query = query.filter(getattr(Entry, field).isnot(None))

            # DO NOT apply analysis fields as filters to base query!
            # Analysis fields determine completion status, not eligibility
            # The base query should include ALL eligible entries (both completed and pending)

            # Apply filters with resolved values
            resolved_filters = resolve_filter_values(criteria.filters, settings)
            has_published_filter = False

            for field, operator, value in resolved_filters:
                if hasattr(Entry, field):
                    col = getattr(Entry, field)
                    if field == 'published':
                        has_published_filter = True

                    if operator == '>=':
                        query = query.filter(col >= value)
                    elif operator == '>':
                        query = query.filter(col > value)
                    elif operator == '<=':
                        query = query.filter(col <= value)
                    elif operator == '<':
                        query = query.filter(col < value)
                    elif operator == '==':
                        query = query.filter(col == value)
                    elif operator == '!=':
                        query = query.filter(col != value)

            # Apply default time filter if no published filter provided (same as monolithic ETL)
            if not has_published_filter:
                lookback_days = settings.NEWS_ETL_LOOKBACK_DAYS  # Use actual config value (2 days)
                from datetime import datetime as dt, timezone as tz
                cutoff_date = dt.now(tz.utc) - timedelta(days=lookback_days)
                query = query.filter(Entry.published >= cutoff_date)

            # Count stage statuses
            stage_stats = {
                'pending': 0,
                'in_progress': 0,
                'completed': 0,
                'failed': 0
            }

            # Count entries based on actual field completion criteria using optimized single query
            # This matches the monolithic ETL behavior where completion is determined by field presence
            from sqlalchemy import and_, func, case

            # Use single optimized query with conditional counting for performance
            if criteria.analysis_fields:
                # Build completion condition: ALL analysis fields must be NOT NULL
                completion_conditions = []
                for field in criteria.analysis_fields:
                    if hasattr(Entry, field):
                        col = getattr(Entry, field)
                        completion_conditions.append(col.isnot(None))

                if completion_conditions:
                    # Single query with conditional counting - much faster than separate queries
                    completion_condition = and_(*completion_conditions)

                    # Use the existing query and add conditional counting
                    result = query.with_entities(
                        func.count(case((completion_condition, 1))).label('completed'),
                        func.count(case((~completion_condition, 1))).label('pending')
                    ).first()

                    stage_stats['completed'] = result.completed or 0
                    stage_stats['pending'] = result.pending or 0
                else:
                    # No valid analysis fields, count all as pending
                    stage_stats['pending'] = query.count()
            else:
                # No analysis fields defined, count all as completed
                stage_stats['completed'] = query.count()

            # Calculate total as entries that would be pending if no work was done for this stage
            # This includes: pending + in_progress + completed (but not failed, as they wouldn't be processed)
            total = stage_stats['pending'] + stage_stats['in_progress'] + stage_stats['completed']

            return {
                **stage_stats,
                "total": total
            }

        except Exception as e:
            self.logger.error(f"Error calculating stage progress for {stage.value}: {e}")
            return None
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """
        Get current progress summary for this worker.
        
        Returns:
            Dictionary containing progress data for all stages
        """
        try:
            with self.db_session_factory() as session:
                worker = session.query(Worker).filter(Worker.worker_id == self.worker_id).first()
                if not worker or not worker.progress:
                    return {}
                
                try:
                    return json.loads(worker.progress)
                except json.JSONDecodeError:
                    self.logger.warning(f"Invalid progress JSON for worker {self.worker_id}")
                    return {}
                    
        except Exception as e:
            self.logger.error(f"Error getting progress summary: {e}")
            return {}
    
    def clear_progress(self) -> None:
        """Clear all progress data for this worker."""
        try:
            with self.db_session_factory() as session:
                worker = session.query(Worker).filter(Worker.worker_id == self.worker_id).first()
                if worker:
                    worker.progress = None
                    session.commit()
                    self.logger.debug("Cleared progress data")
                    
        except Exception as e:
            self.logger.error(f"Error clearing progress: {e}")


def aggregate_worker_progress(session: Session) -> Dict[str, Dict[str, int]]:
    """
    Aggregate progress data from all workers.

    For pending counts: Use the maximum across workers (since all workers see the same pending queue)
    For in_progress: Sum across workers (each worker processes different entries)
    For completed/failed: Sum across workers (cumulative totals)

    Args:
        session: Database session

    Returns:
        Dictionary mapping stage names to aggregated progress counts
    """
    try:
        # Get all workers with progress data
        workers = session.query(Worker).filter(
            Worker.progress.isnot(None),
            Worker.status.in_(['running', 'active'])
        ).all()

        # Collect progress data by stage
        stage_data = {}

        for worker in workers:
            try:
                progress_data = json.loads(worker.progress)

                for stage_name, stage_progress in progress_data.items():
                    if stage_name not in stage_data:
                        stage_data[stage_name] = {
                            'pending_values': [],
                            'in_progress_values': [],
                            'completed_values': [],
                            'failed_values': [],
                            'total_values': []
                        }

                    # Collect values from each worker
                    stage_data[stage_name]['pending_values'].append(stage_progress.get('pending', 0))
                    stage_data[stage_name]['in_progress_values'].append(stage_progress.get('in_progress', 0))
                    stage_data[stage_name]['completed_values'].append(stage_progress.get('completed', 0))
                    stage_data[stage_name]['failed_values'].append(stage_progress.get('failed', 0))
                    stage_data[stage_name]['total_values'].append(stage_progress.get('total', 0))

            except json.JSONDecodeError:
                logger.warning(f"Invalid progress JSON for worker {worker.worker_id}")
                continue

        # Aggregate using appropriate logic for each metric
        aggregated = {}
        for stage_name, data in stage_data.items():
            # Pending: Use maximum (all workers see the same pending queue)
            pending = max(data['pending_values']) if data['pending_values'] else 0

            # In progress: Sum (each worker processes different entries)
            in_progress = sum(data['in_progress_values'])

            # Completed: Sum (cumulative across all workers)
            completed = sum(data['completed_values'])

            # Failed: Sum (cumulative across all workers)
            failed = sum(data['failed_values'])

            # Total: Calculate as pending + in_progress + completed (entries that would be pending if no work done)
            # Use maximum for pending (shared queue), sum for others (worker-specific)
            total = pending + in_progress + completed

            aggregated[stage_name] = {
                'pending': pending,
                'in_progress': in_progress,
                'completed': completed,
                'failed': failed,
                'total': total
            }

        return aggregated

    except Exception as e:
        logger.error(f"Error aggregating worker progress: {e}")
        return {}
