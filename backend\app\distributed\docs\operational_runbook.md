# Distributed ETL Workers Operational Runbook

## Overview

This runbook provides step-by-step procedures for operating, monitoring, and troubleshooting the distributed ETL processing system.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Daily Operations](#daily-operations)
3. [Monitoring and Alerting](#monitoring-and-alerting)
4. [Common Issues and Solutions](#common-issues-and-solutions)
5. [Emergency Procedures](#emergency-procedures)
6. [Maintenance Procedures](#maintenance-procedures)
7. [Performance Optimization](#performance-optimization)
8. [Scaling Procedures](#scaling-procedures)

## System Architecture

### Components Overview

- **Worker Manager**: Coordinates and monitors distributed workers
- **Specialized Workers**: Process specific ETL stages
  - Feed Workers: Download RSS feeds
  - Full Text Workers: Extract article content
  - Analysis Workers: Sentiment analysis and classification
  - ML Workers: Translation and embeddings
  - Generation Workers: Content and image generation
  - Utility Workers: Duplicate checking and RSS generation
- **Cleanup Service**: Manages stale work claims
- **Database**: Stores entries and work queue state

### Key Metrics to Monitor

- Worker health and uptime
- Processing throughput (entries/minute)
- Queue sizes by stage
- Error rates and types
- Resource utilization (CPU, memory, disk)
- Database performance

## Daily Operations

### Morning Checklist

1. **Check System Status**
   ```bash
   # Check all workers are running
   python -m backend.app.distributed.cli list
   
   # Check overall system health
   python -m backend.app.distributed.cli status
   ```

2. **Review Queue Status**
   ```bash
   # Check work queue status
   python -m backend.app.distributed.cli queue-status
   ```

3. **Check for Alerts**
   - Review monitoring dashboard
   - Check error logs for critical issues
   - Verify no workers are in failed state

4. **Validate Processing Pipeline**
   - Ensure entries are flowing through all stages
   - Check for any stuck stages
   - Verify RSS feeds are being processed

### Evening Checklist

1. **Review Daily Metrics**
   - Total entries processed
   - Success rates by stage
   - Performance trends

2. **Check Resource Usage**
   - CPU and memory utilization
   - Disk space availability
   - Database performance

3. **Plan for Tomorrow**
   - Review any pending maintenance
   - Check for expected load changes

## Monitoring and Alerting

### Critical Alerts (Immediate Response Required)

#### Worker Down Alert
**Trigger**: Worker hasn't sent heartbeat in 5 minutes
**Response**:
1. Check worker logs:
   ```bash
   # Docker
   docker logs <worker-container>
   
   # Kubernetes
   kubectl logs <worker-pod> -n breaking-bright-distributed
   ```
2. Attempt to restart worker:
   ```bash
   # Docker
   docker restart <worker-container>
   
   # Kubernetes
   kubectl delete pod <worker-pod> -n breaking-bright-distributed
   ```
3. If restart fails, escalate to on-call engineer

#### High Error Rate Alert
**Trigger**: Error rate > 10% for any stage
**Response**:
1. Identify problematic stage:
   ```bash
   python -m backend.app.distributed.cli.worker_cli metrics --detailed
   ```
2. Check recent error logs
3. If external API issue, check service status
4. Consider temporarily disabling problematic stage

#### Database Connection Issues
**Trigger**: Multiple workers reporting DB connection failures
**Response**:
1. Check database server health
2. Verify connection strings and credentials
3. Check network connectivity
4. If database is down, follow database recovery procedures

#### Disk Space Critical
**Trigger**: Disk usage > 95%
**Response**:
1. Clean up temporary files and old logs
2. Archive or delete old data if safe
3. If critical, stop non-essential workers temporarily
4. Plan for storage expansion

### Warning Alerts (Response Within 1 Hour)

#### High Queue Backlog
**Trigger**: Queue size > 1000 entries for any stage
**Response**:
1. Check if workers for that stage are running
2. Consider scaling up workers for bottleneck stage
3. Investigate if specific entries are causing delays

#### High Resource Usage
**Trigger**: CPU > 80% or Memory > 85% for > 15 minutes
**Response**:
1. Identify resource-intensive workers
2. Consider reducing batch sizes
3. Scale horizontally if possible

## Common Issues and Solutions

### Issue: Workers Not Claiming Work

**Symptoms**:
- Queue has pending entries but workers are idle
- Workers show as healthy but not processing

**Diagnosis**:
```bash
# Check work queue status
python -m backend.app.distributed.cli.worker_cli queue-status

# Check worker configuration
python -m backend.app.distributed.cli.worker_cli status --worker-id <worker-id> --detailed
```

**Solutions**:
1. **Database Schema Issue**:
   - Verify `processing_status`, `claimed_by`, `claimed_at` columns exist
   - Check database indexes are in place

2. **Configuration Mismatch**:
   - Verify worker stages match available work
   - Check database connection string

3. **Stale Claims**:
   ```bash
   # Clean up stale claims
   python -m backend.app.distributed.cli.worker_cli cleanup --timeout 60
   ```

### Issue: High Processing Times

**Symptoms**:
- Entries taking much longer than expected to process
- Timeout errors increasing

**Diagnosis**:
```bash
# Run performance analysis
python -m backend.app.distributed.diagnostics.performance_tuner analyze --worker-id <worker-id>
```

**Solutions**:
1. **Network Issues** (for download stages):
   - Check internet connectivity
   - Verify external APIs are responsive
   - Consider reducing timeout values

2. **Resource Constraints**:
   - Check CPU and memory usage
   - Reduce batch sizes if resources are limited
   - Scale horizontally

3. **Database Performance**:
   - Check for slow queries
   - Verify indexes are optimized
   - Consider database tuning

### Issue: Memory Leaks

**Symptoms**:
- Worker memory usage continuously increasing
- Out of memory errors
- System becoming unresponsive

**Diagnosis**:
```bash
# Monitor memory usage over time
python -m backend.app.distributed.cli.worker_cli metrics --worker-id <worker-id> --since 1h
```

**Solutions**:
1. **Restart Affected Workers**:
   ```bash
   # Docker
   docker restart <worker-container>
   
   # Kubernetes
   kubectl delete pod <worker-pod> -n breaking-bright-distributed
   ```

2. **Reduce Batch Sizes**:
   - Lower memory usage per processing cycle
   - Update worker configuration

3. **Code Review**:
   - Check for unclosed resources
   - Verify proper cleanup after processing

### Issue: External API Rate Limiting

**Symptoms**:
- 429 (Too Many Requests) errors
- Processing failures for API-dependent stages

**Solutions**:
1. **Implement Rate Limiting**:
   - Add delays between API calls
   - Reduce batch sizes for API-heavy stages

2. **Retry Logic**:
   - Implement exponential backoff
   - Queue failed requests for later retry

3. **API Key Management**:
   - Rotate API keys if needed
   - Check API quotas and limits

## Emergency Procedures

### Complete System Shutdown

**When**: Critical security issue, data corruption risk, or major infrastructure failure

**Procedure**:
1. **Stop All Workers**:
   ```bash
   # Docker
   docker-compose -f docker-compose.distributed.yml down
   
   # Kubernetes
   kubectl delete namespace breaking-bright-distributed
   ```

2. **Verify Shutdown**:
   - Check no workers are processing
   - Verify all containers/pods are stopped

3. **Secure Database**:
   - Take database backup if safe
   - Document current state

4. **Communicate**:
   - Notify stakeholders
   - Document reason for shutdown

### Rollback to Monolithic System

**When**: Distributed system has critical issues and needs immediate fallback

**Procedure**:
1. **Stop Distributed Workers**:
   ```bash
   # Stop all distributed workers
   python -m backend.app.distributed.cli.worker_cli stop --all
   ```

2. **Clean Up Work Claims**:
   ```bash
   # Release all claimed work
   python -m backend.app.distributed.cli.worker_cli cleanup --timeout 0 --force
   ```

3. **Start Monolithic System**:
   ```bash
   # Start legacy ETL threads
   python -m etl.news_aggregator --mode monolithic
   ```

4. **Verify Operation**:
   - Check entries are being processed
   - Monitor for any data consistency issues

### Data Recovery

**When**: Data corruption or loss detected

**Procedure**:
1. **Stop All Processing**:
   - Prevent further data corruption
   - Isolate affected systems

2. **Assess Damage**:
   - Identify scope of data loss
   - Check backup availability

3. **Restore from Backup**:
   - Use most recent clean backup
   - Verify data integrity

4. **Replay Processing**:
   - Reprocess entries from last known good state
   - Monitor for consistency

## Maintenance Procedures

### Planned Worker Restart

**Purpose**: Apply configuration changes, updates, or clear memory leaks

**Procedure**:
1. **Graceful Shutdown**:
   ```bash
   # Stop specific worker gracefully
   python -m backend.app.distributed.cli stop --worker-id <worker-id>
   ```

2. **Wait for Completion**:
   - Allow current batch to complete
   - Verify work is released

3. **Apply Changes**:
   - Update configuration
   - Deploy new code if needed

4. **Restart Worker**:
   ```bash
   # Start worker with new configuration
   python -m backend.app.distributed.cli start --worker-id <worker-id> --config-file <config>
   ```

5. **Verify Operation**:
   - Check worker is claiming work
   - Monitor for errors

### Database Maintenance

**Purpose**: Apply schema changes, optimize performance, or backup data

**Procedure**:
1. **Schedule Maintenance Window**:
   - Coordinate with stakeholders
   - Plan for minimal impact time

2. **Backup Database**:
   ```bash
   # Create full backup
   pg_dump -h <host> -U <user> <database> > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

3. **Stop Workers** (if schema changes):
   ```bash
   # Stop all workers for schema changes
   python -m backend.app.distributed.cli.worker_cli stop --all
   ```

4. **Apply Changes**:
   - Run migration scripts
   - Update indexes
   - Optimize tables

5. **Restart System**:
   - Start workers with new schema
   - Verify operation

### Log Rotation and Cleanup

**Purpose**: Manage disk space and maintain log accessibility

**Procedure**:
1. **Archive Old Logs**:
   ```bash
   # Compress and archive logs older than 7 days
   find /var/log/workers -name "*.log" -mtime +7 -exec gzip {} \;
   ```

2. **Clean Up Temporary Files**:
   ```bash
   # Remove temporary processing files
   find /tmp -name "worker_*" -mtime +1 -delete
   ```

3. **Update Log Configuration**:
   - Ensure log rotation is configured
   - Verify log levels are appropriate

## Performance Optimization

### Batch Size Optimization

**Goal**: Find optimal batch size for each worker type

**Procedure**:
1. **Baseline Measurement**:
   ```bash
   # Measure current performance
   python -m backend.app.distributed.diagnostics.performance_tuner analyze
   ```

2. **Test Different Sizes**:
   - Start with current size ± 50%
   - Test for 1 hour each
   - Measure throughput and resource usage

3. **Apply Optimal Size**:
   - Update configuration with best performing size
   - Monitor for stability

### Resource Allocation Tuning

**Goal**: Optimize CPU and memory allocation

**Procedure**:
1. **Monitor Resource Usage**:
   ```bash
   # Check resource utilization patterns
   python -m backend.app.distributed.cli.worker_cli metrics --since 24h
   ```

2. **Adjust Limits**:
   - Increase limits for resource-constrained workers
   - Decrease limits for over-provisioned workers

3. **Test Changes**:
   - Apply changes gradually
   - Monitor for performance impact

### Database Query Optimization

**Goal**: Improve database performance for work claiming

**Procedure**:
1. **Identify Slow Queries**:
   ```sql
   -- Enable slow query logging
   SET log_min_duration_statement = 1000;
   ```

2. **Analyze Query Plans**:
   ```sql
   -- Check execution plans for work claiming queries
   EXPLAIN ANALYZE SELECT * FROM entries WHERE processing_status IS NULL LIMIT 25;
   ```

3. **Add Indexes**:
   ```sql
   -- Add composite indexes for common queries
   CREATE INDEX idx_entries_processing_status ON entries(processing_status, claimed_at);
   ```

## Scaling Procedures

### Horizontal Scaling (Add More Workers)

**When**: Queue backlogs, high processing demand

**Procedure**:
1. **Identify Bottleneck Stage**:
   ```bash
   # Find stages with highest queue sizes
   python -m backend.app.distributed.cli.worker_cli queue-status
   ```

2. **Scale Specific Worker Type**:
   ```bash
   # Docker Compose
   docker-compose -f docker-compose.distributed.yml up -d --scale feed-worker=4
   
   # Kubernetes
   kubectl scale deployment feed-workers --replicas=4 -n breaking-bright-distributed
   ```

3. **Monitor Impact**:
   - Check queue sizes decrease
   - Verify no resource contention
   - Monitor database performance

### Vertical Scaling (Increase Resources)

**When**: Workers are resource-constrained

**Procedure**:
1. **Update Resource Limits**:
   ```yaml
   # Kubernetes - update deployment
   resources:
     requests:
       memory: "1Gi"
       cpu: "500m"
     limits:
       memory: "2Gi"
       cpu: "1000m"
   ```

2. **Apply Changes**:
   ```bash
   # Kubernetes
   kubectl apply -f updated-deployment.yaml
   ```

3. **Verify Improvement**:
   - Check resource utilization
   - Monitor processing performance

### Auto-scaling Configuration

**Goal**: Automatically scale based on queue size or resource usage

**Procedure**:
1. **Configure HPA** (Kubernetes):
   ```yaml
   apiVersion: autoscaling/v2
   kind: HorizontalPodAutoscaler
   metadata:
     name: feed-workers-hpa
   spec:
     scaleTargetRef:
       apiVersion: apps/v1
       kind: Deployment
       name: feed-workers
     minReplicas: 2
     maxReplicas: 10
     metrics:
     - type: Resource
       resource:
         name: cpu
         target:
           type: Utilization
           averageUtilization: 70
   ```

2. **Test Auto-scaling**:
   - Generate load to trigger scaling
   - Verify scaling behavior
   - Adjust thresholds as needed

## Contact Information

### On-Call Escalation

1. **Level 1**: Operations Team
   - Email: <EMAIL>
   - Phone: ******-0123

2. **Level 2**: Engineering Team
   - Email: <EMAIL>
   - Phone: ******-0124

3. **Level 3**: System Architect
   - Email: <EMAIL>
   - Phone: ******-0125

### External Dependencies

- **Database Provider**: Contact info and SLA
- **Cloud Provider**: Support channels
- **API Providers**: Status pages and support

## Appendix

### Useful Commands Reference

```bash
# Worker management
python -m backend.app.distributed.cli start --worker-id worker-001
python -m backend.app.distributed.cli stop --worker-id worker-001
python -m backend.app.distributed.cli list
python -m backend.app.distributed.cli status

# Queue management
python -m backend.app.distributed.cli queue-status
python -m backend.app.distributed.cli cleanup --timeout 60

# Performance analysis (via CLI)
python -m backend.app.distributed.cli metrics --worker-id worker-001

# Docker commands
docker-compose -f docker-compose.distributed.yml ps
docker-compose -f docker-compose.distributed.yml logs -f
docker-compose -f docker-compose.distributed.yml restart

# Kubernetes commands
kubectl get pods -n breaking-bright-distributed
kubectl logs -f deployment/worker-manager -n breaking-bright-distributed
kubectl describe pod <pod-name> -n breaking-bright-distributed
```

### Configuration Files Locations

- Docker: `backend/deployment/docker/`
- Kubernetes: `backend/deployment/k8s/`
- Environment configs: `backend/deployment/config/`
- Worker configs: `backend/app/distributed/`

### Log Locations

- Docker: Container logs via `docker logs`
- Kubernetes: Pod logs via `kubectl logs`
- Application logs: `/var/log/workers/`
- System logs: `/var/log/syslog`