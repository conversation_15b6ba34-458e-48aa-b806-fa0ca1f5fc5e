<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            padding: 20px;
        }
        h1 {
            margin-bottom: 20px;
        }
        .entry-card {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .entry-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .entry-meta {
            color: #666;
            margin-bottom: 15px;
            font-size: 0.9em;
        }
        .entry-content {
            margin-top: 15px;
        }
        .nav-links {
            text-align: center;
            margin-bottom: 20px;
        }
        .nav-links a {
            margin: 0 10px;
            color: #3498db;
            text-decoration: none;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
        .sentiment-score {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }
        .positive {
            background-color: #28a745;
        }
        .negative {
            background-color: #dc3545;
        }
        .neutral {
            background-color: #6c757d;
        }
        .table-container {
            margin-top: 30px;
        }
        .reset-icon {
            cursor: pointer;
            color: #dc3545;
            margin-left: 5px;
            font-size: 0.9em;
        }
        .reset-icon:hover {
            color: #c82333;
        }
    </style>
</head>
<body>
    <div class="nav-links">
        <a href="/">Dashboard Home</a> | <a href="/sql_query">SQL Query Tool</a>
    </div>

    <div class="container">
        <h1>{{ title }}</h1>

        {% if is_single_entry %}
            <!-- Display a single entry -->
            {% for entry in entries %}
                <div class="entry-card">
                    <h2 class="entry-title">{{ entry.title }}</h2>
                    <div class="entry-meta">
                        <p>
                            <strong>Source:</strong> {{ entry.source_name or entry.source }}
                            {% if entry.source %}
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'source')" title="Reset source"></i>
                            {% endif %}
                            
                            {% if entry.category_name %}
                                | <strong>Category:</strong> {{ entry.category_name }}
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'iptc_newscode')" title="Reset category"></i>
                            {% endif %}
                            
                            | <strong>Published:</strong> {{ entry.published.strftime('%Y-%m-%d %H:%M:%S') }}
                        </p>
                        
                        {% if entry.llm_positive is not none %}
                            <p>
                                <strong>Sentiment:</strong>
                                <span class="sentiment-score {% if entry.llm_positive >= 0.7 %}positive{% elif entry.llm_positive <= 0.3 %}negative{% else %}neutral{% endif %}">
                                    {{ "%.2f"|format(entry.llm_positive) }}
                                </span>
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'llm_positive')" title="Reset sentiment"></i>
                            </p>
                        {% endif %}
                        
                        {% if entry.llm_reason_list %}
                            <div class="entry-content">
                                <h3>
                                    LLM Reason
                                </h3>
                                <p>{{ entry.llm_reason_list }}</p>
                            </div>
                        {% endif %}
                                        
                        {% if entry.dup_entry_id %}
                            <p>
                                <strong>Duplicate Group:</strong>
                                <a href="/entry_details?dup_entry_id={{ entry.dup_entry_id }}">{{ entry.dup_entry_id }}</a>
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'dup_entry_id')" title="Reset duplicate group"></i>
                            </p>
                        {% endif %}
                    </div>
                    
                    {% if entry.description %}
                        <div class="entry-content">
                            <h3>
                                Description
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'description')" title="Reset description"></i>
                            </h3>
                            <p>{{ entry.description }}</p>
                        </div>
                    {% endif %}

                    {% if entry.preview_img %}
                        <div class="entry-content">
                            <h3>
                                Preview Image
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'preview_img')" title="Reset preview image"></i>
                            </h3>
                            <img src="data:image/png;base64,{{ entry.preview_img }}" alt="Preview image" class="img-fluid" style="max-width: 688px;">
                        </div>
                    {% endif %}
                    
                    {% if entry.image_prompt %}
                        <div class="entry-content">
                            <h3>
                                Image Keywords
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'image_prompt')" title="Reset image keywords"></i>
                            </h3>
                            <p>{{ entry.image_prompt }}</p>
                        </div>
                    {% endif %}
                    
                    {% if entry.full_text %}
                        <div class="entry-content">
                            <h3>
                                Full Text
                                <i class="fas fa-undo reset-icon" onclick="resetProperty('{{ entry.entry_id }}', 'full_text')" title="Reset full text"></i>
                            </h3>
                            <p>{{ entry.full_text }}</p>
                        </div>
                    {% endif %}
                    
                    <div class="entry-content">
                        <a href="{{ entry.link }}" target="_blank" class="btn btn-primary">Read Original Article</a>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <!-- Display multiple entries using DataTables -->
            <div class="table-container">
                <table id="entries-table" class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>Entry ID</th>
                            <th>Title</th>
                            <th>Source</th>
                            <th>Published</th>
                            <th>Sentiment</th>
                            {% if show_similarity %}
                                <th>Similarity Score</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
{% for entry in entries %}
    <tr>
        <td>
            <a href="/entry_details?entry_id={{ entry.entry_id }}">{{ entry.entry_id }}</a>
        </td>
        <td>{{ entry.title }}</td>
        <td>{{ entry.source_name or entry.source }}</td>
        <td>{{ entry.published.strftime('%Y-%m-%d %H:%M:%S') }}</td>
        <td>
            {% if entry.llm_positive is not none %}
                <span class="sentiment-score {% if entry.llm_positive >= 0.7 %}positive{% elif entry.llm_positive <= 0.3 %}negative{% else %}neutral{% endif %}">
                    {{ "%.2f"|format(entry.llm_positive) }}
                </span>
            {% else %}
                N/A
            {% endif %}
        </td>
        {% if show_similarity %}
            <td>
                {% if entry.dup_entry_conf is not none %}
                    {{ "%.2f"|format(entry.dup_entry_conf) }}
                {% else %}
                    N/A
                {% endif %}
            </td>
        {% endif %}

    </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="text-center mt-3">
                <p>Showing {{ entries|length }} of {{ total_entries }} entries</p>
            </div>
        {% endif %}
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTables if we have a table
            if (document.getElementById('entries-table')) {
                $('#entries-table').DataTable({
                    paging: true,
                    ordering: true,
                    info: true,
                    searching: true,
                    lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                    pageLength: 50,
                    language: {
                        info: "Showing _START_ to _END_ of _TOTAL_ entries",
                        lengthMenu: "Show _MENU_ entries",
                        search: "Search:",
                        paginate: {
                            first: "First",
                            last: "Last",
                            next: "Next",
                            previous: "Previous"
                        }
                    },
                    responsive: true
                });
            }
        });
    </script>
    <script>
async function resetProperty(entryId, property) {
    if (!confirm(`Are you sure you want to reset the ${property} for this entry?`)) {
        return;
    }

    try {
        const response = await fetch('/reset_property', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                entry_id: entryId,
                property: property
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.success) {
            // Reload the page to show updated data
            window.location.reload();
        } else {
            alert(`Failed to reset property: ${result.error}`);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Failed to reset property. Please try again.');
    }
}
</script>
</body>
</html>
