"""
Work Queue Manager for distributed ETL processing.

This module provides atomic work claiming and management functionality
for distributed workers processing news entries.
"""

import logging
import json
from typing import List, Dict, Optional
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_
from sqlalchemy.exc import SQLAlchemyError

from backend.app.models.models import Entry, Worker
from backend.app.distributed.processing_stage import (
    ProcessingStage, ProcessingStatus, stage_has_cooldown, get_stage_cooldown_minutes
)
from backend.app.distributed.optimized_queries import OptimizedWorkQueries

logger = logging.getLogger(__name__)


class WorkQueueManager:
    """
    Manages work distribution and claiming for distributed ETL processing.
    
    Provides atomic operations for claiming batches of entries, releasing work,
    and cleaning up stale claims to ensure no work is lost or duplicated.
    """
    
    def __init__(self, db_session: Session):
        """Initialize with database session."""
        self.db = db_session
        self.optimized_queries = OptimizedWorkQueries(db_session)
    
    def claim_batch(self,
                   stage: ProcessingStage,
                   batch_size: int,
                   worker_id: str,
                   max_retries: int = 3) -> List[str]:
        """
        Atomically claim a batch of entries for processing.

        Args:
            stage: The processing stage to claim work for
            batch_size: Maximum number of entries to claim
            worker_id: Unique identifier for the worker claiming the batch
            max_retries: Maximum number of retries allowed for failed entries

        Returns:
            List of entry IDs that were successfully claimed

        Raises:
            SQLAlchemyError: If database transaction fails
        """
        try:
            # Use optimized query path with smart filtering
            available_entries = self.optimized_queries.find_claimable_entries_optimized(
                stage, batch_size, max_retries
            )

            if not available_entries:
                return []

            entry_ids = [entry.entry_id for entry in available_entries]

            # Use atomic claiming with proper transaction isolation
            return self.optimized_queries.atomic_claim_batch(
                entry_ids, worker_id, stage
            )

        except SQLAlchemyError as e:
            logger.error(f"Failed to claim batch for worker {worker_id}: {e}")
            self.db.rollback()
            raise
    
    def release_batch(self, entry_ids: List[str], worker_id: str) -> None:
        """
        Release a batch of claimed entries back to the work queue.
        
        Args:
            entry_ids: List of entry IDs to release
            worker_id: Worker ID that originally claimed the entries
            
        Raises:
            SQLAlchemyError: If database transaction fails
        """
        if not entry_ids:
            return
            
        try:
            with self.db.begin():
                # Only release entries claimed by this worker
                updated_count = (
                    self.db.query(Entry)
                    .filter(
                        and_(
                            Entry.entry_id.in_(entry_ids),
                            Entry.claimed_by == worker_id
                        )
                    )
                    .update({
                        Entry.claimed_by: None,
                        Entry.claimed_at: None
                    }, synchronize_session=False)
                )
                
                logger.info(
                    f"Worker {worker_id} released {updated_count} entries: "
                    f"{entry_ids[:5]}..."
                )
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to release batch for worker {worker_id}: {e}")
            self.db.rollback()
            raise
    
    def mark_completed(self,
                      entry_ids: List[str],
                      stage: ProcessingStage,
                      worker_id: str) -> None:
        """
        Mark entries as completed for a specific processing stage.

        Args:
            entry_ids: List of entry IDs to mark as completed
            stage: The processing stage that was completed
            worker_id: Worker ID that completed the processing

        Raises:
            SQLAlchemyError: If database transaction fails
        """
        if not entry_ids:
            return

        try:
            # Use optimized bulk update
            self.optimized_queries.bulk_update_completion(entry_ids, stage, worker_id)

        except SQLAlchemyError as e:
            logger.error(
                f"Failed to mark entries completed for worker {worker_id}: {e}"
            )
            self.db.rollback()
            raise
    
    def mark_failed(self,
                   entry_ids: List[str],
                   stage: ProcessingStage,
                   worker_id: str,
                   error_message: str) -> None:
        """
        Mark entries as failed for a specific processing stage.
        
        Args:
            entry_ids: List of entry IDs to mark as failed
            stage: The processing stage that failed
            worker_id: Worker ID that encountered the failure
            error_message: Description of the error that occurred
            
        Raises:
            SQLAlchemyError: If database transaction fails
        """
        if not entry_ids:
            return
            
        try:
            with self.db.begin():
                # Get current entries and update their processing status
                entries = (
                    self.db.query(Entry)
                    .filter(
                        and_(
                            Entry.entry_id.in_(entry_ids),
                            Entry.claimed_by == worker_id
                        )
                    )
                    .all()
                )
                
                for entry in entries:
                    # Parse current processing status
                    processing_status = ProcessingStatus.from_json(entry.processing_status)
                    
                    # Mark stage as failed
                    processing_status.mark_stage_failed(stage, worker_id, error_message)
                    
                    # Update entry
                    entry.processing_status = processing_status.to_json()
                    entry.claimed_by = None
                    entry.claimed_at = None
                    entry.last_error = error_message
                    entry.retry_count = processing_status.get_retry_count(stage)
                
                logger.warning(
                    f"Worker {worker_id} marked {len(entries)} entries as failed "
                    f"for stage {stage.value}: {error_message}"
                )
                
        except SQLAlchemyError as e:
            logger.error(
                f"Failed to mark entries failed for worker {worker_id}: {e}"
            )
            self.db.rollback()
            raise
    
    def cleanup_stale_claims(self,
                            timeout_minutes: int = 30) -> int:
        """
        Clean up stale claims from workers that may have crashed or become unresponsive.

        Args:
            timeout_minutes: Number of minutes after which a claim is considered stale

        Returns:
            Number of stale claims that were cleaned up

        Raises:
            SQLAlchemyError: If database transaction fails
        """
        try:
            # Use optimized batch cleanup
            return self.optimized_queries.efficient_stale_cleanup(timeout_minutes)

        except SQLAlchemyError as e:
            logger.error(f"Failed to cleanup stale claims: {e}")
            self.db.rollback()
            raise

    def is_singleton_stage_claimed(self, stage: ProcessingStage) -> bool:
        """
        Check if a singleton stage is already being processed by another worker.

        Args:
            stage: The singleton processing stage to check

        Returns:
            True if the stage is already being processed, False otherwise

        Raises:
            SQLAlchemyError: If database query fails
        """
        try:
            stage_name = stage.value

            # Query for any worker that has this stage in their singleton_stages
            worker = (
                self.db.query(Worker)
                .filter(
                    and_(
                        Worker.status == 'running',
                        Worker.singleton_stages.like(f'%{stage_name}%')
                    )
                )
                .first()
            )

            return worker is not None

        except SQLAlchemyError as e:
            logger.error(f"Failed to check singleton stage claim for {stage.value}: {e}")
            raise

    def claim_singleton_stage(self, stage: ProcessingStage, worker_id: str) -> bool:
        """
        Attempt to claim a singleton stage for processing.

        Args:
            stage: The singleton processing stage to claim
            worker_id: Worker ID attempting to claim the stage

        Returns:
            True if the stage was successfully claimed, False if already claimed

        Raises:
            SQLAlchemyError: If database transaction fails
        """
        try:
            # Check if we're already in a transaction
            if self.db.in_transaction():
                # Use the existing transaction
                return self._claim_singleton_stage_in_transaction(stage, worker_id)
            else:
                # Start a new transaction
                with self.db.begin():
                    return self._claim_singleton_stage_in_transaction(stage, worker_id)

        except SQLAlchemyError as e:
            logger.error(f"Failed to claim singleton stage {stage.value} for worker {worker_id}: {e}")
            if not self.db.in_transaction():
                self.db.rollback()
            raise

    def _claim_singleton_stage_in_transaction(self, stage: ProcessingStage, worker_id: str) -> bool:
        """
        Internal method to claim singleton stage within an existing transaction.

        Args:
            stage: The singleton processing stage to claim
            worker_id: Worker ID attempting to claim the stage

        Returns:
            True if the stage was successfully claimed, False if already claimed
        """
        stage_name = stage.value

        # First check if any other worker is already processing this stage
        existing_worker = (
            self.db.query(Worker)
            .filter(
                and_(
                    Worker.status == 'running',
                    Worker.singleton_stages.like(f'%{stage_name}%'),
                    Worker.worker_id != worker_id
                )
            )
            .first()
        )

        if existing_worker:
            logger.debug(f"Singleton stage {stage_name} already claimed by worker {existing_worker.worker_id}")
            return False

        # Get the current worker
        worker = (
            self.db.query(Worker)
            .filter(Worker.worker_id == worker_id)
            .first()
        )

        if not worker:
            logger.error(f"Worker {worker_id} not found in database")
            return False

        # Add the stage to the worker's singleton_stages
        current_stages = worker.singleton_stages or ""
        stage_list = [s.strip() for s in current_stages.split(',') if s.strip()]

        if stage_name not in stage_list:
            stage_list.append(stage_name)
            worker.singleton_stages = ','.join(stage_list)

        logger.info(f"Worker {worker_id} claimed singleton stage {stage_name}")
        return True

    def release_singleton_stage(self, stage: ProcessingStage, worker_id: str) -> None:
        """
        Release a singleton stage claim.

        Args:
            stage: The singleton processing stage to release
            worker_id: Worker ID releasing the stage

        Raises:
            SQLAlchemyError: If database transaction fails
        """
        try:
            # Check if we're already in a transaction
            if self.db.in_transaction():
                # Use the existing transaction
                self._release_singleton_stage_in_transaction(stage, worker_id)
            else:
                # Start a new transaction
                with self.db.begin():
                    self._release_singleton_stage_in_transaction(stage, worker_id)

        except SQLAlchemyError as e:
            logger.error(f"Failed to release singleton stage {stage.value} for worker {worker_id}: {e}")
            if not self.db.in_transaction():
                self.db.rollback()
            raise

    def _release_singleton_stage_in_transaction(self, stage: ProcessingStage, worker_id: str) -> None:
        """
        Internal method to release singleton stage within an existing transaction.

        Args:
            stage: The singleton processing stage to release
            worker_id: Worker ID releasing the stage
        """
        stage_name = stage.value

        # Get the current worker
        worker = (
            self.db.query(Worker)
            .filter(Worker.worker_id == worker_id)
            .first()
        )

        if not worker:
            logger.warning(f"Worker {worker_id} not found when releasing singleton stage {stage_name}")
            return

        # Remove the stage from the worker's singleton_stages
        current_stages = worker.singleton_stages or ""
        stage_list = [s.strip() for s in current_stages.split(',') if s.strip()]

        if stage_name in stage_list:
            stage_list.remove(stage_name)
            worker.singleton_stages = ','.join(stage_list) if stage_list else None

            logger.info(f"Worker {worker_id} released singleton stage {stage_name}")

    def is_stage_in_cooldown(self, stage: ProcessingStage) -> bool:
        """
        Check if a stage is currently in cooldown period by checking all workers.

        Args:
            stage: The processing stage to check

        Returns:
            True if the stage is in cooldown, False otherwise
        """
        if not stage_has_cooldown(stage):
            return False

        stage_name = stage.value
        cooldown_minutes = get_stage_cooldown_minutes(stage)
        current_time = datetime.now(timezone.utc)

        # Check all workers for the most recent execution of this stage
        workers = (
            self.db.query(Worker)
            .filter(Worker.singleton_executions.isnot(None))
            .all()
        )

        latest_execution_time = None

        for worker in workers:
            if worker.singleton_executions:
                try:
                    executions = json.loads(worker.singleton_executions)
                    if stage_name in executions and executions[stage_name]:
                        execution_time = datetime.fromisoformat(executions[stage_name].replace('Z', '+00:00'))
                        if latest_execution_time is None or execution_time > latest_execution_time:
                            latest_execution_time = execution_time
                except (json.JSONDecodeError, ValueError) as e:
                    logger.warning(f"Failed to parse singleton_executions for worker {worker.worker_id}: {e}")
                    continue

        if not latest_execution_time:
            # Never executed, not in cooldown
            return False

        # Check if cooldown period has elapsed
        cooldown_end = latest_execution_time + timedelta(minutes=cooldown_minutes)
        is_in_cooldown = current_time < cooldown_end

        if is_in_cooldown:
            remaining_minutes = (cooldown_end - current_time).total_seconds() / 60
            logger.debug(f"Stage {stage_name} is in cooldown for {remaining_minutes:.1f} more minutes")

        return is_in_cooldown

    def record_stage_execution(self, stage: ProcessingStage, worker_id: str) -> None:
        """
        Record the execution of a stage for cooldown tracking.

        Args:
            stage: The processing stage that was executed
            worker_id: Worker ID that executed the stage
        """
        if not stage_has_cooldown(stage):
            return

        stage_name = stage.value
        current_time = datetime.now(timezone.utc)

        try:
            # Check if we're already in a transaction
            if self.db.in_transaction():
                self._record_stage_execution_in_transaction(stage_name, worker_id, current_time)
            else:
                # Start a new transaction
                with self.db.begin():
                    self._record_stage_execution_in_transaction(stage_name, worker_id, current_time)

        except SQLAlchemyError as e:
            logger.error(f"Failed to record stage execution for {stage_name}: {e}")
            if not self.db.in_transaction():
                self.db.rollback()
            raise

    def _record_stage_execution_in_transaction(self, stage_name: str, worker_id: str, execution_time: datetime) -> None:
        """
        Internal method to record stage execution within an existing transaction.

        Args:
            stage_name: Name of the stage
            worker_id: Worker ID that executed the stage
            execution_time: Time of execution
        """
        # Get the worker record
        worker = (
            self.db.query(Worker)
            .filter(Worker.worker_id == worker_id)
            .first()
        )

        if not worker:
            logger.error(f"Worker {worker_id} not found when recording stage execution")
            return

        # Parse existing singleton executions or create new dict
        executions = {}
        if worker.singleton_executions:
            try:
                executions = json.loads(worker.singleton_executions)
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse singleton_executions for worker {worker_id}, creating new")
                executions = {}

        # Update the execution time for this stage
        executions[stage_name] = execution_time.isoformat()

        # Save back to worker record
        worker.singleton_executions = json.dumps(executions)

        logger.info(f"Recorded execution of stage {stage_name} by worker {worker_id} at {execution_time}")
    
    def get_queue_stats(self,
                       stage: ProcessingStage) -> Dict[str, int]:
        """
        Get statistics about the work queue for a specific stage.

        Args:
            stage: The processing stage to get statistics for

        Returns:
            Dictionary with queue statistics
        """
        try:
            # Use optimized metrics collection
            all_metrics = self.optimized_queries.get_queue_metrics_optimized([stage])
            return all_metrics.get(stage.value, {})

        except SQLAlchemyError as e:
            logger.error(f"Failed to get queue stats: {e}")
            return {}
    
    def get_comprehensive_metrics(self) -> Dict[str, Dict[str, int]]:
        """
        Get comprehensive metrics for all processing stages.
        
        Returns:
            Dictionary mapping stage names to their metrics
        """
        try:
            return self.optimized_queries.get_queue_metrics_optimized()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get comprehensive metrics: {e}")
            return {}
    
    def find_worker_entries(self, 
                           worker_id: str,
                           include_completed: bool = False) -> List[Entry]:
        """
        Find all entries associated with a specific worker.
        
        Args:
            worker_id: Worker ID to search for
            include_completed: Whether to include completed entries
            
        Returns:
            List of entries associated with the worker
        """
        try:
            return self.optimized_queries.find_entries_by_worker(
                worker_id, include_completed
            )
        except SQLAlchemyError as e:
            logger.error(f"Failed to find worker entries: {e}")
            return []
    
