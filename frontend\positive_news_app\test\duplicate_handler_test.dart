import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/services/duplicate_handler_service.dart';
import 'package:breakingbright/models/news_model.dart';

void main() {
  group('DuplicateHandlerService Tests', () {
    test('removeDuplicates keeps most recent entry per duplicate group', () {
      final testNewsItems = [
        EntryWithSource(
          entryId: 'entry_1',
          title: 'Breaking News: Important Event',
          link: 'https://source1.com/news1',
          source: 'source1',
          published: DateTime.parse('2024-01-01T12:00:00Z'),
          dupEntryId: 'dup_group_1',
        ),
        EntryWithSource(
          entryId: 'entry_2',
          title: 'Breaking News: Important Event Continues',
          link: 'https://source2.com/news2',
          source: 'source2',
          published: DateTime.parse('2024-01-01T11:00:00Z'), // Earlier
          dupEntryId: 'dup_group_1', // Same duplicate group
        ),
        EntryWithSource(
          entryId: 'entry_3',
          title: 'Different News Story',
          link: 'https://source1.com/news3',
          source: 'source1',
          published: DateTime.parse('2024-01-01T10:00:00Z'),
          dupEntryId: 'dup_group_2',
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(testNewsItems);

      // Should have 2 items: most recent from dup_group_1 and dup_group_2
      expect(result.length, 2);

      // Should keep entry_1 (most recent from dup_group_1)
      expect(result.any((item) => item.entryId == 'entry_1'), true);
      expect(result.any((item) => item.entryId == 'entry_2'), false);

      // Should keep entry_3 (only one in dup_group_2)
      expect(result.any((item) => item.entryId == 'entry_3'), true);
    });

    test('removeDuplicates handles empty list', () {
      final result = DuplicateHandlerService.removeDuplicates([]);
      expect(result, isEmpty);
    });

    test('removeDuplicates handles list with no duplicates', () {
      final uniqueItems = [
        EntryWithSource(
          entryId: 'unique_1',
          title: 'Unique News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now(),
          dupEntryId: null,
        ),
        EntryWithSource(
          entryId: 'unique_2',
          title: 'Unique News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now(),
          dupEntryId: null,
        ),
      ];

      final result = DuplicateHandlerService.removeDuplicates(uniqueItems);
      expect(result.length, 2);
    });

    test('getDuplicateStats provides correct statistics', () {
      final testItems = [
        EntryWithSource(
          entryId: 'entry_1',
          title: 'News 1',
          link: 'https://example.com/1',
          source: 'source1',
          published: DateTime.now(),
          dupEntryId: 'group_1',
        ),
        EntryWithSource(
          entryId: 'entry_2',
          title: 'News 2',
          link: 'https://example.com/2',
          source: 'source2',
          published: DateTime.now(),
          dupEntryId: 'group_1',
        ),
        EntryWithSource(
          entryId: 'entry_3',
          title: 'News 3',
          link: 'https://example.com/3',
          source: 'source3',
          published: DateTime.now(),
          dupEntryId: null,
        ),
      ];

      final stats = DuplicateHandlerService.getDuplicateStats(testItems);

      expect(stats['total_items'], 3);
      expect(stats['unique_items'], 1); // entry_3 has no dupEntryId
      expect(stats['duplicate_groups'], 1); // group_1
      expect(stats['duplicate_items'], 1); // One duplicate item
      expect(stats['final_count'], 2); // After removing duplicates
    });
  });
}
