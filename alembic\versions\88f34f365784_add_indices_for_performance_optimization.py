"""Add indices for performance optimization

Revision ID: 88f34f365784
Revises: 0859eceb52a2
Create Date: 2025-04-21 23:49:14.721523

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '88f34f365784'
down_revision: Union[str, None] = '0859eceb52a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('categories', 'iptc_newscode',
               existing_type=sa.VARCHAR(length=4000),
               nullable=False)
    op.drop_index('iptc_newscode', table_name='categories')
    op.create_index('idx_categories_iptc', 'categories', ['iptc_newscode'], unique=False)
    op.create_index('idx_entries_dup_entry_id', 'entries', ['dup_entry_id'], unique=False)
    op.create_index('idx_entries_dup_published', 'entries', ['dup_entry_id', sa.text('published DESC')], unique=False)
    op.create_index('idx_entries_dup_source', 'entries', ['dup_entry_id', 'source'], unique=False)
    op.create_index('idx_entries_iptc_newscode', 'entries', ['iptc_newscode'], unique=False)
    op.create_index('idx_entries_published', 'entries', [sa.text('published DESC')], unique=False)
    op.create_index('idx_entries_source', 'entries', ['source'], unique=False)
    op.alter_column('sources', 'source',
               existing_type=sa.VARCHAR(length=200),
               nullable=False)
    op.drop_index('source', table_name='sources')
    op.create_index('idx_sources_source', 'sources', ['source'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_sources_source', table_name='sources')
    op.create_index('source', 'sources', ['source'], unique=False)
    op.alter_column('sources', 'source',
               existing_type=sa.VARCHAR(length=200),
               nullable=True)
    op.drop_index('idx_entries_source', table_name='entries')
    op.drop_index('idx_entries_published', table_name='entries')
    op.drop_index('idx_entries_iptc_newscode', table_name='entries')
    op.drop_index('idx_entries_dup_source', table_name='entries')
    op.drop_index('idx_entries_dup_published', table_name='entries')
    op.drop_index('idx_entries_dup_entry_id', table_name='entries')
    op.drop_index('idx_categories_iptc', table_name='categories')
    op.create_index('iptc_newscode', 'categories', ['iptc_newscode'], unique=True)
    op.alter_column('categories', 'iptc_newscode',
               existing_type=sa.VARCHAR(length=4000),
               nullable=True)
    # ### end Alembic commands ###
