apiVersion: v1
kind: Secret
metadata:
  name: breaking-bright-secrets
  annotations:
    kubernetes.io/service-account.name: default
  labels:
    app: breaking-bright-backend
type: Opaque
data:
  database-url: b3JhY2xlK29yYWNsZWRiOi8vREVWWFBTMTU6Ulp0NzJ4RXZDWWN6a3JkQC8/ZHNuPShkZXNjcmlwdGlvbj0ocmV0cnlfY291bnQ9MjApKHJldHJ5X2RlbGF5PTMpKGFkZHJlc3M9KHByb3RvY29sPXRjcHMpKHBvcnQ9MTUyMSkoaG9zdD1hZGIuZXUtZnJhbmtmdXJ0LTEub3JhY2xlY2xvdWQuY29tKSkoY29ubmVjdF9kYXRhPShzZXJ2aWNlX25hbWU9ZzIzNDE2ZGU3NjA5M2I1X2dvb2RuZXdzX2xvdy5hZGIub3JhY2xlY2xvdWQuY29tKSkoc2VjdXJpdHk9KHNzbF9zZXJ2ZXJfZG5fbWF0Y2g9eWVzKSkp
