"""
Integration tests for distributed processing data structures.
"""

import pytest
from backend.app.distributed import (
    ProcessingStage, ProcessingStatus, WorkerConfig,
    create_download_worker_config, get_next_pending_stage, can_process_stage
)


class TestDistributedIntegration:
    """Test integration between processing stages and worker configuration."""
    
    def test_worker_config_with_processing_status(self):
        """Test worker configuration working with processing status."""
        # Create a download worker
        worker_config = create_download_worker_config("integration-worker")
        
        # Create processing status
        status = ProcessingStatus()
        
        # Worker should be able to process download stage
        assert worker_config.has_stage(ProcessingStage.DOWNLOAD_FULL_TEXT)
        assert can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Get next stage for this worker
        next_stage = get_next_pending_stage(status, worker_config.stages)
        assert next_stage == ProcessingStage.DOWNLOAD_FULL_TEXT
        
        # Mark stage as completed
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, worker_config.worker_id)
        
        # No more stages for this worker
        next_stage = get_next_pending_stage(status, worker_config.stages)
        assert next_stage is None
    
    def test_multi_stage_worker_workflow(self):
        """Test workflow with multi-stage worker."""
        # Create analysis worker
        worker_config = WorkerConfig(
            worker_id="analysis-worker",
            stages=[ProcessingStage.SENTIMENT_ANALYSIS, ProcessingStage.LLM_ANALYSIS]
        )
        
        # Create processing status with download already completed
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, "other-worker")
        
        # Worker should process sentiment analysis first
        next_stage = get_next_pending_stage(status, worker_config.stages)
        assert next_stage == ProcessingStage.SENTIMENT_ANALYSIS
        
        # Process sentiment analysis
        status.mark_stage_in_progress(ProcessingStage.SENTIMENT_ANALYSIS, worker_config.worker_id)
        status.mark_stage_completed(ProcessingStage.SENTIMENT_ANALYSIS, worker_config.worker_id)
        
        # Now should process LLM analysis
        next_stage = get_next_pending_stage(status, worker_config.stages)
        assert next_stage == ProcessingStage.LLM_ANALYSIS
        
        # Process LLM analysis
        status.mark_stage_in_progress(ProcessingStage.LLM_ANALYSIS, worker_config.worker_id)
        status.mark_stage_completed(ProcessingStage.LLM_ANALYSIS, worker_config.worker_id)
        
        # No more stages for this worker
        next_stage = get_next_pending_stage(status, worker_config.stages)
        assert next_stage is None
    
    def test_worker_config_serialization_with_processing_status(self):
        """Test serialization of worker config and processing status."""
        # Create worker config
        worker_config = WorkerConfig(
            worker_id="serialization-test",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS],
            batch_size=50
        )
        
        # Create processing status
        status = ProcessingStatus()
        status.mark_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT, worker_config.worker_id)
        
        # Serialize both
        config_json = worker_config.to_json()
        status_json = status.to_json()
        
        # Deserialize both
        restored_config = WorkerConfig.from_json(config_json)
        restored_status = ProcessingStatus.from_json(status_json)
        
        # Verify they work together
        assert restored_config.worker_id == worker_config.worker_id
        assert restored_config.stages == worker_config.stages
        assert restored_status.is_stage_completed(ProcessingStage.DOWNLOAD_FULL_TEXT)
        
        # Should be able to process next stage
        next_stage = get_next_pending_stage(restored_status, restored_config.stages)
        assert next_stage == ProcessingStage.SENTIMENT_ANALYSIS
    
    def test_stage_specific_configuration(self):
        """Test stage-specific configuration in worker config."""
        # Create worker with stage-specific config
        worker_config = WorkerConfig(
            worker_id="stage-config-test",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.LLM_ANALYSIS]
        )
        
        # Set stage-specific configurations
        worker_config.set_stage_config(ProcessingStage.DOWNLOAD_FULL_TEXT, "timeout", 30)
        worker_config.set_stage_config(ProcessingStage.LLM_ANALYSIS, "model", "gpt-4")
        worker_config.set_stage_config(ProcessingStage.LLM_ANALYSIS, "max_tokens", 1000)
        
        # Verify stage configs
        assert worker_config.get_stage_config(ProcessingStage.DOWNLOAD_FULL_TEXT, "timeout") == 30
        assert worker_config.get_stage_config(ProcessingStage.LLM_ANALYSIS, "model") == "gpt-4"
        assert worker_config.get_stage_config(ProcessingStage.LLM_ANALYSIS, "max_tokens") == 1000
        
        # Non-existent config should return default
        assert worker_config.get_stage_config(ProcessingStage.SENTIMENT_ANALYSIS, "timeout", 60) == 60
    
    def test_failed_stage_retry_logic(self):
        """Test retry logic for failed stages."""
        worker_config = WorkerConfig(
            worker_id="retry-test",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            max_retries=3
        )
        
        status = ProcessingStatus()
        
        # Initially can process
        assert can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT, worker_config.max_retries)
        
        # Fail the stage multiple times
        for i in range(worker_config.max_retries):
            status.mark_stage_failed(ProcessingStage.DOWNLOAD_FULL_TEXT, worker_config.worker_id, f"Error {i+1}")
            
            # Should still be processable until max retries reached
            if i < worker_config.max_retries - 1:
                assert can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT, worker_config.max_retries)
            else:
                assert not can_process_stage(status, ProcessingStage.DOWNLOAD_FULL_TEXT, worker_config.max_retries)
        
        # Verify retry count
        assert status.get_retry_count(ProcessingStage.DOWNLOAD_FULL_TEXT) == worker_config.max_retries


if __name__ == "__main__":
    pytest.main([__file__])