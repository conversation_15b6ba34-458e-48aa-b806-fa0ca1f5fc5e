# Database Performance Analysis & Parallel Worker Architecture

## 1. Database Performance Analysis

### Current Performance Issues

Based on the logs, we have identified significant performance disparities:

**Fast Stages (0.5-0.8s)**:
- `download_full_text` (CLOB field): 0.56s
- `llm_analysis` (NUMBER fields): 0.77s  
- `translate_text` (CLOB field): 0.69s
- `generate_rss_feed` (minimal data): 0.05s

**Slow Stages (19-27s)**:
- `compute_embeddings` (BLOB field): 19.11s
- `generate_image_prompts` (CLOB field): 26.88s
- `generate_preview_images` (BLOB field): 26.74s
- `iptc_classification` (VARCHAR2 field): 19.22s
- `duplicate_check` (VARCHAR2 fields): 26.21s

### Root Cause Analysis

#### 1. **BLOB Field Performance Issue**
- **Problem**: Oracle BLOB fields (`embedding`, `preview_img`) are extremely slow with conditional counting
- **Cause**: `func.count(case((col.isnot(None), 1)))` forces Oracle to examine BLOB content for NULL checks
- **Impact**: 19-27 seconds per query

#### 2. **Missing Specialized Indices**
Current indices focus on general queries but lack field-specific NULL/NOT NULL optimization:

```sql
-- Current indices (from models.py)
Index('idx_entries_published', text('published DESC')),
Index('idx_entries_iptc_newscode', 'iptc_newscode'),
Index('idx_entries_source', 'source'),
-- Missing: Field-specific NULL indices for progress calculation
```

#### 3. **Inefficient Query Pattern**
The conditional counting approach is suboptimal for Oracle:
```python
# SLOW: Forces full field evaluation
func.count(case((col.isnot(None), 1))).label('completed')

# BETTER: Separate optimized queries
completed = query.filter(col.isnot(None)).count()
pending = query.filter(col.is_(None)).count()
```

### Performance Optimization Strategy

#### 1. **Add Field-Specific Indices**
```sql
-- Add NULL-optimized indices for progress calculation
CREATE INDEX idx_entries_full_text_null ON entries(CASE WHEN full_text IS NULL THEN 1 END);
CREATE INDEX idx_entries_embedding_null ON entries(CASE WHEN embedding IS NULL THEN 1 END);
CREATE INDEX idx_entries_llm_positive_null ON entries(CASE WHEN llm_positive IS NULL THEN 1 END);
CREATE INDEX idx_entries_iptc_newscode_null ON entries(CASE WHEN iptc_newscode IS NULL THEN 1 END);
CREATE INDEX idx_entries_dup_entry_id_null ON entries(CASE WHEN dup_entry_id IS NULL THEN 1 END);
```

#### 2. **Optimize Query Strategy by Field Type**
```python
def get_optimized_progress_count(query, field_name):
    col = getattr(Entry, field_name)
    
    # Strategy 1: For BLOB fields, use EXISTS subqueries (faster)
    if field_name in ['embedding', 'preview_img']:
        completed = query.filter(col.isnot(None)).count()
        total = query.count()
        pending = total - completed
        
    # Strategy 2: For indexed fields, use separate queries
    elif field_name in ['iptc_newscode', 'dup_entry_id']:
        completed = query.filter(col.isnot(None)).count()
        pending = query.filter(col.is_(None)).count()
        
    # Strategy 3: For simple fields, use conditional counting
    else:
        result = query.with_entities(
            func.count(case((col.isnot(None), 1))).label('completed'),
            func.count(case((col.is_(None), 1))).label('pending')
        ).first()
        completed, pending = result.completed or 0, result.pending or 0
    
    return completed, pending
```

#### 3. **Implement Query Caching**
```python
from functools import lru_cache
from datetime import datetime, timedelta

@lru_cache(maxsize=128)
def get_cached_progress(stage_name: str, cache_key: str):
    # Cache results for 30 seconds to avoid repeated expensive queries
    pass
```

## 2. Parallel Worker Architecture

### Current Sequential Processing Issue

The current `_worker_loop` processes stages sequentially:
```python
# CURRENT: Sequential processing
for stage in self.config.stages:
    success = self._process_stage(stage)
    if not success:
        time.sleep(self.config.no_work_delay_seconds)
```

**Problems**:
- Only one stage active at a time per worker
- Inefficient resource utilization
- Slow overall throughput

### Monolithic ETL Parallel Patterns

From `etl/news_aggregator.py`, the monolithic system uses:

#### 1. **Thread-per-Stage Architecture**
```python
# Parallel threads for different stages
threading.Thread(target=aggregator.download_full_texts, name="DownloadFullTextsThread").start()
threading.Thread(target=aggregator.llm_sentiment_ad, name="LLMSentimentAdThread").start()
threading.Thread(target=aggregator.classify_iptc_newscode, name="ClassifyIPTCNewsCodeThread").start()
threading.Thread(target=aggregator.check_and_update_duplicates, name="DuplicateCheckThread").start()
threading.Thread(target=aggregator.compute_embeddings, name="ComputeEmbeddingsThread").start()
```

#### 2. **Safe Database Operations via Multiprocessing**
```python
# Capsulated operations using multiprocessing + parquet files
def _get_entries(self, mandatory_fields, analysis_fields, filters):
    import multiprocessing
    temp_file = os.path.join(temp_dir, f'_get_entries_{uuid.uuid4().hex}.parquet')
    p = multiprocessing.Process(
        target=_get_entries_in_process,
        args=(mandatory_fields, analysis_fields, filters, temp_file)
    )
    p.start()
    p.join(timeout=600)
    
    # Read results from parquet file
    table = pq.read_table(temp_file)
    return table.to_pylist()
```

#### 3. **ThreadPoolExecutor for I/O Operations**
```python
# Parallel I/O operations within a stage
with ThreadPoolExecutor() as executor:
    future = executor.submit(fetch_article, entry['link'])
    full_text = future.result(timeout=30)
```

### Proposed Parallel Distributed Architecture

#### 1. **Multi-Stage Worker with Thread Pool**
```python
class ParallelDistributedWorker:
    def __init__(self, config: WorkerConfig):
        self.config = config
        self.stage_threads = {}
        self.shutdown_event = threading.Event()
        
    def start(self):
        """Start parallel threads for each configured stage"""
        for stage in self.config.stages:
            thread = threading.Thread(
                target=self._stage_worker_loop,
                args=(stage,),
                name=f"{stage.value}-worker"
            )
            thread.daemon = True
            thread.start()
            self.stage_threads[stage] = thread
            
    def _stage_worker_loop(self, stage: ProcessingStage):
        """Independent worker loop for a specific stage"""
        while not self.shutdown_event.is_set():
            try:
                success = self._process_stage_batch(stage)
                if not success:
                    time.sleep(self.config.no_work_delay_seconds)
                else:
                    time.sleep(self.config.processing_delay_seconds)
            except Exception as e:
                logger.error(f"Error in stage {stage.value} worker: {e}")
                time.sleep(self.config.no_work_delay_seconds)
```

#### 2. **Safe Database Operations via Session Isolation**
```python
class StageWorkerSession:
    def __init__(self, stage: ProcessingStage, db_session_factory):
        self.stage = stage
        self.db_session_factory = db_session_factory
        
    @contextmanager
    def get_isolated_session(self):
        """Get isolated session for this stage worker"""
        session = self.db_session_factory()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
```

#### 3. **Parquet-Based Data Exchange (Like Monolithic)**
```python
class ParquetDataExchange:
    @staticmethod
    def save_batch_to_parquet(entries: List[Entry], temp_file: str):
        """Save batch to parquet for safe inter-process communication"""
        entry_dicts = [e.__dict__ for e in entries]
        for d in entry_dicts:
            d.pop('_sa_instance_state', None)
        table = pa.Table.from_pylist(entry_dicts)
        pq.write_table(table, temp_file)
        
    @staticmethod
    def load_batch_from_parquet(temp_file: str) -> List[Dict]:
        """Load batch from parquet file"""
        table = pq.read_table(temp_file)
        return table.to_pylist()
```

### Implementation Plan

#### Phase 1: Database Performance Optimization
1. **Add specialized indices** for NULL checks on key fields
2. **Implement field-type-aware query optimization**
3. **Add query result caching** with 30-second TTL
4. **Benchmark performance improvements**

#### Phase 2: Parallel Worker Architecture  
1. **Create ParallelDistributedWorker class** with thread-per-stage
2. **Implement isolated session management** per stage thread
3. **Add parquet-based data exchange** for complex operations
4. **Integrate with existing WorkQueueManager**

#### Phase 3: Testing & Validation
1. **Performance testing** with parallel vs sequential workers
2. **Database concurrency testing** with multiple stage threads
3. **Error handling and recovery testing**
4. **Production deployment with monitoring**

### Expected Performance Improvements

#### Database Query Performance:
- **BLOB fields**: 19-27s → 2-5s (80% improvement)
- **Indexed fields**: 0.5-0.8s → 0.1-0.3s (60% improvement)
- **Overall API response**: 2+ minutes → 10-30 seconds (90% improvement)

#### Worker Throughput:
- **Current**: 1 stage at a time per worker
- **Parallel**: N stages simultaneously per worker
- **Expected**: 3-5x throughput improvement

This architecture leverages the proven patterns from the monolithic ETL while adding distributed processing capabilities and database performance optimizations.
