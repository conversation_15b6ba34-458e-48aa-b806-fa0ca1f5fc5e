import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/utils/category_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Category Order Tests', () {
    setUp(() {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test('SettingsProvider should start with empty category order', () async {
      final provider = SettingsProvider();
      await Future.delayed(
          const Duration(milliseconds: 100)); // Wait for initialization

      expect(provider.categoryOrder, isEmpty);
    });

    test('CategoryUtils should provide dynamic category names', () {
      // Clear any existing names
      CategoryUtils.clearDiscoveredNames();

      // Initially unknown categories should return the code itself
      expect(CategoryUtils.getDisplayName('politics'), equals('politics'));
      expect(CategoryUtils.hasDisplayName('politics'), isFalse);

      // Update with discovered names
      CategoryUtils.updateCategoryNames({
        'politics': 'Politik',
        'technology': 'Technologie',
      });

      // Now should return the discovered names
      expect(CategoryUtils.getDisplayName('politics'), equals('Politik'));
      expect(CategoryUtils.getDisplayName('technology'), equals('Technologie'));
      expect(CategoryUtils.getDisplayName('unknown_category'), equals('unknown_category'));

      expect(CategoryUtils.hasDisplayName('politics'), isTrue);
      expect(CategoryUtils.hasDisplayName('unknown_category'), isFalse);
    });

    test('SettingsProvider should save and load custom category order',
        () async {
      final provider = SettingsProvider();
      await Future.delayed(
          const Duration(milliseconds: 100)); // Wait for initialization

      final customOrder = ['sports', 'technology', 'politics'];
      await provider.setCategoryOrder(customOrder);

      expect(provider.categoryOrder, equals(customOrder));
    });

    test('SettingsProvider should reset to empty order', () async {
      final provider = SettingsProvider();
      await Future.delayed(
          const Duration(milliseconds: 100)); // Wait for initialization

      // Set custom order first
      await provider.setCategoryOrder(['sports', 'technology']);
      expect(provider.categoryOrder, equals(['sports', 'technology']));

      // Reset to empty
      await provider.resetCategoryOrder();
      expect(provider.categoryOrder, isEmpty);
    });

    test('SettingsProvider should auto-integrate missing categories', () async {
      final provider = SettingsProvider();
      await Future.delayed(
          const Duration(milliseconds: 100)); // Wait for initialization

      // Start with some categories
      await provider.setCategoryOrder(['politics', 'technology']);
      expect(provider.categoryOrder, equals(['politics', 'technology']));

      // Ensure new categories are added
      await provider.ensureCategoriesIncluded(['politics', 'technology', 'health', 'sports']);
      expect(provider.categoryOrder, equals(['politics', 'technology', 'health', 'sports']));

      // Ensure no duplicates are added
      await provider.ensureCategoriesIncluded(['politics', 'science']);
      expect(provider.categoryOrder, equals(['politics', 'technology', 'health', 'sports', 'science']));
    });

    test('SettingsProvider should handle all category lifecycle scenarios', () async {
      final provider = SettingsProvider();
      await Future.delayed(const Duration(milliseconds: 100));

      // Scenario 1: Fresh installation - categories from backend in order
      await provider.ensureCategoriesIncluded(['politics', 'technology', 'health']);
      expect(provider.categoryOrder, equals(['politics', 'technology', 'health']));

      // Scenario 2: New categories added later - should be added at end
      await provider.ensureCategoriesIncluded(['politics', 'technology', 'health', 'sports', 'business']);
      expect(provider.categoryOrder, equals(['politics', 'technology', 'health', 'sports', 'business']));

      // Scenario 3: User reorders categories
      await provider.setCategoryOrder(['technology', 'politics', 'health', 'sports', 'business']);
      expect(provider.categoryOrder, equals(['technology', 'politics', 'health', 'sports', 'business']));

      // Scenario 4: Some categories missing from API (e.g., 'sports' and 'business' missing)
      // But they should remain in saved order for Categories tab
      await provider.ensureCategoriesIncluded(['technology', 'politics', 'health']);
      // Order should remain unchanged since no new categories
      expect(provider.categoryOrder, equals(['technology', 'politics', 'health', 'sports', 'business']));

      // Scenario 5: New category added while some are still missing
      await provider.ensureCategoriesIncluded(['technology', 'politics', 'health', 'environment']);
      // 'environment' should be added at end, missing ones preserved
      expect(provider.categoryOrder, equals(['technology', 'politics', 'health', 'sports', 'business', 'environment']));
    });
  });
}
