"""
Legacy thread wrapper for backward compatibility with monolithic ETL processing.

This module provides a wrapper that makes distributed workers look like the old
monolithic threads, allowing gradual migration and configuration switching between
monolithic and distributed modes.
"""

import logging
import threading
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timezone
from contextlib import contextmanager

from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy import create_engine

from backend.app.distributed.distributed_worker import DistributedWorker, WorkerState
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.workers.feed_download_worker import FeedDownloadWorker
from backend.app.distributed.workers.embedding_worker import EmbeddingWorker
from backend.app.distributed.workers.sentiment_analysis_worker import SentimentAnalysisWorker
from backend.app.distributed.workers.llm_analysis_worker import LLMAnalysisWorker
from backend.app.distributed.workers.iptc_classification_worker import IPTCClassificationWorker
from backend.app.distributed.workers.duplicate_check_worker import DuplicateCheckWorker
from backend.app.distributed.workers.description_generation_worker import DescriptionGeneration<PERSON>orker
from backend.app.distributed.workers.image_prompt_worker import ImagePromptWorker
from backend.app.distributed.workers.preview_image_worker import PreviewImageWorker
from backend.app.distributed.workers.rss_feed_generation_worker import RSSFeedGenerationWorker

logger = logging.getLogger(__name__)

class LegacyThreadWrapper:
    """
    Wrapper that makes distributed workers look like old monolithic threads.
    
    This class provides the same interface as the original NewsAggregator methods
    but uses distributed workers internally. It allows for seamless switching
    between monolithic and distributed processing modes.
    """
    
    def __init__(self, database_url: str, worker_id_prefix: str = "legacy"):
        """
        Initialize the legacy thread wrapper.
        
        Args:
            database_url: Database connection URL
            worker_id_prefix: Prefix for worker IDs
        """
        self.database_url = database_url
        self.worker_id_prefix = worker_id_prefix
        
        # Database session factory
        self.engine = create_engine(database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        
        # Worker instances
        self._workers: Dict[ProcessingStage, DistributedWorker] = {}
        self._worker_threads: Dict[ProcessingStage, threading.Thread] = {}
        self._shutdown_event = threading.Event()
        
        # Configuration
        self._batch_size = 10
        self._max_workers_per_stage = 1  # Legacy mode uses single worker per stage
        
        # Workers will be initialized lazily when needed
        self._workers_initialized = False
    
    def _initialize_workers(self):
        """Initialize all distributed workers for legacy compatibility."""
        if self._workers_initialized:
            return
            
        logger.info("Initializing distributed workers for legacy compatibility")
        
        # Create worker configurations
        base_config = WorkerConfig(
            worker_id=f"{self.worker_id_prefix}_worker",
            stages=[],  # Will be set per worker
            database_url=self.database_url,
            batch_size=self._batch_size,
            max_retries=3,
            heartbeat_interval_seconds=30,
            claim_timeout_minutes=5
        )
        
        # Initialize workers for each processing stage
        worker_classes = {
            ProcessingStage.DOWNLOAD_FEEDS: FeedDownloadWorker,
            ProcessingStage.COMPUTE_EMBEDDINGS: EmbeddingWorker,
            ProcessingStage.SENTIMENT_ANALYSIS: SentimentAnalysisWorker,
            ProcessingStage.LLM_ANALYSIS: LLMAnalysisWorker,
            ProcessingStage.IPTC_CLASSIFICATION: IPTCClassificationWorker,
            ProcessingStage.DUPLICATE_CHECK: DuplicateCheckWorker,
            ProcessingStage.GENERATE_DESCRIPTIONS: DescriptionGenerationWorker,
            ProcessingStage.GENERATE_IMAGE_PROMPTS: ImagePromptWorker,
            ProcessingStage.GENERATE_PREVIEW_IMAGES: PreviewImageWorker
        }
        
        for stage, worker_class in worker_classes.items():
            config = WorkerConfig(
                worker_id=f"{self.worker_id_prefix}_{stage.value}",
                stages=[stage],
                database_url=base_config.database_url,
                batch_size=base_config.batch_size,
                max_retries=base_config.max_retries,
                heartbeat_interval_seconds=base_config.heartbeat_interval_seconds,
                claim_timeout_minutes=base_config.claim_timeout_minutes
            )
            
            self._workers[stage] = worker_class(config)
            logger.debug(f"Initialized {worker_class.__name__} for stage {stage.value}")
        
        self._workers_initialized = True
        logger.info("All distributed workers initialized successfully")
    
    @contextmanager
    def _get_session(self):
        """Get a database session with proper cleanup."""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def start_workers(self):
        """Start all distributed workers in separate threads."""
        self._initialize_workers()
        
        logger.info("Starting distributed workers in legacy thread mode")
        
        for stage, worker in self._workers.items():
            if stage not in self._worker_threads:
                thread = threading.Thread(
                    target=self._run_worker,
                    args=(worker,),
                    name=f"LegacyWorker-{stage.value}",
                    daemon=True
                )
                self._worker_threads[stage] = thread
                thread.start()
                logger.debug(f"Started worker thread for stage {stage.value}")
        
        logger.info("All distributed workers started successfully")
    
    def _run_worker(self, worker: DistributedWorker):
        """Run a single worker until shutdown is requested."""
        try:
            worker.start()
            
            while not self._shutdown_event.is_set():
                if worker.state == WorkerState.RUNNING:
                    # Process a batch of work
                    worker.process_batch()
                    
                # Small sleep to prevent busy waiting
                time.sleep(0.1)
                
        except Exception as e:
            logger.error(f"Worker {worker.config.worker_id} failed: {e}")
        finally:
            try:
                worker.stop()
            except Exception as e:
                logger.error(f"Error stopping worker {worker.config.worker_id}: {e}")
    
    def stop_workers(self):
        """Stop all distributed workers gracefully."""
        logger.info("Stopping distributed workers")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Stop all workers
        for worker in self._workers.values():
            try:
                worker.stop()
            except Exception as e:
                logger.error(f"Error stopping worker {worker.config.worker_id}: {e}")
        
        # Wait for threads to finish
        for stage, thread in self._worker_threads.items():
            try:
                thread.join(timeout=10)
                if thread.is_alive():
                    logger.warning(f"Worker thread for stage {stage.value} did not stop gracefully")
            except Exception as e:
                logger.error(f"Error joining worker thread for stage {stage.value}: {e}")
        
        self._worker_threads.clear()
        logger.info("All distributed workers stopped")
    
    def is_running(self) -> bool:
        """Check if any workers are currently running."""
        return any(
            thread.is_alive() 
            for thread in self._worker_threads.values()
        )
    
    def get_worker_status(self) -> Dict[str, Any]:
        """Get status information for all workers."""
        if not self._workers_initialized:
            return {"status": "not_initialized", "workers": {}}
        
        status = {
            "status": "running" if self.is_running() else "stopped",
            "workers": {},
            "total_workers": len(self._workers),
            "running_workers": sum(1 for t in self._worker_threads.values() if t.is_alive())
        }
        
        for stage, worker in self._workers.items():
            thread = self._worker_threads.get(stage)
            status["workers"][stage.value] = {
                "worker_id": worker.config.worker_id,
                "state": worker.state.value,
                "thread_alive": thread.is_alive() if thread else False,
                "last_heartbeat": worker.last_heartbeat.isoformat() if worker.last_heartbeat else None,
                "processed_count": getattr(worker, 'processed_count', 0),
                "error_count": getattr(worker, 'error_count', 0)
            }
        
        return status
    
    # Legacy compatibility methods that mimic the original NewsAggregator interface
    
    def download_feeds(self, source_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        Legacy method for downloading RSS feeds.
        
        This method mimics the original download_feeds behavior but uses
        distributed workers internally.
        
        Args:
            source_ids: Optional list of source IDs to process
            
        Returns:
            Dictionary with processing results
        """
        logger.info("Starting legacy feed download process")
        
        self._initialize_workers()
        
        # Get the feed download worker
        worker = self._workers.get(ProcessingStage.DOWNLOAD_FEEDS)
        if not worker:
            raise RuntimeError("Feed download worker not available")
        
        # Process feeds using the distributed worker
        try:
            # Start the worker if not already running
            if worker.state != WorkerState.RUNNING:
                worker.start()
            
            # Process batches until no more work available
            processed_count = 0
            error_count = 0
            
            while True:
                result = worker.process_batch()
                if result['processed_count'] == 0:
                    break  # No more work available
                
                processed_count += result['processed_count']
                error_count += result['error_count']
            
            return {
                'success': True,
                'processed_count': processed_count,
                'error_count': error_count,
                'message': f"Processed {processed_count} feed sources"
            }
            
        except Exception as e:
            logger.error(f"Error in legacy feed download: {e}")
            return {
                'success': False,
                'error': str(e),
                'processed_count': 0,
                'error_count': 1
            }
    
    def download_full_texts(self, entry_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        Legacy method for downloading full text content.
        
        Args:
            entry_ids: Optional list of entry IDs to process
            
        Returns:
            Dictionary with processing results
        """
        logger.info("Starting legacy full text download process")
        
        # Note: Full text download worker not implemented yet
        # This is a placeholder that returns success without processing
        return {
            'success': True,
            'processed_count': 0,
            'error_count': 0,
            'message': "Full text download worker not implemented yet"
        }
    
    def compute_embeddings(self, entry_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        Legacy method for computing embeddings.
        
        Args:
            entry_ids: Optional list of entry IDs to process
            
        Returns:
            Dictionary with processing results
        """
        logger.info("Starting legacy embedding computation process")
        
        self._initialize_workers()
        
        worker = self._workers.get(ProcessingStage.COMPUTE_EMBEDDINGS)
        if not worker:
            raise RuntimeError("Embedding worker not available")
        
        try:
            if worker.state != WorkerState.RUNNING:
                worker.start()
            
            processed_count = 0
            error_count = 0
            
            while True:
                result = worker.process_batch()
                if result['processed_count'] == 0:
                    break
                
                processed_count += result['processed_count']
                error_count += result['error_count']
            
            return {
                'success': True,
                'processed_count': processed_count,
                'error_count': error_count,
                'message': f"Computed embeddings for {processed_count} entries"
            }
            
        except Exception as e:
            logger.error(f"Error in legacy embedding computation: {e}")
            return {
                'success': False,
                'error': str(e),
                'processed_count': 0,
                'error_count': 1
            }
    
    def run_full_pipeline(self, source_ids: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        Legacy method for running the complete ETL pipeline.
        
        This method processes all stages in sequence, mimicking the original
        monolithic behavior but using distributed workers.
        
        Args:
            source_ids: Optional list of source IDs to process
            
        Returns:
            Dictionary with overall processing results
        """
        logger.info("Starting legacy full pipeline process")
        
        # Define the processing order
        pipeline_stages = [
            ProcessingStage.DOWNLOAD_FEEDS,
            ProcessingStage.COMPUTE_EMBEDDINGS,
            ProcessingStage.SENTIMENT_ANALYSIS,
            ProcessingStage.LLM_ANALYSIS,
            ProcessingStage.IPTC_CLASSIFICATION,
            ProcessingStage.DUPLICATE_CHECK,
            ProcessingStage.GENERATE_DESCRIPTIONS,
            ProcessingStage.GENERATE_IMAGE_PROMPTS,
            ProcessingStage.GENERATE_PREVIEW_IMAGES
        ]
        
        self._initialize_workers()
        
        overall_results = {
            'success': True,
            'stages': {},
            'total_processed': 0,
            'total_errors': 0,
            'start_time': datetime.now(timezone.utc).isoformat(),
            'end_time': None
        }
        
        try:
            for stage in pipeline_stages:
                logger.info(f"Processing stage: {stage.value}")
                
                worker = self._workers.get(stage)
                if not worker:
                    logger.warning(f"Worker for stage {stage.value} not available, skipping")
                    continue
                
                stage_start = datetime.now(timezone.utc)
                
                try:
                    if worker.state != WorkerState.RUNNING:
                        worker.start()
                    
                    processed_count = 0
                    error_count = 0
                    
                    while True:
                        result = worker.process_batch()
                        if result['processed_count'] == 0:
                            break
                        
                        processed_count += result['processed_count']
                        error_count += result['error_count']
                    
                    stage_end = datetime.now(timezone.utc)
                    duration = (stage_end - stage_start).total_seconds()
                    
                    overall_results['stages'][stage.value] = {
                        'success': True,
                        'processed_count': processed_count,
                        'error_count': error_count,
                        'duration_seconds': duration,
                        'start_time': stage_start.isoformat(),
                        'end_time': stage_end.isoformat()
                    }
                    
                    overall_results['total_processed'] += processed_count
                    overall_results['total_errors'] += error_count
                    
                    logger.info(f"Stage {stage.value} completed: {processed_count} processed, {error_count} errors")
                    
                except Exception as e:
                    logger.error(f"Error in stage {stage.value}: {e}")
                    overall_results['stages'][stage.value] = {
                        'success': False,
                        'error': str(e),
                        'processed_count': 0,
                        'error_count': 1
                    }
                    overall_results['total_errors'] += 1
                    # Continue with next stage instead of failing completely
            
            overall_results['end_time'] = datetime.now(timezone.utc).isoformat()
            
            if overall_results['total_errors'] > 0:
                overall_results['success'] = False
                overall_results['message'] = f"Pipeline completed with {overall_results['total_errors']} errors"
            else:
                overall_results['message'] = f"Pipeline completed successfully, processed {overall_results['total_processed']} items"
            
            return overall_results
            
        except Exception as e:
            logger.error(f"Critical error in legacy pipeline: {e}")
            overall_results['success'] = False
            overall_results['error'] = str(e)
            overall_results['end_time'] = datetime.now(timezone.utc).isoformat()
            return overall_results
    
    def __enter__(self):
        """Context manager entry."""
        self.start_workers()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup."""
        self.stop_workers()
    
    def cleanup(self):
        """Clean up resources and stop all workers."""
        self.stop_workers()
        
        # Close database connections
        if hasattr(self, 'engine'):
            self.engine.dispose()
        
        logger.info("Legacy thread wrapper cleanup completed")