"""consolidate_singleton_executions_into_workers_table

Revision ID: da503d75ad2a
Revises: e819f59fa192
Create Date: 2025-09-21 00:17:12.534149

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'da503d75ad2a'
down_revision: Union[str, None] = 'e819f59fa192'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add singleton_executions column to workers table
    op.add_column('workers', sa.Column('singleton_executions', sa.CLOB(), nullable=True))

    # Migrate data from singleton_stage_executions table to workers table
    # This will consolidate the execution tracking into the workers table
    connection = op.get_bind()

    # Get all singleton execution records
    result = connection.execute(sa.text("""
        SELECT stage_name, last_execution_time, worker_id
        FROM singleton_stage_executions
    """))

    # Group by worker_id and create JSON objects
    worker_executions = {}
    for row in result:
        stage_name, last_execution_time, worker_id = row
        if worker_id not in worker_executions:
            worker_executions[worker_id] = {}
        worker_executions[worker_id][stage_name] = last_execution_time.isoformat() if last_execution_time else None

    # Update workers table with consolidated execution data
    for worker_id, executions in worker_executions.items():
        import json
        executions_json = json.dumps(executions)
        connection.execute(sa.text("""
            UPDATE workers
            SET singleton_executions = :executions_json
            WHERE worker_id = :worker_id
        """), {"executions_json": executions_json, "worker_id": worker_id})

    # Drop the separate singleton_stage_executions table
    op.drop_table('singleton_stage_executions')


def downgrade() -> None:
    # Recreate singleton_stage_executions table
    op.create_table('singleton_stage_executions',
        sa.Column('stage_name', sa.String(100), primary_key=True, nullable=False),
        sa.Column('last_execution_time', sa.TIMESTAMP(), nullable=False),
        sa.Column('worker_id', sa.String(255), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.func.now(), nullable=False)
    )
    op.create_index('idx_singleton_stage_executions_last_execution', 'singleton_stage_executions', ['last_execution_time'])

    # Migrate data back from workers table to singleton_stage_executions table
    connection = op.get_bind()

    # Get all workers with singleton execution data
    result = connection.execute(sa.text("""
        SELECT worker_id, singleton_executions
        FROM workers
        WHERE singleton_executions IS NOT NULL
    """))

    # Insert records into singleton_stage_executions table
    for row in result:
        worker_id, executions_json = row
        if executions_json:
            import json
            executions = json.loads(executions_json)
            for stage_name, last_execution_time in executions.items():
                if last_execution_time:
                    connection.execute(sa.text("""
                        INSERT INTO singleton_stage_executions
                        (stage_name, last_execution_time, worker_id, created_at, updated_at)
                        VALUES (:stage_name, :last_execution_time, :worker_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """), {
                        "stage_name": stage_name,
                        "last_execution_time": last_execution_time,
                        "worker_id": worker_id
                    })

    # Remove singleton_executions column from workers table
    op.drop_column('workers', 'singleton_executions')
