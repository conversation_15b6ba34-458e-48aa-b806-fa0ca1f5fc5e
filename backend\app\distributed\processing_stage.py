"""
Processing stage definitions and status management for distributed ETL processing.
"""

from enum import Enum
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone
import json
from dataclasses import dataclass, asdict


class ProcessingStage(Enum):
    """Enumeration of all ETL processing stages."""
    DOWNLOAD_FEEDS = "download_feeds"
    DOWNLOAD_FULL_TEXT = "download_full_text"
    TRANSLATE_TEXT = "translate_text"
    COMPUTE_EMBEDDINGS = "compute_embeddings"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    LLM_ANALYSIS = "llm_analysis"
    IPTC_CLASSIFICATION = "iptc_classification"
    DUPLICATE_CHECK = "duplicate_check"
    GENERATE_DESCRIPTIONS = "generate_descriptions"
    GENERATE_IMAGE_PROMPTS = "generate_image_prompts"
    GENERATE_PREVIEW_IMAGES = "generate_preview_images"
    GENERATE_RSS_FEED = "generate_rss_feed"


@dataclass
class StageSelectionCriteria:
    """Entry selection criteria for a processing stage."""
    mandatory_fields: List[str]  # Fields that must not be None
    analysis_fields: List[str]   # Fields that should be None (to be processed)
    filters: List[Tuple[str, str, Any]]  # Additional filters: (field, operator, value)
    creates_new_entries: bool = False  # True if stage creates new entries instead of processing existing ones
    requires_singleton: bool = False  # True if only one worker should process this stage at a time
    cooldown_minutes: int = 0  # Minimum minutes between executions (0 = no cooldown)

    def __post_init__(self):
        """Validate the criteria after initialization."""
        if self.creates_new_entries and (self.mandatory_fields or self.analysis_fields):
            raise ValueError("Stages that create new entries should not have field requirements")
        if self.requires_singleton and not self.creates_new_entries:
            # For now, only stages that create new entries can be singletons
            # This could be relaxed in the future if needed
            raise ValueError("Currently, only stages that create new entries can be singletons")

    def has_field_requirements(self) -> bool:
        """Check if this stage has any field requirements."""
        return bool(self.mandatory_fields or self.analysis_fields)

    def has_filters(self) -> bool:
        """Check if this stage has additional filters."""
        return bool(self.filters)

    def requires_entry_retrieval(self) -> bool:
        """Check if this stage requires retrieving existing entries for processing."""
        return not (self.creates_new_entries or self.requires_singleton)


class ProcessingStatusValue(Enum):
    """Status values for processing stages."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class StageStatus:
    """Status information for a single processing stage."""
    status: ProcessingStatusValue
    completed_at: Optional[datetime] = None
    worker_id: Optional[str] = None
    error: Optional[str] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        result = {
            "status": self.status.value,
            "retry_count": self.retry_count
        }
        if self.completed_at:
            result["completed_at"] = self.completed_at.isoformat()
        if self.worker_id:
            result["worker_id"] = self.worker_id
        if self.error:
            result["error"] = self.error
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StageStatus':
        """Create from dictionary (JSON deserialization)."""
        completed_at = None
        if data.get("completed_at"):
            completed_at = datetime.fromisoformat(data["completed_at"])
        
        return cls(
            status=ProcessingStatusValue(data["status"]),
            completed_at=completed_at,
            worker_id=data.get("worker_id"),
            error=data.get("error"),
            retry_count=data.get("retry_count", 0)
        )


class ProcessingStatus:
    """Manages processing status for all stages of an entry."""
    
    def __init__(self, status_data: Optional[Dict[str, Dict[str, Any]]] = None):
        """Initialize with optional status data."""
        self._stages: Dict[ProcessingStage, StageStatus] = {}
        
        if status_data:
            for stage_name, stage_data in status_data.items():
                try:
                    stage = ProcessingStage(stage_name)
                    self._stages[stage] = StageStatus.from_dict(stage_data)
                except ValueError:
                    # Skip unknown stages for forward compatibility
                    continue
    
    def get_stage_status(self, stage: ProcessingStage) -> StageStatus:
        """Get status for a specific stage, defaulting to PENDING if not set."""
        return self._stages.get(stage, StageStatus(ProcessingStatusValue.PENDING))
    
    def set_stage_status(self, stage: ProcessingStage, status: ProcessingStatusValue, 
                        worker_id: Optional[str] = None, error: Optional[str] = None) -> None:
        """Set status for a specific stage."""
        current_status = self.get_stage_status(stage)
        
        # Update the status
        current_status.status = status
        current_status.worker_id = worker_id
        current_status.error = error
        
        if status == ProcessingStatusValue.COMPLETED:
            current_status.completed_at = datetime.now(timezone.utc)
        elif status == ProcessingStatusValue.FAILED:
            current_status.retry_count += 1
        
        self._stages[stage] = current_status
    
    def mark_stage_in_progress(self, stage: ProcessingStage, worker_id: str) -> None:
        """Mark a stage as in progress by a specific worker."""
        self.set_stage_status(stage, ProcessingStatusValue.IN_PROGRESS, worker_id=worker_id)
    
    def mark_stage_completed(self, stage: ProcessingStage, worker_id: str) -> None:
        """Mark a stage as completed by a specific worker."""
        self.set_stage_status(stage, ProcessingStatusValue.COMPLETED, worker_id=worker_id)
    
    def mark_stage_failed(self, stage: ProcessingStage, worker_id: str, error: str) -> None:
        """Mark a stage as failed with error information."""
        self.set_stage_status(stage, ProcessingStatusValue.FAILED, worker_id=worker_id, error=error)
    
    def is_stage_completed(self, stage: ProcessingStage) -> bool:
        """Check if a stage is completed."""
        return self.get_stage_status(stage).status == ProcessingStatusValue.COMPLETED
    
    def is_stage_pending(self, stage: ProcessingStage) -> bool:
        """Check if a stage is pending (not started)."""
        return self.get_stage_status(stage).status == ProcessingStatusValue.PENDING
    
    def is_stage_in_progress(self, stage: ProcessingStage) -> bool:
        """Check if a stage is currently in progress."""
        return self.get_stage_status(stage).status == ProcessingStatusValue.IN_PROGRESS
    
    def is_stage_failed(self, stage: ProcessingStage) -> bool:
        """Check if a stage has failed."""
        return self.get_stage_status(stage).status == ProcessingStatusValue.FAILED
    
    def get_retry_count(self, stage: ProcessingStage) -> int:
        """Get retry count for a specific stage."""
        return self.get_stage_status(stage).retry_count
    
    def get_pending_stages(self) -> List[ProcessingStage]:
        """Get list of stages that are pending."""
        return [stage for stage in ProcessingStage if self.is_stage_pending(stage)]
    
    def get_completed_stages(self) -> List[ProcessingStage]:
        """Get list of stages that are completed."""
        return [stage for stage in ProcessingStage if self.is_stage_completed(stage)]
    
    def get_failed_stages(self) -> List[ProcessingStage]:
        """Get list of stages that have failed."""
        return [stage for stage in ProcessingStage if self.is_stage_failed(stage)]
    
    def to_json(self) -> str:
        """Serialize to JSON string for database storage."""
        data = {}
        for stage, status in self._stages.items():
            data[stage.value] = status.to_dict()
        return json.dumps(data)
    
    @classmethod
    def from_json(cls, json_str: Optional[str]) -> 'ProcessingStatus':
        """Deserialize from JSON string from database."""
        if not json_str:
            return cls()
        
        try:
            data = json.loads(json_str)
            return cls(data)
        except (json.JSONDecodeError, TypeError):
            # Return empty status if JSON is invalid
            return cls()
    
    def __repr__(self) -> str:
        """String representation for debugging."""
        stage_summaries = []
        for stage in ProcessingStage:
            status = self.get_stage_status(stage)
            stage_summaries.append(f"{stage.value}: {status.status.value}")
        return f"ProcessingStatus({', '.join(stage_summaries)})"


# Utility functions for status manipulation

def create_empty_processing_status() -> ProcessingStatus:
    """Create a new empty processing status with all stages pending."""
    return ProcessingStatus()


def get_next_pending_stage(processing_status: ProcessingStatus, 
                          available_stages: List[ProcessingStage]) -> Optional[ProcessingStage]:
    """Get the next pending stage from a list of available stages."""
    for stage in available_stages:
        if processing_status.is_stage_pending(stage):
            return stage
    return None


def can_process_stage(processing_status: ProcessingStatus, stage: ProcessingStage, 
                     max_retries: int = 3) -> bool:
    """Check if a stage can be processed (pending or failed with retries remaining)."""
    if processing_status.is_stage_pending(stage):
        return True
    
    if processing_status.is_stage_failed(stage):
        return processing_status.get_retry_count(stage) < max_retries
    
    return False


def get_processable_stages(processing_status: ProcessingStatus,
                          available_stages: List[ProcessingStage],
                          max_retries: int = 3) -> List[ProcessingStage]:
    """Get all stages that can be processed (pending or failed with retries remaining)."""
    return [
        stage for stage in available_stages
        if can_process_stage(processing_status, stage, max_retries)
    ]


# Stage Selection Criteria Configuration
# This maps each processing stage to its entry selection requirements
STAGE_SELECTION_CRITERIA: Dict[ProcessingStage, StageSelectionCriteria] = {
    ProcessingStage.DOWNLOAD_FEEDS: StageSelectionCriteria(
        mandatory_fields=[],
        analysis_fields=[],
        filters=[],
        creates_new_entries=True,
        requires_singleton=True,
        cooldown_minutes=15  # Run at most once every 15 minutes
    ),

    ProcessingStage.DOWNLOAD_FULL_TEXT: StageSelectionCriteria(
        mandatory_fields=[],
        analysis_fields=["full_text"],
        filters=[]
    ),

    ProcessingStage.TRANSLATE_TEXT: StageSelectionCriteria(
        mandatory_fields=["full_text"],
        analysis_fields=["english_text"],
        filters=[]
    ),

    ProcessingStage.COMPUTE_EMBEDDINGS: StageSelectionCriteria(
        mandatory_fields=["full_text"],
        analysis_fields=["embedding"],
        filters=[]
    ),

    ProcessingStage.SENTIMENT_ANALYSIS: StageSelectionCriteria(
        mandatory_fields=["full_text", "english_text"],
        analysis_fields=[
            "vader_pos", "vader_neu", "vader_neg", "vader_compound",
            "german_sentiment_positive", "german_sentiment_neutral",
            "german_sentiment_negative", "tbd_polarity", "tbd_subjectivity"
        ],
        filters=[]
    ),

    ProcessingStage.LLM_ANALYSIS: StageSelectionCriteria(
        mandatory_fields=["full_text"],
        analysis_fields=[
            "llm_is_ad", "llm_is_ad_reason", "llm_positive", "llm_neutral", "llm_negative",
            "llm_positive_std", "llm_neutral_std", "llm_negative_std",
            "llm_positive_diff", "llm_neutral_diff", "llm_negative_diff",
            "llm_reason_list", "llm_iterations", "llm_break_reason"
        ],
        filters=[]
    ),

    ProcessingStage.IPTC_CLASSIFICATION: StageSelectionCriteria(
        mandatory_fields=["full_text"],
        analysis_fields=["iptc_newscode", "iptc_score"],
        filters=[]
    ),

    ProcessingStage.DUPLICATE_CHECK: StageSelectionCriteria(
        mandatory_fields=["embedding"],
        analysis_fields=["dup_entry_id", "dup_entry_conf"],
        filters=[]
    ),

    ProcessingStage.GENERATE_DESCRIPTIONS: StageSelectionCriteria(
        mandatory_fields=["full_text"],
        analysis_fields=["description"],
        filters=[("llm_positive", ">=", "NEWS_SENTIMENT_THRESHOLD")]  # Will be resolved at runtime
    ),

    ProcessingStage.GENERATE_IMAGE_PROMPTS: StageSelectionCriteria(
        mandatory_fields=["full_text"],
        analysis_fields=["image_prompt"],
        filters=[
            ("llm_positive", ">=", "NEWS_SENTIMENT_THRESHOLD"),
            ("llm_is_ad", "<", "NEWS_AD_THRESHOLD")
        ]
    ),

    ProcessingStage.GENERATE_PREVIEW_IMAGES: StageSelectionCriteria(
        mandatory_fields=["full_text", "image_prompt"],
        analysis_fields=["preview_img"],
        filters=[
            ("llm_positive", ">=", "NEWS_SENTIMENT_THRESHOLD"),
            ("llm_is_ad", "<", "NEWS_AD_THRESHOLD")
        ]
    ),

    ProcessingStage.GENERATE_RSS_FEED: StageSelectionCriteria(
        mandatory_fields=[],
        analysis_fields=[],
        filters=[
            ("llm_positive", ">=", "NEWS_SENTIMENT_THRESHOLD"),
            ("published", ">=", "TODAY")  # Will be resolved at runtime
        ]
    )
}


def get_stage_selection_criteria(stage: ProcessingStage) -> StageSelectionCriteria:
    """Get the selection criteria for a specific processing stage."""
    if stage not in STAGE_SELECTION_CRITERIA:
        raise ValueError(f"No selection criteria defined for stage: {stage}")
    return STAGE_SELECTION_CRITERIA[stage]


def resolve_filter_values(filters: List[Tuple[str, str, Any]], settings_module=None) -> List[Tuple[str, str, Any]]:
    """
    Resolve placeholder values in filters to actual values.

    Args:
        filters: List of filter tuples that may contain placeholder values
        settings_module: Settings module to resolve values from (optional)

    Returns:
        List of filter tuples with resolved values
    """
    from datetime import datetime, timezone

    resolved_filters = []
    for field, operator, value in filters:
        if isinstance(value, str):
            if value == "NEWS_SENTIMENT_THRESHOLD":
                if settings_module and hasattr(settings_module, 'NEWS_SENTIMENT_THRESHOLD'):
                    value = settings_module.NEWS_SENTIMENT_THRESHOLD
                else:
                    # Default fallback value
                    value = 0.7
            elif value == "NEWS_AD_THRESHOLD":
                if settings_module and hasattr(settings_module, 'NEWS_AD_THRESHOLD'):
                    value = settings_module.NEWS_AD_THRESHOLD
                else:
                    # Default fallback value
                    value = 0.5
            elif value == "TODAY":
                value = datetime.now(timezone.utc).date()

        resolved_filters.append((field, operator, value))

    return resolved_filters


def stage_creates_new_entries(stage: ProcessingStage) -> bool:
    """Check if a stage creates new entries instead of processing existing ones."""
    criteria = get_stage_selection_criteria(stage)
    return criteria.creates_new_entries


def stage_requires_singleton(stage: ProcessingStage) -> bool:
    """Check if a processing stage requires singleton execution (only one worker at a time)."""
    criteria = get_stage_selection_criteria(stage)
    return criteria.requires_singleton


def stage_requires_entry_retrieval(stage: ProcessingStage) -> bool:
    """Check if a processing stage requires retrieving existing entries for processing."""
    criteria = get_stage_selection_criteria(stage)
    return criteria.requires_entry_retrieval()


def stage_has_field_requirements(stage: ProcessingStage) -> bool:
    """Check if a stage has field requirements for entry selection."""
    criteria = get_stage_selection_criteria(stage)
    return criteria.has_field_requirements()


def stage_has_filters(stage: ProcessingStage) -> bool:
    """Check if a stage has additional filters for entry selection."""
    criteria = get_stage_selection_criteria(stage)
    return criteria.has_filters()


def stage_has_cooldown(stage: ProcessingStage) -> bool:
    """
    Check if a stage has a cooldown period.

    Args:
        stage: The processing stage to check

    Returns:
        True if the stage has a cooldown period, False otherwise
    """
    criteria = get_stage_selection_criteria(stage)
    return criteria.cooldown_minutes > 0


def get_stage_cooldown_minutes(stage: ProcessingStage) -> int:
    """
    Get the cooldown period for a stage in minutes.

    Args:
        stage: The processing stage to check

    Returns:
        Cooldown period in minutes (0 if no cooldown)
    """
    criteria = get_stage_selection_criteria(stage)
    return criteria.cooldown_minutes