"""
LLM analysis worker for distributed ETL processing.

This module provides the LLMAnalysisWorker class that handles LLM-based
sentiment analysis and advertisement detection in batch processing mode.
"""

import logging
import time
import json
import requests
from collections import deque
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from jsonschema import validate, ValidationError

from backend.app.distributed.distributed_worker import DistributedWorker
from backend.app.distributed.worker_config import WorkerConfig
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.models.models import Entry
from backend.app.core.config import settings

logger = logging.getLogger(__name__)


class LLMAnalysisWorker(DistributedWorker):
    """
    Worker specialized in LLM-based analysis of news articles.
    
    Adapts the existing LLM sentiment and advertisement analysis logic for batch
    processing with proper rate limiting, provider management, and batch optimization.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory):
        """
        Initialize the LLM analysis worker.
        
        Args:
            config: Worker configuration
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Validate that this worker is configured for the correct stage
        if ProcessingStage.LLM_ANALYSIS not in config.stages:
            raise ValueError("LLMAnalysisWorker requires LLM_ANALYSIS stage")
        
        # Configuration for LLM operations
        self.llm_calls_per_entry = config.get_stage_config(
            ProcessingStage.LLM_ANALYSIS, 'llm_calls_per_entry', 5
        )
        self.max_input_length = config.get_stage_config(
            ProcessingStage.LLM_ANALYSIS, 'max_input_length', 
            getattr(settings, 'NEWS_LLM_MAX_INPUT_LENGTH', 4000)
        )
        self.batch_optimize_calls = config.get_stage_config(
            ProcessingStage.LLM_ANALYSIS, 'batch_optimize_calls', True
        )
        self.rate_limit_buffer = config.get_stage_config(
            ProcessingStage.LLM_ANALYSIS, 'rate_limit_buffer', 0.1
        )
        
        # Rate limiting tracking per provider
        self._llm_call_log: Dict[str, deque] = {}
        
        # LLM provider configuration
        self._providers = [
            {"provider": "local"},
            {"provider": "together", "model": "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"},
            {"provider": "together", "model": "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free"},
            {"provider": "openrouter", "model": "qwen/qwen3-coder:free"},
            {"provider": "openrouter", "model": "deepseek/deepseek-r1-0528:free"},
            {"provider": "openrouter", "model": "deepseek/deepseek-chat-v3-0324:free"},
            {"provider": "openrouter", "model": "openai/gpt-oss-20b:free"},
            {"provider": "chutes", "model": "Qwen/Qwen3-32B"},
        ]
        
        # LLM prompt and schema
        self._llm_prompt = (
            "Analyze the following text (ignore non-core portions related to cookies, GDPR notices, etc.) in two aspects:\n"
            "1. Is this an advertisement, promotional press release, or similar content? Objective comparisons are not ads.\n"
            "2. Is the message positive, uplifting, heartwarming, encouraging or does it make you smile? Does it report about joyful, appreciative coexistence?\n"
            "Provide sentiment probabilities that sum to 1.0 for positive, negative, and neutral.\n"
            "As reason explain in German.\n"
        )
        
        self._llm_schema = {
            "title": "CombinedAnalysisResult",
            "type": "object",
            "properties": {
                "advertisement": {
                    "type": "object",
                    "properties": {
                        "is_advertisement": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1,
                            "description": "Likelihood score for text to be advertisement."
                        },
                        "reason": {
                            "type": "string",
                            "description": "Reason for advertisement estimate."
                        }
                    },
                    "required": ["is_advertisement", "reason"]
                },
                "sentiment": {
                    "type": "object",
                    "properties": {
                        "positive": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1
                        },
                        "neutral": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1
                        },
                        "negative": {
                            "type": "number",
                            "minimum": 0,
                            "maximum": 1
                        },
                        "reason": {
                            "type": "string"
                        }
                    },
                    "required": ["positive", "neutral", "negative", "reason"]
                }
            },
            "required": ["advertisement", "sentiment"]
        }
        
        logger.info(
            f"Initialized LLMAnalysisWorker {self.worker_id} "
            f"(calls_per_entry: {self.llm_calls_per_entry}, "
            f"batch_optimize: {self.batch_optimize_calls})"
        )
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for LLM analysis.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage (should be LLM_ANALYSIS)
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        if stage != ProcessingStage.LLM_ANALYSIS:
            logger.error(f"LLMAnalysisWorker cannot process stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        logger.info(f"Processing batch of {len(entries)} entries for LLM analysis")
        
        # Filter entries that have required text fields
        valid_entries = []
        results = {}
        
        for entry in entries:
            if not entry.full_text:
                logger.warning(f"Entry {entry.entry_id} missing full_text field")
                results[entry.entry_id] = False
            else:
                valid_entries.append(entry)
        
        if not valid_entries:
            logger.warning("No valid entries to process in batch")
            return results
        
        # Process valid entries
        if self.batch_optimize_calls:
            batch_results = self._process_batch_optimized(valid_entries)
        else:
            batch_results = self._process_batch_individual(valid_entries)
        
        results.update(batch_results)
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(
            f"Completed LLM analysis batch: {success_count}/{len(entries)} successful"
        )
        
        return results
    
    def _process_batch_optimized(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries with batch-optimized LLM calls.
        
        Args:
            entries: List of valid entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        # Process entries with rate limiting awareness
        for entry in entries:
            try:
                # Check if we can make LLM calls without hitting rate limits
                if not self._can_make_llm_calls():
                    logger.warning(f"Rate limit reached, skipping entry {entry.entry_id}")
                    results[entry.entry_id] = False
                    continue
                
                # Process single entry with multiple LLM calls
                success = self._process_single_entry_llm(entry)
                results[entry.entry_id] = success
                
                if success:
                    logger.debug(f"Successfully analyzed entry {entry.entry_id} with LLM")
                else:
                    logger.warning(f"Failed to analyze entry {entry.entry_id} with LLM")
            
            except Exception as e:
                logger.error(f"Error processing entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
        
        return results
    
    def _process_batch_individual(self, entries: List[Entry]) -> Dict[str, bool]:
        """
        Process entries individually (fallback method).
        
        Args:
            entries: List of valid entries to process
            
        Returns:
            Dictionary mapping entry_id to success status
        """
        results = {}
        
        for entry in entries:
            try:
                success = self._process_single_entry_llm(entry)
                results[entry.entry_id] = success
                
                if success:
                    logger.debug(f"Successfully analyzed entry {entry.entry_id} with LLM")
                else:
                    logger.warning(f"Failed to analyze entry {entry.entry_id} with LLM")
            
            except Exception as e:
                logger.error(f"Error processing entry {entry.entry_id}: {e}")
                results[entry.entry_id] = False
        
        return results
    
    def _process_single_entry_llm(self, entry: Entry) -> bool:
        """
        Process a single entry with LLM analysis.
        
        Args:
            entry: Entry object to process
            
        Returns:
            True if processing was successful, False otherwise
        """
        try:
            scores_list = []
            reason_list = []
            break_reason = ""
            last_provider = None
            last_model = None
            
            # Prepare input text
            input_text = f"{entry.title}\n{entry.description}\n{entry.full_text}"[:self.max_input_length]
            
            # Make multiple LLM calls for statistical analysis
            for llm_iteration in range(self.llm_calls_per_entry):
                try:
                    data, provider, model = self._call_llm(
                        prompt=self._llm_prompt,
                        schema=self._llm_schema,
                        input_text=input_text,
                        max_tokens=800
                    )
                    last_provider = provider
                    last_model = model
                    
                    # Store advertisement result on first iteration
                    if llm_iteration == 0:
                        ad_result = data.get("advertisement", {})
                        is_ad = ad_result.get("is_advertisement", 0.0)
                        ad_reason = ad_result.get("reason", "")
                    
                    # Collect sentiment scores
                    sentiment = data.get("sentiment", {})
                    scores_list.append({
                        "positive": sentiment.get("positive", 0.0),
                        "neutral": sentiment.get("neutral", 0.0),
                        "negative": sentiment.get("negative", 0.0)
                    })
                    reason_list.append(sentiment.get("reason", ""))
                    
                    logger.debug(f"LLM call {llm_iteration + 1}/{self.llm_calls_per_entry} successful for entry {entry.entry_id}")
                
                except Exception as e:
                    logger.warning(f"LLM call {llm_iteration + 1} failed for entry {entry.entry_id}: {e}")
                    break_reason = f"LLM call failed: {str(e)}"
                    break
            
            if not scores_list:
                logger.error(f"No successful LLM calls for entry {entry.entry_id}")
                return False
            
            # Calculate statistics from multiple calls
            stats = self._calculate_llm_statistics(scores_list)
            
            # Update database with results
            return self._update_entry_llm_analysis(
                entry, is_ad, ad_reason, stats, reason_list, 
                len(scores_list), break_reason, last_provider, last_model
            )
        
        except Exception as e:
            logger.error(f"Error in LLM processing for entry {entry.entry_id}: {e}")
            return False
    
    def _calculate_llm_statistics(self, scores_list: List[Dict[str, float]]) -> Dict[str, float]:
        """
        Calculate statistics from multiple LLM calls.
        
        Args:
            scores_list: List of sentiment score dictionaries
            
        Returns:
            Dictionary with mean, std, and diff statistics
        """
        if not scores_list:
            return {}
        
        # Calculate means
        pos_scores = [s["positive"] for s in scores_list]
        neu_scores = [s["neutral"] for s in scores_list]
        neg_scores = [s["negative"] for s in scores_list]
        
        pos_mean = sum(pos_scores) / len(pos_scores)
        neu_mean = sum(neu_scores) / len(neu_scores)
        neg_mean = sum(neg_scores) / len(neg_scores)
        
        # Calculate standard deviations
        if len(scores_list) > 1:
            pos_std = (sum((x - pos_mean) ** 2 for x in pos_scores) / (len(pos_scores) - 1)) ** 0.5
            neu_std = (sum((x - neu_mean) ** 2 for x in neu_scores) / (len(neu_scores) - 1)) ** 0.5
            neg_std = (sum((x - neg_mean) ** 2 for x in neg_scores) / (len(neg_scores) - 1)) ** 0.5
        else:
            pos_std = neu_std = neg_std = 0.0
        
        # Calculate min-max differences
        pos_diff = max(pos_scores) - min(pos_scores) if pos_scores else 0.0
        neu_diff = max(neu_scores) - min(neu_scores) if neu_scores else 0.0
        neg_diff = max(neg_scores) - min(neg_scores) if neg_scores else 0.0
        
        return {
            "positive_mean": pos_mean,
            "neutral_mean": neu_mean,
            "negative_mean": neg_mean,
            "positive_std": pos_std,
            "neutral_std": neu_std,
            "negative_std": neg_std,
            "positive_diff": pos_diff,
            "neutral_diff": neu_diff,
            "negative_diff": neg_diff
        }
    
    def _update_entry_llm_analysis(self, entry: Entry, is_ad: float, ad_reason: str,
                                 stats: Dict[str, float], reason_list: List[str],
                                 iterations: int, break_reason: str,
                                 llm_provider: str = None, llm_model: str = None) -> bool:
        """
        Update the entry's LLM analysis fields in the database.
        
        Args:
            entry: Entry object to update
            is_ad: Advertisement likelihood score
            ad_reason: Advertisement analysis reason
            stats: Statistical analysis results
            reason_list: List of sentiment analysis reasons
            iterations: Number of successful iterations
            break_reason: Reason for early termination if any
            llm_provider: Last used LLM provider
            llm_model: Last used LLM model
            
        Returns:
            True if update was successful, False otherwise
        """
        try:
            # Create a new database session for this update
            db_session = self.db_session_factory()
            
            try:
                # Prepare update data
                update_data = {
                    "llm_is_ad": is_ad,
                    "llm_is_ad_reason": ad_reason,
                    "llm_positive": stats.get("positive_mean", 0.0),
                    "llm_neutral": stats.get("neutral_mean", 0.0),
                    "llm_negative": stats.get("negative_mean", 0.0),
                    "llm_positive_std": stats.get("positive_std", 0.0),
                    "llm_neutral_std": stats.get("neutral_std", 0.0),
                    "llm_negative_std": stats.get("negative_std", 0.0),
                    "llm_positive_diff": stats.get("positive_diff", 0.0),
                    "llm_neutral_diff": stats.get("neutral_diff", 0.0),
                    "llm_negative_diff": stats.get("negative_diff", 0.0),
                    "llm_reason_list": json.dumps(reason_list),
                    "llm_iterations": iterations,
                    "llm_break_reason": break_reason,
                    "llm_provider": llm_provider,
                    "llm_model": llm_model
                }
                
                db_session.query(Entry).filter_by(entry_id=entry.entry_id).update(update_data)
                db_session.commit()
                
                logger.debug(f"Updated LLM analysis for entry {entry.entry_id}")
                return True
            
            except Exception as e:
                db_session.rollback()
                logger.error(f"Database error updating entry {entry.entry_id}: {e}")
                return False
            
            finally:
                db_session.close()
        
        except Exception as e:
            logger.error(f"Failed to create database session for entry {entry.entry_id}: {e}")
            return False
    
    def _can_make_llm_calls(self) -> bool:
        """
        Check if we can make LLM calls without hitting rate limits.
        
        Returns:
            True if calls can be made, False if rate limited
        """
        # Simple check - in a real implementation, this would check
        # rate limits for all providers and estimate capacity
        return True
    
    def _call_llm(self, prompt: str, schema: dict, input_text: str, 
                  max_tokens: int = 400, temperature: float = 0.1, 
                  top_p: float = 0.95) -> Tuple[dict, str, str]:
        """
        Make an LLM API call with fallback logic.
        
        Args:
            prompt: System prompt for the LLM
            schema: JSON schema for response validation
            input_text: User input text
            max_tokens: Maximum tokens in response
            temperature: Temperature for response generation
            top_p: Top-p parameter for response generation
            
        Returns:
            Tuple of (response_data, provider_name, model_name)
            
        Raises:
            RuntimeError: If all providers fail
        """
        for provider_cfg in self._providers:
            provider_name = provider_cfg.get("provider")
            model = provider_cfg.get("model")
            
            try:
                response, model_used = self._call_llm_provider(
                    provider_cfg, prompt, schema, input_text, 
                    max_tokens, temperature, top_p
                )
                return response, provider_name, model_used
            
            except Exception as e:
                logger.warning(f"{provider_name} LLM failed ({model}): {e}")
                continue
        
        raise RuntimeError("All LLM providers failed")
    
    def _call_llm_provider(self, provider_cfg: dict, prompt: str, schema: dict, 
                          input_text: str, max_tokens: int = 400, 
                          temperature: float = 0.1, top_p: float = 0.95) -> Tuple[dict, str]:
        """
        Make an LLM API call to a specific provider.
        
        Centralized function for making LLM API calls to various providers.
        Adapted from news_aggregator.py implementation.
        
        Args:
            provider_cfg: Dict with keys 'provider' (required), optional 'model', optional 'limit' (calls/hour)
            prompt: System prompt for the LLM
            schema: JSON schema for response validation
            input_text: User input text
            max_tokens: Maximum tokens in response (default: 400)
            temperature: Temperature for response generation (default: 0.1)
            top_p: Top-p parameter for response generation
            
        Returns:
            Tuple of (validated_json_response, model_name)
            
        Raises:
            requests.RequestException: If API call fails
            json.JSONDecodeError: If response is not valid JSON
            ValidationError: If response doesn't match schema
        """
        # Truncate input if needed
        input_text = input_text[:getattr(settings, 'NEWS_LLM_MAX_INPUT_LENGTH', 4000)]

        provider = provider_cfg.get('provider')
        model_override = provider_cfg.get('model')
        limit = provider_cfg.get('limit')

        # Simple per-hour sliding window rate limiting
        if limit is not None:
            now = time.time()
            window_start = now - 3600
            dq = self._llm_call_log.setdefault(provider, deque())
            # Drop events older than 1h
            while dq and dq[0] < window_start:
                dq.popleft()
            if len(dq) >= int(limit):
                raise RuntimeError(f"Rate limit reached for provider '{provider}': {len(dq)}/{limit} calls in the last hour")
            # Count this attempt
            dq.append(now)

        if provider == 'local':
            default_model = "qwen2.5-7b-instruct-1m@q4_k_m"
            model = model_override or default_model
            url = "http://127.0.0.1:1234/v1/chat/completions"
            headers = {}
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        elif provider == 'ionos':
            # apparently completely ignores response_format and/or tools
            default_model = 'mistralai/Mistral-7B-Instruct-v0.3'
            model = model_override or default_model
            url = 'https://openai.inference.de-txl.ionos.com/v1/chat/completions'
            headers = {"Authorization": f"Bearer {getattr(settings, 'IONOS_API_KEY', '')}"}
            # Define your tool using the schema
            tool = {
                "type": "function",
                "function": {
                    "name": "return_combined_analysis_result",
                    "description": "Returns an analysis of advertisement likelihood and sentiment scores.",
                    "parameters": schema
                }
            }
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"strict": True, "schema": schema},
                },
                "tools": [tool],
                "tool_choice": {
                    "type": "function",
                    "function": {"name": "return_combined_analysis_result"}
                },
            }

        elif provider == 'groq':
            # only supports json_object which does not support strict validation
            default_model = "meta-llama/llama-4-scout-17b-16e-instruct"
            model = model_override or default_model
            url = "https://api.groq.com/openai/v1/chat/completions"
            headers = {"Authorization": f"Bearer {getattr(settings, 'GROQ_API_KEY', '')}"}
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        elif provider == 'openrouter':
            url = "https://openrouter.ai/api/v1/chat/completions"
            headers = {"Authorization": f"Bearer {getattr(settings, 'OPENROUTER_API_KEY', '')}",
                       "Content-Type": "application/json"}
            default_model = "openai/gpt-oss-20b:free"
            model = model_override or default_model
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        elif provider == 'chutes':
            default_model = "Qwen/Qwen3-32B"  # works well
            model = model_override or default_model
            url = "https://llm.chutes.ai/v1/chat/completions"
            headers = {"Authorization": f"Bearer {getattr(settings, 'CHUTES_API_KEY', '')}"}
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        elif provider == 'together':
            default_model = "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free"
            model = model_override or default_model
            url = "https://api.together.xyz/v1/chat/completions"
            headers = {"Authorization": f"Bearer {getattr(settings, 'TOGETHER_API_KEY', '')}"}
            payload = {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": input_text}
                ],
                "model": model,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": top_p,
                "frequency_penalty": 0,
                "presence_penalty": 0,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"name": "LlmResponse", "strict": True, "schema": schema}
                },
            }
        else:
            raise ValueError(f"Unsupported provider: {provider}")

        # Make API call
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        try:
            jsonResponse = response.json()
        except Exception as e:
            raise ValueError(f"Non-JSON response: {response.text}")
        if "choices" not in jsonResponse:
            raise ValueError(f"Invalid response: {jsonResponse}")
        
        logger.info(f"{provider} LLM reply ({model}): {input_text.split(chr(10))[0]} -> {response.json()['choices'][0]['message']['content']}")
        response.raise_for_status()

        # Parse and validate response
        response_dict = response.json()
        message_content = response_dict['choices'][0]['message']['content']
        data = json.loads(message_content)
        validate(instance=data, schema=schema)

        return data, model
    
    def get_worker_specific_health(self) -> Dict:
        """
        Get health information specific to LLM analysis worker.
        
        Returns:
            Dictionary with worker-specific health metrics
        """
        health = super().get_health_status()
        
        # Add LLM analysis specific metrics
        health.update({
            'worker_type': 'LLMAnalysisWorker',
            'llm_calls_per_entry': self.llm_calls_per_entry,
            'max_input_length': self.max_input_length,
            'batch_optimize_calls': self.batch_optimize_calls,
            'rate_limit_buffer': self.rate_limit_buffer,
            'providers_configured': len(self._providers),
            'rate_limit_status': self._get_rate_limit_status(),
            'supported_stages': [ProcessingStage.LLM_ANALYSIS.value]
        })
        
        return health
    
    def _get_rate_limit_status(self) -> Dict[str, Dict]:
        """Get current rate limit status for all providers."""
        status = {}
        now = time.time()
        window_start = now - 3600
        
        for provider_name, call_log in self._llm_call_log.items():
            # Count calls in the last hour
            recent_calls = sum(1 for call_time in call_log if call_time >= window_start)
            
            status[provider_name] = {
                'calls_last_hour': recent_calls,
                'last_call': max(call_log) if call_log else None
            }
        
        return status