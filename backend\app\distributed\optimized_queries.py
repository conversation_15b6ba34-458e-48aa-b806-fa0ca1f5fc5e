"""
Optimized database queries for distributed ETL work queue management.

This module provides high-performance, transaction-safe queries for finding
and claiming work in a distributed processing environment.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text, select
from sqlalchemy.sql import Select
from sqlalchemy.exc import SQLAlchemyError

from backend.app.models.models import Entry
from backend.app.distributed.processing_stage import (
    ProcessingStage, ProcessingStatus, ProcessingStatusValue,
    get_stage_selection_criteria, resolve_filter_values, stage_creates_new_entries,
    stage_requires_singleton
)

logger = logging.getLogger(__name__)


class OptimizedWorkQueries:
    """
    High-performance database queries for distributed work queue management.
    
    Provides optimized queries with proper indexing, transaction isolation,
    and concurrent access handling for efficient work distribution.
    """
    
    def __init__(self, db_session: Session):
        """Initialize with database session."""
        self.db = db_session
    
    def find_claimable_entries_optimized(self,
                                       stage: ProcessingStage,
                                       batch_size: int,
                                       max_retries: int = 3,
                                       exclude_sources: Optional[List[str]] = None) -> List[Entry]:
        """
        Find entries available for claiming using optimized queries with smart filtering.

        Uses stage-specific selection criteria to filter entries at the database level,
        reducing data transfer and improving performance.

        Args:
            stage: Processing stage to find work for
            batch_size: Maximum number of entries to return
            max_retries: Maximum retries allowed for failed entries
            exclude_sources: Optional list of sources to exclude

        Returns:
            List of Entry objects ready for claiming
        """
        try:
            # Check if this stage creates new entries or requires singleton processing
            if stage_creates_new_entries(stage) or stage_requires_singleton(stage):
                logger.debug(f"Stage {stage.value} creates new entries or requires singleton processing, returning empty list")
                return []

            # Get stage-specific selection criteria
            criteria = get_stage_selection_criteria(stage)

            # Build base query with optimal index usage
            conditions = [
                Entry.claimed_by.is_(None),  # Use index on claimed_by
                Entry.claimed_at.is_(None)   # Use index on claimed_at
            ]

            # Apply mandatory field filters (fields that must not be None)
            for field in criteria.mandatory_fields:
                if hasattr(Entry, field):
                    conditions.append(getattr(Entry, field).isnot(None))
                else:
                    logger.warning(f"Field {field} not found in Entry model for stage {stage.value}")

            # Apply analysis field filters (fields that should be None)
            if criteria.analysis_fields:
                analysis_conditions = []
                for field in criteria.analysis_fields:
                    if hasattr(Entry, field):
                        analysis_conditions.append(getattr(Entry, field).is_(None))
                    else:
                        logger.warning(f"Field {field} not found in Entry model for stage {stage.value}")

                if analysis_conditions:
                    # At least one analysis field should be None
                    conditions.append(or_(*analysis_conditions))

            # Apply additional filters
            if criteria.filters:
                resolved_filters = resolve_filter_values(criteria.filters)
                for field, operator, value in resolved_filters:
                    if hasattr(Entry, field):
                        col = getattr(Entry, field)
                        if operator == '>=':
                            conditions.append(col >= value)
                        elif operator == '>':
                            conditions.append(col > value)
                        elif operator == '<=':
                            conditions.append(col <= value)
                        elif operator == '<':
                            conditions.append(col < value)
                        elif operator == '==':
                            conditions.append(col == value)
                        elif operator == '!=':
                            conditions.append(col != value)
                        else:
                            logger.warning(f"Unsupported operator {operator} for field {field}")
                    else:
                        logger.warning(f"Field {field} not found in Entry model for stage {stage.value}")

            # Add default time filter if no published filter was specified
            has_published_filter = any(field == 'published' for field, _, _ in criteria.filters)
            if not has_published_filter:
                # Default to entries from the last day (configurable)
                try:
                    from backend.app.core.config import settings
                    lookback_days = getattr(settings, 'NEWS_ETL_LOOKBACK_DAYS', 1)
                except ImportError:
                    lookback_days = 1

                cutoff_time = datetime.now(timezone.utc) - timedelta(days=lookback_days)
                conditions.append(Entry.published >= cutoff_time)

            # Add source exclusion if specified
            if exclude_sources:
                conditions.append(Entry.source.notin_(exclude_sources))

            # Build and execute query
            query = (
                select(Entry)
                .where(and_(*conditions))
                .order_by(Entry.published.desc())  # Use existing published index
                .limit(batch_size * 3)  # Get extra entries for final filtering
            )

            result = self.db.execute(query)
            entries = result.scalars().all()

            # Final filtering for processing status (this still needs to be done in Python
            # since processing_status is stored as JSON)
            claimable_entries = []
            for entry in entries:
                if len(claimable_entries) >= batch_size:
                    break

                if self._is_entry_claimable_for_stage(entry, stage, max_retries):
                    claimable_entries.append(entry)

            logger.debug(
                f"Smart filtering for stage {stage.value}: found {len(claimable_entries)} "
                f"claimable entries from {len(entries)} candidates"
            )

            return claimable_entries

        except SQLAlchemyError as e:
            logger.error(f"Failed to find claimable entries: {e}")
            raise
    
    def atomic_claim_batch(self, 
                          entry_ids: List[str], 
                          worker_id: str,
                          stage: ProcessingStage,
                          claim_timeout_minutes: int = 30) -> List[str]:
        """
        Atomically claim a batch of entries with proper transaction isolation.
        
        Uses SELECT FOR UPDATE to prevent race conditions and ensures
        only unclaimed entries are claimed.
        
        Args:
            entry_ids: List of entry IDs to attempt to claim
            worker_id: Worker claiming the entries
            stage: Processing stage being claimed for
            claim_timeout_minutes: Timeout for the claim
            
        Returns:
            List of entry IDs that were successfully claimed
            
        Raises:
            SQLAlchemyError: If transaction fails
        """
        if not entry_ids:
            return []
        
        try:
            # Ensure we start with a clean transaction state
            if self.db.in_transaction():
                self.db.rollback()
            
            # Use explicit transaction with proper isolation
            self.db.begin()
            
            try:
                # Lock entries for update to prevent race conditions
                locked_entries = (
                    self.db.query(Entry)
                    .filter(
                        and_(
                            Entry.entry_id.in_(entry_ids),
                            Entry.claimed_by.is_(None)  # Only unclaimed entries
                        )
                    )
                    .with_for_update(nowait=False)  # Wait for locks if needed
                    .all()
                )
                
                if not locked_entries:
                    logger.INFO(f"Failed to lock all claimable entries for update: {entry_ids}")
                    self.db.rollback()
                    return []
                
                # Update entries atomically
                claim_time = datetime.now(timezone.utc)
                claimed_ids = []
                
                for entry in locked_entries:
                    # Update processing status
                    processing_status = ProcessingStatus.from_json(entry.processing_status)
                    processing_status.mark_stage_in_progress(stage, worker_id)
                    
                    # Update entry fields
                    entry.processing_status = processing_status.to_json()
                    entry.claimed_by = worker_id
                    entry.claimed_at = claim_time
                    
                    claimed_ids.append(entry.entry_id)
                
                # Commit the transaction
                self.db.commit()
                
                logger.info(
                    f"Atomically claimed {len(claimed_ids)} entries for worker "
                    f"{worker_id}: {claimed_ids[:5]}..."
                )
                
                return claimed_ids
                
            except Exception as e:
                self.db.rollback()
                raise
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to atomically claim batch: {e}")
            raise
    
    def bulk_update_completion(self, 
                              entry_ids: List[str],
                              stage: ProcessingStage,
                              worker_id: str) -> int:
        """
        Bulk update entries to mark them as completed.
        
        Uses efficient bulk update operations for better performance
        when processing large batches.
        
        Args:
            entry_ids: List of entry IDs to mark as completed
            stage: Processing stage that was completed
            worker_id: Worker that completed the processing
            
        Returns:
            Number of entries that were updated
            
        Raises:
            SQLAlchemyError: If update fails
        """
        if not entry_ids:
            return 0
        
        try:
            with self.db.begin():
                # Get entries that need updating
                entries = (
                    self.db.query(Entry)
                    .filter(
                        and_(
                            Entry.entry_id.in_(entry_ids),
                            Entry.claimed_by == worker_id
                        )
                    )
                    .all()
                )
                
                updated_count = 0
                for entry in entries:
                    # Update processing status
                    processing_status = ProcessingStatus.from_json(entry.processing_status)
                    processing_status.mark_stage_completed(stage, worker_id)
                    
                    # Update entry
                    entry.processing_status = processing_status.to_json()
                    entry.claimed_by = None
                    entry.claimed_at = None
                    entry.last_error = None
                    
                    updated_count += 1
                
                logger.info(
                    f"Bulk updated {updated_count} entries as completed "
                    f"for stage {stage.value}"
                )
                
                return updated_count
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to bulk update completion: {e}")
            raise
    
    def efficient_stale_cleanup(self, 
                               timeout_minutes: int = 30,
                               batch_size: int = 1000) -> int:
        """
        Efficiently clean up stale claims using optimized queries.
        
        Processes stale claims in batches to avoid long-running transactions
        and uses indexes for optimal performance.
        
        Args:
            timeout_minutes: Age threshold for stale claims
            batch_size: Number of entries to process per batch
            
        Returns:
            Total number of stale claims cleaned up
            
        Raises:
            SQLAlchemyError: If cleanup fails
        """
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=timeout_minutes)
        total_cleaned = 0
        
        try:
            while True:
                with self.db.begin():
                    # Find batch of stale entries using index
                    stale_entries = (
                        self.db.query(Entry.entry_id)
                        .filter(
                            and_(
                                Entry.claimed_by.isnot(None),
                                Entry.claimed_at < cutoff_time
                            )
                        )
                        .limit(batch_size)
                        .all()
                    )
                    
                    if not stale_entries:
                        break
                    
                    stale_ids = [entry.entry_id for entry in stale_entries]
                    
                    # Bulk update to release claims
                    updated_count = (
                        self.db.query(Entry)
                        .filter(Entry.entry_id.in_(stale_ids))
                        .update({
                            Entry.claimed_by: None,
                            Entry.claimed_at: None
                        }, synchronize_session=False)
                    )
                    
                    total_cleaned += updated_count
                    
                    logger.debug(
                        f"Cleaned up {updated_count} stale claims in batch "
                        f"(total: {total_cleaned})"
                    )
            
            if total_cleaned > 0:
                logger.info(
                    f"Completed stale cleanup: {total_cleaned} claims released"
                )
            
            return total_cleaned
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to cleanup stale claims: {e}")
            raise
    
    def get_queue_metrics_optimized(self, 
                                   stages: Optional[List[ProcessingStage]] = None) -> Dict[str, Dict[str, int]]:
        """
        Get comprehensive queue metrics using optimized aggregation queries.
        
        Uses database-level aggregation for better performance when
        calculating statistics across large datasets.
        
        Args:
            stages: Optional list of stages to get metrics for (default: all)
            
        Returns:
            Dictionary mapping stage names to their metrics
        """
        if stages is None:
            stages = list(ProcessingStage)
        
        try:
            # Get basic counts using database aggregation
            basic_metrics = self._get_basic_queue_metrics()
            
            # Get stage-specific metrics
            stage_metrics = {}
            for stage in stages:
                stage_metrics[stage.value] = self._get_stage_metrics(stage, basic_metrics)
            
            return stage_metrics
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get queue metrics: {e}")
            return {}
    
    def find_entries_by_worker(self, 
                              worker_id: str,
                              include_completed: bool = False) -> List[Entry]:
        """
        Find all entries currently or previously processed by a specific worker.
        
        Args:
            worker_id: Worker ID to search for
            include_completed: Whether to include completed entries
            
        Returns:
            List of entries associated with the worker
        """
        try:
            query = self.db.query(Entry)
            
            if include_completed:
                # Search in both claimed_by and processing_status
                query = query.filter(
                    or_(
                        Entry.claimed_by == worker_id,
                        Entry.processing_status.like(f'%"worker_id": "{worker_id}"%')
                    )
                )
            else:
                # Only currently claimed entries
                query = query.filter(Entry.claimed_by == worker_id)
            
            return query.all()
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to find entries by worker {worker_id}: {e}")
            return []
    
    def _is_entry_claimable_for_stage(self, 
                                     entry: Entry, 
                                     stage: ProcessingStage,
                                     max_retries: int) -> bool:
        """
        Check if an entry can be claimed for a specific processing stage.
        
        Args:
            entry: Entry to check
            stage: Processing stage to check for
            max_retries: Maximum retries allowed
            
        Returns:
            True if entry can be claimed for the stage
        """
        # Parse processing status
        processing_status = ProcessingStatus.from_json(entry.processing_status)
        
        # Check if stage is pending
        if processing_status.is_stage_pending(stage):
            return True
        
        # Check if stage failed but has retries remaining
        if processing_status.is_stage_failed(stage):
            retry_count = processing_status.get_retry_count(stage)
            return retry_count < max_retries
        
        # Stage is completed, for the time being return it's claimable
        if processing_status.is_stage_completed(stage):
            return True

        # Stage is in progress - not claimable
        return False
    
    def _get_basic_queue_metrics(self) -> Dict[str, int]:
        """Get basic queue metrics using database aggregation."""
        # Count total entries
        total_entries = self.db.query(func.count(Entry.entry_id)).scalar()
        
        # Count claimed entries
        claimed_entries = (
            self.db.query(func.count(Entry.entry_id))
            .filter(Entry.claimed_by.isnot(None))
            .scalar()
        )
        
        # Count entries with errors
        error_entries = (
            self.db.query(func.count(Entry.entry_id))
            .filter(Entry.last_error.isnot(None))
            .scalar()
        )
        
        return {
            'total': total_entries or 0,
            'claimed': claimed_entries or 0,
            'with_errors': error_entries or 0
        }
    
    def _get_stage_metrics(self, 
                          stage: ProcessingStage, 
                          basic_metrics: Dict[str, int]) -> Dict[str, int]:
        """
        Get metrics for a specific processing stage.
        
        Note: This requires parsing JSON in Python since database JSON
        querying capabilities vary by database type.
        """
        # Sample a subset of entries for stage-specific metrics
        # This is a trade-off between accuracy and performance
        sample_size = min(1000, basic_metrics['total'])
        
        if sample_size == 0:
            return {
                'pending': 0,
                'in_progress': 0,
                'completed': 0,
                'failed': 0
            }
        
        # Get sample of entries (Oracle uses DBMS_RANDOM.VALUE instead of random())
        sample_entries = (
            self.db.query(Entry.processing_status)
            .order_by(func.dbms_random.value())  # Oracle random sampling
            .limit(sample_size)
            .all()
        )
        
        # Count statuses in sample
        status_counts = {
            'pending': 0,
            'in_progress': 0,
            'completed': 0,
            'failed': 0
        }
        
        for entry_status in sample_entries:
            processing_status = ProcessingStatus.from_json(entry_status.processing_status)
            stage_status = processing_status.get_stage_status(stage)
            status_counts[stage_status.status.value] += 1
        
        # Scale up to estimate total counts
        if sample_size < basic_metrics['total']:
            scale_factor = basic_metrics['total'] / sample_size
            for key in status_counts:
                status_counts[key] = int(status_counts[key] * scale_factor)
        
        return status_counts