# SQL Query Examples for Natural Language Descriptions

Here are some example natural language descriptions you can use with the "Get Help with SQL Query" feature:

## Basic Queries

1. "Show me the 10 most recent news articles"
2. "List all news sources in the database"
3. "Count how many articles we have from each source"
4. "Find all articles published in the last 24 hours"

## Sentiment Analysis Queries

1. "Show me the most positive news articles from the last week"
2. "Find articles with high positive sentiment (over 0.8) that are not advertisements"
3. "Compare average sentiment scores across different news sources"
4. "List articles with conflicting sentiment scores between different analysis methods"

## Category and Content Queries

1. "Show me all news categories and how many articles are in each"
2. "Find articles about technology with positive sentiment"
3. "List articles that might be duplicates of other articles"
4. "Show me articles with the longest full text content"

## Advanced Queries

1. "Calculate the average sentiment score by day of week to see which day has the most positive news"
2. "Find sources that consistently publish more positive news than negative"
3. "Compare sentiment scores between articles with and without full text"
4. "Show me the distribution of sentiment scores across all articles"

## Tips for Writing Good Descriptions

1. Be specific about what data you want to see
2. Mention any filters or conditions (time periods, sentiment thresholds, etc.)
3. Specify how you want the results ordered or grouped
4. Indicate if you want to limit the number of results
