"""
Stale work cleanup and recovery system for distributed ETL processing.

This module provides background cleanup of abandoned work, automatic recovery
from worker failures, and manual intervention tools for stuck entries.
"""

import logging
import threading
import time
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Optional, Callable, Set, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, func, text, desc

from backend.app.models.models import Entry
from backend.app.distributed.processing_stage import ProcessingStage, ProcessingStatus, ProcessingStatusValue
from backend.app.distributed.work_queue_manager import WorkQueueManager
from backend.app.distributed.worker_health_manager import WorkerHealthManager

logger = logging.getLogger(__name__)


class CleanupReason(Enum):
    """Reasons for cleanup actions."""
    STALE_CLAIM = "stale_claim"
    WORKER_FAILURE = "worker_failure"
    TIMEOUT = "timeout"
    MANUAL_INTERVENTION = "manual_intervention"
    STUCK_PROCESSING = "stuck_processing"
    ORPHANED_WORK = "orphaned_work"


class RecoveryAction(Enum):
    """Types of recovery actions."""
    RELEASE_CLAIM = "release_claim"
    RESET_STATUS = "reset_status"
    MARK_FAILED = "mark_failed"
    DEAD_LETTER = "dead_letter"
    MANUAL_REVIEW = "manual_review"


@dataclass
class CleanupResult:
    """Result of a cleanup operation."""
    entries_processed: int
    entries_released: int
    entries_reset: int
    entries_failed: int
    entries_dead_lettered: int
    cleanup_reason: CleanupReason
    recovery_actions: List[RecoveryAction]
    timestamp: datetime
    details: Dict[str, Any]


@dataclass
class StuckEntry:
    """Information about a stuck entry requiring intervention."""
    entry_id: str
    stage: ProcessingStage
    claimed_by: Optional[str]
    claimed_at: Optional[datetime]
    last_error: Optional[str]
    retry_count: int
    processing_status: Dict[str, Any]
    stuck_reason: str
    suggested_action: RecoveryAction
    manual_intervention_required: bool


class StaleWorkCleanupSystem:
    """
    Background system for cleaning up stale work and recovering from failures.
    
    Provides automatic cleanup of abandoned work, detection of stuck entries,
    and recovery mechanisms for various failure scenarios.
    """
    
    def __init__(self, 
                 db_session_factory: Callable[[], Session],
                 cleanup_interval_seconds: int = 300,  # 5 minutes
                 stale_claim_timeout_minutes: int = 30,
                 stuck_processing_timeout_minutes: int = 120,  # 2 hours
                 max_retry_age_hours: int = 24):
        """
        Initialize the cleanup system.
        
        Args:
            db_session_factory: Factory function to create database sessions
            cleanup_interval_seconds: How often to run cleanup
            stale_claim_timeout_minutes: Minutes after which claims are stale
            stuck_processing_timeout_minutes: Minutes after which processing is stuck
            max_retry_age_hours: Hours after which retry entries are cleaned up
        """
        self.db_session_factory = db_session_factory
        self.cleanup_interval_seconds = cleanup_interval_seconds
        self.stale_claim_timeout_minutes = stale_claim_timeout_minutes
        self.stuck_processing_timeout_minutes = stuck_processing_timeout_minutes
        self.max_retry_age_hours = max_retry_age_hours
        
        # System state
        self._running = False
        self._cleanup_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        
        # Statistics
        self._stats = {
            'cleanup_cycles': 0,
            'total_entries_cleaned': 0,
            'total_entries_released': 0,
            'total_entries_reset': 0,
            'total_stuck_entries_found': 0,
            'last_cleanup_time': None,
            'last_error': None
        }
        
        # Callbacks for notifications
        self._cleanup_callbacks: List[Callable[[CleanupResult], None]] = []
        self._stuck_entry_callbacks: List[Callable[[List[StuckEntry]], None]] = []
    
    def start(self) -> None:
        """Start the cleanup system."""
        if self._running:
            logger.warning("Cleanup system is already running")
            return
        
        logger.info(
            f"Starting stale work cleanup system "
            f"(interval: {self.cleanup_interval_seconds}s, "
            f"stale_timeout: {self.stale_claim_timeout_minutes}m)"
        )
        
        self._running = True
        self._shutdown_event.clear()
        
        # Start cleanup thread
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            name="stale-work-cleanup",
            daemon=True
        )
        self._cleanup_thread.start()
        
        logger.info("Stale work cleanup system started")
    
    def stop(self, timeout: int = 30) -> None:
        """Stop the cleanup system."""
        if not self._running:
            return
        
        logger.info("Stopping stale work cleanup system...")
        
        self._running = False
        self._shutdown_event.set()
        
        # Wait for cleanup thread to finish
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=timeout)
            
            if self._cleanup_thread.is_alive():
                logger.warning("Cleanup thread did not stop within timeout")
        
        logger.info("Stale work cleanup system stopped")
    
    def add_cleanup_callback(self, callback: Callable[[CleanupResult], None]) -> None:
        """Add callback to be called when cleanup is performed."""
        self._cleanup_callbacks.append(callback)
    
    def add_stuck_entry_callback(self, callback: Callable[[List[StuckEntry]], None]) -> None:
        """Add callback to be called when stuck entries are found."""
        self._stuck_entry_callbacks.append(callback)
    
    def _cleanup_loop(self) -> None:
        """Main cleanup loop."""
        logger.info("Stale work cleanup loop started")
        
        while self._running and not self._shutdown_event.is_set():
            try:
                # Perform cleanup cycle
                self._perform_cleanup_cycle()
                
                # Wait for next cycle
                self._shutdown_event.wait(self.cleanup_interval_seconds)
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}", exc_info=True)
                self._stats['last_error'] = str(e)
                
                # Wait before retrying
                self._shutdown_event.wait(min(self.cleanup_interval_seconds, 60))
        
        logger.info("Stale work cleanup loop stopped")
    
    def _perform_cleanup_cycle(self) -> None:
        """Perform a single cleanup cycle."""
        db_session = None
        
        try:
            # Create database session
            db_session = self.db_session_factory()
            
            # Perform various cleanup operations
            cleanup_results = []
            
            # 1. Clean up stale claims
            stale_result = self._cleanup_stale_claims(db_session)
            if stale_result.entries_processed > 0:
                cleanup_results.append(stale_result)
            
            # 2. Recover from worker failures
            worker_failure_result = self._recover_from_worker_failures(db_session)
            if worker_failure_result.entries_processed > 0:
                cleanup_results.append(worker_failure_result)
            
            # 3. Clean up stuck processing
            stuck_result = self._cleanup_stuck_processing(db_session)
            if stuck_result.entries_processed > 0:
                cleanup_results.append(stuck_result)
            
            # 4. Clean up old retry entries
            retry_result = self._cleanup_old_retry_entries(db_session)
            if retry_result.entries_processed > 0:
                cleanup_results.append(retry_result)
            
            # 5. Find entries requiring manual intervention
            stuck_entries = self._find_stuck_entries(db_session)
            if stuck_entries:
                self._notify_stuck_entries(stuck_entries)
            
            # Update statistics
            self._update_cleanup_stats(cleanup_results)
            
            # Notify callbacks
            for result in cleanup_results:
                self._notify_cleanup_callbacks(result)
            
        except Exception as e:
            logger.error(f"Error performing cleanup cycle: {e}", exc_info=True)
            self._stats['last_error'] = str(e)
            raise
        
        finally:
            if db_session:
                db_session.close()
    
    def _cleanup_stale_claims(self, db_session: Session) -> CleanupResult:
        """Clean up stale claims from inactive workers."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=self.stale_claim_timeout_minutes)
        
        try:
            with db_session.begin():
                # Find entries with stale claims
                stale_entries = (
                    db_session.query(Entry)
                    .filter(
                        and_(
                            Entry.claimed_by.isnot(None),
                            Entry.claimed_at < cutoff_time
                        )
                    )
                    .all()
                )
                
                entries_released = 0
                if stale_entries:
                    entry_ids = [entry.entry_id for entry in stale_entries]
                    
                    # Release stale claims
                    updated_count = (
                        db_session.query(Entry)
                        .filter(Entry.entry_id.in_(entry_ids))
                        .update({
                            Entry.claimed_by: None,
                            Entry.claimed_at: None
                        }, synchronize_session=False)
                    )
                    
                    entries_released = updated_count
                    
                    logger.info(
                        f"Released {entries_released} stale claims older than "
                        f"{self.stale_claim_timeout_minutes} minutes"
                    )
                
                return CleanupResult(
                    entries_processed=len(stale_entries),
                    entries_released=entries_released,
                    entries_reset=0,
                    entries_failed=0,
                    entries_dead_lettered=0,
                    cleanup_reason=CleanupReason.STALE_CLAIM,
                    recovery_actions=[RecoveryAction.RELEASE_CLAIM],
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'cutoff_time': cutoff_time.isoformat(),
                        'timeout_minutes': self.stale_claim_timeout_minutes
                    }
                )
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to cleanup stale claims: {e}")
            db_session.rollback()
            raise
    
    def _recover_from_worker_failures(self, db_session: Session) -> CleanupResult:
        """Recover work from failed workers."""
        try:
            health_manager = WorkerHealthManager(db_session)
            
            # Detect failed workers
            failed_workers = health_manager.detect_stale_workers(
                stale_threshold_minutes=self.stale_claim_timeout_minutes
            )
            
            if not failed_workers:
                return CleanupResult(
                    entries_processed=0,
                    entries_released=0,
                    entries_reset=0,
                    entries_failed=0,
                    entries_dead_lettered=0,
                    cleanup_reason=CleanupReason.WORKER_FAILURE,
                    recovery_actions=[],
                    timestamp=datetime.now(timezone.utc),
                    details={}
                )
            
            # Get worker IDs
            failed_worker_ids = [worker['worker_id'] for worker in failed_workers]
            
            with db_session.begin():
                # Find entries claimed by failed workers
                failed_worker_entries = (
                    db_session.query(Entry)
                    .filter(Entry.claimed_by.in_(failed_worker_ids))
                    .all()
                )
                
                entries_released = 0
                if failed_worker_entries:
                    entry_ids = [entry.entry_id for entry in failed_worker_entries]
                    
                    # Release claims from failed workers
                    updated_count = (
                        db_session.query(Entry)
                        .filter(Entry.entry_id.in_(entry_ids))
                        .update({
                            Entry.claimed_by: None,
                            Entry.claimed_at: None
                        }, synchronize_session=False)
                    )
                    
                    entries_released = updated_count
                    
                    logger.warning(
                        f"Released {entries_released} entries from {len(failed_worker_ids)} failed workers"
                    )
                
                # Mark workers as failed in health system
                recovery_stats = health_manager.recover_failed_workers(
                    stale_threshold_minutes=self.stale_claim_timeout_minutes,
                    claim_timeout_minutes=self.stale_claim_timeout_minutes
                )
                
                return CleanupResult(
                    entries_processed=len(failed_worker_entries),
                    entries_released=entries_released,
                    entries_reset=0,
                    entries_failed=0,
                    entries_dead_lettered=0,
                    cleanup_reason=CleanupReason.WORKER_FAILURE,
                    recovery_actions=[RecoveryAction.RELEASE_CLAIM],
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'failed_workers': failed_worker_ids,
                        'recovery_stats': recovery_stats
                    }
                )
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to recover from worker failures: {e}")
            db_session.rollback()
            raise
    
    def _cleanup_stuck_processing(self, db_session: Session) -> CleanupResult:
        """Clean up entries that have been processing for too long."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=self.stuck_processing_timeout_minutes)
        
        try:
            with db_session.begin():
                # Find entries that have been claimed for too long
                stuck_entries = (
                    db_session.query(Entry)
                    .filter(
                        and_(
                            Entry.claimed_by.isnot(None),
                            Entry.claimed_at < cutoff_time
                        )
                    )
                    .all()
                )
                
                entries_reset = 0
                entries_failed = 0
                
                for entry in stuck_entries:
                    try:
                        # Parse processing status
                        processing_status = ProcessingStatus.from_json(entry.processing_status)
                        
                        # Check if any stage is in progress for too long
                        should_reset = False
                        should_fail = False
                        
                        for stage in ProcessingStage:
                            stage_status = processing_status.get_stage_status(stage)
                            if stage_status.status == ProcessingStatusValue.IN_PROGRESS:
                                # Check if this stage has been processing too long
                                if entry.retry_count >= 3:  # Too many retries
                                    should_fail = True
                                else:
                                    should_reset = True
                                break
                        
                        if should_fail:
                            # Mark as failed
                            entry.last_error = f"Stuck processing timeout after {self.stuck_processing_timeout_minutes} minutes"
                            entry.claimed_by = None
                            entry.claimed_at = None
                            entry.retry_count += 1
                            entries_failed += 1
                        elif should_reset:
                            # Reset to pending
                            entry.claimed_by = None
                            entry.claimed_at = None
                            entries_reset += 1
                    
                    except Exception as e:
                        logger.warning(f"Error processing stuck entry {entry.entry_id}: {e}")
                        # Default to releasing claim
                        entry.claimed_by = None
                        entry.claimed_at = None
                        entries_reset += 1
                
                if entries_reset > 0 or entries_failed > 0:
                    logger.info(
                        f"Cleaned up stuck processing: {entries_reset} reset, {entries_failed} failed"
                    )
                
                return CleanupResult(
                    entries_processed=len(stuck_entries),
                    entries_released=0,
                    entries_reset=entries_reset,
                    entries_failed=entries_failed,
                    entries_dead_lettered=0,
                    cleanup_reason=CleanupReason.STUCK_PROCESSING,
                    recovery_actions=[RecoveryAction.RESET_STATUS, RecoveryAction.MARK_FAILED],
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'cutoff_time': cutoff_time.isoformat(),
                        'timeout_minutes': self.stuck_processing_timeout_minutes
                    }
                )
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to cleanup stuck processing: {e}")
            db_session.rollback()
            raise
    
    def _cleanup_old_retry_entries(self, db_session: Session) -> CleanupResult:
        """Clean up old retry entries that are no longer relevant."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=self.max_retry_age_hours)
        
        try:
            with db_session.begin():
                # Find entries with old retry information
                old_retry_entries = (
                    db_session.query(Entry)
                    .filter(
                        and_(
                            Entry.processing_status.like('%retry_info%'),
                            Entry.claimed_by.is_(None),  # Not currently claimed
                            Entry.claimed_at < cutoff_time  # Old timestamp
                        )
                    )
                    .limit(1000)  # Process in batches
                    .all()
                )
                
                entries_cleaned = 0
                
                for entry in old_retry_entries:
                    try:
                        # Parse and clean retry information
                        if entry.processing_status:
                            import json
                            status_data = json.loads(entry.processing_status)
                            
                            # Remove old retry info from all stages
                            cleaned = False
                            for stage_name, stage_data in status_data.items():
                                if isinstance(stage_data, dict) and 'retry_info' in stage_data:
                                    retry_info = stage_data['retry_info']
                                    if 'next_retry_at' in retry_info:
                                        retry_time = datetime.fromisoformat(retry_info['next_retry_at'])
                                        if retry_time < cutoff_time:
                                            del stage_data['retry_info']
                                            cleaned = True
                            
                            if cleaned:
                                entry.processing_status = json.dumps(status_data)
                                entries_cleaned += 1
                    
                    except Exception as e:
                        logger.warning(f"Error cleaning retry info for entry {entry.entry_id}: {e}")
                
                if entries_cleaned > 0:
                    logger.info(f"Cleaned retry info from {entries_cleaned} old entries")
                
                return CleanupResult(
                    entries_processed=len(old_retry_entries),
                    entries_released=0,
                    entries_reset=entries_cleaned,
                    entries_failed=0,
                    entries_dead_lettered=0,
                    cleanup_reason=CleanupReason.TIMEOUT,
                    recovery_actions=[RecoveryAction.RESET_STATUS],
                    timestamp=datetime.now(timezone.utc),
                    details={
                        'cutoff_time': cutoff_time.isoformat(),
                        'max_age_hours': self.max_retry_age_hours
                    }
                )
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to cleanup old retry entries: {e}")
            db_session.rollback()
            raise
    
    def _find_stuck_entries(self, db_session: Session) -> List[StuckEntry]:
        """Find entries that require manual intervention."""
        try:
            # Find entries with high retry counts
            high_retry_entries = (
                db_session.query(Entry)
                .filter(Entry.retry_count >= 5)
                .order_by(desc(Entry.retry_count))
                .limit(100)
                .all()
            )
            
            stuck_entries = []
            
            for entry in high_retry_entries:
                try:
                    # Analyze why entry is stuck
                    stuck_reason, suggested_action = self._analyze_stuck_entry(entry)
                    
                    if stuck_reason:
                        stuck_entry = StuckEntry(
                            entry_id=entry.entry_id,
                            stage=self._get_current_stage(entry),
                            claimed_by=entry.claimed_by,
                            claimed_at=entry.claimed_at,
                            last_error=entry.last_error,
                            retry_count=entry.retry_count,
                            processing_status=self._parse_processing_status(entry.processing_status),
                            stuck_reason=stuck_reason,
                            suggested_action=suggested_action,
                            manual_intervention_required=True
                        )
                        
                        stuck_entries.append(stuck_entry)
                
                except Exception as e:
                    logger.warning(f"Error analyzing stuck entry {entry.entry_id}: {e}")
            
            return stuck_entries
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to find stuck entries: {e}")
            return []
    
    def _analyze_stuck_entry(self, entry: Entry) -> Tuple[Optional[str], RecoveryAction]:
        """Analyze why an entry is stuck and suggest recovery action."""
        if entry.retry_count >= 10:
            return "Too many retries", RecoveryAction.DEAD_LETTER
        
        if entry.last_error:
            error_lower = entry.last_error.lower()
            
            if "unauthorized" in error_lower or "forbidden" in error_lower:
                return "Authentication error", RecoveryAction.MANUAL_REVIEW
            
            if "not found" in error_lower or "404" in error_lower:
                return "Resource not found", RecoveryAction.MARK_FAILED
            
            if "timeout" in error_lower:
                return "Persistent timeout", RecoveryAction.RESET_STATUS
            
            if "rate limit" in error_lower:
                return "Rate limiting", RecoveryAction.RESET_STATUS
        
        if entry.claimed_by and entry.claimed_at:
            age = datetime.now(timezone.utc) - entry.claimed_at
            if age.total_seconds() > 3600:  # 1 hour
                return "Long running claim", RecoveryAction.RELEASE_CLAIM
        
        return "High retry count", RecoveryAction.MANUAL_REVIEW
    
    def _get_current_stage(self, entry: Entry) -> ProcessingStage:
        """Get the current processing stage for an entry."""
        try:
            processing_status = ProcessingStatus.from_json(entry.processing_status)
            
            # Find first non-completed stage
            for stage in ProcessingStage:
                if not processing_status.is_stage_completed(stage):
                    return stage
            
            # Default to first stage if all completed
            return ProcessingStage.DOWNLOAD_FEEDS
        
        except Exception:
            return ProcessingStage.DOWNLOAD_FEEDS
    
    def _parse_processing_status(self, processing_status_json: Optional[str]) -> Dict[str, Any]:
        """Parse processing status JSON safely."""
        try:
            if processing_status_json:
                import json
                return json.loads(processing_status_json)
            return {}
        except Exception:
            return {}
    
    def _update_cleanup_stats(self, cleanup_results: List[CleanupResult]) -> None:
        """Update cleanup statistics."""
        self._stats['cleanup_cycles'] += 1
        self._stats['last_cleanup_time'] = datetime.now(timezone.utc).isoformat()
        
        for result in cleanup_results:
            self._stats['total_entries_cleaned'] += result.entries_processed
            self._stats['total_entries_released'] += result.entries_released
            self._stats['total_entries_reset'] += result.entries_reset
    
    def _notify_cleanup_callbacks(self, result: CleanupResult) -> None:
        """Notify cleanup callbacks."""
        for callback in self._cleanup_callbacks:
            try:
                callback(result)
            except Exception as e:
                logger.error(f"Error in cleanup callback: {e}")
    
    def _notify_stuck_entries(self, stuck_entries: List[StuckEntry]) -> None:
        """Notify stuck entry callbacks."""
        self._stats['total_stuck_entries_found'] += len(stuck_entries)
        
        for callback in self._stuck_entry_callbacks:
            try:
                callback(stuck_entries)
            except Exception as e:
                logger.error(f"Error in stuck entry callback: {e}")
    
    def force_cleanup(self) -> Dict[str, Any]:
        """Force an immediate cleanup cycle."""
        if not self._running:
            raise RuntimeError("Cleanup system is not running")
        
        logger.info("Forcing immediate cleanup cycle")
        
        try:
            self._perform_cleanup_cycle()
            return {
                "status": "success",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "stats": self.get_cleanup_stats()
            }
        except Exception as e:
            logger.error(f"Error in forced cleanup: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def get_cleanup_stats(self) -> Dict[str, Any]:
        """Get cleanup system statistics."""
        return {
            'running': self._running,
            'cleanup_interval_seconds': self.cleanup_interval_seconds,
            'stale_claim_timeout_minutes': self.stale_claim_timeout_minutes,
            'stuck_processing_timeout_minutes': self.stuck_processing_timeout_minutes,
            'max_retry_age_hours': self.max_retry_age_hours,
            'stats': self._stats.copy()
        }
    
    @property
    def is_running(self) -> bool:
        """Check if cleanup system is running."""
        return self._running


class ManualInterventionTools:
    """
    Tools for manual intervention on stuck entries.
    
    Provides utilities for operators to manually resolve stuck entries,
    reset processing states, and manage problematic entries.
    """
    
    def __init__(self, db_session: Session):
        """Initialize with database session."""
        self.db = db_session
    
    def get_stuck_entries(self, 
                         min_retry_count: int = 3,
                         limit: int = 100) -> List[StuckEntry]:
        """
        Get entries that appear to be stuck.
        
        Args:
            min_retry_count: Minimum retry count to consider stuck
            limit: Maximum number of entries to return
            
        Returns:
            List of stuck entries
        """
        try:
            entries = (
                self.db.query(Entry)
                .filter(Entry.retry_count >= min_retry_count)
                .order_by(desc(Entry.retry_count), desc(Entry.claimed_at))
                .limit(limit)
                .all()
            )
            
            stuck_entries = []
            
            for entry in entries:
                try:
                    # Analyze entry
                    stuck_reason, suggested_action = self._analyze_entry(entry)
                    
                    stuck_entry = StuckEntry(
                        entry_id=entry.entry_id,
                        stage=self._get_current_stage(entry),
                        claimed_by=entry.claimed_by,
                        claimed_at=entry.claimed_at,
                        last_error=entry.last_error,
                        retry_count=entry.retry_count,
                        processing_status=self._parse_processing_status(entry.processing_status),
                        stuck_reason=stuck_reason,
                        suggested_action=suggested_action,
                        manual_intervention_required=True
                    )
                    
                    stuck_entries.append(stuck_entry)
                
                except Exception as e:
                    logger.warning(f"Error analyzing entry {entry.entry_id}: {e}")
            
            return stuck_entries
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get stuck entries: {e}")
            return []
    
    def reset_entry_processing(self, 
                              entry_id: str,
                              stage: Optional[ProcessingStage] = None,
                              reset_retry_count: bool = True) -> bool:
        """
        Reset processing state for an entry.
        
        Args:
            entry_id: Entry ID to reset
            stage: Specific stage to reset (None for all)
            reset_retry_count: Whether to reset retry count
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.db.begin():
                entry = self.db.query(Entry).filter(Entry.entry_id == entry_id).first()
                if not entry:
                    logger.error(f"Entry {entry_id} not found")
                    return False
                
                # Parse processing status
                processing_status = ProcessingStatus.from_json(entry.processing_status)
                
                if stage:
                    # Reset specific stage
                    processing_status.set_stage_status(stage, ProcessingStatusValue.PENDING)
                else:
                    # Reset all stages
                    for s in ProcessingStage:
                        if not processing_status.is_stage_completed(s):
                            processing_status.set_stage_status(s, ProcessingStatusValue.PENDING)
                
                # Update entry
                entry.processing_status = processing_status.to_json()
                entry.claimed_by = None
                entry.claimed_at = None
                entry.last_error = None
                
                if reset_retry_count:
                    entry.retry_count = 0
                
                logger.info(f"Reset processing for entry {entry_id}")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to reset entry processing: {e}")
            self.db.rollback()
            return False
    
    def mark_entry_failed(self, 
                         entry_id: str,
                         stage: ProcessingStage,
                         reason: str) -> bool:
        """
        Mark an entry as failed for a specific stage.
        
        Args:
            entry_id: Entry ID to mark as failed
            stage: Processing stage that failed
            reason: Reason for failure
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.db.begin():
                entry = self.db.query(Entry).filter(Entry.entry_id == entry_id).first()
                if not entry:
                    logger.error(f"Entry {entry_id} not found")
                    return False
                
                # Parse and update processing status
                processing_status = ProcessingStatus.from_json(entry.processing_status)
                processing_status.mark_stage_failed(stage, "manual_intervention", reason)
                
                # Update entry
                entry.processing_status = processing_status.to_json()
                entry.claimed_by = None
                entry.claimed_at = None
                entry.last_error = f"MANUAL_FAILURE: {reason}"
                entry.retry_count += 1
                
                logger.info(f"Marked entry {entry_id} as failed for stage {stage.value}: {reason}")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to mark entry as failed: {e}")
            self.db.rollback()
            return False
    
    def release_entry_claim(self, entry_id: str) -> bool:
        """
        Release claim on an entry.
        
        Args:
            entry_id: Entry ID to release
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with self.db.begin():
                entry = self.db.query(Entry).filter(Entry.entry_id == entry_id).first()
                if not entry:
                    logger.error(f"Entry {entry_id} not found")
                    return False
                
                # Release claim
                entry.claimed_by = None
                entry.claimed_at = None
                
                logger.info(f"Released claim on entry {entry_id}")
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to release entry claim: {e}")
            self.db.rollback()
            return False
    
    def get_entry_details(self, entry_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about an entry.
        
        Args:
            entry_id: Entry ID to get details for
            
        Returns:
            Dictionary with entry details or None if not found
        """
        try:
            entry = self.db.query(Entry).filter(Entry.entry_id == entry_id).first()
            if not entry:
                return None
            
            processing_status = ProcessingStatus.from_json(entry.processing_status)
            
            return {
                'entry_id': entry.entry_id,
                'title': getattr(entry, 'title', 'Unknown'),
                'published': entry.published.isoformat() if entry.published else None,
                'claimed_by': entry.claimed_by,
                'claimed_at': entry.claimed_at.isoformat() if entry.claimed_at else None,
                'last_error': entry.last_error,
                'retry_count': entry.retry_count,
                'processing_status': self._parse_processing_status(entry.processing_status),
                'stage_summary': {
                    stage.value: {
                        'status': processing_status.get_stage_status(stage).status.value,
                        'completed': processing_status.is_stage_completed(stage),
                        'failed': processing_status.is_stage_failed(stage),
                        'retry_count': processing_status.get_retry_count(stage)
                    }
                    for stage in ProcessingStage
                }
            }
            
        except SQLAlchemyError as e:
            logger.error(f"Failed to get entry details: {e}")
            return None
    
    def _analyze_entry(self, entry: Entry) -> Tuple[str, RecoveryAction]:
        """Analyze an entry to determine why it's stuck."""
        if entry.retry_count >= 10:
            return "Excessive retries", RecoveryAction.DEAD_LETTER
        
        if entry.last_error:
            error_lower = entry.last_error.lower()
            
            if "dead_letter" in error_lower:
                return "Already in dead letter queue", RecoveryAction.MANUAL_REVIEW
            
            if "unauthorized" in error_lower or "forbidden" in error_lower:
                return "Authentication/authorization error", RecoveryAction.MANUAL_REVIEW
            
            if "not found" in error_lower or "404" in error_lower:
                return "Resource not found", RecoveryAction.MARK_FAILED
        
        if entry.claimed_by:
            if entry.claimed_at:
                age = datetime.now(timezone.utc) - entry.claimed_at
                if age.total_seconds() > 7200:  # 2 hours
                    return "Long-running claim", RecoveryAction.RELEASE_CLAIM
            return "Currently claimed", RecoveryAction.RELEASE_CLAIM
        
        return "High retry count", RecoveryAction.RESET_STATUS
    
    def _get_current_stage(self, entry: Entry) -> ProcessingStage:
        """Get current processing stage for an entry."""
        try:
            processing_status = ProcessingStatus.from_json(entry.processing_status)
            
            for stage in ProcessingStage:
                if not processing_status.is_stage_completed(stage):
                    return stage
            
            return ProcessingStage.DOWNLOAD_FEEDS
        except Exception:
            return ProcessingStage.DOWNLOAD_FEEDS
    
    def _parse_processing_status(self, processing_status_json: Optional[str]) -> Dict[str, Any]:
        """Parse processing status JSON safely."""
        try:
            if processing_status_json:
                import json
                return json.loads(processing_status_json)
            return {}
        except Exception:
            return {}


# Default notification handlers

def log_cleanup_results(result: CleanupResult) -> None:
    """Default handler that logs cleanup results."""
    logger.info(
        f"Cleanup completed ({result.cleanup_reason.value}): "
        f"{result.entries_processed} processed, "
        f"{result.entries_released} released, "
        f"{result.entries_reset} reset, "
        f"{result.entries_failed} failed"
    )


def log_stuck_entries(stuck_entries: List[StuckEntry]) -> None:
    """Default handler that logs stuck entries."""
    if stuck_entries:
        logger.warning(f"Found {len(stuck_entries)} stuck entries requiring attention:")
        for entry in stuck_entries[:5]:  # Log first 5
            logger.warning(
                f"  {entry.entry_id}: {entry.stuck_reason} "
                f"(retries: {entry.retry_count}, suggested: {entry.suggested_action.value})"
            )


def create_default_cleanup_system(db_session_factory: Callable[[], Session],
                                **kwargs) -> StaleWorkCleanupSystem:
    """
    Create a cleanup system with default notification handlers.
    
    Args:
        db_session_factory: Database session factory
        **kwargs: Additional arguments for StaleWorkCleanupSystem
        
    Returns:
        Configured StaleWorkCleanupSystem instance
    """
    cleanup_system = StaleWorkCleanupSystem(db_session_factory, **kwargs)
    
    # Add default notification handlers
    cleanup_system.add_cleanup_callback(log_cleanup_results)
    cleanup_system.add_stuck_entry_callback(log_stuck_entries)
    
    return cleanup_system