"""
Metrics Collection System for distributed ETL processing.

This module provides comprehensive metrics collection, aggregation, and monitoring
capabilities for distributed workers and the overall ETL system.
"""

import json
import logging
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field, asdict
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable, Set, Tuple
from enum import Enum

from sqlalchemy.orm import Session

from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.worker_health_manager import WorkerHealthManager
from backend.app.distributed.work_queue_manager import WorkQueueManager

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """Types of metrics that can be collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MetricPoint:
    """A single metric data point."""
    name: str
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    metric_type: MetricType = MetricType.GAUGE
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'value': self.value,
            'timestamp': self.timestamp.isoformat(),
            'labels': self.labels,
            'type': self.metric_type.value
        }


@dataclass
class Alert:
    """An alert generated by the monitoring system."""
    id: str
    name: str
    severity: AlertSeverity
    message: str
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'id': self.id,
            'name': self.name,
            'severity': self.severity.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'labels': self.labels,
            'resolved': self.resolved,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None
        }


@dataclass
class WorkerMetrics:
    """Comprehensive metrics for a single worker."""
    worker_id: str
    worker_type: str
    state: str
    healthy: bool
    
    # Processing metrics
    batches_processed: int = 0
    entries_processed: int = 0
    entries_failed: int = 0
    total_processing_time: float = 0.0
    current_batch_size: int = 0
    
    # Performance metrics
    avg_batch_processing_time: float = 0.0
    entries_per_second: float = 0.0
    success_rate: float = 0.0
    
    # Health metrics
    last_heartbeat: Optional[datetime] = None
    heartbeat_age_seconds: Optional[float] = None
    uptime_seconds: Optional[float] = None
    error_count: int = 0
    last_error: Optional[str] = None
    
    # Resource metrics
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    
    # Stage-specific metrics
    stage_metrics: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)


@dataclass
class SystemMetrics:
    """System-wide metrics for the distributed ETL system."""
    timestamp: datetime
    
    # Worker metrics
    total_workers: int = 0
    running_workers: int = 0
    healthy_workers: int = 0
    stale_workers: int = 0
    error_workers: int = 0
    
    # Processing metrics
    total_entries_processed: int = 0
    total_entries_failed: int = 0
    total_batches_processed: int = 0
    overall_success_rate: float = 0.0
    
    # Queue metrics by stage
    queue_metrics: Dict[str, Dict[str, int]] = field(default_factory=dict)
    
    # Performance metrics
    system_throughput: float = 0.0  # entries per second
    avg_processing_time: float = 0.0
    
    # Resource metrics
    total_memory_usage_mb: float = 0.0
    avg_cpu_usage_percent: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = asdict(self)
        result['timestamp'] = self.timestamp.isoformat()
        return result


class MetricsCollector:
    """
    Collects and aggregates metrics from distributed workers and system components.
    
    Provides real-time metrics collection, historical data storage, and alerting
    capabilities for monitoring the distributed ETL system.
    """
    
    def __init__(self, db_session_factory: Callable[[], Session]):
        """
        Initialize the metrics collector.
        
        Args:
            db_session_factory: Factory function to create database sessions
        """
        self.db_session_factory = db_session_factory
        
        # Metrics storage
        self._worker_metrics: Dict[str, WorkerMetrics] = {}
        self._system_metrics_history: deque = deque(maxlen=1000)  # Keep last 1000 data points
        self._metric_points: deque = deque(maxlen=10000)  # Keep last 10000 metric points
        
        # Alerting
        self._alerts: Dict[str, Alert] = {}
        self._alert_rules: List[Callable[[SystemMetrics, Dict[str, WorkerMetrics]], List[Alert]]] = []
        
        # Collection state
        self._collection_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        self._collection_interval = 30  # seconds
        self._is_running = False
        
        # Metrics aggregation
        self._metrics_lock = threading.RLock()
        
        # Initialize default alert rules
        self._setup_default_alert_rules()
        
        logger.info("Initialized MetricsCollector")
    
    def start(self) -> None:
        """Start the metrics collection process."""
        if self._is_running:
            logger.warning("MetricsCollector is already running")
            return
        
        logger.info("Starting MetricsCollector...")
        
        self._shutdown_event.clear()
        self._collection_thread = threading.Thread(
            target=self._collection_loop,
            name="metrics-collector",
            daemon=False
        )
        self._collection_thread.start()
        self._is_running = True
        
        logger.info("MetricsCollector started")
    
    def stop(self, timeout: int = 30) -> None:
        """Stop the metrics collection process."""
        if not self._is_running:
            return
        
        logger.info("Stopping MetricsCollector...")
        
        self._shutdown_event.set()
        
        if self._collection_thread and self._collection_thread.is_alive():
            self._collection_thread.join(timeout=timeout)
        
        self._is_running = False
        logger.info("MetricsCollector stopped")
    
    @property
    def is_running(self) -> bool:
        """Check if metrics collection is running."""
        return self._is_running
    
    def collect_worker_metrics(self, worker_id: str, health_data: Dict[str, Any]) -> None:
        """
        Collect metrics from a single worker.
        
        Args:
            worker_id: Worker identifier
            health_data: Health data from the worker
        """
        try:
            with self._metrics_lock:
                # Extract metrics from health data
                stats = health_data.get('stats', {})
                
                # Calculate performance metrics
                entries_processed = stats.get('entries_processed', 0)
                entries_failed = stats.get('entries_failed', 0)
                total_entries = entries_processed + entries_failed
                success_rate = (entries_processed / total_entries * 100) if total_entries > 0 else 0.0
                
                total_time = stats.get('total_processing_time', 0.0)
                entries_per_second = (entries_processed / total_time) if total_time > 0 else 0.0
                
                batches_processed = stats.get('batches_processed', 0)
                avg_batch_time = (total_time / batches_processed) if batches_processed > 0 else 0.0
                
                # Parse heartbeat information
                last_heartbeat = None
                heartbeat_age = health_data.get('heartbeat_age_seconds')
                if health_data.get('last_heartbeat'):
                    try:
                        last_heartbeat = datetime.fromisoformat(health_data['last_heartbeat'].replace('Z', '+00:00'))
                    except (ValueError, AttributeError):
                        pass
                
                # Create worker metrics
                worker_metrics = WorkerMetrics(
                    worker_id=worker_id,
                    worker_type=health_data.get('worker_type', 'unknown'),
                    state=health_data.get('state', 'unknown'),
                    healthy=health_data.get('healthy', False),
                    
                    # Processing metrics
                    batches_processed=batches_processed,
                    entries_processed=entries_processed,
                    entries_failed=entries_failed,
                    total_processing_time=total_time,
                    current_batch_size=health_data.get('current_batch_size', 0),
                    
                    # Performance metrics
                    avg_batch_processing_time=avg_batch_time,
                    entries_per_second=entries_per_second,
                    success_rate=success_rate,
                    
                    # Health metrics
                    last_heartbeat=last_heartbeat,
                    heartbeat_age_seconds=heartbeat_age,
                    uptime_seconds=health_data.get('uptime_seconds'),
                    error_count=health_data.get('error_count', 0),
                    last_error=health_data.get('last_error')
                )
                
                # Store metrics
                self._worker_metrics[worker_id] = worker_metrics
                
                # Create metric points for time series data
                timestamp = datetime.now(timezone.utc)
                labels = {'worker_id': worker_id, 'worker_type': worker_metrics.worker_type}
                
                metric_points = [
                    MetricPoint('worker_batches_processed_total', batches_processed, timestamp, labels, MetricType.COUNTER),
                    MetricPoint('worker_entries_processed_total', entries_processed, timestamp, labels, MetricType.COUNTER),
                    MetricPoint('worker_entries_failed_total', entries_failed, timestamp, labels, MetricType.COUNTER),
                    MetricPoint('worker_processing_time_seconds_total', total_time, timestamp, labels, MetricType.COUNTER),
                    MetricPoint('worker_current_batch_size', worker_metrics.current_batch_size, timestamp, labels, MetricType.GAUGE),
                    MetricPoint('worker_healthy', 1 if worker_metrics.healthy else 0, timestamp, labels, MetricType.GAUGE),
                    MetricPoint('worker_success_rate_percent', success_rate, timestamp, labels, MetricType.GAUGE),
                    MetricPoint('worker_entries_per_second', entries_per_second, timestamp, labels, MetricType.GAUGE)
                ]
                
                if heartbeat_age is not None:
                    metric_points.append(
                        MetricPoint('worker_heartbeat_age_seconds', heartbeat_age, timestamp, labels, MetricType.GAUGE)
                    )
                
                # Add metric points to collection
                self._metric_points.extend(metric_points)
                
        except Exception as e:
            logger.error(f"Failed to collect metrics for worker {worker_id}: {e}")
    
    def collect_system_metrics(self) -> SystemMetrics:
        """
        Collect system-wide metrics.
        
        Returns:
            SystemMetrics object with current system state
        """
        try:
            with self._metrics_lock:
                timestamp = datetime.now(timezone.utc)
                
                # Aggregate worker metrics
                total_workers = len(self._worker_metrics)
                running_workers = sum(1 for m in self._worker_metrics.values() if m.state == 'running')
                healthy_workers = sum(1 for m in self._worker_metrics.values() if m.healthy)
                stale_workers = sum(1 for m in self._worker_metrics.values() 
                                  if m.heartbeat_age_seconds and m.heartbeat_age_seconds > 300)  # 5 minutes
                error_workers = sum(1 for m in self._worker_metrics.values() if m.state == 'error')
                
                # Aggregate processing metrics
                total_entries_processed = sum(m.entries_processed for m in self._worker_metrics.values())
                total_entries_failed = sum(m.entries_failed for m in self._worker_metrics.values())
                total_batches_processed = sum(m.batches_processed for m in self._worker_metrics.values())
                
                total_entries = total_entries_processed + total_entries_failed
                overall_success_rate = (total_entries_processed / total_entries * 100) if total_entries > 0 else 0.0
                
                # Calculate system throughput
                total_processing_time = sum(m.total_processing_time for m in self._worker_metrics.values())
                system_throughput = (total_entries_processed / total_processing_time) if total_processing_time > 0 else 0.0
                avg_processing_time = (total_processing_time / total_batches_processed) if total_batches_processed > 0 else 0.0
                
                # Get queue metrics
                queue_metrics = self._collect_queue_metrics()
                
                # Create system metrics
                system_metrics = SystemMetrics(
                    timestamp=timestamp,
                    total_workers=total_workers,
                    running_workers=running_workers,
                    healthy_workers=healthy_workers,
                    stale_workers=stale_workers,
                    error_workers=error_workers,
                    total_entries_processed=total_entries_processed,
                    total_entries_failed=total_entries_failed,
                    total_batches_processed=total_batches_processed,
                    overall_success_rate=overall_success_rate,
                    queue_metrics=queue_metrics,
                    system_throughput=system_throughput,
                    avg_processing_time=avg_processing_time
                )
                
                # Store in history
                self._system_metrics_history.append(system_metrics)
                
                return system_metrics
                
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
            return SystemMetrics(timestamp=datetime.now(timezone.utc))
    
    def get_worker_metrics(self, worker_id: Optional[str] = None) -> Dict[str, WorkerMetrics]:
        """
        Get worker metrics.
        
        Args:
            worker_id: Specific worker ID to get metrics for (optional)
            
        Returns:
            Dictionary of worker metrics
        """
        with self._metrics_lock:
            if worker_id:
                return {worker_id: self._worker_metrics.get(worker_id)} if worker_id in self._worker_metrics else {}
            return self._worker_metrics.copy()
    
    def get_system_metrics_history(self, limit: Optional[int] = None) -> List[SystemMetrics]:
        """
        Get historical system metrics.
        
        Args:
            limit: Maximum number of data points to return
            
        Returns:
            List of SystemMetrics objects
        """
        with self._metrics_lock:
            history = list(self._system_metrics_history)
            if limit:
                history = history[-limit:]
            return history
    
    def get_metric_points(self, 
                         metric_name: Optional[str] = None,
                         labels: Optional[Dict[str, str]] = None,
                         since: Optional[datetime] = None,
                         limit: Optional[int] = None) -> List[MetricPoint]:
        """
        Get metric points with optional filtering.
        
        Args:
            metric_name: Filter by metric name
            labels: Filter by labels (all must match)
            since: Filter by timestamp (only points after this time)
            limit: Maximum number of points to return
            
        Returns:
            List of MetricPoint objects
        """
        with self._metrics_lock:
            points = list(self._metric_points)
            
            # Apply filters
            if metric_name:
                points = [p for p in points if p.name == metric_name]
            
            if labels:
                points = [p for p in points if all(p.labels.get(k) == v for k, v in labels.items())]
            
            if since:
                points = [p for p in points if p.timestamp >= since]
            
            # Sort by timestamp (newest first)
            points.sort(key=lambda p: p.timestamp, reverse=True)
            
            if limit:
                points = points[:limit]
            
            return points
    
    def get_alerts(self, 
                  severity: Optional[AlertSeverity] = None,
                  resolved: Optional[bool] = None) -> List[Alert]:
        """
        Get alerts with optional filtering.
        
        Args:
            severity: Filter by severity level
            resolved: Filter by resolution status
            
        Returns:
            List of Alert objects
        """
        alerts = list(self._alerts.values())
        
        if severity:
            alerts = [a for a in alerts if a.severity == severity]
        
        if resolved is not None:
            alerts = [a for a in alerts if a.resolved == resolved]
        
        # Sort by timestamp (newest first)
        alerts.sort(key=lambda a: a.timestamp, reverse=True)
        
        return alerts
    
    def add_alert_rule(self, rule: Callable[[SystemMetrics, Dict[str, WorkerMetrics]], List[Alert]]) -> None:
        """
        Add a custom alert rule.
        
        Args:
            rule: Function that takes system metrics and worker metrics and returns alerts
        """
        self._alert_rules.append(rule)
        logger.info("Added custom alert rule")
    
    def resolve_alert(self, alert_id: str) -> bool:
        """
        Manually resolve an alert.
        
        Args:
            alert_id: Alert identifier
            
        Returns:
            True if alert was resolved, False if not found
        """
        if alert_id in self._alerts:
            alert = self._alerts[alert_id]
            if not alert.resolved:
                alert.resolved = True
                alert.resolved_at = datetime.now(timezone.utc)
                logger.info(f"Resolved alert: {alert_id}")
            return True
        return False
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """
        Get comprehensive data for monitoring dashboard.
        
        Returns:
            Dictionary with all monitoring data
        """
        with self._metrics_lock:
            # Get latest system metrics
            latest_system_metrics = self._system_metrics_history[-1] if self._system_metrics_history else None
            
            # Get recent system metrics for trends
            recent_system_metrics = list(self._system_metrics_history)[-10:] if self._system_metrics_history else []
            
            # Get active alerts
            active_alerts = [a for a in self._alerts.values() if not a.resolved]
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'system_metrics': latest_system_metrics.to_dict() if latest_system_metrics else None,
                'system_metrics_history': [m.to_dict() for m in recent_system_metrics],
                'worker_metrics': {k: v.to_dict() for k, v in self._worker_metrics.items()},
                'active_alerts': [a.to_dict() for a in active_alerts],
                'alert_summary': {
                    'total_alerts': len(self._alerts),
                    'active_alerts': len(active_alerts),
                    'critical_alerts': len([a for a in active_alerts if a.severity == AlertSeverity.CRITICAL]),
                    'error_alerts': len([a for a in active_alerts if a.severity == AlertSeverity.ERROR]),
                    'warning_alerts': len([a for a in active_alerts if a.severity == AlertSeverity.WARNING])
                }
            }
    
    def _collection_loop(self) -> None:
        """Main metrics collection loop."""
        logger.info("Starting metrics collection loop")
        
        while not self._shutdown_event.is_set():
            try:
                # Collect metrics from database
                self._collect_from_database()
                
                # Collect system metrics
                system_metrics = self.collect_system_metrics()
                
                # Check alert rules
                self._check_alert_rules(system_metrics)
                
                # Wait for next collection
                self._shutdown_event.wait(self._collection_interval)
                
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}", exc_info=True)
                self._shutdown_event.wait(5.0)  # Wait before retry
        
        logger.info("Metrics collection loop stopped")
    
    def _collect_from_database(self) -> None:
        """Collect metrics from database sources."""
        try:
            db_session = self.db_session_factory()
            
            # Collect worker health data
            health_manager = WorkerHealthManager(db_session)
            all_workers_health = health_manager.get_all_workers_health()
            
            # Update worker metrics
            for worker_health in all_workers_health:
                if worker_health:
                    self.collect_worker_metrics(worker_health['worker_id'], worker_health)
            
        except Exception as e:
            logger.error(f"Failed to collect metrics from database: {e}")
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def _collect_queue_metrics(self) -> Dict[str, Dict[str, int]]:
        """Collect queue metrics from database."""
        try:
            db_session = self.db_session_factory()
            work_queue = WorkQueueManager(db_session)
            return work_queue.get_comprehensive_metrics()
        except Exception as e:
            logger.error(f"Failed to collect queue metrics: {e}")
            return {}
        finally:
            if 'db_session' in locals():
                db_session.close()
    
    def _check_alert_rules(self, system_metrics: SystemMetrics) -> None:
        """Check all alert rules and generate alerts."""
        try:
            new_alerts = []
            
            # Run all alert rules
            for rule in self._alert_rules:
                try:
                    alerts = rule(system_metrics, self._worker_metrics)
                    new_alerts.extend(alerts)
                except Exception as e:
                    logger.error(f"Error in alert rule: {e}")
            
            # Process new alerts
            for alert in new_alerts:
                if alert.id not in self._alerts or self._alerts[alert.id].resolved:
                    self._alerts[alert.id] = alert
                    logger.warning(f"New alert: {alert.name} - {alert.message}")
            
        except Exception as e:
            logger.error(f"Failed to check alert rules: {e}")
    
    def _setup_default_alert_rules(self) -> None:
        """Setup default alert rules."""
        
        def worker_health_alert(system_metrics: SystemMetrics, worker_metrics: Dict[str, WorkerMetrics]) -> List[Alert]:
            """Alert for unhealthy workers."""
            alerts = []
            
            for worker_id, metrics in worker_metrics.items():
                if not metrics.healthy:
                    alerts.append(Alert(
                        id=f"worker_unhealthy_{worker_id}",
                        name="Worker Unhealthy",
                        severity=AlertSeverity.ERROR,
                        message=f"Worker {worker_id} is unhealthy (state: {metrics.state})",
                        timestamp=datetime.now(timezone.utc),
                        labels={'worker_id': worker_id, 'worker_type': metrics.worker_type}
                    ))
            
            return alerts
        
        def high_failure_rate_alert(system_metrics: SystemMetrics, worker_metrics: Dict[str, WorkerMetrics]) -> List[Alert]:
            """Alert for high failure rates."""
            alerts = []
            
            # System-wide failure rate
            if system_metrics.overall_success_rate < 90.0 and system_metrics.total_entries_processed > 100:
                alerts.append(Alert(
                    id="high_system_failure_rate",
                    name="High System Failure Rate",
                    severity=AlertSeverity.WARNING,
                    message=f"System success rate is {system_metrics.overall_success_rate:.1f}% (below 90%)",
                    timestamp=datetime.now(timezone.utc),
                    labels={'type': 'system'}
                ))
            
            # Per-worker failure rate
            for worker_id, metrics in worker_metrics.items():
                if metrics.success_rate < 80.0 and metrics.entries_processed > 50:
                    alerts.append(Alert(
                        id=f"high_worker_failure_rate_{worker_id}",
                        name="High Worker Failure Rate",
                        severity=AlertSeverity.WARNING,
                        message=f"Worker {worker_id} success rate is {metrics.success_rate:.1f}% (below 80%)",
                        timestamp=datetime.now(timezone.utc),
                        labels={'worker_id': worker_id, 'worker_type': metrics.worker_type}
                    ))
            
            return alerts
        
        def stale_workers_alert(system_metrics: SystemMetrics, worker_metrics: Dict[str, WorkerMetrics]) -> List[Alert]:
            """Alert for stale workers."""
            alerts = []
            
            if system_metrics.stale_workers > 0:
                alerts.append(Alert(
                    id="stale_workers_detected",
                    name="Stale Workers Detected",
                    severity=AlertSeverity.ERROR,
                    message=f"{system_metrics.stale_workers} workers have stale heartbeats",
                    timestamp=datetime.now(timezone.utc),
                    labels={'type': 'system'}
                ))
            
            return alerts
        
        def low_throughput_alert(system_metrics: SystemMetrics, worker_metrics: Dict[str, WorkerMetrics]) -> List[Alert]:
            """Alert for low system throughput."""
            alerts = []
            
            # Only alert if we have significant processing history
            if (system_metrics.total_entries_processed > 1000 and 
                system_metrics.system_throughput < 1.0):  # Less than 1 entry per second
                alerts.append(Alert(
                    id="low_system_throughput",
                    name="Low System Throughput",
                    severity=AlertSeverity.WARNING,
                    message=f"System throughput is {system_metrics.system_throughput:.2f} entries/second (below 1.0)",
                    timestamp=datetime.now(timezone.utc),
                    labels={'type': 'performance'}
                ))
            
            return alerts
        
        # Add default alert rules
        self._alert_rules.extend([
            worker_health_alert,
            high_failure_rate_alert,
            stale_workers_alert,
            low_throughput_alert
        ])
        
        logger.info("Setup default alert rules")