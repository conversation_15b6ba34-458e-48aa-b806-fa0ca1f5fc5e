"""
Tests for the legacy thread wrapper that ensures backward compatibility.

These tests verify that the distributed workers provide identical processing
results to the original monolithic approach.
"""

import pytest
import threading
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from backend.app.distributed.legacy_thread_wrapper import LegacyThreadWrapper
from backend.app.distributed.processing_mode_config import ProcessingModeConfig, ProcessingMode, ProcessingModeManager
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.distributed_worker import WorkerState


class TestLegacyThreadWrapper:
    """Test cases for LegacyThreadWrapper."""
    
    @pytest.fixture
    def mock_database_url(self):
        """Mock database URL for testing."""
        return "sqlite:///:memory:"
    
    @pytest.fixture
    def wrapper(self, mock_database_url):
        """Create a LegacyThreadWrapper instance for testing."""
        with patch('backend.app.distributed.legacy_thread_wrapper.create_engine'), \
             patch('backend.app.distributed.legacy_thread_wrapper.sessionmaker'):
            wrapper = LegacyThreadWrapper(mock_database_url, "test")
            yield wrapper
            wrapper.stop_workers()
    
    def test_initialization(self, mock_database_url):
        """Test wrapper initialization."""
        with patch('backend.app.distributed.legacy_thread_wrapper.create_engine') as mock_engine, \
             patch('backend.app.distributed.legacy_thread_wrapper.sessionmaker') as mock_session:
            
            wrapper = LegacyThreadWrapper(mock_database_url, "test")
            
            # Verify database setup
            mock_engine.assert_called_once_with(mock_database_url)
            mock_session.assert_called_once()
            
            # Verify initial state
            assert wrapper.database_url == mock_database_url
            assert wrapper.worker_id_prefix == "test"
            assert wrapper._workers == {}
            assert wrapper._worker_threads == {}
            assert not wrapper._workers_initialized
    
    def test_legacy_interface_methods(self, wrapper):
        """Test that all legacy interface methods are available."""
        legacy_methods = [
            'download_feeds',
            'download_full_texts',
            'compute_embeddings',
            'run_full_pipeline'
        ]
        
        for method_name in legacy_methods:
            assert hasattr(wrapper, method_name)
            assert callable(getattr(wrapper, method_name))
    
    def test_initialize_workers(self, wrapper):
        """Test worker initialization."""
        with patch('backend.app.distributed.legacy_thread_wrapper.FeedDownloadWorker') as mock_feed_worker, \
             patch('backend.app.distributed.legacy_thread_wrapper.EmbeddingWorker') as mock_embedding_worker:
            
            wrapper._initialize_workers()
            
            assert wrapper._workers_initialized
            assert len(wrapper._workers) > 0
            
            # Verify workers were created for expected stages
            expected_stages = [
                ProcessingStage.DOWNLOAD_FEEDS,
                ProcessingStage.COMPUTE_EMBEDDINGS
            ]
            
            for stage in expected_stages:
                if stage in wrapper._workers:
                    assert wrapper._workers[stage] is not None
    
    def test_download_feeds(self, wrapper):
        """Test download_feeds method."""
        mock_worker = Mock()
        mock_worker.state = WorkerState.STOPPED
        mock_worker.process_batch.side_effect = [
            {'processed_count': 5, 'error_count': 0},
            {'processed_count': 0, 'error_count': 0}  # No more work
        ]
        
        with patch.object(wrapper, '_initialize_workers'):
            wrapper._workers[ProcessingStage.DOWNLOAD_FEEDS] = mock_worker
            
            result = wrapper.download_feeds()
            
            assert result['success'] is True
            assert result['processed_count'] == 5
            assert result['error_count'] == 0
    
    def test_download_full_texts(self, wrapper):
        """Test download_full_texts method."""
        # This method currently returns a placeholder response
        result = wrapper.download_full_texts()
        
        assert result['success'] is True
        assert result['processed_count'] == 0
        assert result['error_count'] == 0
        assert "not implemented yet" in result['message']
    
    def test_start_workers(self, wrapper):
        """Test starting workers."""
        mock_worker = Mock()
        wrapper._workers[ProcessingStage.DOWNLOAD_FEEDS] = mock_worker
        
        with patch('threading.Thread') as mock_thread:
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            
            wrapper.start_workers()
            
            # Should create thread for the worker
            mock_thread.assert_called_once()
            mock_thread_instance.start.assert_called_once()
    
    def test_stop_workers(self, wrapper):
        """Test stopping workers."""
        mock_worker = Mock()
        wrapper._workers[ProcessingStage.DOWNLOAD_FEEDS] = mock_worker
        
        mock_thread = Mock()
        mock_thread.is_alive.return_value = True
        wrapper._worker_threads[ProcessingStage.DOWNLOAD_FEEDS] = mock_thread
        
        wrapper.stop_workers()
        
        # Verify shutdown event is set
        assert wrapper._shutdown_event.is_set()
        
        # Verify worker is stopped
        mock_worker.stop.assert_called_once()
        
        # Verify thread is joined
        mock_thread.join.assert_called_once_with(timeout=10)
    
    def test_is_running(self, wrapper):
        """Test checking if workers are running."""
        mock_thread = Mock()
        mock_thread.is_alive.return_value = True
        wrapper._worker_threads[ProcessingStage.DOWNLOAD_FEEDS] = mock_thread
        
        assert wrapper.is_running() is True
        
        mock_thread.is_alive.return_value = False
        assert wrapper.is_running() is False
    
    def test_get_worker_status(self, wrapper):
        """Test getting worker status."""
        mock_worker = Mock()
        mock_worker.config.worker_id = "test_worker"
        mock_worker.state = WorkerState.RUNNING
        mock_worker.last_heartbeat = datetime.now(timezone.utc)
        wrapper._workers[ProcessingStage.DOWNLOAD_FEEDS] = mock_worker
        wrapper._workers_initialized = True
        
        mock_thread = Mock()
        mock_thread.is_alive.return_value = True
        wrapper._worker_threads[ProcessingStage.DOWNLOAD_FEEDS] = mock_thread
        
        status = wrapper.get_worker_status()
        
        assert status['status'] == 'running'
        assert status['total_workers'] == 1
        assert status['running_workers'] == 1
        assert ProcessingStage.DOWNLOAD_FEEDS.value in status['workers']
        
        worker_status = status['workers'][ProcessingStage.DOWNLOAD_FEEDS.value]
        assert worker_status['worker_id'] == "test_worker"
        assert worker_status['state'] == WorkerState.RUNNING.value
        assert worker_status['thread_alive'] is True
    
    def test_run_full_pipeline(self, wrapper):
        """Test running the full pipeline."""
        mock_worker = Mock()
        mock_worker.state = WorkerState.STOPPED
        mock_worker.process_batch.side_effect = [
            {'processed_count': 2, 'error_count': 0},
            {'processed_count': 0, 'error_count': 0}  # No more work
        ]
        
        with patch.object(wrapper, '_initialize_workers'):
            # Mock all workers in the pipeline
            for stage in [ProcessingStage.DOWNLOAD_FEEDS, ProcessingStage.COMPUTE_EMBEDDINGS]:
                wrapper._workers[stage] = mock_worker
            
            result = wrapper.run_full_pipeline()
            
            assert result['success'] is True
            assert 'stages' in result
            assert 'start_time' in result
            assert 'end_time' in result
    
    def test_context_manager(self, mock_database_url):
        """Test context manager functionality."""
        with patch('backend.app.distributed.legacy_thread_wrapper.create_engine'), \
             patch('backend.app.distributed.legacy_thread_wrapper.sessionmaker'):
            
            with LegacyThreadWrapper(mock_database_url, "test") as wrapper:
                assert wrapper is not None
                
                # Mock the stop method to verify it's called
                wrapper.stop_workers = Mock()
            
            # Verify cleanup was called
            wrapper.stop_workers.assert_called_once()


class TestProcessingModeConfig:
    """Test cases for ProcessingModeConfig."""
    
    def test_from_environment_defaults(self):
        """Test configuration from environment with defaults."""
        with patch.dict('os.environ', {'DATABASE_URL': 'sqlite:///:memory:'}, clear=True):
            config = ProcessingModeConfig.from_environment()
            
            assert config.mode == ProcessingMode.MONOLITHIC
            assert config.database_url == 'sqlite:///:memory:'
            assert config.worker_id_prefix == 'default'
            assert config.batch_size == 10
            assert config.max_workers_per_stage == 1
            assert config.enable_monitoring is True
    
    def test_from_environment_custom(self):
        """Test configuration from environment with custom values."""
        env_vars = {
            'PROCESSING_MODE': 'distributed',
            'DATABASE_URL': 'postgresql://test',
            'WORKER_ID_PREFIX': 'custom',
            'BATCH_SIZE': '20',
            'MAX_WORKERS_PER_STAGE': '5',
            'ENABLE_MONITORING': 'false',
            'HEARTBEAT_INTERVAL': '60',
            'CLAIM_TIMEOUT': '600',
            'MAX_RETRIES': '5',
            'LEGACY_THREAD_TIMEOUT': '7200'
        }
        
        with patch.dict('os.environ', env_vars, clear=True):
            config = ProcessingModeConfig.from_environment()
            
            assert config.mode == ProcessingMode.DISTRIBUTED
            assert config.database_url == 'postgresql://test'
            assert config.worker_id_prefix == 'custom'
            assert config.batch_size == 20
            assert config.max_workers_per_stage == 5
            assert config.enable_monitoring is False
            assert config.heartbeat_interval == 60
            assert config.claim_timeout == 600
            assert config.max_retries == 5
            assert config.legacy_thread_timeout == 7200
    
    def test_from_environment_missing_database_url(self):
        """Test configuration with missing database URL."""
        with patch.dict('os.environ', {}, clear=True):
            with pytest.raises(ValueError, match="DATABASE_URL environment variable is required"):
                ProcessingModeConfig.from_environment()
    
    def test_from_environment_invalid_mode(self):
        """Test configuration with invalid processing mode."""
        with patch.dict('os.environ', {
            'PROCESSING_MODE': 'invalid',
            'DATABASE_URL': 'sqlite:///:memory:'
        }, clear=True):
            config = ProcessingModeConfig.from_environment()
            
            # Should default to monolithic
            assert config.mode == ProcessingMode.MONOLITHIC
    
    def test_to_dict(self):
        """Test converting configuration to dictionary."""
        config = ProcessingModeConfig(
            mode=ProcessingMode.DISTRIBUTED,
            database_url='sqlite:///:memory:',
            worker_id_prefix='test',
            batch_size=15
        )
        
        result = config.to_dict()
        
        assert result['mode'] == 'distributed'
        assert result['database_url'] == 'sqlite:///:memory:'
        assert result['worker_id_prefix'] == 'test'
        assert result['batch_size'] == 15


class TestProcessingModeManager:
    """Test cases for ProcessingModeManager."""
    
    @pytest.fixture
    def config(self):
        """Create a test configuration."""
        return ProcessingModeConfig(
            mode=ProcessingMode.DISTRIBUTED,
            database_url='sqlite:///:memory:',
            worker_id_prefix='test'
        )
    
    def test_initialization(self, config):
        """Test manager initialization."""
        manager = ProcessingModeManager(config)
        
        assert manager.config == config
        assert manager._processor is None
    
    def test_get_processor_distributed(self, config):
        """Test getting distributed processor."""
        manager = ProcessingModeManager(config)
        
        with patch('backend.app.distributed.processing_mode_config.LegacyThreadWrapper') as mock_wrapper:
            mock_instance = Mock()
            mock_wrapper.return_value = mock_instance
            
            processor = manager.get_processor()
            
            assert processor == mock_instance
            mock_wrapper.assert_called_once_with(
                database_url=config.database_url,
                worker_id_prefix=config.worker_id_prefix
            )
    
    def test_get_processor_monolithic(self):
        """Test getting monolithic processor."""
        config = ProcessingModeConfig(
            mode=ProcessingMode.MONOLITHIC,
            database_url='sqlite:///:memory:'
        )
        manager = ProcessingModeManager(config)
        
        with pytest.raises(NotImplementedError):
            manager.get_processor()
    
    def test_switch_mode(self, config):
        """Test switching processing modes."""
        manager = ProcessingModeManager(config)
        
        # Set up a mock processor
        mock_processor = Mock()
        manager._processor = mock_processor
        
        # Switch to monolithic mode
        manager.switch_mode(ProcessingMode.MONOLITHIC)
        
        assert manager.config.mode == ProcessingMode.MONOLITHIC
        mock_processor.stop_all_workers.assert_called_once()
        assert manager._processor is None
    
    def test_switch_mode_same(self, config):
        """Test switching to the same mode."""
        manager = ProcessingModeManager(config)
        mock_processor = Mock()
        manager._processor = mock_processor
        
        # Switch to the same mode
        manager.switch_mode(ProcessingMode.DISTRIBUTED)
        
        # Should not change anything
        assert manager.config.mode == ProcessingMode.DISTRIBUTED
        mock_processor.stop_all_workers.assert_not_called()
        assert manager._processor == mock_processor
    
    def test_validate_configuration_valid(self, config):
        """Test configuration validation with valid config."""
        manager = ProcessingModeManager(config)
        
        result = manager.validate_configuration()
        
        assert result is True
    
    def test_validate_configuration_invalid_database_url(self):
        """Test configuration validation with invalid database URL."""
        config = ProcessingModeConfig(
            mode=ProcessingMode.DISTRIBUTED,
            database_url='',  # Invalid
            worker_id_prefix='test'
        )
        manager = ProcessingModeManager(config)
        
        result = manager.validate_configuration()
        
        assert result is False
    
    def test_validate_configuration_invalid_batch_size(self, config):
        """Test configuration validation with invalid batch size."""
        config.batch_size = 0  # Invalid
        manager = ProcessingModeManager(config)
        
        result = manager.validate_configuration()
        
        assert result is False
    
    def test_get_status(self, config):
        """Test getting manager status."""
        manager = ProcessingModeManager(config)
        
        status = manager.get_status()
        
        assert status['mode'] == 'distributed'
        assert 'configuration' in status
        assert status['processor_initialized'] is False
    
    def test_get_status_with_processor(self, config):
        """Test getting status with initialized processor."""
        manager = ProcessingModeManager(config)
        mock_processor = Mock()
        mock_processor.get_processing_status.return_value = {'test': 'status'}
        manager._processor = mock_processor
        
        status = manager.get_status()
        
        assert status['processor_initialized'] is True
        assert status['processor_status'] == {'test': 'status'}
    
    def test_context_manager(self, config):
        """Test context manager functionality."""
        with ProcessingModeManager(config) as manager:
            assert manager is not None
            
            # Set up a mock processor
            mock_processor = Mock()
            manager._processor = mock_processor
        
        # Verify cleanup was called
        mock_processor.stop_all_workers.assert_called_once()


if __name__ == '__main__':
    pytest.main([__file__])