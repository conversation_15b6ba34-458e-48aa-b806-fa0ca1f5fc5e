apiVersion: apps/v1
kind: Deployment
metadata:
  name: analysis-workers
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: analysis-worker
    app.kubernetes.io/part-of: distributed-etl
spec:
  replicas: 2
  selector:
    matchLabels:
      app.kubernetes.io/name: breaking-bright
      app.kubernetes.io/component: analysis-worker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: breaking-bright
        app.kubernetes.io/component: analysis-worker
        app.kubernetes.io/part-of: distributed-etl
    spec:
      containers:
      - name: analysis-worker
        image: robertschulze/breaking-bright-worker:latest
        imagePullPolicy: Always
        command: ["python", "-m", "backend.app.distributed.distributed_worker"]
        ports:
        - containerPort: 8080
          name: health-check
          protocol: TCP
        env:
        - name: WORKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: WORKER_STAGES
          value: "sentiment_analysis,llm_analysis,iptc_classification"
        - name: WORKE<PERSON>_BATCH_SIZE
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: ANALYSIS_WORKER_BATCH_SIZE
        - name: WORKER_CLAIM_TIMEOUT_MINUTES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: ANALYSIS_WORKER_CLAIM_TIMEOUT_MINUTES
        - name: WORKER_MAX_RETRIES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: ANALYSIS_WORKER_MAX_RETRIES
        - name: WORKER_PROCESSING_DELAY_SECONDS
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: ANALYSIS_WORKER_PROCESSING_DELAY_SECONDS
        - name: WORKER_HEALTH_CHECK_PORT
          value: "8080"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: openai-api-key
        - name: IONOS_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: ionos-api-key
        - name: HUGGINGFACE_TOKEN
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: huggingface-token
        envFrom:
        - configMapRef:
            name: distributed-worker-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: worker-logs
          mountPath: /var/log/worker
        - name: model-cache
          mountPath: /root/.cache
      volumes:
      - name: worker-logs
        emptyDir: {}
      - name: model-cache
        emptyDir:
          sizeLimit: 5Gi
      imagePullSecrets:
      - name: docker-registry-secret
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: analysis-workers-service
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: analysis-worker
spec:
  selector:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: analysis-worker
  ports:
  - name: health-check
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP