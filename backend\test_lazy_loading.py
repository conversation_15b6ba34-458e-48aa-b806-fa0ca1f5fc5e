#!/usr/bin/env python3
"""
Test script to verify the lazy loading performance improvements.
Run this to compare the old vs new approach.
"""

import asyncio
import aiohttp
import time
import json
from typing import List

BASE_URL = "http://localhost:8000"

async def test_old_approach_simulation(session: aiohttp.ClientSession) -> float:
    """
    Simulate the old approach by loading news and similar data together.
    This would be slow due to complex queries.
    """
    print("🐌 Testing old approach simulation...")
    start_time = time.time()
    
    # Load news (this would be slow in the old approach due to has_similar computation)
    async with session.get(f"{BASE_URL}/news/?limit=20") as response:
        news_data = await response.json()
    
    # In the old approach, has_similar would already be computed (slowly)
    # Here we simulate by checking each entry individually (worst case)
    for item in news_data['items']:
        async with session.get(f"{BASE_URL}/news/{item['entry_id']}/similar") as response:
            await response.json()
    
    return time.time() - start_time

async def test_new_lazy_approach(session: aiohttp.ClientSession) -> float:
    """
    Test the new lazy loading approach.
    """
    print("🚀 Testing new lazy loading approach...")
    start_time = time.time()
    
    # Step 1: Load news quickly (no has_similar computation)
    async with session.get(f"{BASE_URL}/news/?limit=20") as response:
        news_data = await response.json()
    
    print(f"   ✅ Loaded {len(news_data['items'])} news items")
    
    # Step 2: Batch check for similar news
    entry_ids = [item['entry_id'] for item in news_data['items']]
    async with session.post(
        f"{BASE_URL}/news/check-similar",
        json=entry_ids,
        headers={'Content-Type': 'application/json'}
    ) as response:
        similar_flags = await response.json()
    
    similar_count = sum(1 for has_similar in similar_flags.values() if has_similar)
    print(f"   ✅ Found {similar_count} entries with similar news")
    
    # Step 3: Load similar articles only for entries that have them (on-demand simulation)
    # In real usage, this would only happen when user clicks
    for entry_id, has_similar in similar_flags.items():
        if has_similar:
            async with session.get(f"{BASE_URL}/news/{entry_id}/similar") as response:
                similar_data = await response.json()
                print(f"   📰 Entry {entry_id} has {len(similar_data['similar_sources'])} similar articles")
    
    return time.time() - start_time

async def test_initial_load_speed(session: aiohttp.ClientSession) -> dict:
    """
    Test just the initial news loading speed (most important for UX).
    """
    print("⚡ Testing initial load speed...")
    
    tests = {
        "news_list": f"{BASE_URL}/news/?limit=20",
        "categories": f"{BASE_URL}/news/categories/",
        "home_screen": f"{BASE_URL}/news/home",
        "category_specific": f"{BASE_URL}/news/category/01000000?limit=10"
    }
    
    results = {}
    
    for test_name, url in tests.items():
        times = []
        for i in range(5):  # Run 5 times for average
            start_time = time.time()
            async with session.get(url) as response:
                data = await response.json()
                times.append(time.time() - start_time)
        
        avg_time = sum(times) / len(times)
        results[test_name] = {
            'avg_time': avg_time,
            'times': times
        }
        print(f"   {test_name}: {avg_time:.3f}s average")
    
    return results

async def run_performance_comparison():
    """
    Run comprehensive performance tests.
    """
    print("🧪 Starting Performance Comparison Tests")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test 1: Initial load speed (most important)
            initial_results = await test_initial_load_speed(session)
            
            print("\n" + "=" * 50)
            
            # Test 2: New lazy loading approach
            lazy_time = await test_new_lazy_approach(session)
            
            print("\n" + "=" * 50)
            
            # Test 3: Old approach simulation (for comparison)
            # Note: This will be slower as it loads similar data for all entries
            try:
                old_time = await test_old_approach_simulation(session)
                print(f"\n📊 Performance Comparison:")
                print(f"   Old approach (simulated): {old_time:.3f}s")
                print(f"   New lazy approach: {lazy_time:.3f}s")
                print(f"   Improvement: {((old_time - lazy_time) / old_time * 100):.1f}% faster")
            except Exception as e:
                print(f"   Old approach simulation failed: {e}")
            
            print("\n🎯 Key Metrics:")
            print(f"   Initial news load: {initial_results['news_list']['avg_time']:.3f}s")
            print(f"   Categories load: {initial_results['categories']['avg_time']:.3f}s")
            print(f"   Home screen load: {initial_results['home_screen']['avg_time']:.3f}s")
            
            # Performance targets
            print("\n🎯 Performance Targets:")
            targets = {
                'news_list': 0.5,
                'categories': 0.3,
                'home_screen': 0.8
            }
            
            for test_name, target in targets.items():
                actual = initial_results[test_name]['avg_time']
                status = "✅ PASS" if actual <= target else "❌ FAIL"
                print(f"   {test_name}: {actual:.3f}s (target: {target}s) {status}")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            print("Make sure your backend is running on http://localhost:8000")

if __name__ == "__main__":
    print("Make sure your backend is running on http://localhost:8000")
    print("Press Enter to start performance tests or Ctrl+C to cancel...")
    input()
    
    asyncio.run(run_performance_comparison())