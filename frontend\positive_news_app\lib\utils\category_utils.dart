class CategoryUtils {
  // Dynamic storage for category names discovered from API responses
  static final Map<String, String> _discoveredNames = <String, String>{};

  /// Updates the known category names from API responses
  static void updateCategoryNames(Map<String, String> codeToNameMap) {
    _discoveredNames.addAll(codeToNameMap);
  }

  /// Returns the display name for a category code
  /// Uses discovered names from API, falls back to the code itself
  static String getDisplayName(String categoryCode) {
    return _discoveredNames[categoryCode] ?? categoryCode;
  }

  /// Returns all known category codes (from discovered names)
  static Set<String> get allKnownCodes => _discoveredNames.keys.toSet();

  /// Checks if a category code has a known display name
  static bool hasDisplayName(String categoryCode) {
    return _discoveredNames.containsKey(categoryCode);
  }

  /// Clears all discovered category names (useful for testing)
  static void clearDiscoveredNames() {
    _discoveredNames.clear();
  }
}
