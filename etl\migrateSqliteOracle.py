import oracledb
import yaml
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from etl.news_aggregator import Entry, Base
from tqdm import tqdm

# Define the connection descriptor
dsn = (
    "(description="
    "(retry_count=20)"
    "(retry_delay=3)"
    "(address=(protocol=tcps)(port=1521)(host=adb.eu-frankfurt-1.oraclecloud.com))"
    "(connect_data=(service_name=g23416de76093b5_goodnews_low.adb.oraclecloud.com))"
    "(security=(ssl_server_dn_match=yes))"
    ")"
)

# Provide your Oracle Cloud credentials
username = 'DEVXPS15'
password = 'RZt72xEvCYczkrd'

# Establish the connection
connection = oracledb.connect(user=username, password=password, dsn=dsn)

# Create a cursor
cursor = connection.cursor()

# Create the entries table if it does not exist
cursor.execute("""
    BEGIN
        EXECUTE IMMEDIATE '
            CREATE TABLE entries (
                entry_id VARCHAR2(64) PRIMARY KEY,
                title VARCHAR2(4000),
                link VARCHAR2(4000),
                source VARCHAR2(4000),
                description CLOB,
                published TIMESTAMP,
                full_text CLOB,
                english_text CLOB,
                embedding BLOB,
                vaderPos FLOAT,
                vaderNeu FLOAT,
                vaderNeg FLOAT,
                vaderCompound FLOAT,
                germanSentimentPositive FLOAT,
                germanSentimentNeutral FLOAT,
                germanSentimentNegative FLOAT,
                tbdPolarity FLOAT,
                tbdSubjectivity FLOAT,
                llmPositive FLOAT,
                llmNeutral FLOAT,
                llmNegative FLOAT,
                llmPositiveStd FLOAT,
                llmNeutralStd FLOAT,
                llmNegativeStd FLOAT,
                llmPositiveDiff FLOAT,
                llmNeutralDiff FLOAT,
                llmNegativeDiff FLOAT,
                llmReasonList CLOB,
                llmIterations INTEGER,
                llmBreakReason VARCHAR2(4000),
                llmIsAd FLOAT,
                llmIsAdReason CLOB,
                iptcNewscode VARCHAR2(4000),
                iptcScore FLOAT,
                dup_entry_id VARCHAR2(64),
                dup_entry_conf FLOAT
            )
        ';
    EXCEPTION
        WHEN OTHERS THEN
            IF SQLCODE != -955 THEN
                RAISE;
            END IF;
    END;
""")

# Connect to the SQLite database
with open('news_aggregator.yaml', 'r') as file:
    config = yaml.safe_load(file)
sqlite_engine = create_engine(config['database_file'])
Session = sessionmaker(bind=sqlite_engine)
session = Session()

# Load data from SQLite and insert into Oracle DB
entries = session.query(Entry).all()
for entry in tqdm(entries, desc="Inserting entries into Oracle DB"):
    cursor.execute("""
        INSERT INTO entries (
            entry_id, title, link, source, description, published, full_text, english_text, embedding,
            vaderPos, vaderNeu, vaderNeg, vaderCompound, germanSentimentPositive, germanSentimentNeutral,
            germanSentimentNegative, tbdPolarity, tbdSubjectivity, llmPositive, llmNeutral, llmNegative,
            llmPositiveStd, llmNeutralStd, llmNegativeStd, llmPositiveDiff, llmNeutralDiff, llmNegativeDiff,
            llmReasonList, llmIterations, llmBreakReason, llmIsAd, llmIsAdReason, iptcNewscode, iptcScore,
            dup_entry_id, dup_entry_conf
        ) VALUES (
            :entry_id, :title, :link, :source, :description, :published, :full_text, :english_text, :embedding,
            :vaderPos, :vaderNeu, :vaderNeg, :vaderCompound, :germanSentimentPositive, :germanSentimentNeutral,
            :germanSentimentNegative, :tbdPolarity, :tbdSubjectivity, :llmPositive, :llmNeutral, :llmNegative,
            :llmPositiveStd, :llmNeutralStd, :llmNegativeStd, :llmPositiveDiff, :llmNeutralDiff, :llmNegativeDiff,
            :llmReasonList, :llmIterations, :llmBreakReason, :llmIsAd, :llmIsAdReason, :iptcNewscode, :iptcScore,
            :dup_entry_id, :dup_entry_conf
        )
    """, {
        'entry_id': entry.entry_id, 'title': entry.title, 'link': entry.link, 'source': entry.source,
        'description': entry.description, 'published': entry.published, 'full_text': entry.full_text,
        'english_text': entry.english_text, 'embedding': entry.embedding, 'vaderPos': entry.vaderPos,
        'vaderNeu': entry.vaderNeu, 'vaderNeg': entry.vaderNeg, 'vaderCompound': entry.vaderCompound,
        'germanSentimentPositive': entry.germanSentimentPositive, 'germanSentimentNeutral': entry.germanSentimentNeutral,
        'germanSentimentNegative': entry.germanSentimentNegative, 'tbdPolarity': entry.tbdPolarity,
        'tbdSubjectivity': entry.tbdSubjectivity, 'llmPositive': entry.llmPositive, 'llmNeutral': entry.llmNeutral,
        'llmNegative': entry.llmNegative, 'llmPositiveStd': entry.llmPositiveStd, 'llmNeutralStd': entry.llmNeutralStd,
        'llmNegativeStd': entry.llmNegativeStd, 'llmPositiveDiff': entry.llmPositiveDiff, 'llmNeutralDiff': entry.llmNeutralDiff,
        'llmNegativeDiff': entry.llmNegativeDiff, 'llmReasonList': entry.llmReasonList, 'llmIterations': entry.llmIterations,
        'llmBreakReason': entry.llmBreakReason, 'llmIsAd': entry.llmIsAd, 'llmIsAdReason': entry.llmIsAdReason,
        'iptcNewscode': entry.iptcNewscode, 'iptcScore': entry.iptcScore, 'dup_entry_id': entry.dup_entry_id,
        'dup_entry_conf': entry.dup_entry_conf
    })

# Commit the transaction
connection.commit()

# Close the cursor and connection
cursor.close()
connection.close()