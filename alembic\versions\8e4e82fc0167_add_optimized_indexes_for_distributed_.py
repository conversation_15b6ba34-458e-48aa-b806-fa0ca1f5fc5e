"""Add optimized indexes for distributed processing work queue

Revision ID: 8e4e82fc0167
Revises: 1388a5ab8862
Create Date: 2025-09-18 07:13:14.272768

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import oracle

# revision identifiers, used by Alembic.
revision: str = '8e4e82fc0167'
down_revision: Union[str, None] = '1388a5ab8862'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add optimized indexes for distributed processing work queue queries
    op.create_index('idx_entries_retry_count', 'entries', ['retry_count'], unique=False)
    op.create_index('idx_entries_source_unclaimed', 'entries', ['source', 'claimed_by'], unique=False)
    op.create_index('idx_entries_stale_claims', 'entries', ['claimed_at', 'claimed_by'], unique=False)
    op.create_index('idx_entries_unclaimed_published', 'entries', ['claimed_by', 'claimed_at', sa.text('published DESC')], unique=False)
    op.create_index('idx_entries_worker_lookup', 'entries', ['claimed_by', 'entry_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop optimized indexes for distributed processing work queue queries
    op.drop_index('idx_entries_worker_lookup', table_name='entries')
    op.drop_index('idx_entries_unclaimed_published', table_name='entries')
    op.drop_index('idx_entries_stale_claims', table_name='entries')
    op.drop_index('idx_entries_source_unclaimed', table_name='entries')
    op.drop_index('idx_entries_retry_count', table_name='entries')
    # ### end Alembic commands ###
