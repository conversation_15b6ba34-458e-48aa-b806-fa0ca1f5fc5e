"""
Unit tests for worker configuration management.
"""

import pytest
import json
import os
import tempfile
from pathlib import Path
from unittest.mock import patch

from backend.app.distributed.worker_config import (
    WorkerConfig, WorkerConfigLoader, 
    create_download_worker_config, create_analysis_worker_config, create_general_worker_config
)
from backend.app.distributed.processing_stage import ProcessingStage


class TestWorkerConfig:
    """Test WorkerConfig dataclass."""
    
    def test_minimal_valid_config(self):
        """Test creating a minimal valid configuration."""
        config = WorkerConfig(
            worker_id="test-worker",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT]
        )
        
        assert config.worker_id == "test-worker"
        assert config.stages == [ProcessingStage.DOWNLOAD_FULL_TEXT]
        assert config.batch_size == 25  # default
        assert config.claim_timeout_minutes == 30  # default
        assert config.max_retries == 3  # default
    
    def test_full_config(self):
        """Test creating a configuration with all parameters."""
        stages = [ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS]
        
        config = WorkerConfig(
            worker_id="full-worker",
            stages=stages,
            batch_size=50,
            claim_timeout_minutes=60,
            heartbeat_interval_seconds=120,
            max_retries=5,
            database_url="oracle://user:pass@host:1521/db",
            connection_pool_size=10,
            connection_timeout_seconds=60,
            processing_delay_seconds=0.5,
            batch_claim_retry_delay_seconds=2.0,
            max_batch_claim_retries=10,
            log_level="DEBUG",
            metrics_enabled=False,
            health_check_port=8080,
            shutdown_timeout_seconds=600,
            stage_configs={"download_full_text": {"timeout": 30}}
        )
        
        assert config.worker_id == "full-worker"
        assert config.stages == stages
        assert config.batch_size == 50
        assert config.claim_timeout_minutes == 60
        assert config.heartbeat_interval_seconds == 120
        assert config.max_retries == 5
        assert config.database_url == "oracle://user:pass@host:1521/db"
        assert config.connection_pool_size == 10
        assert config.connection_timeout_seconds == 60
        assert config.processing_delay_seconds == 0.5
        assert config.batch_claim_retry_delay_seconds == 2.0
        assert config.max_batch_claim_retries == 10
        assert config.log_level == "DEBUG"
        assert config.metrics_enabled is False
        assert config.health_check_port == 8080
        assert config.shutdown_timeout_seconds == 600
        assert config.stage_configs == {"download_full_text": {"timeout": 30}}
    
    def test_validation_empty_worker_id(self):
        """Test validation fails for empty worker_id."""
        with pytest.raises(ValueError, match="worker_id must be a non-empty string"):
            WorkerConfig(worker_id="", stages=[ProcessingStage.DOWNLOAD_FULL_TEXT])
    
    def test_validation_long_worker_id(self):
        """Test validation fails for too long worker_id."""
        long_id = "x" * 256
        with pytest.raises(ValueError, match="worker_id must be 255 characters or less"):
            WorkerConfig(worker_id=long_id, stages=[ProcessingStage.DOWNLOAD_FULL_TEXT])
    
    def test_validation_empty_stages(self):
        """Test validation fails for empty stages list."""
        with pytest.raises(ValueError, match="stages list cannot be empty"):
            WorkerConfig(worker_id="test", stages=[])
    
    def test_validation_invalid_stage_type(self):
        """Test validation fails for invalid stage type."""
        with pytest.raises(ValueError, match="Invalid stage type"):
            WorkerConfig(worker_id="test", stages=["invalid_stage"])
    
    def test_validation_negative_batch_size(self):
        """Test validation fails for negative batch size."""
        with pytest.raises(ValueError, match="batch_size must be a positive integer"):
            WorkerConfig(
                worker_id="test",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                batch_size=-1
            )
    
    def test_validation_large_batch_size(self):
        """Test validation fails for too large batch size."""
        with pytest.raises(ValueError, match="batch_size should not exceed 1000"):
            WorkerConfig(
                worker_id="test",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                batch_size=1001
            )
    
    def test_validation_invalid_log_level(self):
        """Test validation fails for invalid log level."""
        with pytest.raises(ValueError, match="log_level must be one of"):
            WorkerConfig(
                worker_id="test",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                log_level="INVALID"
            )
    
    def test_validation_invalid_health_check_port(self):
        """Test validation fails for invalid health check port."""
        with pytest.raises(ValueError, match="health_check_port must be an integer between 1 and 65535"):
            WorkerConfig(
                worker_id="test",
                stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
                health_check_port=70000
            )
    
    def test_has_stage(self):
        """Test checking if worker has a specific stage."""
        config = WorkerConfig(
            worker_id="test",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS]
        )
        
        assert config.has_stage(ProcessingStage.DOWNLOAD_FULL_TEXT)
        assert config.has_stage(ProcessingStage.SENTIMENT_ANALYSIS)
        assert not config.has_stage(ProcessingStage.LLM_ANALYSIS)
    
    def test_stage_config_management(self):
        """Test stage-specific configuration management."""
        config = WorkerConfig(
            worker_id="test",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT]
        )
        
        # Test setting and getting stage config
        config.set_stage_config(ProcessingStage.DOWNLOAD_FULL_TEXT, "timeout", 30)
        assert config.get_stage_config(ProcessingStage.DOWNLOAD_FULL_TEXT, "timeout") == 30
        
        # Test default value
        assert config.get_stage_config(ProcessingStage.DOWNLOAD_FULL_TEXT, "nonexistent", "default") == "default"
        
        # Test stage without config
        assert config.get_stage_config(ProcessingStage.SENTIMENT_ANALYSIS, "timeout") is None
    
    def test_to_dict(self):
        """Test converting configuration to dictionary."""
        config = WorkerConfig(
            worker_id="test",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS],
            batch_size=50
        )
        
        result = config.to_dict()
        
        assert result["worker_id"] == "test"
        assert result["stages"] == ["download_full_text", "sentiment_analysis"]
        assert result["batch_size"] == 50
        assert "claim_timeout_minutes" in result
    
    def test_from_dict(self):
        """Test creating configuration from dictionary."""
        data = {
            "worker_id": "test",
            "stages": ["download_full_text", "sentiment_analysis"],
            "batch_size": 50,
            "claim_timeout_minutes": 45
        }
        
        config = WorkerConfig.from_dict(data)
        
        assert config.worker_id == "test"
        assert config.stages == [ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS]
        assert config.batch_size == 50
        assert config.claim_timeout_minutes == 45
    
    def test_from_dict_invalid_stage(self):
        """Test creating configuration from dictionary with invalid stage."""
        data = {
            "worker_id": "test",
            "stages": ["invalid_stage"],
            "batch_size": 50
        }
        
        with pytest.raises(ValueError, match="Unknown processing stage: invalid_stage"):
            WorkerConfig.from_dict(data)
    
    def test_json_serialization(self):
        """Test JSON serialization and deserialization."""
        original_config = WorkerConfig(
            worker_id="test",
            stages=[ProcessingStage.DOWNLOAD_FULL_TEXT],
            batch_size=50,
            stage_configs={"download_full_text": {"timeout": 30}}
        )
        
        # Serialize to JSON
        json_str = original_config.to_json()
        assert isinstance(json_str, str)
        
        # Deserialize from JSON
        restored_config = WorkerConfig.from_json(json_str)
        
        assert restored_config.worker_id == original_config.worker_id
        assert restored_config.stages == original_config.stages
        assert restored_config.batch_size == original_config.batch_size
        assert restored_config.stage_configs == original_config.stage_configs
    
    def test_from_json_invalid(self):
        """Test creating configuration from invalid JSON."""
        with pytest.raises(ValueError, match="Invalid JSON configuration"):
            WorkerConfig.from_json("invalid json")


class TestWorkerConfigLoader:
    """Test WorkerConfigLoader utility class."""
    
    @patch.dict(os.environ, {}, clear=True)
    def test_from_environment_defaults(self):
        """Test loading configuration from environment with defaults."""
        config = WorkerConfigLoader.from_environment(
            worker_id="env-test",
            stages=["download_full_text", "sentiment_analysis"]
        )
        
        assert config.worker_id == "env-test"
        assert config.stages == [ProcessingStage.DOWNLOAD_FULL_TEXT, ProcessingStage.SENTIMENT_ANALYSIS]
        assert config.batch_size == 25  # default
        assert config.log_level == "INFO"  # default
    
    @patch.dict(os.environ, {
        "WORKER_BATCH_SIZE": "50",
        "WORKER_LOG_LEVEL": "DEBUG",
        "WORKER_MAX_RETRIES": "5",
        "DATABASE_URL": "oracle://test:test@localhost:1521/xe"
    })
    def test_from_environment_with_overrides(self):
        """Test loading configuration from environment with overrides."""
        config = WorkerConfigLoader.from_environment(
            worker_id="env-test",
            stages=["llm_analysis"]
        )
        
        assert config.worker_id == "env-test"
        assert config.stages == [ProcessingStage.LLM_ANALYSIS]
        assert config.batch_size == 50
        assert config.log_level == "DEBUG"
        assert config.max_retries == 5
        assert config.database_url == "oracle://test:test@localhost:1521/xe"
    
    @patch.dict(os.environ, {
        "WORKER_ID": "env-worker-id",
        "WORKER_STAGES": "download_full_text,sentiment_analysis,llm_analysis"
    })
    def test_from_environment_full_env(self):
        """Test loading configuration entirely from environment."""
        config = WorkerConfigLoader.from_environment()
        
        assert config.worker_id == "env-worker-id"
        assert config.stages == [
            ProcessingStage.DOWNLOAD_FULL_TEXT,
            ProcessingStage.SENTIMENT_ANALYSIS,
            ProcessingStage.LLM_ANALYSIS
        ]
    
    @patch.dict(os.environ, {}, clear=True)
    @patch('socket.gethostname', return_value='test-host')
    @patch('uuid.uuid4')
    def test_from_environment_auto_worker_id(self, mock_uuid, mock_hostname):
        """Test automatic worker ID generation."""
        # Mock UUID to return different values on each call
        from unittest.mock import Mock as MockObj
        mock_uuid.side_effect = [
            MockObj(hex="12345678"),
            MockObj(hex="87654321")
        ]

        config = WorkerConfigLoader.from_environment(stages=["download_full_text"])

        assert config.worker_id.startswith("test-host-")
        # Should now have format: hostname-uuid-timestamp
        parts = config.worker_id.split("-")
        assert len(parts) >= 3  # hostname, uuid, timestamp parts

        # Test that multiple calls generate unique IDs (due to different timestamps)
        import time
        time.sleep(0.001)  # Ensure different timestamp
        config2 = WorkerConfigLoader.from_environment(stages=["download_full_text"])
        assert config.worker_id != config2.worker_id
    
    @patch.dict(os.environ, {"WORKER_STAGES": "invalid_stage"})
    def test_from_environment_invalid_stage(self):
        """Test loading configuration with invalid stage from environment."""
        with pytest.raises(ValueError, match="Unknown processing stage in environment: invalid_stage"):
            WorkerConfigLoader.from_environment(worker_id="test")
    
    def test_from_file(self):
        """Test loading configuration from file."""
        config_data = {
            "worker_id": "file-worker",
            "stages": ["download_full_text"],
            "batch_size": 75,
            "log_level": "WARNING"
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config = WorkerConfigLoader.from_file(temp_path)
            
            assert config.worker_id == "file-worker"
            assert config.stages == [ProcessingStage.DOWNLOAD_FULL_TEXT]
            assert config.batch_size == 75
            assert config.log_level == "WARNING"
        finally:
            os.unlink(temp_path)
    
    def test_from_file_not_found(self):
        """Test loading configuration from non-existent file."""
        with pytest.raises(FileNotFoundError, match="Configuration file not found"):
            WorkerConfigLoader.from_file("/nonexistent/path.json")
    
    def test_from_file_invalid_json(self):
        """Test loading configuration from file with invalid JSON."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            f.write("invalid json content")
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError, match="Failed to load configuration"):
                WorkerConfigLoader.from_file(temp_path)
        finally:
            os.unlink(temp_path)
    
    @patch.dict(os.environ, {
        "WORKER_BATCH_SIZE": "100",
        "WORKER_LOG_LEVEL": "ERROR"
    })
    def test_from_dict_with_env_override(self):
        """Test loading configuration from dict with environment overrides."""
        base_config = {
            "worker_id": "dict-worker",
            "stages": ["sentiment_analysis"],
            "batch_size": 25,
            "log_level": "INFO"
        }
        
        config = WorkerConfigLoader.from_dict_with_env_override(base_config)
        
        assert config.worker_id == "dict-worker"  # from dict
        assert config.batch_size == 100  # from env override
        assert config.log_level == "ERROR"  # from env override
        assert config.stages == [ProcessingStage.SENTIMENT_ANALYSIS]  # from dict
    
    @patch.dict(os.environ, {"WORKER_STAGES": "llm_analysis,iptc_classification"})
    def test_from_dict_with_env_stages_override(self):
        """Test loading configuration with stages override from environment."""
        base_config = {
            "worker_id": "dict-worker",
            "stages": ["sentiment_analysis"]
        }
        
        config = WorkerConfigLoader.from_dict_with_env_override(base_config)
        
        assert config.stages == [ProcessingStage.LLM_ANALYSIS, ProcessingStage.IPTC_CLASSIFICATION]


class TestConfigurationTemplates:
    """Test predefined configuration templates."""
    
    def test_create_download_worker_config(self):
        """Test creating download worker configuration."""
        config = create_download_worker_config("download-worker")
        
        assert config.worker_id == "download-worker"
        assert config.stages == [ProcessingStage.DOWNLOAD_FULL_TEXT]
        assert config.batch_size == 10  # smaller for network ops
        assert config.claim_timeout_minutes == 15  # shorter timeout
        assert config.max_retries == 5  # more retries for network
    
    def test_create_download_worker_config_with_overrides(self):
        """Test creating download worker configuration with overrides."""
        config = create_download_worker_config(
            "download-worker",
            batch_size=20,
            database_url="oracle://test:test@localhost:1521/xe"
        )
        
        assert config.worker_id == "download-worker"
        assert config.batch_size == 20  # overridden
        assert config.database_url == "oracle://test:test@localhost:1521/xe"  # overridden
        assert config.claim_timeout_minutes == 15  # default from template
    
    def test_create_analysis_worker_config(self):
        """Test creating analysis worker configuration."""
        config = create_analysis_worker_config("analysis-worker")
        
        assert config.worker_id == "analysis-worker"
        assert ProcessingStage.SENTIMENT_ANALYSIS in config.stages
        assert ProcessingStage.LLM_ANALYSIS in config.stages
        assert ProcessingStage.IPTC_CLASSIFICATION in config.stages
        assert config.batch_size == 5  # smaller for CPU-intensive
        assert config.claim_timeout_minutes == 60  # longer timeout
        assert config.processing_delay_seconds == 0.5  # longer delay
    
    def test_create_general_worker_config(self):
        """Test creating general worker configuration."""
        config = create_general_worker_config("general-worker")
        
        assert config.worker_id == "general-worker"
        assert len(config.stages) == len(ProcessingStage)  # all stages
        assert config.batch_size == 25  # standard size
        assert config.claim_timeout_minutes == 30  # standard timeout


if __name__ == "__main__":
    pytest.main([__file__])