"""Add processing status composite index for work queue optimization

Revision ID: 1388a5ab8862
Revises: 4f16f52da124
Create Date: 2025-09-18 00:22:33.471911

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1388a5ab8862'
down_revision: Union[str, None] = '4f16f52da124'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Oracle doesn't support indexes on CLOB columns, so we skip the processing_status index
    # The existing claimed_by and claimed_at indexes will be sufficient for work queue queries
    pass


def downgrade() -> None:
    # No index was created, so nothing to drop
    pass
