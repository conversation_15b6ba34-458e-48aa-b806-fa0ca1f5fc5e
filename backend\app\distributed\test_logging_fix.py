#!/usr/bin/env python3
"""
Test script to verify SQLAlchemy logging format errors are fixed.
"""

import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from backend.app.core.database import SessionLocal, engine
from backend.app.models.models import Entry
from backend.app.distributed.logging_config import setup_distributed_logging

def test_logging_fix():
    """Test that SQLAlchemy operations don't cause logging format errors."""
    
    print("Setting up distributed logging...")
    setup_distributed_logging(log_level="INFO", enable_colors=False)
    
    print("Testing database operations that previously caused logging errors...")
    
    try:
        # Create a database session
        db = SessionLocal()
        
        # Perform operations that previously caused logging format errors
        print("1. Testing query operation...")
        entries = db.query(Entry).limit(5).all()
        print(f"   Found {len(entries)} entries")
        
        print("2. Testing update operation...")
        if entries:
            entry = entries[0]
            # This type of update operation was causing the logging format error
            result = db.query(Entry).filter_by(entry_id=entry.entry_id).update(
                {"full_text": entry.full_text}  # Update with same value (no-op)
            )
            db.commit()
            print(f"   Updated {result} entries")
        
        print("3. Testing filter operations...")
        count = db.query(Entry).filter(Entry.full_text.isnot(None)).count()
        print(f"   Found {count} entries with full text")
        
        db.close()
        print("✅ All database operations completed without logging errors!")
        
    except Exception as e:
        print(f"❌ Error during database operations: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_sqlalchemy_logger_configuration():
    """Test that SQLAlchemy loggers are properly configured."""
    
    print("\nTesting SQLAlchemy logger configuration...")
    
    sqlalchemy_loggers = [
        'sqlalchemy.engine',
        'sqlalchemy.engine.Engine',
        'sqlalchemy.pool',
        'sqlalchemy.dialects',
        'sqlalchemy.orm',
        'sqlalchemy.engine.base',
        'sqlalchemy.engine.base.Engine'
    ]
    
    all_configured = True
    
    for logger_name in sqlalchemy_loggers:
        logger_obj = logging.getLogger(logger_name)
        level = logger_obj.level
        propagate = logger_obj.propagate
        
        print(f"   {logger_name}: level={logging.getLevelName(level)}, propagate={propagate}")
        
        if level < logging.ERROR:
            print(f"   ⚠️  Warning: {logger_name} level is {logging.getLevelName(level)}, should be ERROR")
            all_configured = False
        
        if propagate:
            print(f"   ⚠️  Warning: {logger_name} propagate is True, should be False")
            all_configured = False
    
    if all_configured:
        print("✅ All SQLAlchemy loggers properly configured!")
    else:
        print("❌ Some SQLAlchemy loggers need configuration fixes")
    
    return all_configured

def main():
    """Run all logging tests."""
    print("=" * 60)
    print("SQLAlchemy Logging Format Error Fix Test")
    print("=" * 60)

    # Apply the fix first
    from backend.app.distributed.logging_config import fix_sqlalchemy_logging
    fix_sqlalchemy_logging()

    # Test logger configuration
    config_ok = test_sqlalchemy_logger_configuration()

    # Test actual database operations
    operations_ok = test_logging_fix()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"Logger Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"Database Operations:  {'✅ PASS' if operations_ok else '❌ FAIL'}")
    
    if config_ok and operations_ok:
        print("🎉 All tests passed! Logging format errors should be fixed.")
        return 0
    else:
        print("💥 Some tests failed. Logging format errors may still occur.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
