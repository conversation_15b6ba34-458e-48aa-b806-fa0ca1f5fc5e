# To ensure app dependencies are ported from your virtual environment/host machine into your container, run 'pip freeze > requirements.txt' in the terminal to overwrite this file
fastapi[all]==0.109.0
uvicorn[standard]==0.27.0
gunicorn==21.2.0
sqlalchemy==2.0.38
# python-dotenv==1.0.0
pydantic==2.10.6
pydantic-settings==2.8.1
oracledb==3.0.0
requests==2.32.3
pandas==2.2.3
# numpy==1.26.3
# scikit-learn==1.3.2
