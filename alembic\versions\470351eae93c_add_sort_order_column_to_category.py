"""Add sort_order column to category

Revision ID: 470351eae93c
Revises: 42d06979fb36
Create Date: 2025-09-07 15:32:16.878784

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '470351eae93c'
down_revision: Union[str, None] = '42d06979fb36'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('categories', sa.Column('sort_order', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('categories', 'sort_order')
    # ### end Alembic commands ###
