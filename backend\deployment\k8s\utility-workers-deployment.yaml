apiVersion: apps/v1
kind: Deployment
metadata:
  name: utility-workers
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: utility-worker
    app.kubernetes.io/part-of: distributed-etl
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: breaking-bright
      app.kubernetes.io/component: utility-worker
  template:
    metadata:
      labels:
        app.kubernetes.io/name: breaking-bright
        app.kubernetes.io/component: utility-worker
        app.kubernetes.io/part-of: distributed-etl
    spec:
      containers:
      - name: utility-worker
        image: robertschulze/breaking-bright-worker:latest
        imagePullPolicy: Always
        command: ["python", "-m", "backend.app.distributed.distributed_worker"]
        ports:
        - containerPort: 8080
          name: health-check
          protocol: TCP
        env:
        - name: WORKER_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: WORKER_STAGES
          value: "duplicate_check,generate_rss_feed"
        - name: WORKER_BATCH_SIZE
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: UTILITY_WORKER_BATCH_SIZE
        - name: WORKER_CLAIM_TIMEOUT_MINUTES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: UTILITY_WORKER_CLAIM_TIMEOUT_MINUTES
        - name: WORKER_MAX_RETRIES
          valueFrom:
            configMapKeyRef:
              name: distributed-worker-config
              key: UTILITY_WORKER_MAX_RETRIES
        - name: WORKER_HEALTH_CHECK_PORT
          value: "8080"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: database-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: openai-api-key
        - name: IONOS_API_KEY
          valueFrom:
            secretKeyRef:
              name: distributed-worker-secrets
              key: ionos-api-key
        envFrom:
        - configMapRef:
            name: distributed-worker-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: worker-logs
          mountPath: /var/log/worker
        - name: rss-output
          mountPath: /app/rss
      volumes:
      - name: worker-logs
        emptyDir: {}
      - name: rss-output
        emptyDir:
          sizeLimit: 1Gi
      imagePullSecrets:
      - name: docker-registry-secret
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: utility-workers-service
  namespace: breaking-bright-distributed
  labels:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: utility-worker
spec:
  selector:
    app.kubernetes.io/name: breaking-bright
    app.kubernetes.io/component: utility-worker
  ports:
  - name: health-check
    port: 8080
    targetPort: 8080
    protocol: TCP
  type: ClusterIP