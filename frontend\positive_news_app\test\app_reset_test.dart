import 'package:flutter_test/flutter_test.dart';
import 'package:breakingbright/providers/settings_provider.dart';
import 'package:breakingbright/services/favorites_service.dart';
import 'package:breakingbright/services/app_restart_service.dart';
import 'package:breakingbright/models/news_model.dart';
import 'package:shared_preferences_platform_interface/shared_preferences_platform_interface.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('App Reset Tests', () {
    late SettingsProvider settingsProvider;
    late FavoritesService favoritesService;

    setUp(() {
      // Use in-memory SharedPreferences for testing
      SharedPreferencesStorePlatform.instance = InMemorySharedPreferencesStore.empty();
      settingsProvider = SettingsProvider();
      favoritesService = FavoritesService();
    });

    test('Settings reset clears all preferences', () async {
      // Set some non-default values
      await settingsProvider.setLookbackHours(72);
      await settingsProvider.setShowLlmScore(false);
      await settingsProvider.setCategoryOrder(['test1', 'test2']);

      // Verify values are set
      expect(settingsProvider.lookbackHours, 72);
      expect(settingsProvider.showLlmScore, false);
      expect(settingsProvider.categoryOrder, ['test1', 'test2']);

      // Reset all settings
      await settingsProvider.resetAllSettings();

      // Verify values are back to defaults
      expect(settingsProvider.lookbackHours, SettingsProvider.defaultLookbackHours);
      expect(settingsProvider.showLlmScore, SettingsProvider.defaultShowLlmScore);
      expect(settingsProvider.categoryOrder, isEmpty);
    });

    test('Favorites reset clears all favorites', () async {
      // Add a test favorite
      final testNews = EntryWithSource(
        entryId: 'test_id',
        title: 'Test News',
        link: 'https://example.com',
        source: 'test_source',
        published: DateTime.now(),
        llmPositive: 0.8,
        llmReasonList: 'Test reason',
        hasSimilar: null,
      );

      favoritesService.addFavorite(testNews);
      expect(favoritesService.favorites.length, 1);

      // Clear favorites
      favoritesService.clearFavorites();
      expect(favoritesService.favorites.length, 0);
    });

    test('AppRestartService can register and call restart callback', () {
      bool restartCalled = false;
      
      // Register callback
      AppRestartService.registerRestartCallback(() {
        restartCalled = true;
      });

      expect(AppRestartService.canRestart, true);

      // Call restart
      AppRestartService.restartApp();
      expect(restartCalled, true);
    });

    test('AppRestartService handles missing callback gracefully', () {
      // Clear any existing callback by creating a new instance
      AppRestartService.registerRestartCallback(() {});
      
      // This should not throw an exception
      expect(() => AppRestartService.restartApp(), returnsNormally);
    });
  });
}
