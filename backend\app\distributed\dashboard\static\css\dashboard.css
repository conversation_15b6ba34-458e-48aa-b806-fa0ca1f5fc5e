/* Distributed ETL Dashboard Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Stage-specific colors matching monolithic system */
.stage-download-feeds {
    background-color: #343a40;
    color: white;
}

.stage-download-full-text {
    background-color: #28a745;
    color: white;
}

.stage-compute-embeddings {
    background-color: #28a745;
    color: white;
}

.stage-sentiment-analysis {
    background-color: #90ee90;
    color: black;
}

.stage-llm-analysis {
    background-color: #dda0dd;
    color: black;
}

.stage-iptc-classification {
    background-color: #ffffe0;
    color: black;
}

.stage-duplicate-check {
    background-color: #e0ffff;
    color: black;
}

.stage-generate-descriptions {
    background-color: #f8f9fa;
    color: black;
}

.stage-generate-image-prompts {
    background-color: #007bff;
    color: white;
}

.stage-generate-preview-images {
    background-color: #add8e6;
    color: black;
}

/* Worker status indicators */
.worker-status-running {
    color: #28a745;
}

.worker-status-stopped {
    color: #6c757d;
}

.worker-status-error {
    color: #dc3545;
}

/* Chart containers */
.chart-container {
    position: relative;
    height: 200px;
    margin: 1rem 0;
}

/* Activity log */
.activity-log {
    max-height: 300px;
    overflow-y: auto;
}

/* Connection status */
#connection-status {
    margin-right: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem 0.5rem;
    }
    
    .chart-container {
        height: 150px;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Metrics cards */
.metric-card {
    text-align: center;
    padding: 1rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Table improvements */
.table-responsive {
    border-radius: 0.5rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Button improvements */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
}

.btn-sm {
    font-size: 0.8125rem;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
