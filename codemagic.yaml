# This is a basic codemagic.yaml file.
# It defines a simple workflow for a Flutter app.

workflows:
  my-workflow:
    name: My Workflow
    instance_type: mac_mini_m2
    working_directory: frontend/positive_news_app
    environment:
      flutter: stable
    scripts:
      - name: Install dependencies
        script: |
          flutter pub get
      - name: Run tests
        script: |
          flutter test
      - name: Build Android
        script: |
          flutter build apk --release
      - name: Build iOS
        script: |
          flutter build ios --release --no-codesign
    artifacts:
      - build/app/outputs/apk/release/app-release.apk
      - build/ios/iphoneos/Runner.app
    publishing:
      # This is where you would configure your deployment.
      # For example, to a hosting service or app store.
      # We'll leave this empty for now.
      #
      # For more information, see the Codemagic documentation:
      # https://docs.codemagic.io/yaml-publishing/google-play/
      # https://docs.codemagic.io/yaml-publishing/app-store-connect/
      email:
        recipients:
          - <EMAIL>
        notify:
          success: true
          failure: false
