"""
Multi-stage worker for handling multiple processing stages.

This worker coordinates multiple specialized workers to handle
different processing stages within a single worker instance.
"""

import logging
from typing import List, Dict, Callable
from sqlalchemy.orm import Session

from backend.app.models.models import Entry
from backend.app.distributed.distributed_worker import DistributedWorker
from backend.app.distributed.processing_stage import ProcessingStage
from backend.app.distributed.worker_config import WorkerConfig

logger = logging.getLogger(__name__)


class MultiStageWorker(DistributedWorker):
    """
    Worker that can handle multiple processing stages by coordinating specialized workers.
    
    This worker creates specialized worker instances for each configured stage
    and delegates processing to the appropriate worker.
    """
    
    def __init__(self, config: WorkerConfig, db_session_factory: Callable[[], Session]):
        """
        Initialize the multi-stage worker.
        
        Args:
            config: Worker configuration with multiple stages
            db_session_factory: Factory function to create database sessions
        """
        super().__init__(config, db_session_factory)
        
        # Import here to avoid circular imports
        from backend.app.distributed.worker_factory import WorkerFactory
        
        # Create specialized workers for each stage
        self._stage_workers = {}
        
        for stage in config.stages:
            if WorkerFactory.is_stage_supported(stage):
                # Create a single-stage config for the specialized worker
                # IMPORTANT: Use the SAME worker_id for all specialized workers
                # to maintain singleton behavior across stages
                stage_config = WorkerConfig(
                    worker_id=config.worker_id,  # Same worker_id for singleton consistency
                    stages=[stage],
                    batch_size=config.batch_size,
                    claim_timeout_minutes=config.claim_timeout_minutes,
                    max_retries=config.max_retries,
                    heartbeat_interval_seconds=config.heartbeat_interval_seconds,
                    no_work_delay_seconds=config.no_work_delay_seconds,
                    log_level=config.log_level
                )

                # Create the specialized worker
                worker_class = WorkerFactory.WORKER_CLASSES[stage]
                self._stage_workers[stage] = worker_class(stage_config, db_session_factory)

                logger.info(f"Initialized {worker_class.__name__} for stage {stage.value}")
            else:
                logger.warning(f"No specialized worker available for stage {stage.value}")
        
        logger.info(f"MultiStageWorker initialized with {len(self._stage_workers)} specialized workers")
    
    def process_batch(self, entries: List[Entry], stage: ProcessingStage) -> Dict[str, bool]:
        """
        Process a batch of entries for a specific stage by delegating to the specialized worker.
        
        Args:
            entries: List of Entry objects to process
            stage: Processing stage to execute
            
        Returns:
            Dictionary mapping entry_id to success status (True/False)
        """
        logger.debug(f"Processing batch of {len(entries)} entries for stage {stage.value}")
        
        # Get the specialized worker for this stage
        stage_worker = self._stage_workers.get(stage)
        if not stage_worker:
            logger.error(f"No specialized worker available for stage {stage.value}")
            return {entry.entry_id: False for entry in entries}
        
        try:
            # Delegate to the specialized worker
            return stage_worker.process_batch(entries, stage)
            
        except Exception as e:
            logger.error(f"Error processing batch for stage {stage.value}: {e}")
            return {entry.entry_id: False for entry in entries}
    
    def get_health_status(self) -> Dict[str, any]:
        """
        Get health status including information from all specialized workers.
        
        Returns:
            Dictionary containing health status information
        """
        health = super().get_health_status()
        
        # Add information about specialized workers
        health.update({
            'worker_type': 'MultiStageWorker',
            'specialized_workers': {
                stage.value: worker.__class__.__name__ 
                for stage, worker in self._stage_workers.items()
            },
            'supported_stages': [stage.value for stage in self._stage_workers.keys()]
        })
        
        return health