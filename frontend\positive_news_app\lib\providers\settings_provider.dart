import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider with ChangeNotifier {
  static const String _lookbackHoursKey = 'lookback_hours';
  static const String _showLlmScoreKey = 'show_llm_score';
  static const String _categoryOrderKey = 'category_order';
  static const int defaultLookbackHours = 48;
  static const bool defaultShowLlmScore = true;

  late SharedPreferences _prefs;
  int _lookbackHours = defaultLookbackHours;
  bool _showLlmScore = defaultShowLlmScore;
  List<String> _categoryOrder = [];

  int get lookbackHours => _lookbackHours;
  bool get showLlmScore => _showLlmScore;
  List<String> get categoryOrder => List.from(_categoryOrder);

  SettingsProvider() {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _prefs = await SharedPreferences.getInstance();
    _lookbackHours = _prefs.getInt(_lookbackHoursKey) ?? defaultLookbackHours;
    _showLlmScore = _prefs.getBool(_showLlmScoreKey) ?? defaultShowLlmScore;

    // Load category order
    final savedOrder = _prefs.getStringList(_categoryOrderKey);
    _categoryOrder = savedOrder ?? [];

    notifyListeners();
  }

  Future<void> setLookbackHours(int hours) async {
    if (hours != _lookbackHours) {
      _lookbackHours = hours;
      await _prefs.setInt(_lookbackHoursKey, hours);
      notifyListeners();
    }
  }

  Future<void> setShowLlmScore(bool show) async {
    if (show != _showLlmScore) {
      _showLlmScore = show;
      await _prefs.setBool(_showLlmScoreKey, show);
      notifyListeners();
    }
  }

  Future<void> setCategoryOrder(List<String> newOrder) async {
    if (!_listEquals(_categoryOrder, newOrder)) {
      _categoryOrder = List.from(newOrder);
      await _prefs.setStringList(_categoryOrderKey, _categoryOrder);
      notifyListeners();
    }
  }

  Future<void> resetCategoryOrder() async {
    _categoryOrder = [];
    await _prefs.remove(_categoryOrderKey);
    notifyListeners();
  }

  /// Resets all app settings to their default values
  Future<void> resetAllSettings() async {
    // Reset to default values
    _lookbackHours = defaultLookbackHours;
    _showLlmScore = defaultShowLlmScore;
    _categoryOrder = [];

    // Clear all stored preferences
    await _prefs.clear();

    notifyListeners();
  }

  /// Ensures all provided categories are included in the saved order.
  /// New categories are added at the end, preserving existing order.
  /// This should be called when new categories are discovered from API responses.
  Future<void> ensureCategoriesIncluded(List<String> allCategories) async {
    final currentOrder = List<String>.from(_categoryOrder);
    bool hasChanges = false;

    // Add any missing categories at the end
    for (final category in allCategories) {
      if (!currentOrder.contains(category)) {
        currentOrder.add(category);
        hasChanges = true;
      }
    }

    if (hasChanges) {
      _categoryOrder = currentOrder;
      await _prefs.setStringList(_categoryOrderKey, _categoryOrder);
      notifyListeners();
    }
  }

  // Helper method to compare lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }
}
