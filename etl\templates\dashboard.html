<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>News Aggregator Dashboard</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f7;
            color: #333;
        }
        h1, h2 {
            text-align: center;
            font-weight: 700;
            margin: 20px 0;
            color: #2c3e50;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            padding: 20px;
        }
        .chart-container {
            width: 300px;
            margin: 8px;
            text-align: center;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .stats-chart-container {
            width: 90%;
            max-width: 1200px;
            margin: 20px auto;
            text-align: center;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .chart-container h3, .stats-chart-container h3 {
            font-weight: 500;
            margin-bottom: 10px;
            color: #34495e;
        }
        .last-updated {
            text-align: center;
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
    </style>
    <script>
        const charts = {};
        let newsStateChart = null;
        let newsSourceChart = null;
        let positiveNewsBySourceChart = null;
        let duplicatesBySourceChart = null;
        let lastStatsUpdate = new Date();

        // Custom plugin to render text in the center of the doughnut chart
        const centerTextPlugin = {
            id: 'centerText',
            beforeDraw(chart) {
                const { width, height, ctx } = chart;
                const text = chart.options.plugins.centerText.text;

                const centerX = width / 2;
                const centerY = height / 2;

                ctx.save();
                ctx.font = '16px Roboto, sans-serif';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';

                const textMetrics = ctx.measureText(text);
                const textHeight = textMetrics.actualBoundingBoxAscent + textMetrics.actualBoundingBoxDescent;

                ctx.fillStyle = '#2c3e50';
                ctx.fillText(text, centerX, centerY + textHeight * 1.3);
                ctx.restore();
            }
        };

        async function fetchProgress() {
            try {
                const response = await fetch('/progress');
                const data = await response.json();
                const progressContainer = document.getElementById('progress-container');

                for (const [thread, details] of Object.entries(data)) {
                    const progress = details.progress || 1;
                    const size = details.size || 1;
                    const percentage = Math.round((progress / size) * 100);

                    if (!charts[thread]) {
                        let chartDiv = document.getElementById(`${thread}-container`);
                        if (!chartDiv) {
                            chartDiv = document.createElement('div');
                            chartDiv.id = `${thread}-container`;
                            chartDiv.className = 'chart-container';
                            chartDiv.innerHTML = `
                                <h3>${thread}</h3>
                                <canvas id="${thread}-chart"></canvas>
                            `;
                            progressContainer.appendChild(chartDiv);
                        }

                        const ctx = document.getElementById(`${thread}-chart`).getContext('2d');
                        charts[thread] = new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: ['Completed', 'Remaining'],
                                datasets: [{
                                    data: [progress, size - progress],
                                    backgroundColor: ['#355E3B', '#A9CBA4'],
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                animation: {
                                    duration: 500,
                                },
                                plugins: {
                                    tooltip: {
                                        callbacks: {
                                            label: function (tooltipItem) {
                                                return `${tooltipItem.label}: ${tooltipItem.raw}`;
                                            }
                                        }
                                    },
                                    centerText: {
                                        text: `${progress} / ${size}`
                                    }
                                }
                            },
                            plugins: [centerTextPlugin]
                        });
                    } else {
                        charts[thread].data.datasets[0].data = [progress, size - progress];
                        charts[thread].options.plugins.centerText.text = `${progress} / ${size}`;
                        charts[thread].update();
                    }
                }
            } catch (error) {
                console.error('Error fetching progress:', error);
            }
        }

        async function fetchNewsStateStats() {
            try {
                console.log('Fetching news state stats...');
                const response = await fetch('/stats/news_by_state');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log('Received news state data:', data);

                const chartContainer = document.getElementById('news-state-chart-container');
                const ctx = document.getElementById('news-state-chart').getContext('2d');

                if (newsStateChart) {
                    newsStateChart.destroy();
                }

                // Use sample data if the response doesn't have the expected format
                const dates = data.dates || ['2025-04-01', '2025-04-02', '2025-04-03', '2025-04-04', '2025-04-05'];
                const datasets = data.datasets || [
                    {
                        label: 'Total (Sample)',
                        data: [100, 120, 130, 140, 150],
                        borderColor: '#3e95cd',
                        fill: false
                    },
                    {
                        label: 'Has Full Text (Sample)',
                        data: [80, 90, 100, 110, 120],
                        borderColor: '#8e5ea2',
                        fill: false
                    }
                ];

                newsStateChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: dates,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'News Count by Processing State'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Count'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });

                lastStatsUpdate = new Date();
                document.getElementById('last-updated').textContent = `Last updated: ${lastStatsUpdate.toLocaleTimeString()}`;
            } catch (error) {
                console.error('Error fetching news state stats:', error);
                // Create a chart with sample data if there's an error
                const ctx = document.getElementById('news-state-chart').getContext('2d');
                if (newsStateChart) {
                    newsStateChart.destroy();
                }

                newsStateChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['2025-04-01', '2025-04-02', '2025-04-03', '2025-04-04', '2025-04-05'],
                        datasets: [
                            {
                                label: 'Total (Sample)',
                                data: [100, 120, 130, 140, 150],
                                borderColor: '#3e95cd',
                                fill: false
                            },
                            {
                                label: 'Has Full Text (Sample)',
                                data: [80, 90, 100, 110, 120],
                                borderColor: '#8e5ea2',
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'News Count by Processing State (Sample Data - Error Loading Real Data)'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Count'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });

                lastStatsUpdate = new Date();
                document.getElementById('last-updated').textContent = `Last updated (error): ${lastStatsUpdate.toLocaleTimeString()}`;
            }
        }

        async function fetchNewsSourceStats() {
            try {
                console.log('Fetching news source stats...');
                const response = await fetch('/stats/news_by_source');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log('Received news source data:', data);

                const chartContainer = document.getElementById('news-source-chart-container');
                const ctx = document.getElementById('news-source-chart').getContext('2d');

                if (newsSourceChart) {
                    newsSourceChart.destroy();
                }

                // Use sample data if the response doesn't have the expected format
                const dates = data.dates || ['2025-04-01', '2025-04-02', '2025-04-03', '2025-04-04', '2025-04-05'];
                const datasets = data.datasets || [
                    {
                        label: 'Source 1 (Sample)',
                        data: [30, 40, 50, 45, 60],
                        borderColor: '#3e95cd',
                        fill: false
                    },
                    {
                        label: 'Source 2 (Sample)',
                        data: [20, 30, 25, 40, 35],
                        borderColor: '#8e5ea2',
                        fill: false
                    },
                    {
                        label: 'Source 3 (Sample)',
                        data: [15, 20, 25, 30, 35],
                        borderColor: '#3cba9f',
                        fill: false
                    }
                ];

                newsSourceChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: dates,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'News Count by Source'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Count'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error fetching news source stats:', error);
                // Create a chart with sample data if there's an error
                const ctx = document.getElementById('news-source-chart').getContext('2d');
                if (newsSourceChart) {
                    newsSourceChart.destroy();
                }

                newsSourceChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['2025-04-01', '2025-04-02', '2025-04-03', '2025-04-04', '2025-04-05'],
                        datasets: [
                            {
                                label: 'Source 1 (Sample)',
                                data: [30, 40, 50, 45, 60],
                                borderColor: '#3e95cd',
                                fill: false
                            },
                            {
                                label: 'Source 2 (Sample)',
                                data: [20, 30, 25, 40, 35],
                                borderColor: '#8e5ea2',
                                fill: false
                            },
                            {
                                label: 'Source 3 (Sample)',
                                data: [15, 20, 25, 30, 35],
                                borderColor: '#3cba9f',
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'News Count by Source (Sample Data - Error Loading Real Data)'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Count'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        async function fetchPositiveNewsBySource() {
            try {
                console.log('Fetching positive news by source stats...');
                const response = await fetch('/stats/positive_news_by_source');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log('Received positive news by source data:', data);

                const chartContainer = document.getElementById('positive-news-by-source-chart-container');
                const ctx = document.getElementById('positive-news-by-source-chart').getContext('2d');

                if (positiveNewsBySourceChart) {
                    positiveNewsBySourceChart.destroy();
                }

                // Use sample data if the response doesn't have the expected format
                const labels = data.labels || ['Source 1', 'Source 2', 'Source 3', 'Source 4', 'Source 5'];
                const percentages = data.percentages || [75, 60, 45, 30, 15];

                positiveNewsBySourceChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Percentage of Positive News',
                            data: percentages,
                            backgroundColor: '#3cba9f',
                            borderColor: '#2a9d8f',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',  // Horizontal bar chart
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Percentage of Positive News by Source (llm_positive >= 0.7)'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const index = context.dataIndex;
                                        const total = data.totals ? data.totals[index] : 100;
                                        const positive = data.positives ? data.positives[index] : Math.round(percentages[index]);
                                        return `${context.formattedValue}% (${positive}/${total})`;
                                    }
                                }
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                max: 100,
                                title: {
                                    display: true,
                                    text: 'Percentage (%)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'Source'
                                }
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error fetching positive news by source stats:', error);
                // Create a chart with sample data if there's an error
                const ctx = document.getElementById('positive-news-by-source-chart').getContext('2d');
                if (positiveNewsBySourceChart) {
                    positiveNewsBySourceChart.destroy();
                }

                positiveNewsBySourceChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Source 1', 'Source 2', 'Source 3', 'Source 4', 'Source 5'],
                        datasets: [{
                            label: 'Percentage of Positive News',
                            data: [75, 60, 45, 30, 15],
                            backgroundColor: '#3cba9f',
                            borderColor: '#2a9d8f',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',  // Horizontal bar chart
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Percentage of Positive News by Source (Sample Data - Error Loading Real Data)'
                            },
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                max: 100,
                                title: {
                                    display: true,
                                    text: 'Percentage (%)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: 'Source'
                                }
                            }
                        }
                    }
                });
            }
        }

        async function fetchDuplicatesBySource() {
            try {
                console.log('Fetching duplicates by source stats...');
                const response = await fetch('/stats/duplicates_by_source');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log('Received duplicates by source data:', data);

                const chartContainer = document.getElementById('duplicates-by-source-chart-container');
                const ctx = document.getElementById('duplicates-by-source-chart').getContext('2d');

                if (duplicatesBySourceChart) {
                    duplicatesBySourceChart.destroy();
                }

                duplicatesBySourceChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.dates,
                        datasets: data.datasets
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Duplicate News Count by Source'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Number of Duplicates'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error fetching duplicates by source stats:', error);
                // Create chart with sample data if there's an error
                const ctx = document.getElementById('duplicates-by-source-chart').getContext('2d');
                if (duplicatesBySourceChart) {
                    duplicatesBySourceChart.destroy();
                }

                duplicatesBySourceChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['2025-04-01', '2025-04-02', '2025-04-03', '2025-04-04', '2025-04-05'],
                        datasets: [
                            {
                                label: 'Source 1 (Sample)',
                                data: [5, 8, 6, 9, 7],
                                borderColor: '#3e95cd',
                                fill: false
                            },
                            {
                                label: 'Source 2 (Sample)',
                                data: [3, 4, 5, 3, 6],
                                borderColor: '#8e5ea2',
                                fill: false
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Duplicate News Count by Source (Sample Data - Error Loading Real Data)'
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            x: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            },
                            y: {
                                display: true,
                                title: {
                                    display: true,
                                    text: 'Number of Duplicates'
                                },
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        }

        function updateStats() {
            fetchNewsStateStats();
            fetchNewsSourceStats();
            fetchPositiveNewsBySource();
            fetchDuplicatesBySource();
        }

        // Update progress every second
        setInterval(fetchProgress, 1000);

        // Update statistics every 15 minutes (900000 ms) without page reload
        setInterval(updateStats, 900000);

        // Update individual charts every 5 minutes to stagger the updates
        setInterval(fetchNewsStateStats, 300000);
        setInterval(fetchNewsSourceStats, 300000 + 60000);  // Offset by 1 minute
        setInterval(fetchPositiveNewsBySource, 300000 + 120000);  // Offset by 2 minutes
        setInterval(fetchDuplicatesBySource, 300000 + 180000);  // Offset by 3 minutes

        window.onload = function() {
            fetchProgress();
            updateStats();
        };
    </script>
</head>
<body>
    <div style="text-align: center; margin: 10px 0;">
        <a href="/" style="margin: 0 10px; color: #3498db; text-decoration: none;">Dashboard Home</a> |
        <a href="/sql_query" style="margin: 0 10px; color: #3498db; text-decoration: none;">SQL Query Tool</a>
    </div>

    <h1>News Aggregator Dashboard</h1>

    <h2>Processing Progress</h2>
    <div id="progress-container" class="container">
        <!-- Progress charts will be dynamically inserted here -->
    </div>

    <h2>Statistics</h2>
    <div id="news-state-chart-container" class="stats-chart-container">
        <h3>News by Processing State</h3>
        <canvas id="news-state-chart"></canvas>
    </div>

    <div id="news-source-chart-container" class="stats-chart-container">
        <h3>News by Source</h3>
        <canvas id="news-source-chart"></canvas>
    </div>

    <div id="positive-news-by-source-chart-container" class="stats-chart-container">
        <h3>Percentage of Positive News by Source</h3>
        <canvas id="positive-news-by-source-chart"></canvas>
    </div>

    <div id="duplicates-by-source-chart-container" class="stats-chart-container">
        <h3>Duplicates by Source</h3>
        <canvas id="duplicates-by-source-chart"></canvas>
    </div>

    <p id="last-updated" class="last-updated">Last updated: -</p>
</body>
</html>
